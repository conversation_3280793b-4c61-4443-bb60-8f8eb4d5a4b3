/**
 * Claude Code 3.0 - Simplified Entry Point
 * 
 * Production-ready multi-agent AI system with minimal exports
 */

// Core API Server (main working component)
export { default as startServer } from './api-server.js';

// Basic types for external use
export interface SimpleSystemConfig {
  port?: number;
  environment?: 'development' | 'production';
  enableDebug?: boolean;
}

// Simple system starter function
export async function startClaudeCode(config: SimpleSystemConfig = {}) {
  const { port = 8080, environment = 'development' } = config;
  
  console.log(`🚀 Starting Claude Code 3.0 on port ${port}`);
  console.log(`📊 Environment: ${environment}`);
  
  // Start the API server (this is the main working component)
  const server = await startServer();
  
  return {
    server,
    port,
    environment,
    stop: () => {
      console.log('🛑 Stopping Claude Code 3.0...');
      server.close();
    }
  };
}

// Default export for simple usage
export default startClaudeCode;
