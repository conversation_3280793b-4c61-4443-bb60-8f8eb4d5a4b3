# CLAUDE.md Initialization Command Prompt

## 基本信息
- **文件位置**: improved-claude-code-5.mjs:56246-56270
- **命令**: /init
- **提示词类型**: 代码库分析和配置文件生成指导

## 运行时机
- **触发条件**: 用户执行 `/init` 命令时
- **调用场景**: 
  - 首次在新项目中使用Claude Code
  - 需要生成或更新CLAUDE.md配置文件
  - 项目结构发生重大变化时
- **执行路径**: 命令解析 → init命令识别 → getPromptForCommand调用

## 系统运转时机
- **生命周期**: 命令级别，单次执行完成特定任务
- **优先级**: 高优先级，关系到Claude Code的项目适配
- **持续性**: 执行完成后生成持久化的配置文件

## 作用时机
- **项目分析**: 在开始分析项目结构前
- **配置生成**: 在创建CLAUDE.md文件时
- **适配优化**: 为后续的Claude Code使用建立基础

## 作用目的
1. **项目理解**: 深入分析代码库结构和架构
2. **配置生成**: 创建专门的CLAUDE.md配置文件
3. **效率提升**: 为后续实例提供项目上下文
4. **标准化**: 建立项目特定的操作规范
5. **知识传递**: 将项目知识编码到配置文件中

## 具体作用
- **架构分析**: 识别项目的高层架构和设计模式
- **命令收集**: 找到常用的构建、测试、运行命令
- **文档整合**: 整合README、规则文件等重要信息
- **最佳实践**: 建立项目特定的开发最佳实践
- **工具配置**: 为Claude Code提供项目特定的工具配置

## 提示词内容
```
Please analyze this codebase and create a CLAUDE.md file, which will be given to future instances of Claude Code to operate in this repository.
            
What to add:
1. Commands that will be commonly used, such as how to build, lint, and run tests. Include the necessary commands to develop in this codebase, such as how to run a single test.
2. High-level code architecture and structure so that future instances can be productive more quickly. Focus on the "big picture" architecture that requires reading multiple files to understand

Usage notes:
- If there's already a CLAUDE.md, suggest improvements to it.
- When you make the initial CLAUDE.md, do not repeat yourself and do not include obvious instructions like "Provide helpful error messages to users", "Write unit tests for all new utilities", "Never include sensitive information (API keys, tokens) in code or commits" 
- Avoid listing every component or file structure that can be easily discovered
- Don't include generic development practices
- If there are Cursor rules (in .cursor/rules/ or .cursorrules) or Copilot rules (in .github/copilot-instructions.md), make sure to include the important parts.
- If there is a README.md, make sure to include the important parts. 
- Do not make up information such as "Common Development Tasks", "Tips for Development", "Support and Documentation" unless this is expressly included in other files that you read.
- Be sure to prefix the file with the following text:

```
# CLAUDE.md

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.
```
```

## 分析要求

### 1. 常用命令识别
- **构建命令**: npm run build, make, cargo build等
- **测试命令**: npm test, pytest, go test等
- **代码检查**: npm run lint, flake8, clippy等
- **单个测试**: 如何运行特定的测试文件或用例
- **开发服务**: npm run dev, cargo run等

### 2. 架构理解
- **项目类型**: 前端、后端、库、CLI工具等
- **技术栈**: 框架、语言、主要依赖
- **模块结构**: 主要模块和它们的关系
- **设计模式**: 使用的重要设计模式
- **数据流**: 数据在系统中的流动方式

### 3. 配置文件整合
- **README.md**: 提取关键的项目信息
- **.cursorrules**: Cursor编辑器的规则
- **.github/copilot-instructions.md**: GitHub Copilot指令
- **package.json scripts**: NPM脚本命令
- **Makefile**: 构建目标和规则

### 4. 质量要求
- **避免冗余**: 不重复显而易见的信息
- **聚焦重点**: 关注需要多文件理解的"大图"架构
- **实用性**: 提供真正有用的操作指导
- **准确性**: 不编造不存在的信息
- **标准化**: 使用规定的文件格式和前缀

## 相关上下文代码
```javascript
// init命令定义
{
  name: "init",
  isEnabled: () => !0,
  isHidden: !1,
  progressMessage: "analyzing your codebase",
  userFacingName() {
    return "init"
  },
  async getPromptForCommand() {
    return ou(), [{
      type: "text",
      text: `Please analyze this codebase and create a CLAUDE.md file...`
    }]
  },
  allowedTools: [yG.name, oN.name, TD.name, VJ1.name, FJ1.name, XJ1.name]
}
```

## 允许的工具
- **yG.name**: TodoWrite - 任务管理
- **oN.name**: TodoRead - 任务查看  
- **TD.name**: Read - 文件读取
- **VJ1.name**: LS - 目录列表
- **FJ1.name**: Glob - 文件匹配
- **XJ1.name**: Grep - 内容搜索

## 工作流程
1. **项目扫描**: 使用LS和Glob工具探索项目结构
2. **文件分析**: 使用Read工具读取关键配置文件
3. **内容搜索**: 使用Grep工具查找特定模式和配置
4. **架构理解**: 分析代码组织和依赖关系
5. **命令提取**: 从配置文件中提取构建和测试命令
6. **文档生成**: 创建结构化的CLAUDE.md文件

## CLAUDE.md文件结构
```markdown
# CLAUDE.md

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.

## Essential Commands
- Build: [具体命令]
- Test: [具体命令]
- Lint: [具体命令]
- Dev Server: [具体命令]

## Architecture Overview
[高层架构描述]

## Key Modules
[主要模块说明]

## Development Workflow
[开发流程指导]

## Important Notes
[项目特定注意事项]
```

## 质量控制机制
1. **存在检查**: 检查是否已有CLAUDE.md文件
2. **信息验证**: 确保命令和信息的准确性
3. **重复检测**: 避免与README.md内容重复
4. **完整性**: 确保涵盖必要的开发信息
5. **格式规范**: 遵循标准的Markdown格式

## 更新和维护
- **增量更新**: 在现有CLAUDE.md基础上改进
- **变化适应**: 根据项目演进更新配置
- **反馈循环**: 根据使用效果优化配置
- **版本控制**: 配置文件纳入版本控制

## 架构地位
这是Claude Code自适应能力的核心体现，通过智能分析项目特征生成定制化配置，实现了从通用工具到项目专用助手的转换。

## 技术特点
1. **智能分析**: 基于文件内容和结构的智能理解
2. **配置生成**: 自动化的配置文件创建
3. **标准化**: 统一的配置文件格式和内容结构
4. **可扩展**: 支持各种项目类型和技术栈
5. **持久化**: 生成的配置长期有效并可迭代改进

## 应用场景
- 新项目的Claude Code初始化
- 项目架构重构后的配置更新  
- 团队成员的快速项目理解
- CI/CD流程的标准化配置
- 项目文档的自动化生成