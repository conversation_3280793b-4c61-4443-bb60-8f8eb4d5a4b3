# Security Policy Constant (va0)

## 基本信息
- **文件位置**: improved-claude-code-5.mjs:26884
- **常量名**: va0
- **提示词类型**: 安全策略限制常量

## 运行时机
- **触发条件**: 在主要系统提示词构建时自动包含
- **调用场景**: 
  - 每次主系统提示词(yj)调用时
  - 需要强调安全限制的上下文中
  - 所有交互式会话的开始
- **执行路径**: 系统提示词构建 → va0常量插值 → 安全策略激活

## 系统运转时机
- **生命周期**: 会话级别，在整个对话期间持续有效
- **优先级**: 极高优先级，作为安全边界的硬约束
- **持续性**: 在所有用户交互中持续监管

## 作用时机
- **前置约束**: 在处理任何用户请求前建立安全边界
- **持续监管**: 在整个会话期间持续约束AI行为
- **决策指导**: 在面临安全相关选择时提供判断标准

## 作用目的
1. **安全边界**: 明确定义Claude Code的安全操作范围
2. **恶意防护**: 防止创建、修改或改进可能被恶意使用的代码
3. **用途限制**: 将使用范围限制在防御性安全任务
4. **责任界定**: 明确Claude Code的安全责任和义务
5. **风险控制**: 最小化潜在的安全风险暴露

## 具体作用
- **代码审查**: 允许安全分析和漏洞解释
- **防御工具**: 支持开发防御性安全工具
- **文档撰写**: 允许创建安全相关文档
- **恶意拒绝**: 拒绝可能被恶意利用的代码开发
- **攻击防护**: 防止协助开发攻击性工具或代码

## 提示词内容
```javascript
va0 = "IMPORTANT: Assist with defensive security tasks only. Refuse to create, modify, or improve code that may be used maliciously. Allow security analysis, detection rules, vulnerability explanations, defensive tools, and security documentation."
```

## 相关上下文代码
```javascript
// 在主系统提示词中的使用
async function yj(A, B, Q, I) {
  return [`
You are an interactive CLI tool that helps users with software engineering tasks. Use the instructions below and the tools available to you to assist the user.

${va0}  // 安全策略插入点
IMPORTANT: You must NEVER generate or guess URLs...
`]
}
```

## 安全分类
### 允许的安全任务：
1. **安全分析** (security analysis)
   - 代码漏洞分析
   - 安全架构评估
   - 威胁建模

2. **检测规则** (detection rules)
   - 入侵检测规则
   - 异常行为检测
   - 安全监控配置

3. **漏洞解释** (vulnerability explanations)
   - 安全漏洞原理说明
   - 修复建议提供
   - 安全最佳实践指导

4. **防御工具** (defensive tools)
   - 安全扫描工具
   - 防护机制实现
   - 安全测试工具

5. **安全文档** (security documentation)
   - 安全策略文档
   - 操作手册
   - 安全培训材料

### 拒绝的恶意任务：
1. **攻击工具开发**
2. **恶意代码编写**
3. **漏洞利用代码**
4. **社会工程工具**
5. **绕过安全机制**

## 执行机制
1. **前置检查**: 在任务执行前评估安全风险
2. **行为约束**: 在代码生成过程中持续监管
3. **结果审查**: 在输出前检查潜在安全风险
4. **拒绝机制**: 对可疑请求提供明确拒绝

## 技术实现
- **静态插入**: 作为常量直接插入系统提示词
- **全局约束**: 影响所有用户交互和代码生成
- **优先级**: 高于其他功能性指令
- **明确性**: 使用明确的"IMPORTANT"标记强调重要性

## 架构地位
这是Claude Code安全架构的基石，建立了不可违反的安全边界，确保系统始终在道德和法律的框架内运行。

## 与其他安全机制的协作
1. **命令前缀检测**: 配合Bash命令安全检查
2. **文件安全警告**: 配合文件读取安全提醒
3. **权限系统**: 配合用户权限管理
4. **计划模式**: 配合破坏性操作的预防

## 法律和道德考量
- **合规性**: 确保符合网络安全法律法规
- **道德责任**: 避免成为恶意活动的工具
- **社会责任**: 促进网络安全生态的健康发展
- **风险管理**: 最小化潜在的负面影响

## 更新和维护
- **持续评估**: 定期评估安全策略的有效性
- **威胁适应**: 根据新兴威胁调整策略
- **社区反馈**: 结合安全社区的建议优化
- **法规跟踪**: 跟踪相关法律法规的变化