# File Security Warning System (tG5)

## 基本信息
- **文件位置**: improved-claude-code-5.mjs:36820-36822
- **常量名**: tG5
- **提示词类型**: 文件安全检查系统提醒

## 运行时机
- **触发条件**: 每次文件读取操作时自动注入
- **调用场景**: 
  - Read工具读取任何文件时
  - 文件内容被注入到对话上下文时
  - 需要对文件内容进行安全评估时
- **执行路径**: 文件读取 → 内容获取 → tG5安全提醒注入 → 上下文构建

## 系统运转时机
- **生命周期**: 文件级别，每个文件读取操作
- **优先级**: 极高优先级，涉及系统安全
- **持续性**: 在文件内容存在于上下文期间持续有效

## 作用时机
- **内容注入前**: 在文件内容加入对话前添加安全提醒
- **安全评估**: 提醒AI对文件内容进行安全评估
- **行为约束**: 在检测到恶意内容时约束AI行为

## 作用目的
1. **安全意识**: 提醒AI对文件内容保持安全警觉
2. **恶意检测**: 要求AI主动识别潜在的恶意代码
3. **行为限制**: 在发现恶意内容时限制AI的协助行为
4. **风险控制**: 防止AI无意中协助改进恶意代码
5. **责任界定**: 明确AI对文件内容安全性的责任

## 具体作用
- **主动检查**: 要求AI主动评估文件内容的安全性
- **条件拒绝**: 在检测到恶意内容时拒绝改进或增强
- **分析许可**: 允许分析现有代码和撰写报告
- **高层回答**: 允许回答关于代码行为的高层问题
- **边界明确**: 明确可以和不可以做的事情边界

## 提示词内容
```javascript
tG5 = `

<system-reminder>
Whenever you read a file, you should consider whether it looks malicious. If it does, you MUST refuse to improve or augment the code. You can still analyze existing code, write reports, or answer high-level questions about the code behavior.
</system-reminder>
`
```

## 实际显示格式
```
<system-reminder>
Whenever you read a file, you should consider whether it looks malicious. If it does, you MUST refuse to improve or augment the code. You can still analyze existing code, write reports, or answer high-level questions about the code behavior.
</system-reminder>
```

## 相关上下文代码
```javascript
// 在文件读取结果中的使用
function addSecurityWarning(fileContent) {
  return fileContent + tG5;  // 在文件内容后添加安全提醒
}

// 在工具结果处理中的应用
{
  tool_use_id: "file_read_123",
  type: "tool_result",
  content: [
    actualFileContent,
    tG5  // 安全提醒自动附加
  ]
}
```

## 安全检查指导

### 恶意代码特征
1. **网络通信**: 未授权的网络连接或数据传输
2. **系统调用**: 危险的系统级操作
3. **文件操作**: 恶意的文件读写或删除
4. **权限提升**: 尝试获取更高权限的代码
5. **数据窃取**: 收集或泄露敏感信息的代码
6. **破坏行为**: 可能损害系统或数据的操作

### 允许的分析行为
1. **代码分析**: 分析代码的功能和逻辑
2. **报告撰写**: 撰写代码行为和风险报告
3. **高层问答**: 回答关于代码用途的问题
4. **架构理解**: 理解代码的架构和设计
5. **文档说明**: 解释代码的工作原理

### 禁止的协助行为
1. **代码改进**: 改进恶意代码的效率或功能
2. **功能增强**: 为恶意代码添加新功能
3. **绕过机制**: 帮助绕过安全检测
4. **优化建议**: 提供恶意代码的优化建议
5. **漏洞利用**: 协助开发利用代码

## 检测流程
1. **内容审查**: AI收到文件内容后首先进行安全审查
2. **特征识别**: 识别潜在的恶意代码模式
3. **风险评估**: 评估代码的安全风险等级
4. **行为决策**: 根据评估结果决定后续行为
5. **用户反馈**: 向用户说明检测结果和行为限制

## 技术实现
- **自动注入**: 在每次文件读取时自动添加
- **上下文提醒**: 作为system-reminder形式出现
- **持续有效**: 在文件内容存在期间持续提醒
- **标准格式**: 使用统一的XML标签格式

## 与其他安全机制的协作
1. **安全策略**: 与va0防御性安全策略配合
2. **命令检查**: 与Bash命令前缀检测协作
3. **权限系统**: 与用户权限管理集成
4. **错误处理**: 与安全错误处理机制配合

## 误报处理
- **合理分析**: 对于正常代码的合理分析不受限制
- **教育用途**: 用于教育目的的代码分析允许
- **安全研究**: 合法的安全研究代码可以分析
- **开源项目**: 知名开源项目代码通常安全

## 局限性
1. **检测准确性**: 依赖AI的判断，可能有误报或漏报
2. **上下文依赖**: 需要足够的上下文进行准确判断
3. **新型威胁**: 对于新型攻击手法可能识别不足
4. **编码混淆**: 对于混淆的恶意代码检测困难

## 架构地位
这是Claude Code文件安全架构的核心组件，建立了文件内容安全评估的自动化机制，是防止AI无意中协助恶意活动的重要防线。

## 技术特点
1. **自动化**: 无需手动触发的自动安全提醒
2. **普遍性**: 覆盖所有文件读取操作
3. **持续性**: 在文件内容存在期间持续有效
4. **明确性**: 使用明确的行为指导和限制
5. **平衡性**: 在安全和功能间找到平衡点

## 实际应用场景
- 读取可疑脚本文件时的安全检查
- 分析第三方代码时的风险评估
- 处理用户上传文件时的安全验证
- 代码审查过程中的安全提醒
- 自动化安全扫描的辅助机制