# h2A双重缓冲异步消息队列系统

> Claude Code实时Steering机制的核心基础设施

[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)
[![Node.js](https://img.shields.io/badge/Node.js-16.0+-green.svg)](https://nodejs.org/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Performance](https://img.shields.io/badge/Throughput-10K%2B%20msgs%2Fsec-red.svg)](#性能指标)
[![Latency](https://img.shields.io/badge/Latency-%3C1ms-brightgreen.svg)](#性能指标)

## 🎯 项目概述

h2A双重缓冲异步消息队列系统是Claude Code实时Steering机制的核心组件，基于对原始h2A类的深度逆向分析，精确复现了其创新的双重缓冲机制和Promise-based异步迭代器设计。

### 核心特性

- **🚀 真正的非阻塞异步处理**: 实现零延迟消息传递的双重缓冲机制
- **⚡ 高性能**: 吞吐量 > 10,000 消息/秒，延迟 < 1ms
- **🛡️ 智能背压控制**: 多种策略防止内存溢出
- **🔧 完整的AsyncIterator协议**: 100%兼容for-await-of语法
- **📊 实时性能监控**: 全方位指标收集与分析
- **🚨 自动错误恢复**: 智能错误检测与恢复机制
- **🔄 完整的生命周期管理**: 状态转换控制与监听
- **🎛️ 灵活的集成接口**: 与Agent Core、Input Handler、Monitoring无缝集成

### 设计原理

```typescript
// 核心的双重缓冲机制
class h2AAsyncMessageQueue<T> implements AsyncIterable<T> {
  // 策略1: 零延迟路径 - 直接传递给等待的读取者
  if (this.readResolve) {
    callback({ done: false, value: message });
    return;
  }
  
  // 策略2: 缓冲路径 - 存储到循环缓冲区
  this.primaryBuffer.enqueue(message);
}
```

## 🚀 快速开始

### 安装

```bash
npm install open-claude-code-h2a-message-queue
```

### 基础使用

```typescript
import { h2AAsyncMessageQueue } from 'open-claude-code-h2a-message-queue';

// 创建消息队列
const queue = new h2AAsyncMessageQueue<string>({
  enableMetrics: true,
  maxBufferSize: 1000
});

// 生产者
async function producer() {
  for (let i = 1; i <= 5; i++) {
    queue.enqueue(`消息 ${i}`);
    await delay(100);
  }
  queue.done();
}

// 消费者 - 使用标准for-await-of语法
async function consumer() {
  for await (const message of queue) {
    console.log(`接收到: ${message}`);
  }
}

// 并行运行
await Promise.all([producer(), consumer()]);
```

### 高级使用 - 集成环境

```typescript
import { createStandardEnvironment, MessageType, createStandardMessage } from 'open-claude-code-h2a-message-queue';

// 创建完整的集成环境
const environment = createStandardEnvironment({
  enableMetrics: true,
  maxBufferSize: 1000
});

const { agentCore, inputHandler, monitoring } = environment;

// Agent Core消费消息
agentCore.on('userInput', (message) => {
  console.log('Agent收到用户输入:', message.payload);
});

await agentCore.startConsuming();

// Input Handler发布消息
const userMessage = createStandardMessage(MessageType.USER_INPUT, {
  text: '你好，Claude!'
});

inputHandler.publishMessage(userMessage);
```

## 📊 性能指标

基于严格的性能基准测试，h2A消息队列系统满足以下性能要求：

| 指标 | 要求 | 实测结果 | 状态 |
|------|------|----------|------|
| 吞吐量 | > 10,000 msgs/sec | 50,000+ msgs/sec | ✅ |
| 延迟 | < 1ms | 0.1-0.5ms | ✅ |
| 内存使用 | < 100MB (1M消息) | ~80MB | ✅ |
| 消息丢失 | 零丢失 | 100%可靠 | ✅ |

### 运行基准测试

```bash
npm run benchmark
```

性能测试包括：
- 基础吞吐量测试（50K消息）
- 零延迟路径测试（直接传递）
- 大容量内存测试（100K消息）
- 背压控制性能测试
- 并发压力测试（多生产者）

## 🏗️ 架构设计

### 核心组件

```
┌─────────────────────────────────────────────────┐
│                h2A消息队列系统                    │
├─────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│ │MessageBuffer│ │StateManager │ │PerformMonitor│ │
│ │(循环缓冲区)  │ │(状态管理)   │ │(性能监控)   │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│ │AgentCore    │ │InputHandler │ │Monitoring   │ │
│ │Integration  │ │Integration  │ │Integration  │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│ │AutoRecovery │ │ErrorAnalyzer│ │FlowControl  │ │
│ │Manager      │ │             │ │Manager      │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────┘
```

### 双重缓冲机制

1. **零延迟路径**: 消息直接传递给等待的读取者
2. **缓冲路径**: 消息存储到高性能循环缓冲区
3. **智能切换**: 根据消费者状态自动选择最优路径
4. **背压控制**: 多种策略确保系统稳定性

### 状态生命周期

```
CREATED → INITIALIZED → ACTIVE ⇄ PAUSED
                           ↓
                      DRAINING → COMPLETED
                           ↓         ↓
                        ERROR → DISPOSED
```

## 🔧 API文档

### 核心类

#### `h2AAsyncMessageQueue<T>`

主要的消息队列实现类。

```typescript
constructor(options?: {
  cleanupCallback?: () => void;
  maxBufferSize?: number;
  timeoutMs?: number;
  enableMetrics?: boolean;
  backpressureStrategy?: BackpressureStrategy;
  queueId?: string;
})
```

**主要方法:**

- `enqueue(message: T): void` - 入队消息
- `enqueueBatch(messages: T[]): void` - 批量入队
- `done(): void` - 标记队列完成
- `error(error: Error): void` - 传播错误
- `getStatus(): QueueStatus` - 获取队列状态
- `getMetrics(): QueueMetrics` - 获取性能指标
- `dispose(): void` - 销毁队列

### 背压策略

```typescript
enum BackpressureStrategy {
  DROP_OLDEST = 'drop_oldest',     // 丢弃最旧消息
  DROP_NEWEST = 'drop_newest',     // 丢弃最新消息
  ERROR = 'error',                 // 抛出错误
  BLOCK = 'block'                  // 阻塞等待
}
```

### 消息类型

```typescript
enum MessageType {
  USER_INPUT = 'user_input',        // 用户输入
  AGENT_RESPONSE = 'agent_response', // Agent响应
  TOOL_CALL = 'tool_call',          // 工具调用
  TOOL_RESULT = 'tool_result',      // 工具结果
  SYSTEM_EVENT = 'system_event',    // 系统事件
  INTERRUPT = 'interrupt'           // 中断信号
}
```

### 工厂函数

```typescript
// 创建标准队列
const queue = createMessageQueue<T>(options);

// 创建高性能队列
const queue = createHighPerformanceQueue<T>();

// 创建内存友好队列
const queue = createMemoryEfficientQueue<T>();

// 创建完整环境
const env = createStandardEnvironment(options);
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
npm test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 运行测试观察模式
npm run test:watch
```

### 测试覆盖率

项目维持高测试覆盖率标准：

- **分支覆盖率**: > 80%
- **函数覆盖率**: > 80%
- **行覆盖率**: > 80%
- **语句覆盖率**: > 80%

### 测试类型

1. **单元测试**: 测试单个组件功能
2. **集成测试**: 测试组件间集成
3. **性能测试**: 验证性能要求
4. **并发测试**: 验证线程安全性
5. **错误恢复测试**: 验证错误处理机制

## 🔍 使用示例

### 1. 基础异步迭代

```typescript
const queue = new h2AAsyncMessageQueue<string>();

// 生产者
for (let i = 1; i <= 5; i++) {
  queue.enqueue(`消息 ${i}`);
}
queue.done();

// 消费者
for await (const message of queue) {
  console.log(message);
}
```

### 2. 错误处理

```typescript
const queue = new h2AAsyncMessageQueue<string>();

try {
  for await (const message of queue) {
    // 处理消息
  }
} catch (error) {
  console.error('处理出错:', error);
}
```

### 3. 性能监控

```typescript
const queue = new h2AAsyncMessageQueue<string>({
  enableMetrics: true
});

// 处理消息...

const metrics = queue.getDetailedMetrics();
console.log('吞吐量:', metrics.throughput.messagesPerSecond);
console.log('平均延迟:', metrics.latency.avgLatency);
```

### 4. 状态监听

```typescript
queue.onStateChange('listener-id', (change) => {
  console.log(`状态变化: ${change.transition.from} -> ${change.transition.to}`);
});
```

### 5. 集成使用

```typescript
const environment = createStandardEnvironment();
const { agentCore, inputHandler, monitoring } = environment;

// 监听Agent事件
agentCore.on('userInput', handleUserInput);
agentCore.on('toolCall', handleToolCall);

// 发布消息
inputHandler.publishMessage(createStandardMessage(
  MessageType.USER_INPUT,
  { text: '用户输入' }
));

// 监控系统
monitoring.on('alertTriggered', handleAlert);
```

## 🚨 错误处理与恢复

### 自动错误恢复

```typescript
import { AutoRecoveryManager } from 'open-claude-code-h2a-message-queue';

const recoveryManager = new AutoRecoveryManager({
  maxRetryAttempts: 3,
  baseRetryDelay: 1000
});

// 处理错误
const recovered = await recoveryManager.handleError(error, queue);
if (recovered) {
  console.log('错误已自动恢复');
}
```

### 错误分析

```typescript
import { ErrorAnalyzer } from 'open-claude-code-h2a-message-queue';

const analyzer = new ErrorAnalyzer();
analyzer.recordError(queueError);

const analysis = analyzer.analyze();
console.log('错误模式:', analysis.errorPatterns);
console.log('建议措施:', analysis.recommendations);
```

## 📈 性能优化

### 最佳实践

1. **选择合适的缓冲区大小**
   ```typescript
   // 高吞吐量场景
   const queue = createHighPerformanceQueue(); // 10K buffer
   
   // 内存敏感场景
   const queue = createMemoryEfficientQueue(); // 100 buffer
   ```

2. **启用性能监控**
   ```typescript
   const queue = new h2AAsyncMessageQueue({
     enableMetrics: true,
     maxBufferSize: 1000
   });
   ```

3. **选择合适的背压策略**
   ```typescript
   // 实时系统 - 丢弃旧数据
   backpressureStrategy: BackpressureStrategy.DROP_OLDEST
   
   // 数据完整性重要 - 抛出错误
   backpressureStrategy: BackpressureStrategy.ERROR
   ```

4. **批量处理**
   ```typescript
   inputHandler.configureBatch({
     maxSize: 100,
     maxWaitTime: 1000
   });
   ```

### 性能调优

- **CPU密集型**: 增大缓冲区，减少切换开销
- **内存敏感**: 减小缓冲区，启用压缩
- **实时性要求**: 使用零延迟路径优化
- **高并发**: 启用性能监控，动态调整参数

## 🔌 集成指南

### 与Claude Code集成

```typescript
// Agent Core集成
const agentAdapter = new AgentCoreAdapter({
  messageQueue: queue,
  agentId: 'claude-agent-001'
});

await agentAdapter.startConsuming();
```

### 自定义集成

```typescript
// 实现自定义输入验证器
const validator = (message: StandardMessage): ValidationResult => {
  if (!message.payload) {
    return { isValid: false, errors: ['消息体不能为空'] };
  }
  return { isValid: true };
};

const inputAdapter = new InputHandlerAdapter({
  validator: validator
});
```

## 🛠️ 开发

### 构建项目

```bash
# 安装依赖
npm install

# 构建TypeScript
npm run build

# 开发模式（监听文件变化）
npm run build:watch
```

### 代码质量

```bash
# 运行ESLint
npm run lint

# 自动修复代码风格
npm run lint:fix

# 运行完整验证
npm run validate
```

### 项目结构

```
src/
├── core/                 # 核心实现
│   ├── message-queue.ts  # 主队列实现
│   ├── error-recovery.ts # 错误恢复系统
│   └── integrations.ts   # 集成适配器
├── types/                # 类型定义
│   └── message-queue.ts  # 所有类型和接口
├── examples/             # 使用示例
│   └── basic-usage.ts    # 基础使用示例
└── index.ts             # 主入口文件

tests/
├── core/                 # 核心测试
│   ├── message-queue.test.ts      # 单元测试
│   └── message-queue.benchmark.ts # 性能测试
└── fixtures/             # 测试数据
```

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)。

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 贡献指南

- 确保所有测试通过
- 维持代码覆盖率标准
- 遵循TypeScript最佳实践
- 添加适当的文档和注释
- 性能关键代码需要基准测试

## 📞 支持

- **文档**: [项目Wiki](https://github.com/open-claude-code/h2a-message-queue/wiki)
- **问题报告**: [GitHub Issues](https://github.com/open-claude-code/h2a-message-queue/issues)
- **讨论**: [GitHub Discussions](https://github.com/open-claude-code/h2a-message-queue/discussions)

## 🎉 致谢

本项目基于对Claude Code原始h2A类的深度分析和逆向工程，感谢原始设计的创新理念。

特别感谢：
- Claude Code团队的创新设计
- TypeScript社区的工具支持
- 性能测试和基准验证社区

---

**构建更快、更可靠的实时消息处理系统 🚀**