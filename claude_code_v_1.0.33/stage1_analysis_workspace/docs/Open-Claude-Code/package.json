{"name": "open-claude-code-h2a-message-queue", "version": "1.0.0", "description": "h2A双重缓冲异步消息队列系统 - Claude Code实时Steering机制核心组件", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "benchmark": "node --expose-gc dist/tests/core/message-queue.benchmark.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsx src/examples/basic-usage.ts", "docs": "typedoc", "validate": "npm run lint && npm run test && npm run benchmark"}, "keywords": ["message-queue", "async-iterator", "claude-code", "h2a", "typescript", "performance", "real-time", "streaming"], "author": "Open Claude Code Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/open-claude-code/h2a-message-queue.git"}, "bugs": {"url": "https://github.com/open-claude-code/h2a-message-queue/issues"}, "homepage": "https://github.com/open-claude-code/h2a-message-queue#readme", "engines": {"node": ">=16.0.0"}, "files": ["dist/", "README.md", "LICENSE"], "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.5.0", "rimraf": "^5.0.0", "ts-jest": "^29.1.0", "tsx": "^4.0.0", "typedoc": "^0.25.0", "typescript": "^5.0.0"}, "dependencies": {}, "peerDependencies": {}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "extensionsToTreatAsEsm": [".ts"], "moduleNameMapping": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "transform": {"^.+\\.tsx?$": ["ts-jest", {"useESM": true}]}, "testMatch": ["**/tests/**/*.test.ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/examples/**"], "coverageReporters": ["text", "lcov", "html"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}, "eslintConfig": {"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "@typescript-eslint/recommended"], "env": {"node": true, "es2022": true}, "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/no-explicit-any": "off", "no-console": "off"}}}