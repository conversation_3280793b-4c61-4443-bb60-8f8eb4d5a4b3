# 阶段1：项目初始化和基础架构施工指南

## 📋 面向对象
**本文档面向：菜鸟级别的初级程序员**
- 无需深度思考，严格按步骤执行
- 每个步骤都有明确的文件操作指令
- 包含必要的代码模板和配置

## 🎯 阶段目标
建立Open Claude Code项目的完整基础架构，实现CLI命令行框架和基础组件系统。

**预期交付成果**：
- ✅ 完整的Node.js项目结构
- ✅ CLI命令行界面框架
- ✅ 基础UI组件系统(React/Ink)
- ✅ 核心配置管理系统
- ✅ 基础测试框架

**工作时间**：4周 (160工时)

---

## 📁 第一周：项目框架搭建

### 步骤1.1: 环境准备
**操作清单**：
```bash
# 1. 检查Node.js版本
node --version  # 必须 >= 18.0.0

# 2. 安装项目依赖管理工具
npm install -g pnpm typescript tsx

# 3. 创建项目根目录
mkdir open-claude-code
cd open-claude-code
git init
```

### 步骤1.2: 创建项目文件结构
**待创建的完整文件树**：
```
open-claude-code/
├── package.json                 # 项目配置
├── tsconfig.json               # TypeScript配置
├── .eslintrc.js               # 代码规范
├── .prettierrc                # 代码格式化
├── jest.config.js             # 测试配置
├── .gitignore                 # Git忽略文件
├── README.md                  # 项目说明
├── src/                       # 源代码目录
│   ├── cli.ts                 # CLI入口文件
│   ├── types/                 # 类型定义
│   │   ├── index.ts           # 导出所有类型
│   │   ├── agent.ts           # Agent相关类型
│   │   ├── tool.ts            # 工具相关类型
│   │   └── config.ts          # 配置相关类型
│   ├── core/                  # 核心模块
│   │   ├── index.ts           # 核心模块导出
│   │   ├── agent-core.ts      # Agent核心引擎
│   │   ├── message-queue.ts   # h2A异步消息队列
│   │   └── config-manager.ts  # 配置管理器
│   ├── ui/                    # 用户界面
│   │   ├── index.ts           # UI模块导出
│   │   ├── app.tsx            # 主应用组件
│   │   ├── components/        # UI组件
│   │   │   ├── chat.tsx       # 对话组件
│   │   │   ├── status.tsx     # 状态显示组件
│   │   │   └── progress.tsx   # 进度条组件
│   │   └── hooks/             # React Hooks
│   │       ├── use-chat.ts    # 对话状态管理
│   │       └── use-agent.ts   # Agent状态管理
│   ├── tools/                 # 工具系统
│   │   ├── index.ts           # 工具系统导出
│   │   ├── base.ts            # 工具基类
│   │   ├── registry.ts        # 工具注册表
│   │   └── implementations/   # 具体工具实现
│   │       ├── read.ts        # Read工具
│   │       ├── write.ts       # Write工具
│   │       └── bash.ts        # Bash工具
│   ├── config/                # 配置文件
│   │   ├── default.ts         # 默认配置
│   │   └── schema.ts          # 配置模式验证
│   └── utils/                 # 工具函数
│       ├── index.ts           # 工具函数导出
│       ├── logger.ts          # 日志系统
│       └── helpers.ts         # 辅助函数
├── tests/                     # 测试文件
│   ├── unit/                  # 单元测试
│   ├── integration/           # 集成测试
│   └── fixtures/              # 测试数据
├── docs/                      # 文档目录
│   ├── API.md                 # API文档
│   └── CONTRIBUTING.md        # 贡献指南
└── scripts/                   # 构建脚本
    ├── build.js               # 构建脚本
    └── test.js                # 测试脚本
```

**执行指令**：
```bash
# 创建所有目录结构
mkdir -p src/{types,core,ui/{components,hooks},tools/implementations,config,utils}
mkdir -p tests/{unit,integration,fixtures}
mkdir -p docs scripts
```

### 步骤1.3: 创建package.json配置文件
**文件路径**: `package.json`
**文件内容**:
```json
{
  "name": "open-claude-code",
  "version": "1.0.0-alpha.1",
  "description": "Open source AI programming assistant based on Claude Code reverse engineering",
  "main": "dist/cli.js",
  "bin": {
    "claude": "./dist/cli.js"
  },
  "scripts": {
    "build": "tsc && chmod +x dist/cli.js",
    "dev": "tsx src/cli.ts",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint src --ext .ts,.tsx",
    "lint:fix": "eslint src --ext .ts,.tsx --fix",
    "format": "prettier --write src",
    "type-check": "tsc --noEmit",
    "start": "node dist/cli.js"
  },
  "engines": {
    "node": ">=18.0.0"
  },
  "keywords": ["ai", "programming", "assistant", "cli", "claude", "anthropic"],
  "author": "Open Claude Code Contributors",
  "license": "MIT",
  "dependencies": {
    "commander": "^11.1.0",
    "ink": "^4.4.1",
    "react": "^18.2.0",
    "anthropic": "^0.20.0",
    "openai": "^4.28.0",
    "ws": "^8.16.0",
    "node-fetch": "^3.3.2",
    "chalk": "^5.3.0",
    "ora": "^7.0.1",
    "inquirer": "^9.2.12",
    "zod": "^3.22.4",
    "uuid": "^9.0.1",
    "dotenv": "^16.4.1"
  },
  "devDependencies": {
    "@types/node": "^20.11.0",
    "@types/react": "^18.2.0",
    "@types/ws": "^8.5.10",
    "@types/inquirer": "^9.0.7",
    "@types/uuid": "^9.0.7",
    "@types/jest": "^29.5.12",
    "typescript": "^5.3.3",
    "tsx": "^4.7.0",
    "jest": "^29.7.0",
    "ts-jest": "^29.1.1",
    "eslint": "^8.56.0",
    "@typescript-eslint/eslint-plugin": "^6.19.1",
    "@typescript-eslint/parser": "^6.19.1",
    "prettier": "^3.2.4"
  }
}
```

### 步骤1.4: 创建TypeScript配置文件
**文件路径**: `tsconfig.json`
**文件内容**:
```json
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["ES2022"],
    "module": "CommonJS",
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "jsx": "react",
    "declaration": true,
    "outDir": "./dist",
    "rootDir": "./src",
    "removeComments": true,
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/types": ["src/types"],
      "@/core": ["src/core"],
      "@/ui": ["src/ui"],
      "@/tools": ["src/tools"],
      "@/utils": ["src/utils"],
      "@/config": ["src/config"]
    }
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "tests"
  ]
}
```

### 步骤1.5: 创建代码规范配置
**文件路径**: `.eslintrc.js`
**文件内容**:
```javascript
module.exports = {
  parser: '@typescript-eslint/parser',
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
  ],
  plugins: ['@typescript-eslint'],
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
  },
  rules: {
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-empty-function': 'off',
    'prefer-const': 'error',
    'no-var': 'error',
  },
  env: {
    node: true,
    es6: true,
    jest: true,
  },
};
```

**文件路径**: `.prettierrc`
**文件内容**:
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

---

## 📁 第二周：核心类型系统设计

### 步骤2.1: 创建基础类型定义
**文件路径**: `src/types/index.ts`
**文件内容**:
```typescript
// 导出所有类型定义
export * from './agent';
export * from './tool';
export * from './config';
export * from './message';
export * from './ui';
```

### 步骤2.2: 创建Agent相关类型
**文件路径**: `src/types/agent.ts`
**文件内容**:
```typescript
/**
 * Agent核心类型定义
 * 基于逆向分析的nO函数和h2A异步队列机制设计
 */

// Agent执行状态
export type AgentStatus = 
  | 'idle'           // 空闲状态
  | 'thinking'       // 思考中
  | 'tool_calling'   // 工具调用中
  | 'streaming'      // 流式输出中
  | 'waiting_input'  // 等待用户输入
  | 'error'          // 错误状态
  | 'aborted';       // 已中止

// 消息类型定义(基于逆向分析的消息结构)
export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  timestamp: number;
  metadata?: {
    toolCalls?: ToolCall[];
    toolResults?: ToolResult[];
    steeringMessage?: boolean;  // 实时Steering消息标记
  };
}

// Agent配置接口
export interface AgentConfig {
  model: string;
  fallbackModel?: string;
  maxTokens?: number;
  temperature?: number;
  enableSteering: boolean;      // 实时Steering开关
  concurrencyLimit: number;     // 基于gW5机制的并发控制
  allowedTools?: string[];
  disallowedTools?: string[];
  planMode?: boolean;           // Plan模式开关
}

// Agent上下文信息
export interface AgentContext {
  sessionId: string;
  workingDirectory: string;
  environment: Record<string, string>;
  fileStates: Record<string, FileState>;  // readFileState追踪
  memoryContext?: string;
  parentAgent?: string;         // 多Agent架构支持
}

// 文件状态追踪(基于Edit工具强制读取机制)
export interface FileState {
  path: string;
  lastRead: number;            // 时间戳
  lastModified: number;
  contentHash: string;
  isRead: boolean;             // 是否已读取
}

// Agent执行结果
export interface AgentResult {
  success: boolean;
  message?: string;
  data?: any;
  toolResults?: ToolResult[];
  error?: Error;
}
```

### 步骤2.3: 创建工具系统类型
**文件路径**: `src/types/tool.ts`
**文件内容**:
```typescript
/**
 * 工具系统类型定义
 * 基于逆向分析的MH1工具引擎设计
 */

// 工具基础接口
export interface Tool {
  name: string;
  description: string;
  schema: ToolSchema;
  execute: ToolExecutor;
  category: ToolCategory;
  requiresFileRead?: boolean;   // 是否需要强制读取检查
  subAgentCapable?: boolean;    // 是否支持SubAgent(如Task工具)
}

// 工具分类
export type ToolCategory = 
  | 'file'        // 文件操作
  | 'system'      // 系统操作 
  | 'network'     // 网络操作
  | 'task'        // 任务管理
  | 'special';    // 特殊工具

// 工具参数模式
export interface ToolSchema {
  type: 'object';
  properties: Record<string, any>;
  required: string[];
  additionalProperties: boolean;
}

// 工具执行器接口
export type ToolExecutor = (
  params: any,
  context: ToolExecutionContext
) => Promise<ToolResult> | AsyncGenerator<ToolResult>;

// 工具执行上下文
export interface ToolExecutionContext {
  sessionId: string;
  workingDirectory: string;
  fileStates: Record<string, FileState>;
  permissions: ToolPermissions;
  abortSignal?: AbortSignal;
  steeringQueue?: h2AQueue;     // 实时Steering队列引用
}

// 工具权限控制
export interface ToolPermissions {
  allowFileWrite: boolean;
  allowFileRead: boolean;
  allowBashExecution: boolean;
  allowNetworkAccess: boolean;
  restrictedPaths: string[];
}

// 工具调用信息
export interface ToolCall {
  id: string;
  name: string;
  parameters: any;
  timestamp: number;
}

// 工具执行结果
export interface ToolResult {
  toolCallId: string;
  success: boolean;
  data?: any;
  error?: string;
  duration: number;
  subAgentResult?: AgentResult; // SubAgent执行结果
}

// h2A异步队列接口(实时Steering核心机制)
export interface h2AQueue {
  push(message: SteeringMessage): void;
  next(): Promise<{ value: SteeringMessage | null; done: boolean }>;
  abort(): void;
  isAborted: boolean;
}

// 实时Steering消息
export interface SteeringMessage {
  type: 'user_input' | 'abort' | 'adjust_strategy';
  content: string;
  timestamp: number;
  priority: number;
}
```

### 步骤2.4: 创建配置系统类型
**文件路径**: `src/types/config.ts`
**文件内容**:
```typescript
/**
 * 配置系统类型定义
 * 支持分层配置管理和MCP集成
 */

// 主配置接口
export interface OpenClaudeConfig {
  // 核心配置
  core: CoreConfig;
  // UI配置
  ui: UIConfig;
  // Agent配置
  agent: AgentConfig;
  // 工具配置
  tools: ToolsConfig;
  // MCP配置
  mcp: MCPConfig;
  // 日志配置
  logging: LoggingConfig;
}

// 核心配置
export interface CoreConfig {
  apiKey?: string;
  apiBaseUrl?: string;
  model: string;
  fallbackModel?: string;
  maxRetries: number;
  timeout: number;
  workingDirectory: string;
}

// UI配置
export interface UIConfig {
  theme: 'light' | 'dark' | 'auto';
  showProgress: boolean;
  enableAnimations: boolean;
  compactMode: boolean;
}

// 工具配置
export interface ToolsConfig {
  enabled: string[];
  disabled: string[];
  permissions: ToolPermissions;
  concurrency: {
    maxConcurrent: number;        // 基于gW5机制
    enableLoadBalancing: boolean; // 动态负载均衡
  };
}

// MCP配置(基于逆向分析的MCP机制)
export interface MCPConfig {
  servers: Record<string, MCPServerConfig>;
  globalTimeout: number;
  retryAttempts: number;
}

export interface MCPServerConfig {
  command: string;
  args: string[];
  transport: 'stdio' | 'http' | 'websocket';
  timeout?: number;
  env?: Record<string, string>;
  workingDirectory?: string;
  oauth?: OAuth2Config;
}

export interface OAuth2Config {
  clientId: string;
  clientSecret: string;
  authUrl: string;
  tokenUrl: string;
  scopes: string[];
}

// 日志配置
export interface LoggingConfig {
  level: 'debug' | 'info' | 'warn' | 'error';
  file?: string;
  maxFileSize: number;
  maxFiles: number;
}

// 配置验证错误
export class ConfigValidationError extends Error {
  constructor(
    message: string,
    public path: string,
    public value: any
  ) {
    super(message);
    this.name = 'ConfigValidationError';
  }
}
```

---

## 📁 第三周：CLI框架和UI系统

### 步骤3.1: 创建CLI入口文件
**文件路径**: `src/cli.ts`
**文件内容**:
```typescript
#!/usr/bin/env node

/**
 * Open Claude Code CLI入口
 * 基于逆向分析的CLI架构设计
 */

import { Command } from 'commander';
import { render } from 'ink';
import React from 'react';
import { App } from './ui/app';
import { ConfigManager } from './core/config-manager';
import { Logger } from './utils/logger';

const program = new Command();

// CLI版本和基本信息
program
  .name('claude')
  .description('Open source AI programming assistant')
  .version('1.0.0-alpha.1');

// 主要命令定义(基于逆向分析的CLI命令结构)
program
  .argument('[prompt]', 'Initial prompt to send to Claude')
  .option('-m, --model <model>', 'Model to use (claude-3-5-sonnet, gpt-4)', 'claude-3-5-sonnet')
  .option('--fallback-model <model>', 'Fallback model if primary fails')
  .option('-c, --continue <session>', 'Continue previous session')
  .option('-r, --resume <session>', 'Resume session from history')
  .option('-p, --print', 'Print mode - non-interactive output')
  .option('-d, --debug', 'Enable debug logging')
  .option('--allowed-tools <tools>', 'Comma-separated list of allowed tools')
  .option('--disallowed-tools <tools>', 'Comma-separated list of disallowed tools')
  .option('--plan-mode', 'Start in plan mode (analysis only)')
  .option('--disable-steering', 'Disable real-time steering mechanism')
  .action(async (prompt, options) => {
    try {
      // 初始化配置管理器
      const configManager = new ConfigManager();
      await configManager.initialize();
      
      // 初始化日志系统
      const logger = new Logger(options.debug ? 'debug' : 'info');
      
      // 解析工具权限
      const allowedTools = options.allowedTools?.split(',').map((s: string) => s.trim());
      const disallowedTools = options.disallowedTools?.split(',').map((s: string) => s.trim());
      
      // 构建应用配置
      const appConfig = {
        initialPrompt: prompt,
        model: options.model,
        fallbackModel: options.fallbackModel,
        sessionId: options.continue || options.resume,
        printMode: options.print,
        planMode: options.planMode,
        enableSteering: !options.disableSteering,
        allowedTools,
        disallowedTools,
        debug: options.debug,
      };
      
      if (options.print && prompt) {
        // 非交互式模式 - 直接输出结果
        const { AgentCore } = await import('./core/agent-core');
        const agent = new AgentCore(appConfig, configManager, logger);
        const result = await agent.executePrompt(prompt);
        console.log(result);
        process.exit(0);
      } else {
        // 交互式模式 - 启动React/Ink UI
        const { unmount } = render(
          React.createElement(App, { 
            config: appConfig, 
            configManager, 
            logger 
          })
        );
        
        // 优雅退出处理
        process.on('SIGINT', () => {
          unmount();
          process.exit(0);
        });
        
        process.on('SIGTERM', () => {
          unmount();
          process.exit(0);
        });
      }
    } catch (error) {
      console.error('Failed to start Claude Code:', error);
      process.exit(1);
    }
  });

// 解析命令行参数
program.parse();
```

### 步骤3.2: 创建主UI应用组件
**文件路径**: `src/ui/app.tsx`
**文件内容**:
```typescript
/**
 * 主应用UI组件
 * 基于逆向分析的React/Ink架构设计
 */

import React, { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { ChatComponent } from './components/chat';
import { StatusComponent } from './components/status';
import { ProgressComponent } from './components/progress';
import { useAgent } from './hooks/use-agent';
import { useChat } from './hooks/use-chat';
import type { ConfigManager } from '../core/config-manager';
import type { Logger } from '../utils/logger';

interface AppProps {
  config: any;
  configManager: ConfigManager;
  logger: Logger;
}

export const App: React.FC<AppProps> = ({ config, configManager, logger }) => {
  // Agent状态管理
  const { 
    agent, 
    status, 
    error, 
    initialize, 
    executePrompt, 
    abort 
  } = useAgent(config, configManager, logger);
  
  // 对话状态管理
  const {
    messages,
    addMessage,
    clearMessages
  } = useChat();
  
  // UI状态
  const [currentMode, setCurrentMode] = useState<'default' | 'plan' | 'bash' | 'note'>('default');
  const [inputBuffer, setInputBuffer] = useState('');
  const [isMultilineInput, setIsMultilineInput] = useState(false);
  
  // 初始化Agent
  useEffect(() => {
    initialize();
    
    // 处理初始提示
    if (config.initialPrompt) {
      addMessage({
        id: Date.now().toString(),
        role: 'user',
        content: config.initialPrompt,
        timestamp: Date.now()
      });
      executePrompt(config.initialPrompt);
    }
  }, []);
  
  // 键盘输入处理(基于逆向分析的交互模式)
  useInput((input, key) => {
    // Shift+Tab 模式切换(基于逆向分析的Plan模式机制)
    if (key.shift && key.tab) {
      const modes = ['default', 'plan', 'bash', 'note'] as const;
      const currentIndex = modes.indexOf(currentMode);
      const nextMode = modes[(currentIndex + 1) % modes.length];
      setCurrentMode(nextMode);
      return;
    }
    
    // Ctrl+C 中止执行
    if (key.ctrl && input === 'c') {
      abort();
      return;
    }
    
    // Ctrl+L 清除对话
    if (key.ctrl && input === 'l') {
      clearMessages();
      return;
    }
    
    // Enter 发送消息
    if (key.return) {
      if (inputBuffer.trim()) {
        handleUserInput(inputBuffer.trim());
        setInputBuffer('');
      }
      return;
    }
    
    // 普通字符输入
    if (!key.ctrl && !key.meta) {
      setInputBuffer(prev => prev + input);
    }
    
    // Backspace 删除字符
    if (key.backspace || key.delete) {
      setInputBuffer(prev => prev.slice(0, -1));
    }
  });
  
  // 处理用户输入(基于逆向分析的交互模式机制)
  const handleUserInput = async (input: string) => {
    // 检查特殊模式前缀
    if (input.startsWith('!')) {
      // Bash执行模式
      const command = input.slice(1).trim();
      await handleBashCommand(command);
      return;
    }
    
    if (input.startsWith('#')) {
      // 笔记记录模式
      const note = input.slice(1).trim();
      await handleNoteCommand(note);
      return;
    }
    
    if (input.startsWith('/')) {
      // 斜杠命令处理
      await handleSlashCommand(input);
      return;
    }
    
    // 普通对话消息
    const message = {
      id: Date.now().toString(),
      role: 'user' as const,
      content: input,
      timestamp: Date.now()
    };
    
    addMessage(message);
    await executePrompt(input);
  };
  
  // Bash命令处理
  const handleBashCommand = async (command: string) => {
    addMessage({
      id: Date.now().toString(),
      role: 'user',
      content: `!${command}`,
      timestamp: Date.now()
    });
    
    // 直接调用Bash工具
    await executePrompt(`执行命令: ${command}`, { 
      forceToolUse: 'bash',
      toolParams: { command }
    });
  };
  
  // 笔记命令处理  
  const handleNoteCommand = async (note: string) => {
    addMessage({
      id: Date.now().toString(),
      role: 'user',
      content: `#${note}`,
      timestamp: Date.now()
    });
    
    // 记录到笔记系统
    await executePrompt(`记录笔记: ${note}`, {
      forceToolUse: 'todo_write',
      isNote: true
    });
  };
  
  // 斜杠命令处理
  const handleSlashCommand = async (command: string) => {
    const [cmd, ...args] = command.slice(1).split(' ');
    
    switch (cmd) {
      case 'help':
        showHelpMessage();
        break;
      case 'clear':
        clearMessages();
        break;
      case 'login':
        await handleLoginCommand();
        break;
      case 'logout':
        await handleLogoutCommand();
        break;
      case 'mcp':
        await handleMCPCommand(args);
        break;
      case 'status':
        showStatusMessage();
        break;
      default:
        addMessage({
          id: Date.now().toString(),
          role: 'system',
          content: `未知命令: ${cmd}。输入 /help 查看可用命令。`,
          timestamp: Date.now()
        });
    }
  };
  
  // 帮助信息显示
  const showHelpMessage = () => {
    addMessage({
      id: Date.now().toString(),
      role: 'system',
      content: `可用命令：
/help - 显示帮助信息
/clear - 清除对话历史
/login - 用户登录
/logout - 用户注销
/mcp - MCP服务器管理
/status - 显示系统状态

特殊模式：
! <命令> - 直接执行Bash命令
# <笔记> - 记录笔记
Shift+Tab - 切换模式 (default/plan/bash/note)
Ctrl+C - 中止执行
Ctrl+L - 清除对话`,
      timestamp: Date.now()
    });
  };
  
  // 状态信息显示
  const showStatusMessage = () => {
    addMessage({
      id: Date.now().toString(),
      role: 'system',
      content: `系统状态：
Agent状态: ${status}
当前模式: ${currentMode}
实时Steering: ${config.enableSteering ? '启用' : '禁用'}
Plan模式: ${config.planMode ? '启用' : '禁用'}
当前模型: ${config.model}`,
      timestamp: Date.now()
    });
  };
  
  // 登录处理
  const handleLoginCommand = async () => {
    // TODO: 实现OAuth登录流程
    addMessage({
      id: Date.now().toString(),
      role: 'system',
      content: '登录功能将在后续版本实现',
      timestamp: Date.now()
    });
  };
  
  // 注销处理
  const handleLogoutCommand = async () => {
    // TODO: 实现注销逻辑
    addMessage({
      id: Date.now().toString(),
      role: 'system',
      content: '注销功能将在后续版本实现',
      timestamp: Date.now()
    });
  };
  
  // MCP命令处理
  const handleMCPCommand = async (args: string[]) => {
    // TODO: 实现MCP管理逻辑
    addMessage({
      id: Date.now().toString(),
      role: 'system',
      content: 'MCP管理功能将在后续版本实现',
      timestamp: Date.now()
    });
  };
  
  return (
    <Box flexDirection="column" height="100%">
      {/* 标题栏 */}
      <Box borderStyle="single" paddingX={1}>
        <Text bold color="blue">
          Open Claude Code v1.0.0-alpha.1
        </Text>
        <Text> - 模式: {currentMode}</Text>
        {config.planMode && (
          <Text color="yellow"> [PLAN MODE - 仅分析不执行]</Text>
        )}
      </Box>
      
      {/* 状态栏 */}
      <StatusComponent 
        status={status}
        model={config.model}
        enableSteering={config.enableSteering}
      />
      
      {/* 主要内容区域 */}
      <Box flex={1} flexDirection="column">
        {/* 对话区域 */}
        <Box flex={1}>
          <ChatComponent 
            messages={messages}
            status={status}
          />
        </Box>
        
        {/* 进度区域 */}
        {status === 'tool_calling' || status === 'streaming' ? (
          <ProgressComponent 
            status={status}
          />
        ) : null}
      </Box>
      
      {/* 输入区域 */}
      <Box borderStyle="single" paddingX={1}>
        <Text>
          [{currentMode}] > {inputBuffer}
          <Text backgroundColor="white" color="black">█</Text>
        </Text>
      </Box>
      
      {/* 错误显示 */}
      {error && (
        <Box borderStyle="single" borderColor="red" paddingX={1}>
          <Text color="red">错误: {error.message}</Text>
        </Box>
      )}
      
      {/* 帮助提示 */}
      <Box paddingX={1}>
        <Text dimColor>
          Shift+Tab: 切换模式 | Ctrl+C: 中止 | Ctrl+L: 清空 | /help: 帮助
        </Text>
      </Box>
    </Box>
  );
};
```

---

## 📁 第四周：基础配置和构建系统

### 步骤4.1: 创建配置管理器
**文件路径**: `src/core/config-manager.ts`
**文件内容**:
```typescript
/**
 * 配置管理器
 * 支持分层配置和运行时更新
 */

import fs from 'fs/promises';
import path from 'path';
import { z } from 'zod';
import type { OpenClaudeConfig } from '../types/config';

// 配置验证模式
const ConfigSchema = z.object({
  core: z.object({
    apiKey: z.string().optional(),
    apiBaseUrl: z.string().optional(),
    model: z.string().default('claude-3-5-sonnet'),
    fallbackModel: z.string().optional(),
    maxRetries: z.number().default(3),
    timeout: z.number().default(30000),
    workingDirectory: z.string().default(process.cwd()),
  }),
  ui: z.object({
    theme: z.enum(['light', 'dark', 'auto']).default('auto'),
    showProgress: z.boolean().default(true),
    enableAnimations: z.boolean().default(true),
    compactMode: z.boolean().default(false),
  }),
  agent: z.object({
    model: z.string().default('claude-3-5-sonnet'),
    fallbackModel: z.string().optional(),
    maxTokens: z.number().optional(),
    temperature: z.number().default(0.7),
    enableSteering: z.boolean().default(true),
    concurrencyLimit: z.number().default(10),
    allowedTools: z.array(z.string()).optional(),
    disallowedTools: z.array(z.string()).optional(),
    planMode: z.boolean().default(false),
  }),
  tools: z.object({
    enabled: z.array(z.string()).default([]),
    disabled: z.array(z.string()).default([]),
    permissions: z.object({
      allowFileWrite: z.boolean().default(true),
      allowFileRead: z.boolean().default(true),
      allowBashExecution: z.boolean().default(true),
      allowNetworkAccess: z.boolean().default(true),
      restrictedPaths: z.array(z.string()).default([]),
    }),
    concurrency: z.object({
      maxConcurrent: z.number().default(10),
      enableLoadBalancing: z.boolean().default(true),
    }),
  }),
  mcp: z.object({
    servers: z.record(z.object({
      command: z.string(),
      args: z.array(z.string()).default([]),
      transport: z.enum(['stdio', 'http', 'websocket']).default('stdio'),
      timeout: z.number().optional(),
      env: z.record(z.string()).optional(),
      workingDirectory: z.string().optional(),
      oauth: z.object({
        clientId: z.string(),
        clientSecret: z.string(),
        authUrl: z.string(),
        tokenUrl: z.string(),
        scopes: z.array(z.string()).default([]),
      }).optional(),
    })).default({}),
    globalTimeout: z.number().default(30000),
    retryAttempts: z.number().default(3),
  }),
  logging: z.object({
    level: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
    file: z.string().optional(),
    maxFileSize: z.number().default(10 * 1024 * 1024), // 10MB
    maxFiles: z.number().default(5),
  }),
});

export class ConfigManager {
  private config: OpenClaudeConfig;
  private configPaths: string[];
  
  constructor() {
    // 配置文件查找路径(分层配置支持)
    this.configPaths = [
      path.join(process.cwd(), '.claude.json'),     // 项目级配置
      path.join(process.cwd(), '.claude.config.js'), // 项目级JS配置
      path.join(os.homedir(), '.claude', 'config.json'), // 用户级配置
      '/etc/claude/config.json',                    // 系统级配置
    ];
    
    // 默认配置
    this.config = ConfigSchema.parse({});
  }
  
  async initialize(): Promise<void> {
    // 按优先级加载配置文件
    for (const configPath of this.configPaths) {
      try {
        await this.loadConfigFile(configPath);
      } catch (error) {
        // 配置文件不存在或格式错误，继续尝试下一个
        continue;
      }
    }
    
    // 加载环境变量配置
    this.loadEnvironmentConfig();
    
    // 验证最终配置
    this.validateConfig();
  }
  
  private async loadConfigFile(configPath: string): Promise<void> {
    try {
      const content = await fs.readFile(configPath, 'utf-8');
      let configData: any;
      
      if (configPath.endsWith('.js')) {
        // 动态导入JS配置文件
        const module = await import(configPath);
        configData = module.default || module;
      } else {
        // 解析JSON配置文件
        configData = JSON.parse(content);
      }
      
      // 深度合并配置
      this.config = this.deepMerge(this.config, configData);
    } catch (error) {
      throw new Error(`Failed to load config from ${configPath}: ${error}`);
    }
  }
  
  private loadEnvironmentConfig(): void {
    // 从环境变量加载配置
    const envConfig: any = {};
    
    // API配置
    if (process.env.ANTHROPIC_API_KEY) {
      envConfig.core = { 
        ...envConfig.core, 
        apiKey: process.env.ANTHROPIC_API_KEY 
      };
    }
    
    if (process.env.CLAUDE_MODEL) {
      enconfig.agent = { 
        ...envConfig.agent, 
        model: process.env.CLAUDE_MODEL 
      };
    }
    
    if (process.env.CLAUDE_DEBUG) {
      envConfig.logging = { 
        ...envConfig.logging, 
        level: 'debug' 
      };
    }
    
    // 合并环境变量配置
    if (Object.keys(envConfig).length > 0) {
      this.config = this.deepMerge(this.config, envConfig);
    }
  }
  
  private validateConfig(): void {
    try {
      this.config = ConfigSchema.parse(this.config);
    } catch (error) {
      throw new Error(`Configuration validation failed: ${error}`);
    }
  }
  
  private deepMerge(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }
  
  // 获取配置
  getConfig(): OpenClaudeConfig {
    return { ...this.config };
  }
  
  // 获取特定配置节
  getSection<K extends keyof OpenClaudeConfig>(section: K): OpenClaudeConfig[K] {
    return { ...this.config[section] };
  }
  
  // 更新配置
  updateConfig(updates: Partial<OpenClaudeConfig>): void {
    this.config = this.deepMerge(this.config, updates);
    this.validateConfig();
  }
  
  // 保存配置到文件
  async saveConfig(configPath?: string): Promise<void> {
    const targetPath = configPath || this.configPaths[0];
    const configJson = JSON.stringify(this.config, null, 2);
    
    // 确保目录存在
    await fs.mkdir(path.dirname(targetPath), { recursive: true });
    
    // 写入配置文件
    await fs.writeFile(targetPath, configJson, 'utf-8');
  }
}
```

### 步骤4.2: 创建测试配置
**文件路径**: `jest.config.js`
**文件内容**:
```javascript
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: [
    '**/__tests__/**/*.ts',
    '**/?(*.)+(spec|test).ts'
  ],
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/cli.ts',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
};
```

### 步骤4.3: 创建Git忽略文件
**文件路径**: `.gitignore`
**文件内容**:
```
# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist/
build/
*.tsbuildinfo

# 测试覆盖率
coverage/
.nyc_output/

# 运行时文件
*.log
*.pid
*.seed
*.pid.lock

# 环境变量
.env
.env.local
.env.*.local

# IDE配置
.vscode/
.idea/
*.swp
*.swo

# 操作系统文件
.DS_Store
Thumbs.db

# 配置文件
.claude.json
.claude.config.js

# 临时文件
*.tmp
*.temp
.cache/
```

---

## ✅ 阶段1验收检查清单

### 环境验证
- [ ] Node.js版本 >= 18.0.0
- [ ] TypeScript编译成功
- [ ] 所有依赖安装完成
- [ ] ESLint检查通过
- [ ] Jest测试框架运行正常

### 文件结构验证
- [ ] 所有目录按规范创建
- [ ] 类型定义文件完整
- [ ] CLI入口文件可执行
- [ ] 配置管理器工作正常
- [ ] UI框架基础搭建完成

### 功能验证
- [ ] `npm run build` 编译成功
- [ ] `npm run dev` 开发模式启动
- [ ] `npm run test` 测试运行
- [ ] `npm run lint` 代码检查通过
- [ ] CLI命令 `claude --help` 显示正常

### 代码质量验证
- [ ] TypeScript类型定义完整
- [ ] 代码符合ESLint规范
- [ ] 单元测试覆盖率 > 60%
- [ ] 所有文件都有适当的注释
- [ ] 错误处理机制完善

---

## 🚀 完成阶段1后的下一步

完成阶段1后，您将拥有一个完整的项目基础架构。接下来应该：

1. **验收测试**: 确保所有检查项目都已通过
2. **文档更新**: 完善README.md和API文档
3. **代码审查**: 检查代码质量和规范性
4. **进入阶段2**: 开始工具系统和Agent核心的开发

**预期结果**: 一个可以启动、具备基础CLI和UI框架的项目骨架，为后续开发打下坚实基础。

---

*基于Claude Code逆向分析的精确技术指导，确保每个实现细节都符合原版架构设计。*