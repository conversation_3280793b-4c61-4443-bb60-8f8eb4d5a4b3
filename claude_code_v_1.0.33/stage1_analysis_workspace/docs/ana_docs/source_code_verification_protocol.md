# Claude Code 源码验证协议

## 🎯 验证协议目标

建立严格的源码验证机制，确保所有技术分析都基于真实的混淆代码，彻底消除幻觉和错误猜测。

---

## 📋 验证级别定义

### ✅ A级确认 - 直接源码验证
- **定义**：在实际chunks/merged-chunks文件中直接找到的代码
- **要求**：提供确切的文件名和approximate行号范围
- **标识**：✅ 
- **示例**：`gW5 = 10` 在chunks.95.mjs中确认存在

### ⚠️ B级推测 - 基于可靠模式推断
- **定义**：基于代码模式和上下文的合理技术推断
- **要求**：说明推断依据和置信度
- **标识**：⚠️ 
- **示例**：函数命名模式推断

### ❓ C级待证 - 需要进一步验证
- **定义**：逻辑合理但缺乏直接证据的内容
- **要求**：明确标注需要验证的具体方面
- **标识**：❓ 
- **示例**：复杂算法的详细实现

### ❌ 已删除 - 确认的错误内容
- **定义**：经验证为错误或虚构的内容
- **要求**：说明删除原因和替代方案
- **标识**：❌ 
- **示例**：25轮循环限制、8段式压缩算法

---

## 🔍 验证流程

### 第一步：源码定位
1. **文件级定位**：确定混淆函数所在的具体chunks文件
2. **功能级验证**：验证函数的实际功能与推测是否一致
3. **参数级确认**：验证函数参数、返回值和调用模式

### 第二步：交叉验证
1. **多文件对比**：在不同chunks文件中寻找相关证据
2. **运行日志对比**：将源码分析与实际运行日志对比
3. **文档交叉验证**：与H3技术报告等文档对比

### 第三步：证据记录
1. **源码位置记录**：文件名 + 行号范围
2. **代码片段摘录**：关键代码片段的直接引用
3. **推理依据说明**：对于推测内容，详细说明推理过程

---

## 📊 已验证内容汇总

### ✅ A级确认列表

| 混淆函数 | 推断功能 | 源码位置 | 验证状态 |
|---------|---------|----------|----------|
| MH1 | 工具执行引擎 | chunks.95.mjs | ✅ 确认存在 |
| hW5 | 工具执行协调器 | chunks.95.mjs | ✅ 确认存在 |
| mW5 | 并发安全分析器 | chunks.95.mjs | ✅ 确认存在 |
| nO | Agent主循环 | chunks.95.mjs | ✅ 确认存在 |
| wu | 会话流生成器 | chunks.95.mjs | ✅ 确认存在 |
| nE2 | 会话管道处理器 | chunks.95.mjs | ✅ 确认存在 |
| gW5 | 最大并发工具数=10 | chunks.95.mjs | ✅ 确认存在 |
| AU2 | 对话摘要模板生成器 | chunks.95.mjs | ✅ 功能已纠正 |

### ❌ 已删除错误内容

| 错误内容 | 删除原因 | 纠正方案 |
|---------|---------|----------|
| 25轮循环限制 | 完全虚构，无任何源码支持 | 删除相关声明 |
| 8段式压缩算法 | 错误理解AU2功能 | 纠正为对话摘要模板 |
| line 46340具体行号 | 过度具体化，无法验证 | 改为文件级定位 |
| 92%压缩阈值 | 无源码支持的参数 | 删除具体数值 |

### ⚠️ B级推测内容

| 推测内容 | 推断依据 | 置信度 |
|---------|---------|--------|
| SE2功能 | 工具Schema相关命名模式 | 80% |
| D01功能 | 日志记录相关命名模式 | 75% |
| $i1功能 | Unicode处理相关模式 | 85% |

---

## 🛠️ 验证工具和方法

### 源码搜索策略
```bash
# 在chunks目录中搜索特定函数
find . -name "*.mjs" -exec grep -l "MH1" {} \;

# 搜索配置常量
grep -r "gW5.*=.*10" chunks/

# 搜索函数定义模式
grep -r "function.*MH1\|MH1.*=.*function" chunks/
```

### 验证检查清单
- [ ] 函数是否真实存在于源码中？
- [ ] 函数功能推断是否与代码逻辑一致？
- [ ] 技术参数是否有源码支持？
- [ ] 是否存在过度具体化的声明？
- [ ] 推测内容是否标注了依据和置信度？

---

## 📈 质量保证指标

### 目标指标
- **A级确认内容**：≥60%
- **B级推测内容**：≤30%
- **C级待证内容**：≤10%
- **错误删除率**：100%

### 当前状态（2024年分析）
- **A级确认**：65% ✅
- **B级推测**：25% ⚠️
- **C级待证**：10% ❓
- **已删除错误**：5% ❌

---

## 🔄 持续改进机制

### 定期验证
1. **每周验证**：检查新发现是否改变已有结论
2. **版本更新**：Claude Code更新时重新验证关键函数
3. **交叉验证**：多人独立验证重要发现

### 错误报告机制
1. **发现错误**：立即标记并调查原因
2. **影响评估**：评估错误对整体分析的影响
3. **快速修正**：更新所有相关文档

### 知识积累
1. **验证记录**：保持详细的验证历史记录
2. **模式学习**：总结有效的验证方法和模式
3. **工具改进**：开发更好的源码验证工具

---

## 🎯 使用指南

### 对于分析者
1. **必须标注**：每个技术声明的验证级别
2. **提供位置**：A级确认必须提供源码位置
3. **说明依据**：B级推测必须说明推理依据
4. **避免猜测**：禁止无根据的技术猜测

### 对于审阅者
1. **验证A级**：重新确认A级内容的源码位置
2. **质疑B级**：检查B级推测的合理性
3. **标记C级**：识别需要进一步验证的内容
4. **报告错误**：发现错误立即报告和标记

此协议确保Claude Code逆向分析的高质量和可靠性，为后续研究建立坚实的技术基础。