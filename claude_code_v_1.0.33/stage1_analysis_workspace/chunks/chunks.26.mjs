
// @from(Start 2474930, End 2487806)
gbA = z((F98, bbA) => {
  bbA.exports = () => {
    return /[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE89\uDE8F-\uDEC2\uDEC6\uDECE-\uDEDC\uDEDF-\uDEE9]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g
  }
})
// @from(Start 2487812, End 2500688)
ubA = z((E98, dbA) => {
  dbA.exports = () => {
    return /[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE89\uDE8F-\uDEC2\uDEC6\uDECE-\uDEDC\uDEDF-\uDEE9]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g
  }
})
// @from(Start 2500694, End 2513570)
AgA = z((k98, ebA) => {
  ebA.exports = () => {
    return /[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE89\uDE8F-\uDEC2\uDEC6\uDECE-\uDEDC\uDEDF-\uDEE9]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g
  }
})
// @from(Start 2513576, End 2514065)
AN = z((s98, WgA) => {
  var DgA = ["nodebuffer", "arraybuffer", "fragments"],
    YgA = typeof Blob !== "undefined";
  if (YgA) DgA.push("blob");
  WgA.exports = {
    BINARY_TYPES: DgA,
    EMPTY_BUFFER: Buffer.alloc(0),
    GUID: "258EAFA5-E914-47DA-95CA-C5AB0DC85B11",
    hasBlob: YgA,
    kForOnEventAttribute: Symbol("kIsForOnEventAttribute"),
    kListener: Symbol("kListener"),
    kStatusCode: Symbol("status-code"),
    kWebSocket: Symbol("websocket"),
    NOOP: () => {}
  }
})
// @from(Start 2514071, End 2515611)
Un = z((r98, hB1) => {
  var {
    EMPTY_BUFFER: Ke9
  } = AN(), AS1 = Buffer[Symbol.species];

  function He9(A, B) {
    if (A.length === 0) return Ke9;
    if (A.length === 1) return A[0];
    let Q = Buffer.allocUnsafe(B),
      I = 0;
    for (let G = 0; G < A.length; G++) {
      let Z = A[G];
      Q.set(Z, I), I += Z.length
    }
    if (I < B) return new AS1(Q.buffer, Q.byteOffset, I);
    return Q
  }

  function JgA(A, B, Q, I, G) {
    for (let Z = 0; Z < G; Z++) Q[I + Z] = A[Z] ^ B[Z & 3]
  }

  function FgA(A, B) {
    for (let Q = 0; Q < A.length; Q++) A[Q] ^= B[Q & 3]
  }

  function ze9(A) {
    if (A.length === A.buffer.byteLength) return A.buffer;
    return A.buffer.slice(A.byteOffset, A.byteOffset + A.length)
  }

  function BS1(A) {
    if (BS1.readOnly = !0, Buffer.isBuffer(A)) return A;
    let B;
    if (A instanceof ArrayBuffer) B = new AS1(A);
    else if (ArrayBuffer.isView(A)) B = new AS1(A.buffer, A.byteOffset, A.byteLength);
    else B = Buffer.from(A), BS1.readOnly = !1;
    return B
  }
  hB1.exports = {
    concat: He9,
    mask: JgA,
    toArrayBuffer: ze9,
    toBuffer: BS1,
    unmask: FgA
  };
  if (!process.env.WS_NO_BUFFER_UTIL) try {
    let A = (() => {
      throw new Error("Cannot require module " + "bufferutil");
    })();
    hB1.exports.mask = function(B, Q, I, G, Z) {
      if (Z < 48) JgA(B, Q, I, G, Z);
      else A.mask(B, Q, I, G, Z)
    }, hB1.exports.unmask = function(B, Q) {
      if (B.length < 32) FgA(B, Q);
      else A.unmask(B, Q)
    }
  } catch (A) {}
})
// @from(Start 2515617, End 2516133)
KgA = z((o98, CgA) => {
  var XgA = Symbol("kDone"),
    QS1 = Symbol("kRun");
  class VgA {
    constructor(A) {
      this[XgA] = () => {
        this.pending--, this[QS1]()
      }, this.concurrency = A || 1 / 0, this.jobs = [], this.pending = 0
    }
    add(A) {
      this.jobs.push(A), this[QS1]()
    } [QS1]() {
      if (this.pending === this.concurrency) return;
      if (this.jobs.length) {
        let A = this.jobs.shift();
        this.pending++, A(this[XgA])
      }
    }
  }
  CgA.exports = VgA
})
// @from(Start 2516139, End 2523528)
qn = z((t98, UgA) => {
  var Nn = Z1("zlib"),
    HgA = Un(),
    we9 = KgA(),
    {
      kStatusCode: zgA
    } = AN(),
    Ee9 = Buffer[Symbol.species],
    Ue9 = Buffer.from([0, 0, 255, 255]),
    uB1 = Symbol("permessage-deflate"),
    BN = Symbol("total-length"),
    $n = Symbol("callback"),
    YL = Symbol("buffers"),
    dB1 = Symbol("error"),
    mB1;
  class wgA {
    constructor(A, B, Q) {
      if (this._maxPayload = Q | 0, this._options = A || {}, this._threshold = this._options.threshold !== void 0 ? this._options.threshold : 1024, this._isServer = !!B, this._deflate = null, this._inflate = null, this.params = null, !mB1) {
        let I = this._options.concurrencyLimit !== void 0 ? this._options.concurrencyLimit : 10;
        mB1 = new we9(I)
      }
    }
    static get extensionName() {
      return "permessage-deflate"
    }
    offer() {
      let A = {};
      if (this._options.serverNoContextTakeover) A.server_no_context_takeover = !0;
      if (this._options.clientNoContextTakeover) A.client_no_context_takeover = !0;
      if (this._options.serverMaxWindowBits) A.server_max_window_bits = this._options.serverMaxWindowBits;
      if (this._options.clientMaxWindowBits) A.client_max_window_bits = this._options.clientMaxWindowBits;
      else if (this._options.clientMaxWindowBits == null) A.client_max_window_bits = !0;
      return A
    }
    accept(A) {
      return A = this.normalizeParams(A), this.params = this._isServer ? this.acceptAsServer(A) : this.acceptAsClient(A), this.params
    }
    cleanup() {
      if (this._inflate) this._inflate.close(), this._inflate = null;
      if (this._deflate) {
        let A = this._deflate[$n];
        if (this._deflate.close(), this._deflate = null, A) A(new Error("The deflate stream was closed while data was being processed"))
      }
    }
    acceptAsServer(A) {
      let B = this._options,
        Q = A.find((I) => {
          if (B.serverNoContextTakeover === !1 && I.server_no_context_takeover || I.server_max_window_bits && (B.serverMaxWindowBits === !1 || typeof B.serverMaxWindowBits === "number" && B.serverMaxWindowBits > I.server_max_window_bits) || typeof B.clientMaxWindowBits === "number" && !I.client_max_window_bits) return !1;
          return !0
        });
      if (!Q) throw new Error("None of the extension offers can be accepted");
      if (B.serverNoContextTakeover) Q.server_no_context_takeover = !0;
      if (B.clientNoContextTakeover) Q.client_no_context_takeover = !0;
      if (typeof B.serverMaxWindowBits === "number") Q.server_max_window_bits = B.serverMaxWindowBits;
      if (typeof B.clientMaxWindowBits === "number") Q.client_max_window_bits = B.clientMaxWindowBits;
      else if (Q.client_max_window_bits === !0 || B.clientMaxWindowBits === !1) delete Q.client_max_window_bits;
      return Q
    }
    acceptAsClient(A) {
      let B = A[0];
      if (this._options.clientNoContextTakeover === !1 && B.client_no_context_takeover) throw new Error('Unexpected parameter "client_no_context_takeover"');
      if (!B.client_max_window_bits) {
        if (typeof this._options.clientMaxWindowBits === "number") B.client_max_window_bits = this._options.clientMaxWindowBits
      } else if (this._options.clientMaxWindowBits === !1 || typeof this._options.clientMaxWindowBits === "number" && B.client_max_window_bits > this._options.clientMaxWindowBits) throw new Error('Unexpected or invalid parameter "client_max_window_bits"');
      return B
    }
    normalizeParams(A) {
      return A.forEach((B) => {
        Object.keys(B).forEach((Q) => {
          let I = B[Q];
          if (I.length > 1) throw new Error(`Parameter "${Q}" must have only a single value`);
          if (I = I[0], Q === "client_max_window_bits") {
            if (I !== !0) {
              let G = +I;
              if (!Number.isInteger(G) || G < 8 || G > 15) throw new TypeError(`Invalid value for parameter "${Q}": ${I}`);
              I = G
            } else if (!this._isServer) throw new TypeError(`Invalid value for parameter "${Q}": ${I}`)
          } else if (Q === "server_max_window_bits") {
            let G = +I;
            if (!Number.isInteger(G) || G < 8 || G > 15) throw new TypeError(`Invalid value for parameter "${Q}": ${I}`);
            I = G
          } else if (Q === "client_no_context_takeover" || Q === "server_no_context_takeover") {
            if (I !== !0) throw new TypeError(`Invalid value for parameter "${Q}": ${I}`)
          } else throw new Error(`Unknown parameter "${Q}"`);
          B[Q] = I
        })
      }), A
    }
    decompress(A, B, Q) {
      mB1.add((I) => {
        this._decompress(A, B, (G, Z) => {
          I(), Q(G, Z)
        })
      })
    }
    compress(A, B, Q) {
      mB1.add((I) => {
        this._compress(A, B, (G, Z) => {
          I(), Q(G, Z)
        })
      })
    }
    _decompress(A, B, Q) {
      let I = this._isServer ? "client" : "server";
      if (!this._inflate) {
        let G = `${I}_max_window_bits`,
          Z = typeof this.params[G] !== "number" ? Nn.Z_DEFAULT_WINDOWBITS : this.params[G];
        this._inflate = Nn.createInflateRaw({
          ...this._options.zlibInflateOptions,
          windowBits: Z
        }), this._inflate[uB1] = this, this._inflate[BN] = 0, this._inflate[YL] = [], this._inflate.on("error", $e9), this._inflate.on("data", EgA)
      }
      if (this._inflate[$n] = Q, this._inflate.write(A), B) this._inflate.write(Ue9);
      this._inflate.flush(() => {
        let G = this._inflate[dB1];
        if (G) {
          this._inflate.close(), this._inflate = null, Q(G);
          return
        }
        let Z = HgA.concat(this._inflate[YL], this._inflate[BN]);
        if (this._inflate._readableState.endEmitted) this._inflate.close(), this._inflate = null;
        else if (this._inflate[BN] = 0, this._inflate[YL] = [], B && this.params[`${I}_no_context_takeover`]) this._inflate.reset();
        Q(null, Z)
      })
    }
    _compress(A, B, Q) {
      let I = this._isServer ? "server" : "client";
      if (!this._deflate) {
        let G = `${I}_max_window_bits`,
          Z = typeof this.params[G] !== "number" ? Nn.Z_DEFAULT_WINDOWBITS : this.params[G];
        this._deflate = Nn.createDeflateRaw({
          ...this._options.zlibDeflateOptions,
          windowBits: Z
        }), this._deflate[BN] = 0, this._deflate[YL] = [], this._deflate.on("data", Ne9)
      }
      this._deflate[$n] = Q, this._deflate.write(A), this._deflate.flush(Nn.Z_SYNC_FLUSH, () => {
        if (!this._deflate) return;
        let G = HgA.concat(this._deflate[YL], this._deflate[BN]);
        if (B) G = new Ee9(G.buffer, G.byteOffset, G.length - 4);
        if (this._deflate[$n] = null, this._deflate[BN] = 0, this._deflate[YL] = [], B && this.params[`${I}_no_context_takeover`]) this._deflate.reset();
        Q(null, G)
      })
    }
  }
  UgA.exports = wgA;

  function Ne9(A) {
    this[YL].push(A), this[BN] += A.length
  }

  function EgA(A) {
    if (this[BN] += A.length, this[uB1]._maxPayload < 1 || this[BN] <= this[uB1]._maxPayload) {
      this[YL].push(A);
      return
    }
    this[dB1] = new RangeError("Max payload size exceeded"), this[dB1].code = "WS_ERR_UNSUPPORTED_MESSAGE_LENGTH", this[dB1][zgA] = 1009, this.removeListener("data", EgA), this.reset()
  }

  function $e9(A) {
    this[uB1]._inflate = null, A[zgA] = 1007, this[$n](A)
  }
})
// @from(Start 2523534, End 2525642)
lv = z((e98, pB1) => {
  var {
    isUtf8: NgA
  } = Z1("buffer"), {
    hasBlob: qe9
  } = AN(), Me9 = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0];

  function Le9(A) {
    return A >= 1000 && A <= 1014 && A !== 1004 && A !== 1005 && A !== 1006 || A >= 3000 && A <= 4999
  }

  function IS1(A) {
    let B = A.length,
      Q = 0;
    while (Q < B)
      if ((A[Q] & 128) === 0) Q++;
      else if ((A[Q] & 224) === 192) {
      if (Q + 1 === B || (A[Q + 1] & 192) !== 128 || (A[Q] & 254) === 192) return !1;
      Q += 2
    } else if ((A[Q] & 240) === 224) {
      if (Q + 2 >= B || (A[Q + 1] & 192) !== 128 || (A[Q + 2] & 192) !== 128 || A[Q] === 224 && (A[Q + 1] & 224) === 128 || A[Q] === 237 && (A[Q + 1] & 224) === 160) return !1;
      Q += 3
    } else if ((A[Q] & 248) === 240) {
      if (Q + 3 >= B || (A[Q + 1] & 192) !== 128 || (A[Q + 2] & 192) !== 128 || (A[Q + 3] & 192) !== 128 || A[Q] === 240 && (A[Q + 1] & 240) === 128 || A[Q] === 244 && A[Q + 1] > 143 || A[Q] > 244) return !1;
      Q += 4
    } else return !1;
    return !0
  }

  function Re9(A) {
    return qe9 && typeof A === "object" && typeof A.arrayBuffer === "function" && typeof A.type === "string" && typeof A.stream === "function" && (A[Symbol.toStringTag] === "Blob" || A[Symbol.toStringTag] === "File")
  }
  pB1.exports = {
    isBlob: Re9,
    isValidStatusCode: Le9,
    isValidUTF8: IS1,
    tokenChars: Me9
  };
  if (NgA) pB1.exports.isValidUTF8 = function(A) {
    return A.length < 24 ? IS1(A) : NgA(A)
  };
  else if (!process.env.WS_NO_UTF_8_VALIDATE) try {
    let A = (() => {
      throw new Error("Cannot require module " + "utf-8-validate");
    })();
    pB1.exports.isValidUTF8 = function(B) {
      return B.length < 32 ? IS1(B) : A(B)
    }
  } catch (A) {}
})
// @from(Start 2525648, End 2536044)
ZS1 = z((A48, RgA) => {
  var {
    Writable: Oe9
  } = Z1("stream"), $gA = qn(), {
    BINARY_TYPES: Te9,
    EMPTY_BUFFER: qgA,
    kStatusCode: Pe9,
    kWebSocket: Se9
  } = AN(), {
    concat: GS1,
    toArrayBuffer: _e9,
    unmask: je9
  } = Un(), {
    isValidStatusCode: ye9,
    isValidUTF8: MgA
  } = lv(), cB1 = Buffer[Symbol.species];
  class LgA extends Oe9 {
    constructor(A = {}) {
      super();
      this._allowSynchronousEvents = A.allowSynchronousEvents !== void 0 ? A.allowSynchronousEvents : !0, this._binaryType = A.binaryType || Te9[0], this._extensions = A.extensions || {}, this._isServer = !!A.isServer, this._maxPayload = A.maxPayload | 0, this._skipUTF8Validation = !!A.skipUTF8Validation, this[Se9] = void 0, this._bufferedBytes = 0, this._buffers = [], this._compressed = !1, this._payloadLength = 0, this._mask = void 0, this._fragmented = 0, this._masked = !1, this._fin = !1, this._opcode = 0, this._totalPayloadLength = 0, this._messageLength = 0, this._fragments = [], this._errored = !1, this._loop = !1, this._state = 0
    }
    _write(A, B, Q) {
      if (this._opcode === 8 && this._state == 0) return Q();
      this._bufferedBytes += A.length, this._buffers.push(A), this.startLoop(Q)
    }
    consume(A) {
      if (this._bufferedBytes -= A, A === this._buffers[0].length) return this._buffers.shift();
      if (A < this._buffers[0].length) {
        let Q = this._buffers[0];
        return this._buffers[0] = new cB1(Q.buffer, Q.byteOffset + A, Q.length - A), new cB1(Q.buffer, Q.byteOffset, A)
      }
      let B = Buffer.allocUnsafe(A);
      do {
        let Q = this._buffers[0],
          I = B.length - A;
        if (A >= Q.length) B.set(this._buffers.shift(), I);
        else B.set(new Uint8Array(Q.buffer, Q.byteOffset, A), I), this._buffers[0] = new cB1(Q.buffer, Q.byteOffset + A, Q.length - A);
        A -= Q.length
      } while (A > 0);
      return B
    }
    startLoop(A) {
      this._loop = !0;
      do switch (this._state) {
        case 0:
          this.getInfo(A);
          break;
        case 1:
          this.getPayloadLength16(A);
          break;
        case 2:
          this.getPayloadLength64(A);
          break;
        case 3:
          this.getMask();
          break;
        case 4:
          this.getData(A);
          break;
        case 5:
        case 6:
          this._loop = !1;
          return
      }
      while (this._loop);
      if (!this._errored) A()
    }
    getInfo(A) {
      if (this._bufferedBytes < 2) {
        this._loop = !1;
        return
      }
      let B = this.consume(2);
      if ((B[0] & 48) !== 0) {
        let I = this.createError(RangeError, "RSV2 and RSV3 must be clear", !0, 1002, "WS_ERR_UNEXPECTED_RSV_2_3");
        A(I);
        return
      }
      let Q = (B[0] & 64) === 64;
      if (Q && !this._extensions[$gA.extensionName]) {
        let I = this.createError(RangeError, "RSV1 must be clear", !0, 1002, "WS_ERR_UNEXPECTED_RSV_1");
        A(I);
        return
      }
      if (this._fin = (B[0] & 128) === 128, this._opcode = B[0] & 15, this._payloadLength = B[1] & 127, this._opcode === 0) {
        if (Q) {
          let I = this.createError(RangeError, "RSV1 must be clear", !0, 1002, "WS_ERR_UNEXPECTED_RSV_1");
          A(I);
          return
        }
        if (!this._fragmented) {
          let I = this.createError(RangeError, "invalid opcode 0", !0, 1002, "WS_ERR_INVALID_OPCODE");
          A(I);
          return
        }
        this._opcode = this._fragmented
      } else if (this._opcode === 1 || this._opcode === 2) {
        if (this._fragmented) {
          let I = this.createError(RangeError, `invalid opcode ${this._opcode}`, !0, 1002, "WS_ERR_INVALID_OPCODE");
          A(I);
          return
        }
        this._compressed = Q
      } else if (this._opcode > 7 && this._opcode < 11) {
        if (!this._fin) {
          let I = this.createError(RangeError, "FIN must be set", !0, 1002, "WS_ERR_EXPECTED_FIN");
          A(I);
          return
        }
        if (Q) {
          let I = this.createError(RangeError, "RSV1 must be clear", !0, 1002, "WS_ERR_UNEXPECTED_RSV_1");
          A(I);
          return
        }
        if (this._payloadLength > 125 || this._opcode === 8 && this._payloadLength === 1) {
          let I = this.createError(RangeError, `invalid payload length ${this._payloadLength}`, !0, 1002, "WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH");
          A(I);
          return
        }
      } else {
        let I = this.createError(RangeError, `invalid opcode ${this._opcode}`, !0, 1002, "WS_ERR_INVALID_OPCODE");
        A(I);
        return
      }
      if (!this._fin && !this._fragmented) this._fragmented = this._opcode;
      if (this._masked = (B[1] & 128) === 128, this._isServer) {
        if (!this._masked) {
          let I = this.createError(RangeError, "MASK must be set", !0, 1002, "WS_ERR_EXPECTED_MASK");
          A(I);
          return
        }
      } else if (this._masked) {
        let I = this.createError(RangeError, "MASK must be clear", !0, 1002, "WS_ERR_UNEXPECTED_MASK");
        A(I);
        return
      }
      if (this._payloadLength === 126) this._state = 1;
      else if (this._payloadLength === 127) this._state = 2;
      else this.haveLength(A)
    }
    getPayloadLength16(A) {
      if (this._bufferedBytes < 2) {
        this._loop = !1;
        return
      }
      this._payloadLength = this.consume(2).readUInt16BE(0), this.haveLength(A)
    }
    getPayloadLength64(A) {
      if (this._bufferedBytes < 8) {
        this._loop = !1;
        return
      }
      let B = this.consume(8),
        Q = B.readUInt32BE(0);
      if (Q > Math.pow(2, 21) - 1) {
        let I = this.createError(RangeError, "Unsupported WebSocket frame: payload length > 2^53 - 1", !1, 1009, "WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH");
        A(I);
        return
      }
      this._payloadLength = Q * Math.pow(2, 32) + B.readUInt32BE(4), this.haveLength(A)
    }
    haveLength(A) {
      if (this._payloadLength && this._opcode < 8) {
        if (this._totalPayloadLength += this._payloadLength, this._totalPayloadLength > this._maxPayload && this._maxPayload > 0) {
          let B = this.createError(RangeError, "Max payload size exceeded", !1, 1009, "WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");
          A(B);
          return
        }
      }
      if (this._masked) this._state = 3;
      else this._state = 4
    }
    getMask() {
      if (this._bufferedBytes < 4) {
        this._loop = !1;
        return
      }
      this._mask = this.consume(4), this._state = 4
    }
    getData(A) {
      let B = qgA;
      if (this._payloadLength) {
        if (this._bufferedBytes < this._payloadLength) {
          this._loop = !1;
          return
        }
        if (B = this.consume(this._payloadLength), this._masked && (this._mask[0] | this._mask[1] | this._mask[2] | this._mask[3]) !== 0) je9(B, this._mask)
      }
      if (this._opcode > 7) {
        this.controlMessage(B, A);
        return
      }
      if (this._compressed) {
        this._state = 5, this.decompress(B, A);
        return
      }
      if (B.length) this._messageLength = this._totalPayloadLength, this._fragments.push(B);
      this.dataMessage(A)
    }
    decompress(A, B) {
      this._extensions[$gA.extensionName].decompress(A, this._fin, (I, G) => {
        if (I) return B(I);
        if (G.length) {
          if (this._messageLength += G.length, this._messageLength > this._maxPayload && this._maxPayload > 0) {
            let Z = this.createError(RangeError, "Max payload size exceeded", !1, 1009, "WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");
            B(Z);
            return
          }
          this._fragments.push(G)
        }
        if (this.dataMessage(B), this._state === 0) this.startLoop(B)
      })
    }
    dataMessage(A) {
      if (!this._fin) {
        this._state = 0;
        return
      }
      let B = this._messageLength,
        Q = this._fragments;
      if (this._totalPayloadLength = 0, this._messageLength = 0, this._fragmented = 0, this._fragments = [], this._opcode === 2) {
        let I;
        if (this._binaryType === "nodebuffer") I = GS1(Q, B);
        else if (this._binaryType === "arraybuffer") I = _e9(GS1(Q, B));
        else if (this._binaryType === "blob") I = new Blob(Q);
        else I = Q;
        if (this._allowSynchronousEvents) this.emit("message", I, !0), this._state = 0;
        else this._state = 6, setImmediate(() => {
          this.emit("message", I, !0), this._state = 0, this.startLoop(A)
        })
      } else {
        let I = GS1(Q, B);
        if (!this._skipUTF8Validation && !MgA(I)) {
          let G = this.createError(Error, "invalid UTF-8 sequence", !0, 1007, "WS_ERR_INVALID_UTF8");
          A(G);
          return
        }
        if (this._state === 5 || this._allowSynchronousEvents) this.emit("message", I, !1), this._state = 0;
        else this._state = 6, setImmediate(() => {
          this.emit("message", I, !1), this._state = 0, this.startLoop(A)
        })
      }
    }
    controlMessage(A, B) {
      if (this._opcode === 8) {
        if (A.length === 0) this._loop = !1, this.emit("conclude", 1005, qgA), this.end();
        else {
          let Q = A.readUInt16BE(0);
          if (!ye9(Q)) {
            let G = this.createError(RangeError, `invalid status code ${Q}`, !0, 1002, "WS_ERR_INVALID_CLOSE_CODE");
            B(G);
            return
          }
          let I = new cB1(A.buffer, A.byteOffset + 2, A.length - 2);
          if (!this._skipUTF8Validation && !MgA(I)) {
            let G = this.createError(Error, "invalid UTF-8 sequence", !0, 1007, "WS_ERR_INVALID_UTF8");
            B(G);
            return
          }
          this._loop = !1, this.emit("conclude", Q, I), this.end()
        }
        this._state = 0;
        return
      }
      if (this._allowSynchronousEvents) this.emit(this._opcode === 9 ? "ping" : "pong", A), this._state = 0;
      else this._state = 6, setImmediate(() => {
        this.emit(this._opcode === 9 ? "ping" : "pong", A), this._state = 0, this.startLoop(B)
      })
    }
    createError(A, B, Q, I, G) {
      this._loop = !1, this._errored = !0;
      let Z = new A(Q ? `Invalid WebSocket frame: ${B}` : B);
      return Error.captureStackTrace(Z, this.createError), Z.code = G, Z[Pe9] = I, Z
    }
  }
  RgA.exports = LgA
})
// @from(Start 2536050, End 2543512)
YS1 = z((Q48, PgA) => {
  var {
    Duplex: B48
  } = Z1("stream"), {
    randomFillSync: ke9
  } = Z1("crypto"), OgA = qn(), {
    EMPTY_BUFFER: xe9,
    kWebSocket: fe9,
    NOOP: ve9
  } = AN(), {
    isBlob: iv,
    isValidStatusCode: be9
  } = lv(), {
    mask: TgA,
    toBuffer: TS
  } = Un(), QX = Symbol("kByteLength"), ge9 = Buffer.alloc(4), PS, nv = 8192, wC = 0, he9 = 1, me9 = 2;
  class WL {
    constructor(A, B, Q) {
      if (this._extensions = B || {}, Q) this._generateMask = Q, this._maskBuffer = Buffer.alloc(4);
      this._socket = A, this._firstFragment = !0, this._compress = !1, this._bufferedBytes = 0, this._queue = [], this._state = wC, this.onerror = ve9, this[fe9] = void 0
    }
    static frame(A, B) {
      let Q, I = !1,
        G = 2,
        Z = !1;
      if (B.mask) {
        if (Q = B.maskBuffer || ge9, B.generateMask) B.generateMask(Q);
        else {
          if (nv === 8192) {
            if (PS === void 0) PS = Buffer.alloc(8192);
            ke9(PS, 0, 8192), nv = 0
          }
          Q[0] = PS[nv++], Q[1] = PS[nv++], Q[2] = PS[nv++], Q[3] = PS[nv++]
        }
        Z = (Q[0] | Q[1] | Q[2] | Q[3]) === 0, G = 6
      }
      let D;
      if (typeof A === "string")
        if ((!B.mask || Z) && B[QX] !== void 0) D = B[QX];
        else A = Buffer.from(A), D = A.length;
      else D = A.length, I = B.mask && B.readOnly && !Z;
      let Y = D;
      if (D >= 65536) G += 8, Y = 127;
      else if (D > 125) G += 2, Y = 126;
      let W = Buffer.allocUnsafe(I ? D + G : G);
      if (W[0] = B.fin ? B.opcode | 128 : B.opcode, B.rsv1) W[0] |= 64;
      if (W[1] = Y, Y === 126) W.writeUInt16BE(D, 2);
      else if (Y === 127) W[2] = W[3] = 0, W.writeUIntBE(D, 4, 6);
      if (!B.mask) return [W, A];
      if (W[1] |= 128, W[G - 4] = Q[0], W[G - 3] = Q[1], W[G - 2] = Q[2], W[G - 1] = Q[3], Z) return [W, A];
      if (I) return TgA(A, Q, W, G, D), [W];
      return TgA(A, Q, A, 0, D), [W, A]
    }
    close(A, B, Q, I) {
      let G;
      if (A === void 0) G = xe9;
      else if (typeof A !== "number" || !be9(A)) throw new TypeError("First argument must be a valid error code number");
      else if (B === void 0 || !B.length) G = Buffer.allocUnsafe(2), G.writeUInt16BE(A, 0);
      else {
        let D = Buffer.byteLength(B);
        if (D > 123) throw new RangeError("The message must not be greater than 123 bytes");
        if (G = Buffer.allocUnsafe(2 + D), G.writeUInt16BE(A, 0), typeof B === "string") G.write(B, 2);
        else G.set(B, 2)
      }
      let Z = {
        [QX]: G.length,
        fin: !0,
        generateMask: this._generateMask,
        mask: Q,
        maskBuffer: this._maskBuffer,
        opcode: 8,
        readOnly: !1,
        rsv1: !1
      };
      if (this._state !== wC) this.enqueue([this.dispatch, G, !1, Z, I]);
      else this.sendFrame(WL.frame(G, Z), I)
    }
    ping(A, B, Q) {
      let I, G;
      if (typeof A === "string") I = Buffer.byteLength(A), G = !1;
      else if (iv(A)) I = A.size, G = !1;
      else A = TS(A), I = A.length, G = TS.readOnly;
      if (I > 125) throw new RangeError("The data size must not be greater than 125 bytes");
      let Z = {
        [QX]: I,
        fin: !0,
        generateMask: this._generateMask,
        mask: B,
        maskBuffer: this._maskBuffer,
        opcode: 9,
        readOnly: G,
        rsv1: !1
      };
      if (iv(A))
        if (this._state !== wC) this.enqueue([this.getBlobData, A, !1, Z, Q]);
        else this.getBlobData(A, !1, Z, Q);
      else if (this._state !== wC) this.enqueue([this.dispatch, A, !1, Z, Q]);
      else this.sendFrame(WL.frame(A, Z), Q)
    }
    pong(A, B, Q) {
      let I, G;
      if (typeof A === "string") I = Buffer.byteLength(A), G = !1;
      else if (iv(A)) I = A.size, G = !1;
      else A = TS(A), I = A.length, G = TS.readOnly;
      if (I > 125) throw new RangeError("The data size must not be greater than 125 bytes");
      let Z = {
        [QX]: I,
        fin: !0,
        generateMask: this._generateMask,
        mask: B,
        maskBuffer: this._maskBuffer,
        opcode: 10,
        readOnly: G,
        rsv1: !1
      };
      if (iv(A))
        if (this._state !== wC) this.enqueue([this.getBlobData, A, !1, Z, Q]);
        else this.getBlobData(A, !1, Z, Q);
      else if (this._state !== wC) this.enqueue([this.dispatch, A, !1, Z, Q]);
      else this.sendFrame(WL.frame(A, Z), Q)
    }
    send(A, B, Q) {
      let I = this._extensions[OgA.extensionName],
        G = B.binary ? 2 : 1,
        Z = B.compress,
        D, Y;
      if (typeof A === "string") D = Buffer.byteLength(A), Y = !1;
      else if (iv(A)) D = A.size, Y = !1;
      else A = TS(A), D = A.length, Y = TS.readOnly;
      if (this._firstFragment) {
        if (this._firstFragment = !1, Z && I && I.params[I._isServer ? "server_no_context_takeover" : "client_no_context_takeover"]) Z = D >= I._threshold;
        this._compress = Z
      } else Z = !1, G = 0;
      if (B.fin) this._firstFragment = !0;
      let W = {
        [QX]: D,
        fin: B.fin,
        generateMask: this._generateMask,
        mask: B.mask,
        maskBuffer: this._maskBuffer,
        opcode: G,
        readOnly: Y,
        rsv1: Z
      };
      if (iv(A))
        if (this._state !== wC) this.enqueue([this.getBlobData, A, this._compress, W, Q]);
        else this.getBlobData(A, this._compress, W, Q);
      else if (this._state !== wC) this.enqueue([this.dispatch, A, this._compress, W, Q]);
      else this.dispatch(A, this._compress, W, Q)
    }
    getBlobData(A, B, Q, I) {
      this._bufferedBytes += Q[QX], this._state = me9, A.arrayBuffer().then((G) => {
        if (this._socket.destroyed) {
          let D = new Error("The socket was closed while the blob was being read");
          process.nextTick(DS1, this, D, I);
          return
        }
        this._bufferedBytes -= Q[QX];
        let Z = TS(G);
        if (!B) this._state = wC, this.sendFrame(WL.frame(Z, Q), I), this.dequeue();
        else this.dispatch(Z, B, Q, I)
      }).catch((G) => {
        process.nextTick(de9, this, G, I)
      })
    }
    dispatch(A, B, Q, I) {
      if (!B) {
        this.sendFrame(WL.frame(A, Q), I);
        return
      }
      let G = this._extensions[OgA.extensionName];
      this._bufferedBytes += Q[QX], this._state = he9, G.compress(A, Q.fin, (Z, D) => {
        if (this._socket.destroyed) {
          let Y = new Error("The socket was closed while data was being compressed");
          DS1(this, Y, I);
          return
        }
        this._bufferedBytes -= Q[QX], this._state = wC, Q.readOnly = !1, this.sendFrame(WL.frame(D, Q), I), this.dequeue()
      })
    }
    dequeue() {
      while (this._state === wC && this._queue.length) {
        let A = this._queue.shift();
        this._bufferedBytes -= A[3][QX], Reflect.apply(A[0], this, A.slice(1))
      }
    }
    enqueue(A) {
      this._bufferedBytes += A[3][QX], this._queue.push(A)
    }
    sendFrame(A, B) {
      if (A.length === 2) this._socket.cork(), this._socket.write(A[0]), this._socket.write(A[1], B), this._socket.uncork();
      else this._socket.write(A[0], B)
    }
  }
  PgA.exports = WL;

  function DS1(A, B, Q) {
    if (typeof Q === "function") Q(B);
    for (let I = 0; I < A._queue.length; I++) {
      let G = A._queue[I],
        Z = G[G.length - 1];
      if (typeof Z === "function") Z(B)
    }
  }

  function de9(A, B, Q) {
    DS1(A, B, Q), A.onerror(B)
  }
})
// @from(Start 2543518, End 2546883)
bgA = z((I48, vgA) => {
  var {
    kForOnEventAttribute: Mn,
    kListener: WS1
  } = AN(), SgA = Symbol("kCode"), _gA = Symbol("kData"), jgA = Symbol("kError"), ygA = Symbol("kMessage"), kgA = Symbol("kReason"), av = Symbol("kTarget"), xgA = Symbol("kType"), fgA = Symbol("kWasClean");
  class JL {
    constructor(A) {
      this[av] = null, this[xgA] = A
    }
    get target() {
      return this[av]
    }
    get type() {
      return this[xgA]
    }
  }
  Object.defineProperty(JL.prototype, "target", {
    enumerable: !0
  });
  Object.defineProperty(JL.prototype, "type", {
    enumerable: !0
  });
  class sv extends JL {
    constructor(A, B = {}) {
      super(A);
      this[SgA] = B.code === void 0 ? 0 : B.code, this[kgA] = B.reason === void 0 ? "" : B.reason, this[fgA] = B.wasClean === void 0 ? !1 : B.wasClean
    }
    get code() {
      return this[SgA]
    }
    get reason() {
      return this[kgA]
    }
    get wasClean() {
      return this[fgA]
    }
  }
  Object.defineProperty(sv.prototype, "code", {
    enumerable: !0
  });
  Object.defineProperty(sv.prototype, "reason", {
    enumerable: !0
  });
  Object.defineProperty(sv.prototype, "wasClean", {
    enumerable: !0
  });
  class Ln extends JL {
    constructor(A, B = {}) {
      super(A);
      this[jgA] = B.error === void 0 ? null : B.error, this[ygA] = B.message === void 0 ? "" : B.message
    }
    get error() {
      return this[jgA]
    }
    get message() {
      return this[ygA]
    }
  }
  Object.defineProperty(Ln.prototype, "error", {
    enumerable: !0
  });
  Object.defineProperty(Ln.prototype, "message", {
    enumerable: !0
  });
  class iB1 extends JL {
    constructor(A, B = {}) {
      super(A);
      this[_gA] = B.data === void 0 ? null : B.data
    }
    get data() {
      return this[_gA]
    }
  }
  Object.defineProperty(iB1.prototype, "data", {
    enumerable: !0
  });
  var ue9 = {
    addEventListener(A, B, Q = {}) {
      for (let G of this.listeners(A))
        if (!Q[Mn] && G[WS1] === B && !G[Mn]) return;
      let I;
      if (A === "message") I = function G(Z, D) {
        let Y = new iB1("message", {
          data: D ? Z : Z.toString()
        });
        Y[av] = this, lB1(B, this, Y)
      };
      else if (A === "close") I = function G(Z, D) {
        let Y = new sv("close", {
          code: Z,
          reason: D.toString(),
          wasClean: this._closeFrameReceived && this._closeFrameSent
        });
        Y[av] = this, lB1(B, this, Y)
      };
      else if (A === "error") I = function G(Z) {
        let D = new Ln("error", {
          error: Z,
          message: Z.message
        });
        D[av] = this, lB1(B, this, D)
      };
      else if (A === "open") I = function G() {
        let Z = new JL("open");
        Z[av] = this, lB1(B, this, Z)
      };
      else return;
      if (I[Mn] = !!Q[Mn], I[WS1] = B, Q.once) this.once(A, I);
      else this.on(A, I)
    },
    removeEventListener(A, B) {
      for (let Q of this.listeners(A))
        if (Q[WS1] === B && !Q[Mn]) {
          this.removeListener(A, Q);
          break
        }
    }
  };
  vgA.exports = {
    CloseEvent: sv,
    ErrorEvent: Ln,
    Event: JL,
    EventTarget: ue9,
    MessageEvent: iB1
  };

  function lB1(A, B, Q) {
    if (typeof A === "object" && A.handleEvent) A.handleEvent.call(A, Q);
    else A.call(B, Q)
  }
})
// @from(Start 2546889, End 2550197)
JS1 = z((G48, ggA) => {
  var {
    tokenChars: Rn
  } = lv();

  function Pz(A, B, Q) {
    if (A[B] === void 0) A[B] = [Q];
    else A[B].push(Q)
  }

  function pe9(A) {
    let B = Object.create(null),
      Q = Object.create(null),
      I = !1,
      G = !1,
      Z = !1,
      D, Y, W = -1,
      J = -1,
      F = -1,
      X = 0;
    for (; X < A.length; X++)
      if (J = A.charCodeAt(X), D === void 0)
        if (F === -1 && Rn[J] === 1) {
          if (W === -1) W = X
        } else if (X !== 0 && (J === 32 || J === 9)) {
      if (F === -1 && W !== -1) F = X
    } else if (J === 59 || J === 44) {
      if (W === -1) throw new SyntaxError(`Unexpected character at index ${X}`);
      if (F === -1) F = X;
      let C = A.slice(W, F);
      if (J === 44) Pz(B, C, Q), Q = Object.create(null);
      else D = C;
      W = F = -1
    } else throw new SyntaxError(`Unexpected character at index ${X}`);
    else if (Y === void 0)
      if (F === -1 && Rn[J] === 1) {
        if (W === -1) W = X
      } else if (J === 32 || J === 9) {
      if (F === -1 && W !== -1) F = X
    } else if (J === 59 || J === 44) {
      if (W === -1) throw new SyntaxError(`Unexpected character at index ${X}`);
      if (F === -1) F = X;
      if (Pz(Q, A.slice(W, F), !0), J === 44) Pz(B, D, Q), Q = Object.create(null), D = void 0;
      W = F = -1
    } else if (J === 61 && W !== -1 && F === -1) Y = A.slice(W, X), W = F = -1;
    else throw new SyntaxError(`Unexpected character at index ${X}`);
    else if (G) {
      if (Rn[J] !== 1) throw new SyntaxError(`Unexpected character at index ${X}`);
      if (W === -1) W = X;
      else if (!I) I = !0;
      G = !1
    } else if (Z)
      if (Rn[J] === 1) {
        if (W === -1) W = X
      } else if (J === 34 && W !== -1) Z = !1, F = X;
    else if (J === 92) G = !0;
    else throw new SyntaxError(`Unexpected character at index ${X}`);
    else if (J === 34 && A.charCodeAt(X - 1) === 61) Z = !0;
    else if (F === -1 && Rn[J] === 1) {
      if (W === -1) W = X
    } else if (W !== -1 && (J === 32 || J === 9)) {
      if (F === -1) F = X
    } else if (J === 59 || J === 44) {
      if (W === -1) throw new SyntaxError(`Unexpected character at index ${X}`);
      if (F === -1) F = X;
      let C = A.slice(W, F);
      if (I) C = C.replace(/\\/g, ""), I = !1;
      if (Pz(Q, Y, C), J === 44) Pz(B, D, Q), Q = Object.create(null), D = void 0;
      Y = void 0, W = F = -1
    } else throw new SyntaxError(`Unexpected character at index ${X}`);
    if (W === -1 || Z || J === 32 || J === 9) throw new SyntaxError("Unexpected end of input");
    if (F === -1) F = X;
    let V = A.slice(W, F);
    if (D === void 0) Pz(B, V, Q);
    else {
      if (Y === void 0) Pz(Q, V, !0);
      else if (I) Pz(Q, Y, V.replace(/\\/g, ""));
      else Pz(Q, Y, V);
      Pz(B, D, Q)
    }
    return B
  }

  function ce9(A) {
    return Object.keys(A).map((B) => {
      let Q = A[B];
      if (!Array.isArray(Q)) Q = [Q];
      return Q.map((I) => {
        return [B].concat(Object.keys(I).map((G) => {
          let Z = I[G];
          if (!Array.isArray(Z)) Z = [Z];
          return Z.map((D) => D === !0 ? G : `${G}=${D}`).join("; ")
        })).join("; ")
      }).join(", ")
    }).join(", ")
  }
  ggA.exports = {
    format: ce9,
    parse: pe9
  }
})
// @from(Start 2550203, End 2568567)
rB1 = z((Y48, rgA) => {
  var le9 = Z1("events"),
    ie9 = Z1("https"),
    ne9 = Z1("http"),
    dgA = Z1("net"),
    ae9 = Z1("tls"),
    {
      randomBytes: se9,
      createHash: re9
    } = Z1("crypto"),
    {
      Duplex: Z48,
      Readable: D48
    } = Z1("stream"),
    {
      URL: FS1
    } = Z1("url"),
    FL = qn(),
    oe9 = ZS1(),
    te9 = YS1(),
    {
      isBlob: ee9
    } = lv(),
    {
      BINARY_TYPES: hgA,
      EMPTY_BUFFER: nB1,
      GUID: A14,
      kForOnEventAttribute: XS1,
      kListener: B14,
      kStatusCode: Q14,
      kWebSocket: EI,
      NOOP: ugA
    } = AN(),
    {
      EventTarget: {
        addEventListener: I14,
        removeEventListener: G14
      }
    } = bgA(),
    {
      format: Z14,
      parse: D14
    } = JS1(),
    {
      toBuffer: Y14
    } = Un(),
    pgA = Symbol("kAborted"),
    VS1 = [8, 13],
    QN = ["CONNECTING", "OPEN", "CLOSING", "CLOSED"],
    W14 = /^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;
  class _4 extends le9 {
    constructor(A, B, Q) {
      super();
      if (this._binaryType = hgA[0], this._closeCode = 1006, this._closeFrameReceived = !1, this._closeFrameSent = !1, this._closeMessage = nB1, this._closeTimer = null, this._errorEmitted = !1, this._extensions = {}, this._paused = !1, this._protocol = "", this._readyState = _4.CONNECTING, this._receiver = null, this._sender = null, this._socket = null, A !== null) {
        if (this._bufferedAmount = 0, this._isServer = !1, this._redirects = 0, B === void 0) B = [];
        else if (!Array.isArray(B))
          if (typeof B === "object" && B !== null) Q = B, B = [];
          else B = [B];
        cgA(this, A, B, Q)
      } else this._autoPong = Q.autoPong, this._isServer = !0
    }
    get binaryType() {
      return this._binaryType
    }
    set binaryType(A) {
      if (!hgA.includes(A)) return;
      if (this._binaryType = A, this._receiver) this._receiver._binaryType = A
    }
    get bufferedAmount() {
      if (!this._socket) return this._bufferedAmount;
      return this._socket._writableState.length + this._sender._bufferedBytes
    }
    get extensions() {
      return Object.keys(this._extensions).join()
    }
    get isPaused() {
      return this._paused
    }
    get onclose() {
      return null
    }
    get onerror() {
      return null
    }
    get onopen() {
      return null
    }
    get onmessage() {
      return null
    }
    get protocol() {
      return this._protocol
    }
    get readyState() {
      return this._readyState
    }
    get url() {
      return this._url
    }
    setSocket(A, B, Q) {
      let I = new oe9({
          allowSynchronousEvents: Q.allowSynchronousEvents,
          binaryType: this.binaryType,
          extensions: this._extensions,
          isServer: this._isServer,
          maxPayload: Q.maxPayload,
          skipUTF8Validation: Q.skipUTF8Validation
        }),
        G = new te9(A, this._extensions, Q.generateMask);
      if (this._receiver = I, this._sender = G, this._socket = A, I[EI] = this, G[EI] = this, A[EI] = this, I.on("conclude", X14), I.on("drain", V14), I.on("error", C14), I.on("message", K14), I.on("ping", H14), I.on("pong", z14), G.onerror = w14, A.setTimeout) A.setTimeout(0);
      if (A.setNoDelay) A.setNoDelay();
      if (B.length > 0) A.unshift(B);
      A.on("close", ngA), A.on("data", sB1), A.on("end", agA), A.on("error", sgA), this._readyState = _4.OPEN, this.emit("open")
    }
    emitClose() {
      if (!this._socket) {
        this._readyState = _4.CLOSED, this.emit("close", this._closeCode, this._closeMessage);
        return
      }
      if (this._extensions[FL.extensionName]) this._extensions[FL.extensionName].cleanup();
      this._receiver.removeAllListeners(), this._readyState = _4.CLOSED, this.emit("close", this._closeCode, this._closeMessage)
    }
    close(A, B) {
      if (this.readyState === _4.CLOSED) return;
      if (this.readyState === _4.CONNECTING) {
        XJ(this, this._req, "WebSocket was closed before the connection was established");
        return
      }
      if (this.readyState === _4.CLOSING) {
        if (this._closeFrameSent && (this._closeFrameReceived || this._receiver._writableState.errorEmitted)) this._socket.end();
        return
      }
      this._readyState = _4.CLOSING, this._sender.close(A, B, !this._isServer, (Q) => {
        if (Q) return;
        if (this._closeFrameSent = !0, this._closeFrameReceived || this._receiver._writableState.errorEmitted) this._socket.end()
      }), igA(this)
    }
    pause() {
      if (this.readyState === _4.CONNECTING || this.readyState === _4.CLOSED) return;
      this._paused = !0, this._socket.pause()
    }
    ping(A, B, Q) {
      if (this.readyState === _4.CONNECTING) throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");
      if (typeof A === "function") Q = A, A = B = void 0;
      else if (typeof B === "function") Q = B, B = void 0;
      if (typeof A === "number") A = A.toString();
      if (this.readyState !== _4.OPEN) {
        CS1(this, A, Q);
        return
      }
      if (B === void 0) B = !this._isServer;
      this._sender.ping(A || nB1, B, Q)
    }
    pong(A, B, Q) {
      if (this.readyState === _4.CONNECTING) throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");
      if (typeof A === "function") Q = A, A = B = void 0;
      else if (typeof B === "function") Q = B, B = void 0;
      if (typeof A === "number") A = A.toString();
      if (this.readyState !== _4.OPEN) {
        CS1(this, A, Q);
        return
      }
      if (B === void 0) B = !this._isServer;
      this._sender.pong(A || nB1, B, Q)
    }
    resume() {
      if (this.readyState === _4.CONNECTING || this.readyState === _4.CLOSED) return;
      if (this._paused = !1, !this._receiver._writableState.needDrain) this._socket.resume()
    }
    send(A, B, Q) {
      if (this.readyState === _4.CONNECTING) throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");
      if (typeof B === "function") Q = B, B = {};
      if (typeof A === "number") A = A.toString();
      if (this.readyState !== _4.OPEN) {
        CS1(this, A, Q);
        return
      }
      let I = {
        binary: typeof A !== "string",
        mask: !this._isServer,
        compress: !0,
        fin: !0,
        ...B
      };
      if (!this._extensions[FL.extensionName]) I.compress = !1;
      this._sender.send(A || nB1, I, Q)
    }
    terminate() {
      if (this.readyState === _4.CLOSED) return;
      if (this.readyState === _4.CONNECTING) {
        XJ(this, this._req, "WebSocket was closed before the connection was established");
        return
      }
      if (this._socket) this._readyState = _4.CLOSING, this._socket.destroy()
    }
  }
  Object.defineProperty(_4, "CONNECTING", {
    enumerable: !0,
    value: QN.indexOf("CONNECTING")
  });
  Object.defineProperty(_4.prototype, "CONNECTING", {
    enumerable: !0,
    value: QN.indexOf("CONNECTING")
  });
  Object.defineProperty(_4, "OPEN", {
    enumerable: !0,
    value: QN.indexOf("OPEN")
  });
  Object.defineProperty(_4.prototype, "OPEN", {
    enumerable: !0,
    value: QN.indexOf("OPEN")
  });
  Object.defineProperty(_4, "CLOSING", {
    enumerable: !0,
    value: QN.indexOf("CLOSING")
  });
  Object.defineProperty(_4.prototype, "CLOSING", {
    enumerable: !0,
    value: QN.indexOf("CLOSING")
  });
  Object.defineProperty(_4, "CLOSED", {
    enumerable: !0,
    value: QN.indexOf("CLOSED")
  });
  Object.defineProperty(_4.prototype, "CLOSED", {
    enumerable: !0,
    value: QN.indexOf("CLOSED")
  });
  ["binaryType", "bufferedAmount", "extensions", "isPaused", "protocol", "readyState", "url"].forEach((A) => {
    Object.defineProperty(_4.prototype, A, {
      enumerable: !0
    })
  });
  ["open", "error", "close", "message"].forEach((A) => {
    Object.defineProperty(_4.prototype, `on${A}`, {
      enumerable: !0,
      get() {
        for (let B of this.listeners(A))
          if (B[XS1]) return B[B14];
        return null
      },
      set(B) {
        for (let Q of this.listeners(A))
          if (Q[XS1]) {
            this.removeListener(A, Q);
            break
          } if (typeof B !== "function") return;
        this.addEventListener(A, B, {
          [XS1]: !0
        })
      }
    })
  });
  _4.prototype.addEventListener = I14;
  _4.prototype.removeEventListener = G14;
  rgA.exports = _4;

  function cgA(A, B, Q, I) {
    let G = {
      allowSynchronousEvents: !0,
      autoPong: !0,
      protocolVersion: VS1[1],
      maxPayload: 104857600,
      skipUTF8Validation: !1,
      perMessageDeflate: !0,
      followRedirects: !1,
      maxRedirects: 10,
      ...I,
      socketPath: void 0,
      hostname: void 0,
      protocol: void 0,
      timeout: void 0,
      method: "GET",
      host: void 0,
      path: void 0,
      port: void 0
    };
    if (A._autoPong = G.autoPong, !VS1.includes(G.protocolVersion)) throw new RangeError(`Unsupported protocol version: ${G.protocolVersion} (supported versions: ${VS1.join(", ")})`);
    let Z;
    if (B instanceof FS1) Z = B;
    else try {
      Z = new FS1(B)
    } catch (E) {
      throw new SyntaxError(`Invalid URL: ${B}`)
    }
    if (Z.protocol === "http:") Z.protocol = "ws:";
    else if (Z.protocol === "https:") Z.protocol = "wss:";
    A._url = Z.href;
    let D = Z.protocol === "wss:",
      Y = Z.protocol === "ws+unix:",
      W;
    if (Z.protocol !== "ws:" && !D && !Y) W = `The URL's protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"`;
    else if (Y && !Z.pathname) W = "The URL's pathname is empty";
    else if (Z.hash) W = "The URL contains a fragment identifier";
    if (W) {
      let E = new SyntaxError(W);
      if (A._redirects === 0) throw E;
      else {
        aB1(A, E);
        return
      }
    }
    let J = D ? 443 : 80,
      F = se9(16).toString("base64"),
      X = D ? ie9.request : ne9.request,
      V = new Set,
      C;
    if (G.createConnection = G.createConnection || (D ? F14 : J14), G.defaultPort = G.defaultPort || J, G.port = Z.port || J, G.host = Z.hostname.startsWith("[") ? Z.hostname.slice(1, -1) : Z.hostname, G.headers = {
        ...G.headers,
        "Sec-WebSocket-Version": G.protocolVersion,
        "Sec-WebSocket-Key": F,
        Connection: "Upgrade",
        Upgrade: "websocket"
      }, G.path = Z.pathname + Z.search, G.timeout = G.handshakeTimeout, G.perMessageDeflate) C = new FL(G.perMessageDeflate !== !0 ? G.perMessageDeflate : {}, !1, G.maxPayload), G.headers["Sec-WebSocket-Extensions"] = Z14({
      [FL.extensionName]: C.offer()
    });
    if (Q.length) {
      for (let E of Q) {
        if (typeof E !== "string" || !W14.test(E) || V.has(E)) throw new SyntaxError("An invalid or duplicated subprotocol was specified");
        V.add(E)
      }
      G.headers["Sec-WebSocket-Protocol"] = Q.join(",")
    }
    if (G.origin)
      if (G.protocolVersion < 13) G.headers["Sec-WebSocket-Origin"] = G.origin;
      else G.headers.Origin = G.origin;
    if (Z.username || Z.password) G.auth = `${Z.username}:${Z.password}`;
    if (Y) {
      let E = G.path.split(":");
      G.socketPath = E[0], G.path = E[1]
    }
    let K;
    if (G.followRedirects) {
      if (A._redirects === 0) {
        A._originalIpc = Y, A._originalSecure = D, A._originalHostOrSocketPath = Y ? G.socketPath : Z.host;
        let E = I && I.headers;
        if (I = {
            ...I,
            headers: {}
          }, E)
          for (let [N, q] of Object.entries(E)) I.headers[N.toLowerCase()] = q
      } else if (A.listenerCount("redirect") === 0) {
        let E = Y ? A._originalIpc ? G.socketPath === A._originalHostOrSocketPath : !1 : A._originalIpc ? !1 : Z.host === A._originalHostOrSocketPath;
        if (!E || A._originalSecure && !D) {
          if (delete G.headers.authorization, delete G.headers.cookie, !E) delete G.headers.host;
          G.auth = void 0
        }
      }
      if (G.auth && !I.headers.authorization) I.headers.authorization = "Basic " + Buffer.from(G.auth).toString("base64");
      if (K = A._req = X(G), A._redirects) A.emit("redirect", A.url, K)
    } else K = A._req = X(G);
    if (G.timeout) K.on("timeout", () => {
      XJ(A, K, "Opening handshake has timed out")
    });
    if (K.on("error", (E) => {
        if (K === null || K[pgA]) return;
        K = A._req = null, aB1(A, E)
      }), K.on("response", (E) => {
        let N = E.headers.location,
          q = E.statusCode;
        if (N && G.followRedirects && q >= 300 && q < 400) {
          if (++A._redirects > G.maxRedirects) {
            XJ(A, K, "Maximum redirects exceeded");
            return
          }
          K.abort();
          let O;
          try {
            O = new FS1(N, B)
          } catch (R) {
            let T = new SyntaxError(`Invalid URL: ${N}`);
            aB1(A, T);
            return
          }
          cgA(A, O, Q, I)
        } else if (!A.emit("unexpected-response", K, E)) XJ(A, K, `Unexpected server response: ${E.statusCode}`)
      }), K.on("upgrade", (E, N, q) => {
        if (A.emit("upgrade", E), A.readyState !== _4.CONNECTING) return;
        K = A._req = null;
        let O = E.headers.upgrade;
        if (O === void 0 || O.toLowerCase() !== "websocket") {
          XJ(A, N, "Invalid Upgrade header");
          return
        }
        let R = re9("sha1").update(F + A14).digest("base64");
        if (E.headers["sec-websocket-accept"] !== R) {
          XJ(A, N, "Invalid Sec-WebSocket-Accept header");
          return
        }
        let T = E.headers["sec-websocket-protocol"],
          L;
        if (T !== void 0) {
          if (!V.size) L = "Server sent a subprotocol but none was requested";
          else if (!V.has(T)) L = "Server sent an invalid subprotocol"
        } else if (V.size) L = "Server sent no subprotocol";
        if (L) {
          XJ(A, N, L);
          return
        }
        if (T) A._protocol = T;
        let _ = E.headers["sec-websocket-extensions"];
        if (_ !== void 0) {
          if (!C) {
            XJ(A, N, "Server sent a Sec-WebSocket-Extensions header but no extension was requested");
            return
          }
          let k;
          try {
            k = D14(_)
          } catch (x) {
            XJ(A, N, "Invalid Sec-WebSocket-Extensions header");
            return
          }
          let i = Object.keys(k);
          if (i.length !== 1 || i[0] !== FL.extensionName) {
            XJ(A, N, "Server indicated an extension that was not requested");
            return
          }
          try {
            C.accept(k[FL.extensionName])
          } catch (x) {
            XJ(A, N, "Invalid Sec-WebSocket-Extensions header");
            return
          }
          A._extensions[FL.extensionName] = C
        }
        A.setSocket(N, q, {
          allowSynchronousEvents: G.allowSynchronousEvents,
          generateMask: G.generateMask,
          maxPayload: G.maxPayload,
          skipUTF8Validation: G.skipUTF8Validation
        })
      }), G.finishRequest) G.finishRequest(K, A);
    else K.end()
  }

  function aB1(A, B) {
    A._readyState = _4.CLOSING, A._errorEmitted = !0, A.emit("error", B), A.emitClose()
  }

  function J14(A) {
    return A.path = A.socketPath, dgA.connect(A)
  }

  function F14(A) {
    if (A.path = void 0, !A.servername && A.servername !== "") A.servername = dgA.isIP(A.host) ? "" : A.host;
    return ae9.connect(A)
  }

  function XJ(A, B, Q) {
    A._readyState = _4.CLOSING;
    let I = new Error(Q);
    if (Error.captureStackTrace(I, XJ), B.setHeader) {
      if (B[pgA] = !0, B.abort(), B.socket && !B.socket.destroyed) B.socket.destroy();
      process.nextTick(aB1, A, I)
    } else B.destroy(I), B.once("error", A.emit.bind(A, "error")), B.once("close", A.emitClose.bind(A))
  }

  function CS1(A, B, Q) {
    if (B) {
      let I = ee9(B) ? B.size : Y14(B).length;
      if (A._socket) A._sender._bufferedBytes += I;
      else A._bufferedAmount += I
    }
    if (Q) {
      let I = new Error(`WebSocket is not open: readyState ${A.readyState} (${QN[A.readyState]})`);
      process.nextTick(Q, I)
    }
  }

  function X14(A, B) {
    let Q = this[EI];
    if (Q._closeFrameReceived = !0, Q._closeMessage = B, Q._closeCode = A, Q._socket[EI] === void 0) return;
    if (Q._socket.removeListener("data", sB1), process.nextTick(lgA, Q._socket), A === 1005) Q.close();
    else Q.close(A, B)
  }

  function V14() {
    let A = this[EI];
    if (!A.isPaused) A._socket.resume()
  }

  function C14(A) {
    let B = this[EI];
    if (B._socket[EI] !== void 0) B._socket.removeListener("data", sB1), process.nextTick(lgA, B._socket), B.close(A[Q14]);
    if (!B._errorEmitted) B._errorEmitted = !0, B.emit("error", A)
  }

  function mgA() {
    this[EI].emitClose()
  }

  function K14(A, B) {
    this[EI].emit("message", A, B)
  }

  function H14(A) {
    let B = this[EI];
    if (B._autoPong) B.pong(A, !this._isServer, ugA);
    B.emit("ping", A)
  }

  function z14(A) {
    this[EI].emit("pong", A)
  }

  function lgA(A) {
    A.resume()
  }

  function w14(A) {
    let B = this[EI];
    if (B.readyState === _4.CLOSED) return;
    if (B.readyState === _4.OPEN) B._readyState = _4.CLOSING, igA(B);
    if (this._socket.end(), !B._errorEmitted) B._errorEmitted = !0, B.emit("error", A)
  }

  function igA(A) {
    A._closeTimer = setTimeout(A._socket.destroy.bind(A._socket), 30000)
  }

  function ngA() {
    let A = this[EI];
    this.removeListener("close", ngA), this.removeListener("data", sB1), this.removeListener("end", agA), A._readyState = _4.CLOSING;
    let B;
    if (!this._readableState.endEmitted && !A._closeFrameReceived && !A._receiver._writableState.errorEmitted && (B = A._socket.read()) !== null) A._receiver.write(B);
    if (A._receiver.end(), this[EI] = void 0, clearTimeout(A._closeTimer), A._receiver._writableState.finished || A._receiver._writableState.errorEmitted) A.emitClose();
    else A._receiver.on("error", mgA), A._receiver.on("finish", mgA)
  }

  function sB1(A) {
    if (!this[EI]._receiver.write(A)) this.pause()
  }

  function agA() {
    let A = this[EI];
    A._readyState = _4.CLOSING, A._receiver.end(), this.end()
  }

  function sgA() {
    let A = this[EI];
    if (this.removeListener("error", sgA), this.on("error", ugA), A) A._readyState = _4.CLOSING, this.destroy()
  }
})
// @from(Start 2568573, End 2570553)
AhA = z((J48, egA) => {
  var W48 = rB1(),
    {
      Duplex: E14
    } = Z1("stream");

  function ogA(A) {
    A.emit("close")
  }

  function U14() {
    if (!this.destroyed && this._writableState.finished) this.destroy()
  }

  function tgA(A) {
    if (this.removeListener("error", tgA), this.destroy(), this.listenerCount("error") === 0) this.emit("error", A)
  }

  function N14(A, B) {
    let Q = !0,
      I = new E14({
        ...B,
        autoDestroy: !1,
        emitClose: !1,
        objectMode: !1,
        writableObjectMode: !1
      });
    return A.on("message", function G(Z, D) {
      let Y = !D && I._readableState.objectMode ? Z.toString() : Z;
      if (!I.push(Y)) A.pause()
    }), A.once("error", function G(Z) {
      if (I.destroyed) return;
      Q = !1, I.destroy(Z)
    }), A.once("close", function G() {
      if (I.destroyed) return;
      I.push(null)
    }), I._destroy = function(G, Z) {
      if (A.readyState === A.CLOSED) {
        Z(G), process.nextTick(ogA, I);
        return
      }
      let D = !1;
      if (A.once("error", function Y(W) {
          D = !0, Z(W)
        }), A.once("close", function Y() {
          if (!D) Z(G);
          process.nextTick(ogA, I)
        }), Q) A.terminate()
    }, I._final = function(G) {
      if (A.readyState === A.CONNECTING) {
        A.once("open", function Z() {
          I._final(G)
        });
        return
      }
      if (A._socket === null) return;
      if (A._socket._writableState.finished) {
        if (G(), I._readableState.endEmitted) I.destroy()
      } else A._socket.once("finish", function Z() {
        G()
      }), A.close()
    }, I._read = function() {
      if (A.isPaused) A.resume()
    }, I._write = function(G, Z, D) {
      if (A.readyState === A.CONNECTING) {
        A.once("open", function Y() {
          I._write(G, Z, D)
        });
        return
      }
      A.send(G, D)
    }, I.on("end", U14), I.on("error", tgA), I
  }
  egA.exports = N14
})
// @from(Start 2570559, End 2571548)
QhA = z((F48, BhA) => {
  var {
    tokenChars: $14
  } = lv();

  function q14(A) {
    let B = new Set,
      Q = -1,
      I = -1,
      G = 0;
    for (G; G < A.length; G++) {
      let D = A.charCodeAt(G);
      if (I === -1 && $14[D] === 1) {
        if (Q === -1) Q = G
      } else if (G !== 0 && (D === 32 || D === 9)) {
        if (I === -1 && Q !== -1) I = G
      } else if (D === 44) {
        if (Q === -1) throw new SyntaxError(`Unexpected character at index ${G}`);
        if (I === -1) I = G;
        let Y = A.slice(Q, I);
        if (B.has(Y)) throw new SyntaxError(`The "${Y}" subprotocol is duplicated`);
        B.add(Y), Q = I = -1
      } else throw new SyntaxError(`Unexpected character at index ${G}`)
    }
    if (Q === -1 || I !== -1) throw new SyntaxError("Unexpected end of input");
    let Z = A.slice(Q, G);
    if (B.has(Z)) throw new SyntaxError(`The "${Z}" subprotocol is duplicated`);
    return B.add(Z), B
  }
  BhA.exports = {
    parse: q14
  }
})