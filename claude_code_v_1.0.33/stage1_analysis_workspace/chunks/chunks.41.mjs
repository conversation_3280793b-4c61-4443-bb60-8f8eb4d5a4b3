
// @from(Start 4102856, End 4133393)
Iw0 = z((rz0) => {
  Object.defineProperty(rz0, "__esModule", {
    value: !0
  });
  rz0.SEMATTRS_NET_HOST_CARRIER_ICC = rz0.SEMATTRS_NET_HOST_CARRIER_MNC = rz0.SEMATTRS_NET_HOST_CARRIER_MCC = rz0.SEMATTRS_NET_HOST_CARRIER_NAME = rz0.SEMATTRS_NET_HOST_CONNECTION_SUBTYPE = rz0.SEMATTRS_NET_HOST_CONNECTION_TYPE = rz0.SEMATTRS_NET_HOST_NAME = rz0.SEMATTRS_NET_HOST_PORT = rz0.SEMATTRS_NET_HOST_IP = rz0.SEMATTRS_NET_PEER_NAME = rz0.SEMATTRS_NET_PEER_PORT = rz0.SEMATTRS_NET_PEER_IP = rz0.SEMATTRS_NET_TRANSPORT = rz0.SEMATTRS_FAAS_INVOKED_REGION = rz0.SEMATTRS_FAAS_INVOKED_PROVIDER = rz0.SEMATTRS_FAAS_INVOKED_NAME = rz0.SEMATTRS_FAAS_COLDSTART = rz0.SEMATTRS_FAAS_CRON = rz0.SEMATTRS_FAAS_TIME = rz0.SEMATTRS_FAAS_DOCUMENT_NAME = rz0.SEMATTRS_FAAS_DOCUMENT_TIME = rz0.SEMATTRS_FAAS_DOCUMENT_OPERATION = rz0.SEMATTRS_FAAS_DOCUMENT_COLLECTION = rz0.SEMATTRS_FAAS_EXECUTION = rz0.SEMATTRS_FAAS_TRIGGER = rz0.SEMATTRS_EXCEPTION_ESCAPED = rz0.SEMATTRS_EXCEPTION_STACKTRACE = rz0.SEMATTRS_EXCEPTION_MESSAGE = rz0.SEMATTRS_EXCEPTION_TYPE = rz0.SEMATTRS_DB_SQL_TABLE = rz0.SEMATTRS_DB_MONGODB_COLLECTION = rz0.SEMATTRS_DB_REDIS_DATABASE_INDEX = rz0.SEMATTRS_DB_HBASE_NAMESPACE = rz0.SEMATTRS_DB_CASSANDRA_COORDINATOR_DC = rz0.SEMATTRS_DB_CASSANDRA_COORDINATOR_ID = rz0.SEMATTRS_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT = rz0.SEMATTRS_DB_CASSANDRA_IDEMPOTENCE = rz0.SEMATTRS_DB_CASSANDRA_TABLE = rz0.SEMATTRS_DB_CASSANDRA_CONSISTENCY_LEVEL = rz0.SEMATTRS_DB_CASSANDRA_PAGE_SIZE = rz0.SEMATTRS_DB_CASSANDRA_KEYSPACE = rz0.SEMATTRS_DB_MSSQL_INSTANCE_NAME = rz0.SEMATTRS_DB_OPERATION = rz0.SEMATTRS_DB_STATEMENT = rz0.SEMATTRS_DB_NAME = rz0.SEMATTRS_DB_JDBC_DRIVER_CLASSNAME = rz0.SEMATTRS_DB_USER = rz0.SEMATTRS_DB_CONNECTION_STRING = rz0.SEMATTRS_DB_SYSTEM = rz0.SEMATTRS_AWS_LAMBDA_INVOKED_ARN = void 0;
  rz0.SEMATTRS_MESSAGING_DESTINATION_KIND = rz0.SEMATTRS_MESSAGING_DESTINATION = rz0.SEMATTRS_MESSAGING_SYSTEM = rz0.SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES = rz0.SEMATTRS_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS = rz0.SEMATTRS_AWS_DYNAMODB_SCANNED_COUNT = rz0.SEMATTRS_AWS_DYNAMODB_COUNT = rz0.SEMATTRS_AWS_DYNAMODB_TOTAL_SEGMENTS = rz0.SEMATTRS_AWS_DYNAMODB_SEGMENT = rz0.SEMATTRS_AWS_DYNAMODB_SCAN_FORWARD = rz0.SEMATTRS_AWS_DYNAMODB_TABLE_COUNT = rz0.SEMATTRS_AWS_DYNAMODB_EXCLUSIVE_START_TABLE = rz0.SEMATTRS_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES = rz0.SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES = rz0.SEMATTRS_AWS_DYNAMODB_SELECT = rz0.SEMATTRS_AWS_DYNAMODB_INDEX_NAME = rz0.SEMATTRS_AWS_DYNAMODB_ATTRIBUTES_TO_GET = rz0.SEMATTRS_AWS_DYNAMODB_LIMIT = rz0.SEMATTRS_AWS_DYNAMODB_PROJECTION = rz0.SEMATTRS_AWS_DYNAMODB_CONSISTENT_READ = rz0.SEMATTRS_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY = rz0.SEMATTRS_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY = rz0.SEMATTRS_AWS_DYNAMODB_ITEM_COLLECTION_METRICS = rz0.SEMATTRS_AWS_DYNAMODB_CONSUMED_CAPACITY = rz0.SEMATTRS_AWS_DYNAMODB_TABLE_NAMES = rz0.SEMATTRS_HTTP_CLIENT_IP = rz0.SEMATTRS_HTTP_ROUTE = rz0.SEMATTRS_HTTP_SERVER_NAME = rz0.SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED = rz0.SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH = rz0.SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED = rz0.SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH = rz0.SEMATTRS_HTTP_USER_AGENT = rz0.SEMATTRS_HTTP_FLAVOR = rz0.SEMATTRS_HTTP_STATUS_CODE = rz0.SEMATTRS_HTTP_SCHEME = rz0.SEMATTRS_HTTP_HOST = rz0.SEMATTRS_HTTP_TARGET = rz0.SEMATTRS_HTTP_URL = rz0.SEMATTRS_HTTP_METHOD = rz0.SEMATTRS_CODE_LINENO = rz0.SEMATTRS_CODE_FILEPATH = rz0.SEMATTRS_CODE_NAMESPACE = rz0.SEMATTRS_CODE_FUNCTION = rz0.SEMATTRS_THREAD_NAME = rz0.SEMATTRS_THREAD_ID = rz0.SEMATTRS_ENDUSER_SCOPE = rz0.SEMATTRS_ENDUSER_ROLE = rz0.SEMATTRS_ENDUSER_ID = rz0.SEMATTRS_PEER_SERVICE = void 0;
  rz0.DBSYSTEMVALUES_FILEMAKER = rz0.DBSYSTEMVALUES_DERBY = rz0.DBSYSTEMVALUES_FIREBIRD = rz0.DBSYSTEMVALUES_ADABAS = rz0.DBSYSTEMVALUES_CACHE = rz0.DBSYSTEMVALUES_EDB = rz0.DBSYSTEMVALUES_FIRSTSQL = rz0.DBSYSTEMVALUES_INGRES = rz0.DBSYSTEMVALUES_HANADB = rz0.DBSYSTEMVALUES_MAXDB = rz0.DBSYSTEMVALUES_PROGRESS = rz0.DBSYSTEMVALUES_HSQLDB = rz0.DBSYSTEMVALUES_CLOUDSCAPE = rz0.DBSYSTEMVALUES_HIVE = rz0.DBSYSTEMVALUES_REDSHIFT = rz0.DBSYSTEMVALUES_POSTGRESQL = rz0.DBSYSTEMVALUES_DB2 = rz0.DBSYSTEMVALUES_ORACLE = rz0.DBSYSTEMVALUES_MYSQL = rz0.DBSYSTEMVALUES_MSSQL = rz0.DBSYSTEMVALUES_OTHER_SQL = rz0.SemanticAttributes = rz0.SEMATTRS_MESSAGE_UNCOMPRESSED_SIZE = rz0.SEMATTRS_MESSAGE_COMPRESSED_SIZE = rz0.SEMATTRS_MESSAGE_ID = rz0.SEMATTRS_MESSAGE_TYPE = rz0.SEMATTRS_RPC_JSONRPC_ERROR_MESSAGE = rz0.SEMATTRS_RPC_JSONRPC_ERROR_CODE = rz0.SEMATTRS_RPC_JSONRPC_REQUEST_ID = rz0.SEMATTRS_RPC_JSONRPC_VERSION = rz0.SEMATTRS_RPC_GRPC_STATUS_CODE = rz0.SEMATTRS_RPC_METHOD = rz0.SEMATTRS_RPC_SERVICE = rz0.SEMATTRS_RPC_SYSTEM = rz0.SEMATTRS_MESSAGING_KAFKA_TOMBSTONE = rz0.SEMATTRS_MESSAGING_KAFKA_PARTITION = rz0.SEMATTRS_MESSAGING_KAFKA_CLIENT_ID = rz0.SEMATTRS_MESSAGING_KAFKA_CONSUMER_GROUP = rz0.SEMATTRS_MESSAGING_KAFKA_MESSAGE_KEY = rz0.SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY = rz0.SEMATTRS_MESSAGING_CONSUMER_ID = rz0.SEMATTRS_MESSAGING_OPERATION = rz0.SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES = rz0.SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES = rz0.SEMATTRS_MESSAGING_CONVERSATION_ID = rz0.SEMATTRS_MESSAGING_MESSAGE_ID = rz0.SEMATTRS_MESSAGING_URL = rz0.SEMATTRS_MESSAGING_PROTOCOL_VERSION = rz0.SEMATTRS_MESSAGING_PROTOCOL = rz0.SEMATTRS_MESSAGING_TEMP_DESTINATION = void 0;
  rz0.FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD = rz0.FaasDocumentOperationValues = rz0.FAASDOCUMENTOPERATIONVALUES_DELETE = rz0.FAASDOCUMENTOPERATIONVALUES_EDIT = rz0.FAASDOCUMENTOPERATIONVALUES_INSERT = rz0.FaasTriggerValues = rz0.FAASTRIGGERVALUES_OTHER = rz0.FAASTRIGGERVALUES_TIMER = rz0.FAASTRIGGERVALUES_PUBSUB = rz0.FAASTRIGGERVALUES_HTTP = rz0.FAASTRIGGERVALUES_DATASOURCE = rz0.DbCassandraConsistencyLevelValues = rz0.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL = rz0.DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL = rz0.DBCASSANDRACONSISTENCYLEVELVALUES_ANY = rz0.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE = rz0.DBCASSANDRACONSISTENCYLEVELVALUES_THREE = rz0.DBCASSANDRACONSISTENCYLEVELVALUES_TWO = rz0.DBCASSANDRACONSISTENCYLEVELVALUES_ONE = rz0.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM = rz0.DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM = rz0.DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM = rz0.DBCASSANDRACONSISTENCYLEVELVALUES_ALL = rz0.DbSystemValues = rz0.DBSYSTEMVALUES_COCKROACHDB = rz0.DBSYSTEMVALUES_MEMCACHED = rz0.DBSYSTEMVALUES_ELASTICSEARCH = rz0.DBSYSTEMVALUES_GEODE = rz0.DBSYSTEMVALUES_NEO4J = rz0.DBSYSTEMVALUES_DYNAMODB = rz0.DBSYSTEMVALUES_COSMOSDB = rz0.DBSYSTEMVALUES_COUCHDB = rz0.DBSYSTEMVALUES_COUCHBASE = rz0.DBSYSTEMVALUES_REDIS = rz0.DBSYSTEMVALUES_MONGODB = rz0.DBSYSTEMVALUES_HBASE = rz0.DBSYSTEMVALUES_CASSANDRA = rz0.DBSYSTEMVALUES_COLDFUSION = rz0.DBSYSTEMVALUES_H2 = rz0.DBSYSTEMVALUES_VERTICA = rz0.DBSYSTEMVALUES_TERADATA = rz0.DBSYSTEMVALUES_SYBASE = rz0.DBSYSTEMVALUES_SQLITE = rz0.DBSYSTEMVALUES_POINTBASE = rz0.DBSYSTEMVALUES_PERVASIVE = rz0.DBSYSTEMVALUES_NETEZZA = rz0.DBSYSTEMVALUES_MARIADB = rz0.DBSYSTEMVALUES_INTERBASE = rz0.DBSYSTEMVALUES_INSTANTDB = rz0.DBSYSTEMVALUES_INFORMIX = void 0;
  rz0.MESSAGINGOPERATIONVALUES_RECEIVE = rz0.MessagingDestinationKindValues = rz0.MESSAGINGDESTINATIONKINDVALUES_TOPIC = rz0.MESSAGINGDESTINATIONKINDVALUES_QUEUE = rz0.HttpFlavorValues = rz0.HTTPFLAVORVALUES_QUIC = rz0.HTTPFLAVORVALUES_SPDY = rz0.HTTPFLAVORVALUES_HTTP_2_0 = rz0.HTTPFLAVORVALUES_HTTP_1_1 = rz0.HTTPFLAVORVALUES_HTTP_1_0 = rz0.NetHostConnectionSubtypeValues = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_NR = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_GSM = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_LTE = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_IDEN = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_HSPA = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0 = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_CDMA = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_UMTS = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_EDGE = rz0.NETHOSTCONNECTIONSUBTYPEVALUES_GPRS = rz0.NetHostConnectionTypeValues = rz0.NETHOSTCONNECTIONTYPEVALUES_UNKNOWN = rz0.NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE = rz0.NETHOSTCONNECTIONTYPEVALUES_CELL = rz0.NETHOSTCONNECTIONTYPEVALUES_WIRED = rz0.NETHOSTCONNECTIONTYPEVALUES_WIFI = rz0.NetTransportValues = rz0.NETTRANSPORTVALUES_OTHER = rz0.NETTRANSPORTVALUES_INPROC = rz0.NETTRANSPORTVALUES_PIPE = rz0.NETTRANSPORTVALUES_UNIX = rz0.NETTRANSPORTVALUES_IP = rz0.NETTRANSPORTVALUES_IP_UDP = rz0.NETTRANSPORTVALUES_IP_TCP = rz0.FaasInvokedProviderValues = rz0.FAASINVOKEDPROVIDERVALUES_GCP = rz0.FAASINVOKEDPROVIDERVALUES_AZURE = rz0.FAASINVOKEDPROVIDERVALUES_AWS = void 0;
  rz0.MessageTypeValues = rz0.MESSAGETYPEVALUES_RECEIVED = rz0.MESSAGETYPEVALUES_SENT = rz0.RpcGrpcStatusCodeValues = rz0.RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED = rz0.RPCGRPCSTATUSCODEVALUES_DATA_LOSS = rz0.RPCGRPCSTATUSCODEVALUES_UNAVAILABLE = rz0.RPCGRPCSTATUSCODEVALUES_INTERNAL = rz0.RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED = rz0.RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE = rz0.RPCGRPCSTATUSCODEVALUES_ABORTED = rz0.RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION = rz0.RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED = rz0.RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED = rz0.RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS = rz0.RPCGRPCSTATUSCODEVALUES_NOT_FOUND = rz0.RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED = rz0.RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT = rz0.RPCGRPCSTATUSCODEVALUES_UNKNOWN = rz0.RPCGRPCSTATUSCODEVALUES_CANCELLED = rz0.RPCGRPCSTATUSCODEVALUES_OK = rz0.MessagingOperationValues = rz0.MESSAGINGOPERATIONVALUES_PROCESS = void 0;
  var qX = _b1(),
    YV0 = "aws.lambda.invoked_arn",
    WV0 = "db.system",
    JV0 = "db.connection_string",
    FV0 = "db.user",
    XV0 = "db.jdbc.driver_classname",
    VV0 = "db.name",
    CV0 = "db.statement",
    KV0 = "db.operation",
    HV0 = "db.mssql.instance_name",
    zV0 = "db.cassandra.keyspace",
    wV0 = "db.cassandra.page_size",
    EV0 = "db.cassandra.consistency_level",
    UV0 = "db.cassandra.table",
    NV0 = "db.cassandra.idempotence",
    $V0 = "db.cassandra.speculative_execution_count",
    qV0 = "db.cassandra.coordinator.id",
    MV0 = "db.cassandra.coordinator.dc",
    LV0 = "db.hbase.namespace",
    RV0 = "db.redis.database_index",
    OV0 = "db.mongodb.collection",
    TV0 = "db.sql.table",
    PV0 = "exception.type",
    SV0 = "exception.message",
    _V0 = "exception.stacktrace",
    jV0 = "exception.escaped",
    yV0 = "faas.trigger",
    kV0 = "faas.execution",
    xV0 = "faas.document.collection",
    fV0 = "faas.document.operation",
    vV0 = "faas.document.time",
    bV0 = "faas.document.name",
    gV0 = "faas.time",
    hV0 = "faas.cron",
    mV0 = "faas.coldstart",
    dV0 = "faas.invoked_name",
    uV0 = "faas.invoked_provider",
    pV0 = "faas.invoked_region",
    cV0 = "net.transport",
    lV0 = "net.peer.ip",
    iV0 = "net.peer.port",
    nV0 = "net.peer.name",
    aV0 = "net.host.ip",
    sV0 = "net.host.port",
    rV0 = "net.host.name",
    oV0 = "net.host.connection.type",
    tV0 = "net.host.connection.subtype",
    eV0 = "net.host.carrier.name",
    AC0 = "net.host.carrier.mcc",
    BC0 = "net.host.carrier.mnc",
    QC0 = "net.host.carrier.icc",
    IC0 = "peer.service",
    GC0 = "enduser.id",
    ZC0 = "enduser.role",
    DC0 = "enduser.scope",
    YC0 = "thread.id",
    WC0 = "thread.name",
    JC0 = "code.function",
    FC0 = "code.namespace",
    XC0 = "code.filepath",
    VC0 = "code.lineno",
    CC0 = "http.method",
    KC0 = "http.url",
    HC0 = "http.target",
    zC0 = "http.host",
    wC0 = "http.scheme",
    EC0 = "http.status_code",
    UC0 = "http.flavor",
    NC0 = "http.user_agent",
    $C0 = "http.request_content_length",
    qC0 = "http.request_content_length_uncompressed",
    MC0 = "http.response_content_length",
    LC0 = "http.response_content_length_uncompressed",
    RC0 = "http.server_name",
    OC0 = "http.route",
    TC0 = "http.client_ip",
    PC0 = "aws.dynamodb.table_names",
    SC0 = "aws.dynamodb.consumed_capacity",
    _C0 = "aws.dynamodb.item_collection_metrics",
    jC0 = "aws.dynamodb.provisioned_read_capacity",
    yC0 = "aws.dynamodb.provisioned_write_capacity",
    kC0 = "aws.dynamodb.consistent_read",
    xC0 = "aws.dynamodb.projection",
    fC0 = "aws.dynamodb.limit",
    vC0 = "aws.dynamodb.attributes_to_get",
    bC0 = "aws.dynamodb.index_name",
    gC0 = "aws.dynamodb.select",
    hC0 = "aws.dynamodb.global_secondary_indexes",
    mC0 = "aws.dynamodb.local_secondary_indexes",
    dC0 = "aws.dynamodb.exclusive_start_table",
    uC0 = "aws.dynamodb.table_count",
    pC0 = "aws.dynamodb.scan_forward",
    cC0 = "aws.dynamodb.segment",
    lC0 = "aws.dynamodb.total_segments",
    iC0 = "aws.dynamodb.count",
    nC0 = "aws.dynamodb.scanned_count",
    aC0 = "aws.dynamodb.attribute_definitions",
    sC0 = "aws.dynamodb.global_secondary_index_updates",
    rC0 = "messaging.system",
    oC0 = "messaging.destination",
    tC0 = "messaging.destination_kind",
    eC0 = "messaging.temp_destination",
    AK0 = "messaging.protocol",
    BK0 = "messaging.protocol_version",
    QK0 = "messaging.url",
    IK0 = "messaging.message_id",
    GK0 = "messaging.conversation_id",
    ZK0 = "messaging.message_payload_size_bytes",
    DK0 = "messaging.message_payload_compressed_size_bytes",
    YK0 = "messaging.operation",
    WK0 = "messaging.consumer_id",
    JK0 = "messaging.rabbitmq.routing_key",
    FK0 = "messaging.kafka.message_key",
    XK0 = "messaging.kafka.consumer_group",
    VK0 = "messaging.kafka.client_id",
    CK0 = "messaging.kafka.partition",
    KK0 = "messaging.kafka.tombstone",
    HK0 = "rpc.system",
    zK0 = "rpc.service",
    wK0 = "rpc.method",
    EK0 = "rpc.grpc.status_code",
    UK0 = "rpc.jsonrpc.version",
    NK0 = "rpc.jsonrpc.request_id",
    $K0 = "rpc.jsonrpc.error_code",
    qK0 = "rpc.jsonrpc.error_message",
    MK0 = "message.type",
    LK0 = "message.id",
    RK0 = "message.compressed_size",
    OK0 = "message.uncompressed_size";
  rz0.SEMATTRS_AWS_LAMBDA_INVOKED_ARN = YV0;
  rz0.SEMATTRS_DB_SYSTEM = WV0;
  rz0.SEMATTRS_DB_CONNECTION_STRING = JV0;
  rz0.SEMATTRS_DB_USER = FV0;
  rz0.SEMATTRS_DB_JDBC_DRIVER_CLASSNAME = XV0;
  rz0.SEMATTRS_DB_NAME = VV0;
  rz0.SEMATTRS_DB_STATEMENT = CV0;
  rz0.SEMATTRS_DB_OPERATION = KV0;
  rz0.SEMATTRS_DB_MSSQL_INSTANCE_NAME = HV0;
  rz0.SEMATTRS_DB_CASSANDRA_KEYSPACE = zV0;
  rz0.SEMATTRS_DB_CASSANDRA_PAGE_SIZE = wV0;
  rz0.SEMATTRS_DB_CASSANDRA_CONSISTENCY_LEVEL = EV0;
  rz0.SEMATTRS_DB_CASSANDRA_TABLE = UV0;
  rz0.SEMATTRS_DB_CASSANDRA_IDEMPOTENCE = NV0;
  rz0.SEMATTRS_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT = $V0;
  rz0.SEMATTRS_DB_CASSANDRA_COORDINATOR_ID = qV0;
  rz0.SEMATTRS_DB_CASSANDRA_COORDINATOR_DC = MV0;
  rz0.SEMATTRS_DB_HBASE_NAMESPACE = LV0;
  rz0.SEMATTRS_DB_REDIS_DATABASE_INDEX = RV0;
  rz0.SEMATTRS_DB_MONGODB_COLLECTION = OV0;
  rz0.SEMATTRS_DB_SQL_TABLE = TV0;
  rz0.SEMATTRS_EXCEPTION_TYPE = PV0;
  rz0.SEMATTRS_EXCEPTION_MESSAGE = SV0;
  rz0.SEMATTRS_EXCEPTION_STACKTRACE = _V0;
  rz0.SEMATTRS_EXCEPTION_ESCAPED = jV0;
  rz0.SEMATTRS_FAAS_TRIGGER = yV0;
  rz0.SEMATTRS_FAAS_EXECUTION = kV0;
  rz0.SEMATTRS_FAAS_DOCUMENT_COLLECTION = xV0;
  rz0.SEMATTRS_FAAS_DOCUMENT_OPERATION = fV0;
  rz0.SEMATTRS_FAAS_DOCUMENT_TIME = vV0;
  rz0.SEMATTRS_FAAS_DOCUMENT_NAME = bV0;
  rz0.SEMATTRS_FAAS_TIME = gV0;
  rz0.SEMATTRS_FAAS_CRON = hV0;
  rz0.SEMATTRS_FAAS_COLDSTART = mV0;
  rz0.SEMATTRS_FAAS_INVOKED_NAME = dV0;
  rz0.SEMATTRS_FAAS_INVOKED_PROVIDER = uV0;
  rz0.SEMATTRS_FAAS_INVOKED_REGION = pV0;
  rz0.SEMATTRS_NET_TRANSPORT = cV0;
  rz0.SEMATTRS_NET_PEER_IP = lV0;
  rz0.SEMATTRS_NET_PEER_PORT = iV0;
  rz0.SEMATTRS_NET_PEER_NAME = nV0;
  rz0.SEMATTRS_NET_HOST_IP = aV0;
  rz0.SEMATTRS_NET_HOST_PORT = sV0;
  rz0.SEMATTRS_NET_HOST_NAME = rV0;
  rz0.SEMATTRS_NET_HOST_CONNECTION_TYPE = oV0;
  rz0.SEMATTRS_NET_HOST_CONNECTION_SUBTYPE = tV0;
  rz0.SEMATTRS_NET_HOST_CARRIER_NAME = eV0;
  rz0.SEMATTRS_NET_HOST_CARRIER_MCC = AC0;
  rz0.SEMATTRS_NET_HOST_CARRIER_MNC = BC0;
  rz0.SEMATTRS_NET_HOST_CARRIER_ICC = QC0;
  rz0.SEMATTRS_PEER_SERVICE = IC0;
  rz0.SEMATTRS_ENDUSER_ID = GC0;
  rz0.SEMATTRS_ENDUSER_ROLE = ZC0;
  rz0.SEMATTRS_ENDUSER_SCOPE = DC0;
  rz0.SEMATTRS_THREAD_ID = YC0;
  rz0.SEMATTRS_THREAD_NAME = WC0;
  rz0.SEMATTRS_CODE_FUNCTION = JC0;
  rz0.SEMATTRS_CODE_NAMESPACE = FC0;
  rz0.SEMATTRS_CODE_FILEPATH = XC0;
  rz0.SEMATTRS_CODE_LINENO = VC0;
  rz0.SEMATTRS_HTTP_METHOD = CC0;
  rz0.SEMATTRS_HTTP_URL = KC0;
  rz0.SEMATTRS_HTTP_TARGET = HC0;
  rz0.SEMATTRS_HTTP_HOST = zC0;
  rz0.SEMATTRS_HTTP_SCHEME = wC0;
  rz0.SEMATTRS_HTTP_STATUS_CODE = EC0;
  rz0.SEMATTRS_HTTP_FLAVOR = UC0;
  rz0.SEMATTRS_HTTP_USER_AGENT = NC0;
  rz0.SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH = $C0;
  rz0.SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED = qC0;
  rz0.SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH = MC0;
  rz0.SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED = LC0;
  rz0.SEMATTRS_HTTP_SERVER_NAME = RC0;
  rz0.SEMATTRS_HTTP_ROUTE = OC0;
  rz0.SEMATTRS_HTTP_CLIENT_IP = TC0;
  rz0.SEMATTRS_AWS_DYNAMODB_TABLE_NAMES = PC0;
  rz0.SEMATTRS_AWS_DYNAMODB_CONSUMED_CAPACITY = SC0;
  rz0.SEMATTRS_AWS_DYNAMODB_ITEM_COLLECTION_METRICS = _C0;
  rz0.SEMATTRS_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY = jC0;
  rz0.SEMATTRS_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY = yC0;
  rz0.SEMATTRS_AWS_DYNAMODB_CONSISTENT_READ = kC0;
  rz0.SEMATTRS_AWS_DYNAMODB_PROJECTION = xC0;
  rz0.SEMATTRS_AWS_DYNAMODB_LIMIT = fC0;
  rz0.SEMATTRS_AWS_DYNAMODB_ATTRIBUTES_TO_GET = vC0;
  rz0.SEMATTRS_AWS_DYNAMODB_INDEX_NAME = bC0;
  rz0.SEMATTRS_AWS_DYNAMODB_SELECT = gC0;
  rz0.SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES = hC0;
  rz0.SEMATTRS_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES = mC0;
  rz0.SEMATTRS_AWS_DYNAMODB_EXCLUSIVE_START_TABLE = dC0;
  rz0.SEMATTRS_AWS_DYNAMODB_TABLE_COUNT = uC0;
  rz0.SEMATTRS_AWS_DYNAMODB_SCAN_FORWARD = pC0;
  rz0.SEMATTRS_AWS_DYNAMODB_SEGMENT = cC0;
  rz0.SEMATTRS_AWS_DYNAMODB_TOTAL_SEGMENTS = lC0;
  rz0.SEMATTRS_AWS_DYNAMODB_COUNT = iC0;
  rz0.SEMATTRS_AWS_DYNAMODB_SCANNED_COUNT = nC0;
  rz0.SEMATTRS_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS = aC0;
  rz0.SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES = sC0;
  rz0.SEMATTRS_MESSAGING_SYSTEM = rC0;
  rz0.SEMATTRS_MESSAGING_DESTINATION = oC0;
  rz0.SEMATTRS_MESSAGING_DESTINATION_KIND = tC0;
  rz0.SEMATTRS_MESSAGING_TEMP_DESTINATION = eC0;
  rz0.SEMATTRS_MESSAGING_PROTOCOL = AK0;
  rz0.SEMATTRS_MESSAGING_PROTOCOL_VERSION = BK0;
  rz0.SEMATTRS_MESSAGING_URL = QK0;
  rz0.SEMATTRS_MESSAGING_MESSAGE_ID = IK0;
  rz0.SEMATTRS_MESSAGING_CONVERSATION_ID = GK0;
  rz0.SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES = ZK0;
  rz0.SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES = DK0;
  rz0.SEMATTRS_MESSAGING_OPERATION = YK0;
  rz0.SEMATTRS_MESSAGING_CONSUMER_ID = WK0;
  rz0.SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY = JK0;
  rz0.SEMATTRS_MESSAGING_KAFKA_MESSAGE_KEY = FK0;
  rz0.SEMATTRS_MESSAGING_KAFKA_CONSUMER_GROUP = XK0;
  rz0.SEMATTRS_MESSAGING_KAFKA_CLIENT_ID = VK0;
  rz0.SEMATTRS_MESSAGING_KAFKA_PARTITION = CK0;
  rz0.SEMATTRS_MESSAGING_KAFKA_TOMBSTONE = KK0;
  rz0.SEMATTRS_RPC_SYSTEM = HK0;
  rz0.SEMATTRS_RPC_SERVICE = zK0;
  rz0.SEMATTRS_RPC_METHOD = wK0;
  rz0.SEMATTRS_RPC_GRPC_STATUS_CODE = EK0;
  rz0.SEMATTRS_RPC_JSONRPC_VERSION = UK0;
  rz0.SEMATTRS_RPC_JSONRPC_REQUEST_ID = NK0;
  rz0.SEMATTRS_RPC_JSONRPC_ERROR_CODE = $K0;
  rz0.SEMATTRS_RPC_JSONRPC_ERROR_MESSAGE = qK0;
  rz0.SEMATTRS_MESSAGE_TYPE = MK0;
  rz0.SEMATTRS_MESSAGE_ID = LK0;
  rz0.SEMATTRS_MESSAGE_COMPRESSED_SIZE = RK0;
  rz0.SEMATTRS_MESSAGE_UNCOMPRESSED_SIZE = OK0;
  rz0.SemanticAttributes = qX.createConstMap([YV0, WV0, JV0, FV0, XV0, VV0, CV0, KV0, HV0, zV0, wV0, EV0, UV0, NV0, $V0, qV0, MV0, LV0, RV0, OV0, TV0, PV0, SV0, _V0, jV0, yV0, kV0, xV0, fV0, vV0, bV0, gV0, hV0, mV0, dV0, uV0, pV0, cV0, lV0, iV0, nV0, aV0, sV0, rV0, oV0, tV0, eV0, AC0, BC0, QC0, IC0, GC0, ZC0, DC0, YC0, WC0, JC0, FC0, XC0, VC0, CC0, KC0, HC0, zC0, wC0, EC0, UC0, NC0, $C0, qC0, MC0, LC0, RC0, OC0, TC0, PC0, SC0, _C0, jC0, yC0, kC0, xC0, fC0, vC0, bC0, gC0, hC0, mC0, dC0, uC0, pC0, cC0, lC0, iC0, nC0, aC0, sC0, rC0, oC0, tC0, eC0, AK0, BK0, QK0, IK0, GK0, ZK0, DK0, YK0, WK0, JK0, FK0, XK0, VK0, CK0, KK0, HK0, zK0, wK0, EK0, UK0, NK0, $K0, qK0, MK0, LK0, RK0, OK0]);
  var TK0 = "other_sql",
    PK0 = "mssql",
    SK0 = "mysql",
    _K0 = "oracle",
    jK0 = "db2",
    yK0 = "postgresql",
    kK0 = "redshift",
    xK0 = "hive",
    fK0 = "cloudscape",
    vK0 = "hsqldb",
    bK0 = "progress",
    gK0 = "maxdb",
    hK0 = "hanadb",
    mK0 = "ingres",
    dK0 = "firstsql",
    uK0 = "edb",
    pK0 = "cache",
    cK0 = "adabas",
    lK0 = "firebird",
    iK0 = "derby",
    nK0 = "filemaker",
    aK0 = "informix",
    sK0 = "instantdb",
    rK0 = "interbase",
    oK0 = "mariadb",
    tK0 = "netezza",
    eK0 = "pervasive",
    AH0 = "pointbase",
    BH0 = "sqlite",
    QH0 = "sybase",
    IH0 = "teradata",
    GH0 = "vertica",
    ZH0 = "h2",
    DH0 = "coldfusion",
    YH0 = "cassandra",
    WH0 = "hbase",
    JH0 = "mongodb",
    FH0 = "redis",
    XH0 = "couchbase",
    VH0 = "couchdb",
    CH0 = "cosmosdb",
    KH0 = "dynamodb",
    HH0 = "neo4j",
    zH0 = "geode",
    wH0 = "elasticsearch",
    EH0 = "memcached",
    UH0 = "cockroachdb";
  rz0.DBSYSTEMVALUES_OTHER_SQL = TK0;
  rz0.DBSYSTEMVALUES_MSSQL = PK0;
  rz0.DBSYSTEMVALUES_MYSQL = SK0;
  rz0.DBSYSTEMVALUES_ORACLE = _K0;
  rz0.DBSYSTEMVALUES_DB2 = jK0;
  rz0.DBSYSTEMVALUES_POSTGRESQL = yK0;
  rz0.DBSYSTEMVALUES_REDSHIFT = kK0;
  rz0.DBSYSTEMVALUES_HIVE = xK0;
  rz0.DBSYSTEMVALUES_CLOUDSCAPE = fK0;
  rz0.DBSYSTEMVALUES_HSQLDB = vK0;
  rz0.DBSYSTEMVALUES_PROGRESS = bK0;
  rz0.DBSYSTEMVALUES_MAXDB = gK0;
  rz0.DBSYSTEMVALUES_HANADB = hK0;
  rz0.DBSYSTEMVALUES_INGRES = mK0;
  rz0.DBSYSTEMVALUES_FIRSTSQL = dK0;
  rz0.DBSYSTEMVALUES_EDB = uK0;
  rz0.DBSYSTEMVALUES_CACHE = pK0;
  rz0.DBSYSTEMVALUES_ADABAS = cK0;
  rz0.DBSYSTEMVALUES_FIREBIRD = lK0;
  rz0.DBSYSTEMVALUES_DERBY = iK0;
  rz0.DBSYSTEMVALUES_FILEMAKER = nK0;
  rz0.DBSYSTEMVALUES_INFORMIX = aK0;
  rz0.DBSYSTEMVALUES_INSTANTDB = sK0;
  rz0.DBSYSTEMVALUES_INTERBASE = rK0;
  rz0.DBSYSTEMVALUES_MARIADB = oK0;
  rz0.DBSYSTEMVALUES_NETEZZA = tK0;
  rz0.DBSYSTEMVALUES_PERVASIVE = eK0;
  rz0.DBSYSTEMVALUES_POINTBASE = AH0;
  rz0.DBSYSTEMVALUES_SQLITE = BH0;
  rz0.DBSYSTEMVALUES_SYBASE = QH0;
  rz0.DBSYSTEMVALUES_TERADATA = IH0;
  rz0.DBSYSTEMVALUES_VERTICA = GH0;
  rz0.DBSYSTEMVALUES_H2 = ZH0;
  rz0.DBSYSTEMVALUES_COLDFUSION = DH0;
  rz0.DBSYSTEMVALUES_CASSANDRA = YH0;
  rz0.DBSYSTEMVALUES_HBASE = WH0;
  rz0.DBSYSTEMVALUES_MONGODB = JH0;
  rz0.DBSYSTEMVALUES_REDIS = FH0;
  rz0.DBSYSTEMVALUES_COUCHBASE = XH0;
  rz0.DBSYSTEMVALUES_COUCHDB = VH0;
  rz0.DBSYSTEMVALUES_COSMOSDB = CH0;
  rz0.DBSYSTEMVALUES_DYNAMODB = KH0;
  rz0.DBSYSTEMVALUES_NEO4J = HH0;
  rz0.DBSYSTEMVALUES_GEODE = zH0;
  rz0.DBSYSTEMVALUES_ELASTICSEARCH = wH0;
  rz0.DBSYSTEMVALUES_MEMCACHED = EH0;
  rz0.DBSYSTEMVALUES_COCKROACHDB = UH0;
  rz0.DbSystemValues = qX.createConstMap([TK0, PK0, SK0, _K0, jK0, yK0, kK0, xK0, fK0, vK0, bK0, gK0, hK0, mK0, dK0, uK0, pK0, cK0, lK0, iK0, nK0, aK0, sK0, rK0, oK0, tK0, eK0, AH0, BH0, QH0, IH0, GH0, ZH0, DH0, YH0, WH0, JH0, FH0, XH0, VH0, CH0, KH0, HH0, zH0, wH0, EH0, UH0]);
  var NH0 = "all",
    $H0 = "each_quorum",
    qH0 = "quorum",
    MH0 = "local_quorum",
    LH0 = "one",
    RH0 = "two",
    OH0 = "three",
    TH0 = "local_one",
    PH0 = "any",
    SH0 = "serial",
    _H0 = "local_serial";
  rz0.DBCASSANDRACONSISTENCYLEVELVALUES_ALL = NH0;
  rz0.DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM = $H0;
  rz0.DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM = qH0;
  rz0.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM = MH0;
  rz0.DBCASSANDRACONSISTENCYLEVELVALUES_ONE = LH0;
  rz0.DBCASSANDRACONSISTENCYLEVELVALUES_TWO = RH0;
  rz0.DBCASSANDRACONSISTENCYLEVELVALUES_THREE = OH0;
  rz0.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE = TH0;
  rz0.DBCASSANDRACONSISTENCYLEVELVALUES_ANY = PH0;
  rz0.DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL = SH0;
  rz0.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL = _H0;
  rz0.DbCassandraConsistencyLevelValues = qX.createConstMap([NH0, $H0, qH0, MH0, LH0, RH0, OH0, TH0, PH0, SH0, _H0]);
  var jH0 = "datasource",
    yH0 = "http",
    kH0 = "pubsub",
    xH0 = "timer",
    fH0 = "other";
  rz0.FAASTRIGGERVALUES_DATASOURCE = jH0;
  rz0.FAASTRIGGERVALUES_HTTP = yH0;
  rz0.FAASTRIGGERVALUES_PUBSUB = kH0;
  rz0.FAASTRIGGERVALUES_TIMER = xH0;
  rz0.FAASTRIGGERVALUES_OTHER = fH0;
  rz0.FaasTriggerValues = qX.createConstMap([jH0, yH0, kH0, xH0, fH0]);
  var vH0 = "insert",
    bH0 = "edit",
    gH0 = "delete";
  rz0.FAASDOCUMENTOPERATIONVALUES_INSERT = vH0;
  rz0.FAASDOCUMENTOPERATIONVALUES_EDIT = bH0;
  rz0.FAASDOCUMENTOPERATIONVALUES_DELETE = gH0;
  rz0.FaasDocumentOperationValues = qX.createConstMap([vH0, bH0, gH0]);
  var hH0 = "alibaba_cloud",
    mH0 = "aws",
    dH0 = "azure",
    uH0 = "gcp";
  rz0.FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD = hH0;
  rz0.FAASINVOKEDPROVIDERVALUES_AWS = mH0;
  rz0.FAASINVOKEDPROVIDERVALUES_AZURE = dH0;
  rz0.FAASINVOKEDPROVIDERVALUES_GCP = uH0;
  rz0.FaasInvokedProviderValues = qX.createConstMap([hH0, mH0, dH0, uH0]);
  var pH0 = "ip_tcp",
    cH0 = "ip_udp",
    lH0 = "ip",
    iH0 = "unix",
    nH0 = "pipe",
    aH0 = "inproc",
    sH0 = "other";
  rz0.NETTRANSPORTVALUES_IP_TCP = pH0;
  rz0.NETTRANSPORTVALUES_IP_UDP = cH0;
  rz0.NETTRANSPORTVALUES_IP = lH0;
  rz0.NETTRANSPORTVALUES_UNIX = iH0;
  rz0.NETTRANSPORTVALUES_PIPE = nH0;
  rz0.NETTRANSPORTVALUES_INPROC = aH0;
  rz0.NETTRANSPORTVALUES_OTHER = sH0;
  rz0.NetTransportValues = qX.createConstMap([pH0, cH0, lH0, iH0, nH0, aH0, sH0]);
  var rH0 = "wifi",
    oH0 = "wired",
    tH0 = "cell",
    eH0 = "unavailable",
    Az0 = "unknown";
  rz0.NETHOSTCONNECTIONTYPEVALUES_WIFI = rH0;
  rz0.NETHOSTCONNECTIONTYPEVALUES_WIRED = oH0;
  rz0.NETHOSTCONNECTIONTYPEVALUES_CELL = tH0;
  rz0.NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE = eH0;
  rz0.NETHOSTCONNECTIONTYPEVALUES_UNKNOWN = Az0;
  rz0.NetHostConnectionTypeValues = qX.createConstMap([rH0, oH0, tH0, eH0, Az0]);
  var Bz0 = "gprs",
    Qz0 = "edge",
    Iz0 = "umts",
    Gz0 = "cdma",
    Zz0 = "evdo_0",
    Dz0 = "evdo_a",
    Yz0 = "cdma2000_1xrtt",
    Wz0 = "hsdpa",
    Jz0 = "hsupa",
    Fz0 = "hspa",
    Xz0 = "iden",
    Vz0 = "evdo_b",
    Cz0 = "lte",
    Kz0 = "ehrpd",
    Hz0 = "hspap",
    zz0 = "gsm",
    wz0 = "td_scdma",
    Ez0 = "iwlan",
    Uz0 = "nr",
    Nz0 = "nrnsa",
    $z0 = "lte_ca";
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_GPRS = Bz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_EDGE = Qz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_UMTS = Iz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_CDMA = Gz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0 = Zz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A = Dz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT = Yz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA = Wz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA = Jz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_HSPA = Fz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_IDEN = Xz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B = Vz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_LTE = Cz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD = Kz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP = Hz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_GSM = zz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA = wz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN = Ez0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_NR = Uz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA = Nz0;
  rz0.NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA = $z0;
  rz0.NetHostConnectionSubtypeValues = qX.createConstMap([Bz0, Qz0, Iz0, Gz0, Zz0, Dz0, Yz0, Wz0, Jz0, Fz0, Xz0, Vz0, Cz0, Kz0, Hz0, zz0, wz0, Ez0, Uz0, Nz0, $z0]);
  var qz0 = "1.0",
    Mz0 = "1.1",
    Lz0 = "2.0",
    Rz0 = "SPDY",
    Oz0 = "QUIC";
  rz0.HTTPFLAVORVALUES_HTTP_1_0 = qz0;
  rz0.HTTPFLAVORVALUES_HTTP_1_1 = Mz0;
  rz0.HTTPFLAVORVALUES_HTTP_2_0 = Lz0;
  rz0.HTTPFLAVORVALUES_SPDY = Rz0;
  rz0.HTTPFLAVORVALUES_QUIC = Oz0;
  rz0.HttpFlavorValues = {
    HTTP_1_0: qz0,
    HTTP_1_1: Mz0,
    HTTP_2_0: Lz0,
    SPDY: Rz0,
    QUIC: Oz0
  };
  var Tz0 = "queue",
    Pz0 = "topic";
  rz0.MESSAGINGDESTINATIONKINDVALUES_QUEUE = Tz0;
  rz0.MESSAGINGDESTINATIONKINDVALUES_TOPIC = Pz0;
  rz0.MessagingDestinationKindValues = qX.createConstMap([Tz0, Pz0]);
  var Sz0 = "receive",
    _z0 = "process";
  rz0.MESSAGINGOPERATIONVALUES_RECEIVE = Sz0;
  rz0.MESSAGINGOPERATIONVALUES_PROCESS = _z0;
  rz0.MessagingOperationValues = qX.createConstMap([Sz0, _z0]);
  var jz0 = 0,
    yz0 = 1,
    kz0 = 2,
    xz0 = 3,
    fz0 = 4,
    vz0 = 5,
    bz0 = 6,
    gz0 = 7,
    hz0 = 8,
    mz0 = 9,
    dz0 = 10,
    uz0 = 11,
    pz0 = 12,
    cz0 = 13,
    lz0 = 14,
    iz0 = 15,
    nz0 = 16;
  rz0.RPCGRPCSTATUSCODEVALUES_OK = jz0;
  rz0.RPCGRPCSTATUSCODEVALUES_CANCELLED = yz0;
  rz0.RPCGRPCSTATUSCODEVALUES_UNKNOWN = kz0;
  rz0.RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT = xz0;
  rz0.RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED = fz0;
  rz0.RPCGRPCSTATUSCODEVALUES_NOT_FOUND = vz0;
  rz0.RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS = bz0;
  rz0.RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED = gz0;
  rz0.RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED = hz0;
  rz0.RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION = mz0;
  rz0.RPCGRPCSTATUSCODEVALUES_ABORTED = dz0;
  rz0.RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE = uz0;
  rz0.RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED = pz0;
  rz0.RPCGRPCSTATUSCODEVALUES_INTERNAL = cz0;
  rz0.RPCGRPCSTATUSCODEVALUES_UNAVAILABLE = lz0;
  rz0.RPCGRPCSTATUSCODEVALUES_DATA_LOSS = iz0;
  rz0.RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED = nz0;
  rz0.RpcGrpcStatusCodeValues = {
    OK: jz0,
    CANCELLED: yz0,
    UNKNOWN: kz0,
    INVALID_ARGUMENT: xz0,
    DEADLINE_EXCEEDED: fz0,
    NOT_FOUND: vz0,
    ALREADY_EXISTS: bz0,
    PERMISSION_DENIED: gz0,
    RESOURCE_EXHAUSTED: hz0,
    FAILED_PRECONDITION: mz0,
    ABORTED: dz0,
    OUT_OF_RANGE: uz0,
    UNIMPLEMENTED: pz0,
    INTERNAL: cz0,
    UNAVAILABLE: lz0,
    DATA_LOSS: iz0,
    UNAUTHENTICATED: nz0
  };
  var az0 = "SENT",
    sz0 = "RECEIVED";
  rz0.MESSAGETYPEVALUES_SENT = az0;
  rz0.MESSAGETYPEVALUES_RECEIVED = sz0;
  rz0.MessageTypeValues = qX.createConstMap([az0, sz0])
})
// @from(Start 4133399, End 4134134)
Gw0 = z((S_) => {
  var Li4 = S_ && S_.__createBinding || (Object.create ? function(A, B, Q, I) {
      if (I === void 0) I = Q;
      var G = Object.getOwnPropertyDescriptor(B, Q);
      if (!G || ("get" in G ? !B.__esModule : G.writable || G.configurable)) G = {
        enumerable: !0,
        get: function() {
          return B[Q]
        }
      };
      Object.defineProperty(A, I, G)
    } : function(A, B, Q, I) {
      if (I === void 0) I = Q;
      A[I] = B[Q]
    }),
    Ri4 = S_ && S_.__exportStar || function(A, B) {
      for (var Q in A)
        if (Q !== "default" && !Object.prototype.hasOwnProperty.call(B, Q)) Li4(B, A, Q)
    };
  Object.defineProperty(S_, "__esModule", {
    value: !0
  });
  Ri4(Iw0(), S_)
})
// @from(Start 4134140, End 4149211)
kU0 = z((SU0) => {
  Object.defineProperty(SU0, "__esModule", {
    value: !0
  });
  SU0.SEMRESATTRS_K8S_STATEFULSET_NAME = SU0.SEMRESATTRS_K8S_STATEFULSET_UID = SU0.SEMRESATTRS_K8S_DEPLOYMENT_NAME = SU0.SEMRESATTRS_K8S_DEPLOYMENT_UID = SU0.SEMRESATTRS_K8S_REPLICASET_NAME = SU0.SEMRESATTRS_K8S_REPLICASET_UID = SU0.SEMRESATTRS_K8S_CONTAINER_NAME = SU0.SEMRESATTRS_K8S_POD_NAME = SU0.SEMRESATTRS_K8S_POD_UID = SU0.SEMRESATTRS_K8S_NAMESPACE_NAME = SU0.SEMRESATTRS_K8S_NODE_UID = SU0.SEMRESATTRS_K8S_NODE_NAME = SU0.SEMRESATTRS_K8S_CLUSTER_NAME = SU0.SEMRESATTRS_HOST_IMAGE_VERSION = SU0.SEMRESATTRS_HOST_IMAGE_ID = SU0.SEMRESATTRS_HOST_IMAGE_NAME = SU0.SEMRESATTRS_HOST_ARCH = SU0.SEMRESATTRS_HOST_TYPE = SU0.SEMRESATTRS_HOST_NAME = SU0.SEMRESATTRS_HOST_ID = SU0.SEMRESATTRS_FAAS_MAX_MEMORY = SU0.SEMRESATTRS_FAAS_INSTANCE = SU0.SEMRESATTRS_FAAS_VERSION = SU0.SEMRESATTRS_FAAS_ID = SU0.SEMRESATTRS_FAAS_NAME = SU0.SEMRESATTRS_DEVICE_MODEL_NAME = SU0.SEMRESATTRS_DEVICE_MODEL_IDENTIFIER = SU0.SEMRESATTRS_DEVICE_ID = SU0.SEMRESATTRS_DEPLOYMENT_ENVIRONMENT = SU0.SEMRESATTRS_CONTAINER_IMAGE_TAG = SU0.SEMRESATTRS_CONTAINER_IMAGE_NAME = SU0.SEMRESATTRS_CONTAINER_RUNTIME = SU0.SEMRESATTRS_CONTAINER_ID = SU0.SEMRESATTRS_CONTAINER_NAME = SU0.SEMRESATTRS_AWS_LOG_STREAM_ARNS = SU0.SEMRESATTRS_AWS_LOG_STREAM_NAMES = SU0.SEMRESATTRS_AWS_LOG_GROUP_ARNS = SU0.SEMRESATTRS_AWS_LOG_GROUP_NAMES = SU0.SEMRESATTRS_AWS_EKS_CLUSTER_ARN = SU0.SEMRESATTRS_AWS_ECS_TASK_REVISION = SU0.SEMRESATTRS_AWS_ECS_TASK_FAMILY = SU0.SEMRESATTRS_AWS_ECS_TASK_ARN = SU0.SEMRESATTRS_AWS_ECS_LAUNCHTYPE = SU0.SEMRESATTRS_AWS_ECS_CLUSTER_ARN = SU0.SEMRESATTRS_AWS_ECS_CONTAINER_ARN = SU0.SEMRESATTRS_CLOUD_PLATFORM = SU0.SEMRESATTRS_CLOUD_AVAILABILITY_ZONE = SU0.SEMRESATTRS_CLOUD_REGION = SU0.SEMRESATTRS_CLOUD_ACCOUNT_ID = SU0.SEMRESATTRS_CLOUD_PROVIDER = void 0;
  SU0.CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE = SU0.CLOUDPLATFORMVALUES_AZURE_APP_SERVICE = SU0.CLOUDPLATFORMVALUES_AZURE_FUNCTIONS = SU0.CLOUDPLATFORMVALUES_AZURE_AKS = SU0.CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES = SU0.CLOUDPLATFORMVALUES_AZURE_VM = SU0.CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK = SU0.CLOUDPLATFORMVALUES_AWS_LAMBDA = SU0.CLOUDPLATFORMVALUES_AWS_EKS = SU0.CLOUDPLATFORMVALUES_AWS_ECS = SU0.CLOUDPLATFORMVALUES_AWS_EC2 = SU0.CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC = SU0.CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS = SU0.CloudProviderValues = SU0.CLOUDPROVIDERVALUES_GCP = SU0.CLOUDPROVIDERVALUES_AZURE = SU0.CLOUDPROVIDERVALUES_AWS = SU0.CLOUDPROVIDERVALUES_ALIBABA_CLOUD = SU0.SemanticResourceAttributes = SU0.SEMRESATTRS_WEBENGINE_DESCRIPTION = SU0.SEMRESATTRS_WEBENGINE_VERSION = SU0.SEMRESATTRS_WEBENGINE_NAME = SU0.SEMRESATTRS_TELEMETRY_AUTO_VERSION = SU0.SEMRESATTRS_TELEMETRY_SDK_VERSION = SU0.SEMRESATTRS_TELEMETRY_SDK_LANGUAGE = SU0.SEMRESATTRS_TELEMETRY_SDK_NAME = SU0.SEMRESATTRS_SERVICE_VERSION = SU0.SEMRESATTRS_SERVICE_INSTANCE_ID = SU0.SEMRESATTRS_SERVICE_NAMESPACE = SU0.SEMRESATTRS_SERVICE_NAME = SU0.SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION = SU0.SEMRESATTRS_PROCESS_RUNTIME_VERSION = SU0.SEMRESATTRS_PROCESS_RUNTIME_NAME = SU0.SEMRESATTRS_PROCESS_OWNER = SU0.SEMRESATTRS_PROCESS_COMMAND_ARGS = SU0.SEMRESATTRS_PROCESS_COMMAND_LINE = SU0.SEMRESATTRS_PROCESS_COMMAND = SU0.SEMRESATTRS_PROCESS_EXECUTABLE_PATH = SU0.SEMRESATTRS_PROCESS_EXECUTABLE_NAME = SU0.SEMRESATTRS_PROCESS_PID = SU0.SEMRESATTRS_OS_VERSION = SU0.SEMRESATTRS_OS_NAME = SU0.SEMRESATTRS_OS_DESCRIPTION = SU0.SEMRESATTRS_OS_TYPE = SU0.SEMRESATTRS_K8S_CRONJOB_NAME = SU0.SEMRESATTRS_K8S_CRONJOB_UID = SU0.SEMRESATTRS_K8S_JOB_NAME = SU0.SEMRESATTRS_K8S_JOB_UID = SU0.SEMRESATTRS_K8S_DAEMONSET_NAME = SU0.SEMRESATTRS_K8S_DAEMONSET_UID = void 0;
  SU0.TelemetrySdkLanguageValues = SU0.TELEMETRYSDKLANGUAGEVALUES_WEBJS = SU0.TELEMETRYSDKLANGUAGEVALUES_RUBY = SU0.TELEMETRYSDKLANGUAGEVALUES_PYTHON = SU0.TELEMETRYSDKLANGUAGEVALUES_PHP = SU0.TELEMETRYSDKLANGUAGEVALUES_NODEJS = SU0.TELEMETRYSDKLANGUAGEVALUES_JAVA = SU0.TELEMETRYSDKLANGUAGEVALUES_GO = SU0.TELEMETRYSDKLANGUAGEVALUES_ERLANG = SU0.TELEMETRYSDKLANGUAGEVALUES_DOTNET = SU0.TELEMETRYSDKLANGUAGEVALUES_CPP = SU0.OsTypeValues = SU0.OSTYPEVALUES_Z_OS = SU0.OSTYPEVALUES_SOLARIS = SU0.OSTYPEVALUES_AIX = SU0.OSTYPEVALUES_HPUX = SU0.OSTYPEVALUES_DRAGONFLYBSD = SU0.OSTYPEVALUES_OPENBSD = SU0.OSTYPEVALUES_NETBSD = SU0.OSTYPEVALUES_FREEBSD = SU0.OSTYPEVALUES_DARWIN = SU0.OSTYPEVALUES_LINUX = SU0.OSTYPEVALUES_WINDOWS = SU0.HostArchValues = SU0.HOSTARCHVALUES_X86 = SU0.HOSTARCHVALUES_PPC64 = SU0.HOSTARCHVALUES_PPC32 = SU0.HOSTARCHVALUES_IA64 = SU0.HOSTARCHVALUES_ARM64 = SU0.HOSTARCHVALUES_ARM32 = SU0.HOSTARCHVALUES_AMD64 = SU0.AwsEcsLaunchtypeValues = SU0.AWSECSLAUNCHTYPEVALUES_FARGATE = SU0.AWSECSLAUNCHTYPEVALUES_EC2 = SU0.CloudPlatformValues = SU0.CLOUDPLATFORMVALUES_GCP_APP_ENGINE = SU0.CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS = SU0.CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE = SU0.CLOUDPLATFORMVALUES_GCP_CLOUD_RUN = void 0;
  var __ = _b1(),
    Zw0 = "cloud.provider",
    Dw0 = "cloud.account.id",
    Yw0 = "cloud.region",
    Ww0 = "cloud.availability_zone",
    Jw0 = "cloud.platform",
    Fw0 = "aws.ecs.container.arn",
    Xw0 = "aws.ecs.cluster.arn",
    Vw0 = "aws.ecs.launchtype",
    Cw0 = "aws.ecs.task.arn",
    Kw0 = "aws.ecs.task.family",
    Hw0 = "aws.ecs.task.revision",
    zw0 = "aws.eks.cluster.arn",
    ww0 = "aws.log.group.names",
    Ew0 = "aws.log.group.arns",
    Uw0 = "aws.log.stream.names",
    Nw0 = "aws.log.stream.arns",
    $w0 = "container.name",
    qw0 = "container.id",
    Mw0 = "container.runtime",
    Lw0 = "container.image.name",
    Rw0 = "container.image.tag",
    Ow0 = "deployment.environment",
    Tw0 = "device.id",
    Pw0 = "device.model.identifier",
    Sw0 = "device.model.name",
    _w0 = "faas.name",
    jw0 = "faas.id",
    yw0 = "faas.version",
    kw0 = "faas.instance",
    xw0 = "faas.max_memory",
    fw0 = "host.id",
    vw0 = "host.name",
    bw0 = "host.type",
    gw0 = "host.arch",
    hw0 = "host.image.name",
    mw0 = "host.image.id",
    dw0 = "host.image.version",
    uw0 = "k8s.cluster.name",
    pw0 = "k8s.node.name",
    cw0 = "k8s.node.uid",
    lw0 = "k8s.namespace.name",
    iw0 = "k8s.pod.uid",
    nw0 = "k8s.pod.name",
    aw0 = "k8s.container.name",
    sw0 = "k8s.replicaset.uid",
    rw0 = "k8s.replicaset.name",
    ow0 = "k8s.deployment.uid",
    tw0 = "k8s.deployment.name",
    ew0 = "k8s.statefulset.uid",
    AE0 = "k8s.statefulset.name",
    BE0 = "k8s.daemonset.uid",
    QE0 = "k8s.daemonset.name",
    IE0 = "k8s.job.uid",
    GE0 = "k8s.job.name",
    ZE0 = "k8s.cronjob.uid",
    DE0 = "k8s.cronjob.name",
    YE0 = "os.type",
    WE0 = "os.description",
    JE0 = "os.name",
    FE0 = "os.version",
    XE0 = "process.pid",
    VE0 = "process.executable.name",
    CE0 = "process.executable.path",
    KE0 = "process.command",
    HE0 = "process.command_line",
    zE0 = "process.command_args",
    wE0 = "process.owner",
    EE0 = "process.runtime.name",
    UE0 = "process.runtime.version",
    NE0 = "process.runtime.description",
    $E0 = "service.name",
    qE0 = "service.namespace",
    ME0 = "service.instance.id",
    LE0 = "service.version",
    RE0 = "telemetry.sdk.name",
    OE0 = "telemetry.sdk.language",
    TE0 = "telemetry.sdk.version",
    PE0 = "telemetry.auto.version",
    SE0 = "webengine.name",
    _E0 = "webengine.version",
    jE0 = "webengine.description";
  SU0.SEMRESATTRS_CLOUD_PROVIDER = Zw0;
  SU0.SEMRESATTRS_CLOUD_ACCOUNT_ID = Dw0;
  SU0.SEMRESATTRS_CLOUD_REGION = Yw0;
  SU0.SEMRESATTRS_CLOUD_AVAILABILITY_ZONE = Ww0;
  SU0.SEMRESATTRS_CLOUD_PLATFORM = Jw0;
  SU0.SEMRESATTRS_AWS_ECS_CONTAINER_ARN = Fw0;
  SU0.SEMRESATTRS_AWS_ECS_CLUSTER_ARN = Xw0;
  SU0.SEMRESATTRS_AWS_ECS_LAUNCHTYPE = Vw0;
  SU0.SEMRESATTRS_AWS_ECS_TASK_ARN = Cw0;
  SU0.SEMRESATTRS_AWS_ECS_TASK_FAMILY = Kw0;
  SU0.SEMRESATTRS_AWS_ECS_TASK_REVISION = Hw0;
  SU0.SEMRESATTRS_AWS_EKS_CLUSTER_ARN = zw0;
  SU0.SEMRESATTRS_AWS_LOG_GROUP_NAMES = ww0;
  SU0.SEMRESATTRS_AWS_LOG_GROUP_ARNS = Ew0;
  SU0.SEMRESATTRS_AWS_LOG_STREAM_NAMES = Uw0;
  SU0.SEMRESATTRS_AWS_LOG_STREAM_ARNS = Nw0;
  SU0.SEMRESATTRS_CONTAINER_NAME = $w0;
  SU0.SEMRESATTRS_CONTAINER_ID = qw0;
  SU0.SEMRESATTRS_CONTAINER_RUNTIME = Mw0;
  SU0.SEMRESATTRS_CONTAINER_IMAGE_NAME = Lw0;
  SU0.SEMRESATTRS_CONTAINER_IMAGE_TAG = Rw0;
  SU0.SEMRESATTRS_DEPLOYMENT_ENVIRONMENT = Ow0;
  SU0.SEMRESATTRS_DEVICE_ID = Tw0;
  SU0.SEMRESATTRS_DEVICE_MODEL_IDENTIFIER = Pw0;
  SU0.SEMRESATTRS_DEVICE_MODEL_NAME = Sw0;
  SU0.SEMRESATTRS_FAAS_NAME = _w0;
  SU0.SEMRESATTRS_FAAS_ID = jw0;
  SU0.SEMRESATTRS_FAAS_VERSION = yw0;
  SU0.SEMRESATTRS_FAAS_INSTANCE = kw0;
  SU0.SEMRESATTRS_FAAS_MAX_MEMORY = xw0;
  SU0.SEMRESATTRS_HOST_ID = fw0;
  SU0.SEMRESATTRS_HOST_NAME = vw0;
  SU0.SEMRESATTRS_HOST_TYPE = bw0;
  SU0.SEMRESATTRS_HOST_ARCH = gw0;
  SU0.SEMRESATTRS_HOST_IMAGE_NAME = hw0;
  SU0.SEMRESATTRS_HOST_IMAGE_ID = mw0;
  SU0.SEMRESATTRS_HOST_IMAGE_VERSION = dw0;
  SU0.SEMRESATTRS_K8S_CLUSTER_NAME = uw0;
  SU0.SEMRESATTRS_K8S_NODE_NAME = pw0;
  SU0.SEMRESATTRS_K8S_NODE_UID = cw0;
  SU0.SEMRESATTRS_K8S_NAMESPACE_NAME = lw0;
  SU0.SEMRESATTRS_K8S_POD_UID = iw0;
  SU0.SEMRESATTRS_K8S_POD_NAME = nw0;
  SU0.SEMRESATTRS_K8S_CONTAINER_NAME = aw0;
  SU0.SEMRESATTRS_K8S_REPLICASET_UID = sw0;
  SU0.SEMRESATTRS_K8S_REPLICASET_NAME = rw0;
  SU0.SEMRESATTRS_K8S_DEPLOYMENT_UID = ow0;
  SU0.SEMRESATTRS_K8S_DEPLOYMENT_NAME = tw0;
  SU0.SEMRESATTRS_K8S_STATEFULSET_UID = ew0;
  SU0.SEMRESATTRS_K8S_STATEFULSET_NAME = AE0;
  SU0.SEMRESATTRS_K8S_DAEMONSET_UID = BE0;
  SU0.SEMRESATTRS_K8S_DAEMONSET_NAME = QE0;
  SU0.SEMRESATTRS_K8S_JOB_UID = IE0;
  SU0.SEMRESATTRS_K8S_JOB_NAME = GE0;
  SU0.SEMRESATTRS_K8S_CRONJOB_UID = ZE0;
  SU0.SEMRESATTRS_K8S_CRONJOB_NAME = DE0;
  SU0.SEMRESATTRS_OS_TYPE = YE0;
  SU0.SEMRESATTRS_OS_DESCRIPTION = WE0;
  SU0.SEMRESATTRS_OS_NAME = JE0;
  SU0.SEMRESATTRS_OS_VERSION = FE0;
  SU0.SEMRESATTRS_PROCESS_PID = XE0;
  SU0.SEMRESATTRS_PROCESS_EXECUTABLE_NAME = VE0;
  SU0.SEMRESATTRS_PROCESS_EXECUTABLE_PATH = CE0;
  SU0.SEMRESATTRS_PROCESS_COMMAND = KE0;
  SU0.SEMRESATTRS_PROCESS_COMMAND_LINE = HE0;
  SU0.SEMRESATTRS_PROCESS_COMMAND_ARGS = zE0;
  SU0.SEMRESATTRS_PROCESS_OWNER = wE0;
  SU0.SEMRESATTRS_PROCESS_RUNTIME_NAME = EE0;
  SU0.SEMRESATTRS_PROCESS_RUNTIME_VERSION = UE0;
  SU0.SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION = NE0;
  SU0.SEMRESATTRS_SERVICE_NAME = $E0;
  SU0.SEMRESATTRS_SERVICE_NAMESPACE = qE0;
  SU0.SEMRESATTRS_SERVICE_INSTANCE_ID = ME0;
  SU0.SEMRESATTRS_SERVICE_VERSION = LE0;
  SU0.SEMRESATTRS_TELEMETRY_SDK_NAME = RE0;
  SU0.SEMRESATTRS_TELEMETRY_SDK_LANGUAGE = OE0;
  SU0.SEMRESATTRS_TELEMETRY_SDK_VERSION = TE0;
  SU0.SEMRESATTRS_TELEMETRY_AUTO_VERSION = PE0;
  SU0.SEMRESATTRS_WEBENGINE_NAME = SE0;
  SU0.SEMRESATTRS_WEBENGINE_VERSION = _E0;
  SU0.SEMRESATTRS_WEBENGINE_DESCRIPTION = jE0;
  SU0.SemanticResourceAttributes = __.createConstMap([Zw0, Dw0, Yw0, Ww0, Jw0, Fw0, Xw0, Vw0, Cw0, Kw0, Hw0, zw0, ww0, Ew0, Uw0, Nw0, $w0, qw0, Mw0, Lw0, Rw0, Ow0, Tw0, Pw0, Sw0, _w0, jw0, yw0, kw0, xw0, fw0, vw0, bw0, gw0, hw0, mw0, dw0, uw0, pw0, cw0, lw0, iw0, nw0, aw0, sw0, rw0, ow0, tw0, ew0, AE0, BE0, QE0, IE0, GE0, ZE0, DE0, YE0, WE0, JE0, FE0, XE0, VE0, CE0, KE0, HE0, zE0, wE0, EE0, UE0, NE0, $E0, qE0, ME0, LE0, RE0, OE0, TE0, PE0, SE0, _E0, jE0]);
  var yE0 = "alibaba_cloud",
    kE0 = "aws",
    xE0 = "azure",
    fE0 = "gcp";
  SU0.CLOUDPROVIDERVALUES_ALIBABA_CLOUD = yE0;
  SU0.CLOUDPROVIDERVALUES_AWS = kE0;
  SU0.CLOUDPROVIDERVALUES_AZURE = xE0;
  SU0.CLOUDPROVIDERVALUES_GCP = fE0;
  SU0.CloudProviderValues = __.createConstMap([yE0, kE0, xE0, fE0]);
  var vE0 = "alibaba_cloud_ecs",
    bE0 = "alibaba_cloud_fc",
    gE0 = "aws_ec2",
    hE0 = "aws_ecs",
    mE0 = "aws_eks",
    dE0 = "aws_lambda",
    uE0 = "aws_elastic_beanstalk",
    pE0 = "azure_vm",
    cE0 = "azure_container_instances",
    lE0 = "azure_aks",
    iE0 = "azure_functions",
    nE0 = "azure_app_service",
    aE0 = "gcp_compute_engine",
    sE0 = "gcp_cloud_run",
    rE0 = "gcp_kubernetes_engine",
    oE0 = "gcp_cloud_functions",
    tE0 = "gcp_app_engine";
  SU0.CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS = vE0;
  SU0.CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC = bE0;
  SU0.CLOUDPLATFORMVALUES_AWS_EC2 = gE0;
  SU0.CLOUDPLATFORMVALUES_AWS_ECS = hE0;
  SU0.CLOUDPLATFORMVALUES_AWS_EKS = mE0;
  SU0.CLOUDPLATFORMVALUES_AWS_LAMBDA = dE0;
  SU0.CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK = uE0;
  SU0.CLOUDPLATFORMVALUES_AZURE_VM = pE0;
  SU0.CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES = cE0;
  SU0.CLOUDPLATFORMVALUES_AZURE_AKS = lE0;
  SU0.CLOUDPLATFORMVALUES_AZURE_FUNCTIONS = iE0;
  SU0.CLOUDPLATFORMVALUES_AZURE_APP_SERVICE = nE0;
  SU0.CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE = aE0;
  SU0.CLOUDPLATFORMVALUES_GCP_CLOUD_RUN = sE0;
  SU0.CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE = rE0;
  SU0.CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS = oE0;
  SU0.CLOUDPLATFORMVALUES_GCP_APP_ENGINE = tE0;
  SU0.CloudPlatformValues = __.createConstMap([vE0, bE0, gE0, hE0, mE0, dE0, uE0, pE0, cE0, lE0, iE0, nE0, aE0, sE0, rE0, oE0, tE0]);
  var eE0 = "ec2",
    AU0 = "fargate";
  SU0.AWSECSLAUNCHTYPEVALUES_EC2 = eE0;
  SU0.AWSECSLAUNCHTYPEVALUES_FARGATE = AU0;
  SU0.AwsEcsLaunchtypeValues = __.createConstMap([eE0, AU0]);
  var BU0 = "amd64",
    QU0 = "arm32",
    IU0 = "arm64",
    GU0 = "ia64",
    ZU0 = "ppc32",
    DU0 = "ppc64",
    YU0 = "x86";
  SU0.HOSTARCHVALUES_AMD64 = BU0;
  SU0.HOSTARCHVALUES_ARM32 = QU0;
  SU0.HOSTARCHVALUES_ARM64 = IU0;
  SU0.HOSTARCHVALUES_IA64 = GU0;
  SU0.HOSTARCHVALUES_PPC32 = ZU0;
  SU0.HOSTARCHVALUES_PPC64 = DU0;
  SU0.HOSTARCHVALUES_X86 = YU0;
  SU0.HostArchValues = __.createConstMap([BU0, QU0, IU0, GU0, ZU0, DU0, YU0]);
  var WU0 = "windows",
    JU0 = "linux",
    FU0 = "darwin",
    XU0 = "freebsd",
    VU0 = "netbsd",
    CU0 = "openbsd",
    KU0 = "dragonflybsd",
    HU0 = "hpux",
    zU0 = "aix",
    wU0 = "solaris",
    EU0 = "z_os";
  SU0.OSTYPEVALUES_WINDOWS = WU0;
  SU0.OSTYPEVALUES_LINUX = JU0;
  SU0.OSTYPEVALUES_DARWIN = FU0;
  SU0.OSTYPEVALUES_FREEBSD = XU0;
  SU0.OSTYPEVALUES_NETBSD = VU0;
  SU0.OSTYPEVALUES_OPENBSD = CU0;
  SU0.OSTYPEVALUES_DRAGONFLYBSD = KU0;
  SU0.OSTYPEVALUES_HPUX = HU0;
  SU0.OSTYPEVALUES_AIX = zU0;
  SU0.OSTYPEVALUES_SOLARIS = wU0;
  SU0.OSTYPEVALUES_Z_OS = EU0;
  SU0.OsTypeValues = __.createConstMap([WU0, JU0, FU0, XU0, VU0, CU0, KU0, HU0, zU0, wU0, EU0]);
  var UU0 = "cpp",
    NU0 = "dotnet",
    $U0 = "erlang",
    qU0 = "go",
    MU0 = "java",
    LU0 = "nodejs",
    RU0 = "php",
    OU0 = "python",
    TU0 = "ruby",
    PU0 = "webjs";
  SU0.TELEMETRYSDKLANGUAGEVALUES_CPP = UU0;
  SU0.TELEMETRYSDKLANGUAGEVALUES_DOTNET = NU0;
  SU0.TELEMETRYSDKLANGUAGEVALUES_ERLANG = $U0;
  SU0.TELEMETRYSDKLANGUAGEVALUES_GO = qU0;
  SU0.TELEMETRYSDKLANGUAGEVALUES_JAVA = MU0;
  SU0.TELEMETRYSDKLANGUAGEVALUES_NODEJS = LU0;
  SU0.TELEMETRYSDKLANGUAGEVALUES_PHP = RU0;
  SU0.TELEMETRYSDKLANGUAGEVALUES_PYTHON = OU0;
  SU0.TELEMETRYSDKLANGUAGEVALUES_RUBY = TU0;
  SU0.TELEMETRYSDKLANGUAGEVALUES_WEBJS = PU0;
  SU0.TelemetrySdkLanguageValues = __.createConstMap([UU0, NU0, $U0, qU0, MU0, LU0, RU0, OU0, TU0, PU0])
})
// @from(Start 4149217, End 4149952)
xU0 = z((j_) => {
  var As4 = j_ && j_.__createBinding || (Object.create ? function(A, B, Q, I) {
      if (I === void 0) I = Q;
      var G = Object.getOwnPropertyDescriptor(B, Q);
      if (!G || ("get" in G ? !B.__esModule : G.writable || G.configurable)) G = {
        enumerable: !0,
        get: function() {
          return B[Q]
        }
      };
      Object.defineProperty(A, I, G)
    } : function(A, B, Q, I) {
      if (I === void 0) I = Q;
      A[I] = B[Q]
    }),
    Bs4 = j_ && j_.__exportStar || function(A, B) {
      for (var Q in A)
        if (Q !== "default" && !Object.prototype.hasOwnProperty.call(B, Q)) As4(B, A, Q)
    };
  Object.defineProperty(j_, "__esModule", {
    value: !0
  });
  Bs4(kU0(), j_)
})
// @from(Start 4149958, End 4160789)
hU0 = z((fU0) => {
  Object.defineProperty(fU0, "__esModule", {
    value: !0
  });
  fU0.ATTR_JVM_GC_NAME = fU0.ATTR_JVM_GC_ACTION = fU0.ATTR_HTTP_ROUTE = fU0.ATTR_HTTP_RESPONSE_STATUS_CODE = fU0.ATTR_HTTP_RESPONSE_HEADER = fU0.ATTR_HTTP_REQUEST_RESEND_COUNT = fU0.ATTR_HTTP_REQUEST_METHOD_ORIGINAL = fU0.HTTP_REQUEST_METHOD_VALUE_TRACE = fU0.HTTP_REQUEST_METHOD_VALUE_PUT = fU0.HTTP_REQUEST_METHOD_VALUE_POST = fU0.HTTP_REQUEST_METHOD_VALUE_PATCH = fU0.HTTP_REQUEST_METHOD_VALUE_OPTIONS = fU0.HTTP_REQUEST_METHOD_VALUE_HEAD = fU0.HTTP_REQUEST_METHOD_VALUE_GET = fU0.HTTP_REQUEST_METHOD_VALUE_DELETE = fU0.HTTP_REQUEST_METHOD_VALUE_CONNECT = fU0.HTTP_REQUEST_METHOD_VALUE_OTHER = fU0.ATTR_HTTP_REQUEST_METHOD = fU0.ATTR_HTTP_REQUEST_HEADER = fU0.ATTR_EXCEPTION_TYPE = fU0.ATTR_EXCEPTION_STACKTRACE = fU0.ATTR_EXCEPTION_MESSAGE = fU0.ATTR_EXCEPTION_ESCAPED = fU0.ERROR_TYPE_VALUE_OTHER = fU0.ATTR_ERROR_TYPE = fU0.DOTNET_GC_HEAP_GENERATION_VALUE_POH = fU0.DOTNET_GC_HEAP_GENERATION_VALUE_LOH = fU0.DOTNET_GC_HEAP_GENERATION_VALUE_GEN2 = fU0.DOTNET_GC_HEAP_GENERATION_VALUE_GEN1 = fU0.DOTNET_GC_HEAP_GENERATION_VALUE_GEN0 = fU0.ATTR_DOTNET_GC_HEAP_GENERATION = fU0.ATTR_CLIENT_PORT = fU0.ATTR_CLIENT_ADDRESS = fU0.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_SUCCESS = fU0.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_FAILURE = fU0.ATTR_ASPNETCORE_ROUTING_MATCH_STATUS = fU0.ATTR_ASPNETCORE_ROUTING_IS_FALLBACK = fU0.ATTR_ASPNETCORE_REQUEST_IS_UNHANDLED = fU0.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_REQUEST_CANCELED = fU0.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_GLOBAL_LIMITER = fU0.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ENDPOINT_LIMITER = fU0.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ACQUIRED = fU0.ATTR_ASPNETCORE_RATE_LIMITING_RESULT = fU0.ATTR_ASPNETCORE_RATE_LIMITING_POLICY = fU0.ATTR_ASPNETCORE_DIAGNOSTICS_HANDLER_TYPE = fU0.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_UNHANDLED = fU0.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_SKIPPED = fU0.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_HANDLED = fU0.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_ABORTED = fU0.ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT = void 0;
  fU0.TELEMETRY_SDK_LANGUAGE_VALUE_GO = fU0.TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG = fU0.TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET = fU0.TELEMETRY_SDK_LANGUAGE_VALUE_CPP = fU0.ATTR_TELEMETRY_SDK_LANGUAGE = fU0.SIGNALR_TRANSPORT_VALUE_WEB_SOCKETS = fU0.SIGNALR_TRANSPORT_VALUE_SERVER_SENT_EVENTS = fU0.SIGNALR_TRANSPORT_VALUE_LONG_POLLING = fU0.ATTR_SIGNALR_TRANSPORT = fU0.SIGNALR_CONNECTION_STATUS_VALUE_TIMEOUT = fU0.SIGNALR_CONNECTION_STATUS_VALUE_NORMAL_CLOSURE = fU0.SIGNALR_CONNECTION_STATUS_VALUE_APP_SHUTDOWN = fU0.ATTR_SIGNALR_CONNECTION_STATUS = fU0.ATTR_SERVICE_VERSION = fU0.ATTR_SERVICE_NAME = fU0.ATTR_SERVER_PORT = fU0.ATTR_SERVER_ADDRESS = fU0.ATTR_OTEL_STATUS_DESCRIPTION = fU0.OTEL_STATUS_CODE_VALUE_OK = fU0.OTEL_STATUS_CODE_VALUE_ERROR = fU0.ATTR_OTEL_STATUS_CODE = fU0.ATTR_OTEL_SCOPE_VERSION = fU0.ATTR_OTEL_SCOPE_NAME = fU0.NETWORK_TYPE_VALUE_IPV6 = fU0.NETWORK_TYPE_VALUE_IPV4 = fU0.ATTR_NETWORK_TYPE = fU0.NETWORK_TRANSPORT_VALUE_UNIX = fU0.NETWORK_TRANSPORT_VALUE_UDP = fU0.NETWORK_TRANSPORT_VALUE_TCP = fU0.NETWORK_TRANSPORT_VALUE_QUIC = fU0.NETWORK_TRANSPORT_VALUE_PIPE = fU0.ATTR_NETWORK_TRANSPORT = fU0.ATTR_NETWORK_PROTOCOL_VERSION = fU0.ATTR_NETWORK_PROTOCOL_NAME = fU0.ATTR_NETWORK_PEER_PORT = fU0.ATTR_NETWORK_PEER_ADDRESS = fU0.ATTR_NETWORK_LOCAL_PORT = fU0.ATTR_NETWORK_LOCAL_ADDRESS = fU0.JVM_THREAD_STATE_VALUE_WAITING = fU0.JVM_THREAD_STATE_VALUE_TIMED_WAITING = fU0.JVM_THREAD_STATE_VALUE_TERMINATED = fU0.JVM_THREAD_STATE_VALUE_RUNNABLE = fU0.JVM_THREAD_STATE_VALUE_NEW = fU0.JVM_THREAD_STATE_VALUE_BLOCKED = fU0.ATTR_JVM_THREAD_STATE = fU0.ATTR_JVM_THREAD_DAEMON = fU0.JVM_MEMORY_TYPE_VALUE_NON_HEAP = fU0.JVM_MEMORY_TYPE_VALUE_HEAP = fU0.ATTR_JVM_MEMORY_TYPE = fU0.ATTR_JVM_MEMORY_POOL_NAME = void 0;
  fU0.ATTR_USER_AGENT_ORIGINAL = fU0.ATTR_URL_SCHEME = fU0.ATTR_URL_QUERY = fU0.ATTR_URL_PATH = fU0.ATTR_URL_FULL = fU0.ATTR_URL_FRAGMENT = fU0.ATTR_TELEMETRY_SDK_VERSION = fU0.ATTR_TELEMETRY_SDK_NAME = fU0.TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS = fU0.TELEMETRY_SDK_LANGUAGE_VALUE_SWIFT = fU0.TELEMETRY_SDK_LANGUAGE_VALUE_RUST = fU0.TELEMETRY_SDK_LANGUAGE_VALUE_RUBY = fU0.TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON = fU0.TELEMETRY_SDK_LANGUAGE_VALUE_PHP = fU0.TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS = fU0.TELEMETRY_SDK_LANGUAGE_VALUE_JAVA = void 0;
  fU0.ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT = "aspnetcore.diagnostics.exception.result";
  fU0.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_ABORTED = "aborted";
  fU0.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_HANDLED = "handled";
  fU0.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_SKIPPED = "skipped";
  fU0.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_UNHANDLED = "unhandled";
  fU0.ATTR_ASPNETCORE_DIAGNOSTICS_HANDLER_TYPE = "aspnetcore.diagnostics.handler.type";
  fU0.ATTR_ASPNETCORE_RATE_LIMITING_POLICY = "aspnetcore.rate_limiting.policy";
  fU0.ATTR_ASPNETCORE_RATE_LIMITING_RESULT = "aspnetcore.rate_limiting.result";
  fU0.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ACQUIRED = "acquired";
  fU0.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ENDPOINT_LIMITER = "endpoint_limiter";
  fU0.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_GLOBAL_LIMITER = "global_limiter";
  fU0.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_REQUEST_CANCELED = "request_canceled";
  fU0.ATTR_ASPNETCORE_REQUEST_IS_UNHANDLED = "aspnetcore.request.is_unhandled";
  fU0.ATTR_ASPNETCORE_ROUTING_IS_FALLBACK = "aspnetcore.routing.is_fallback";
  fU0.ATTR_ASPNETCORE_ROUTING_MATCH_STATUS = "aspnetcore.routing.match_status";
  fU0.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_FAILURE = "failure";
  fU0.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_SUCCESS = "success";
  fU0.ATTR_CLIENT_ADDRESS = "client.address";
  fU0.ATTR_CLIENT_PORT = "client.port";
  fU0.ATTR_DOTNET_GC_HEAP_GENERATION = "dotnet.gc.heap.generation";
  fU0.DOTNET_GC_HEAP_GENERATION_VALUE_GEN0 = "gen0";
  fU0.DOTNET_GC_HEAP_GENERATION_VALUE_GEN1 = "gen1";
  fU0.DOTNET_GC_HEAP_GENERATION_VALUE_GEN2 = "gen2";
  fU0.DOTNET_GC_HEAP_GENERATION_VALUE_LOH = "loh";
  fU0.DOTNET_GC_HEAP_GENERATION_VALUE_POH = "poh";
  fU0.ATTR_ERROR_TYPE = "error.type";
  fU0.ERROR_TYPE_VALUE_OTHER = "_OTHER";
  fU0.ATTR_EXCEPTION_ESCAPED = "exception.escaped";
  fU0.ATTR_EXCEPTION_MESSAGE = "exception.message";
  fU0.ATTR_EXCEPTION_STACKTRACE = "exception.stacktrace";
  fU0.ATTR_EXCEPTION_TYPE = "exception.type";
  var Qs4 = (A) => `http.request.header.${A}`;
  fU0.ATTR_HTTP_REQUEST_HEADER = Qs4;
  fU0.ATTR_HTTP_REQUEST_METHOD = "http.request.method";
  fU0.HTTP_REQUEST_METHOD_VALUE_OTHER = "_OTHER";
  fU0.HTTP_REQUEST_METHOD_VALUE_CONNECT = "CONNECT";
  fU0.HTTP_REQUEST_METHOD_VALUE_DELETE = "DELETE";
  fU0.HTTP_REQUEST_METHOD_VALUE_GET = "GET";
  fU0.HTTP_REQUEST_METHOD_VALUE_HEAD = "HEAD";
  fU0.HTTP_REQUEST_METHOD_VALUE_OPTIONS = "OPTIONS";
  fU0.HTTP_REQUEST_METHOD_VALUE_PATCH = "PATCH";
  fU0.HTTP_REQUEST_METHOD_VALUE_POST = "POST";
  fU0.HTTP_REQUEST_METHOD_VALUE_PUT = "PUT";
  fU0.HTTP_REQUEST_METHOD_VALUE_TRACE = "TRACE";
  fU0.ATTR_HTTP_REQUEST_METHOD_ORIGINAL = "http.request.method_original";
  fU0.ATTR_HTTP_REQUEST_RESEND_COUNT = "http.request.resend_count";
  var Is4 = (A) => `http.response.header.${A}`;
  fU0.ATTR_HTTP_RESPONSE_HEADER = Is4;
  fU0.ATTR_HTTP_RESPONSE_STATUS_CODE = "http.response.status_code";
  fU0.ATTR_HTTP_ROUTE = "http.route";
  fU0.ATTR_JVM_GC_ACTION = "jvm.gc.action";
  fU0.ATTR_JVM_GC_NAME = "jvm.gc.name";
  fU0.ATTR_JVM_MEMORY_POOL_NAME = "jvm.memory.pool.name";
  fU0.ATTR_JVM_MEMORY_TYPE = "jvm.memory.type";
  fU0.JVM_MEMORY_TYPE_VALUE_HEAP = "heap";
  fU0.JVM_MEMORY_TYPE_VALUE_NON_HEAP = "non_heap";
  fU0.ATTR_JVM_THREAD_DAEMON = "jvm.thread.daemon";
  fU0.ATTR_JVM_THREAD_STATE = "jvm.thread.state";
  fU0.JVM_THREAD_STATE_VALUE_BLOCKED = "blocked";
  fU0.JVM_THREAD_STATE_VALUE_NEW = "new";
  fU0.JVM_THREAD_STATE_VALUE_RUNNABLE = "runnable";
  fU0.JVM_THREAD_STATE_VALUE_TERMINATED = "terminated";
  fU0.JVM_THREAD_STATE_VALUE_TIMED_WAITING = "timed_waiting";
  fU0.JVM_THREAD_STATE_VALUE_WAITING = "waiting";
  fU0.ATTR_NETWORK_LOCAL_ADDRESS = "network.local.address";
  fU0.ATTR_NETWORK_LOCAL_PORT = "network.local.port";
  fU0.ATTR_NETWORK_PEER_ADDRESS = "network.peer.address";
  fU0.ATTR_NETWORK_PEER_PORT = "network.peer.port";
  fU0.ATTR_NETWORK_PROTOCOL_NAME = "network.protocol.name";
  fU0.ATTR_NETWORK_PROTOCOL_VERSION = "network.protocol.version";
  fU0.ATTR_NETWORK_TRANSPORT = "network.transport";
  fU0.NETWORK_TRANSPORT_VALUE_PIPE = "pipe";
  fU0.NETWORK_TRANSPORT_VALUE_QUIC = "quic";
  fU0.NETWORK_TRANSPORT_VALUE_TCP = "tcp";
  fU0.NETWORK_TRANSPORT_VALUE_UDP = "udp";
  fU0.NETWORK_TRANSPORT_VALUE_UNIX = "unix";
  fU0.ATTR_NETWORK_TYPE = "network.type";
  fU0.NETWORK_TYPE_VALUE_IPV4 = "ipv4";
  fU0.NETWORK_TYPE_VALUE_IPV6 = "ipv6";
  fU0.ATTR_OTEL_SCOPE_NAME = "otel.scope.name";
  fU0.ATTR_OTEL_SCOPE_VERSION = "otel.scope.version";
  fU0.ATTR_OTEL_STATUS_CODE = "otel.status_code";
  fU0.OTEL_STATUS_CODE_VALUE_ERROR = "ERROR";
  fU0.OTEL_STATUS_CODE_VALUE_OK = "OK";
  fU0.ATTR_OTEL_STATUS_DESCRIPTION = "otel.status_description";
  fU0.ATTR_SERVER_ADDRESS = "server.address";
  fU0.ATTR_SERVER_PORT = "server.port";
  fU0.ATTR_SERVICE_NAME = "service.name";
  fU0.ATTR_SERVICE_VERSION = "service.version";
  fU0.ATTR_SIGNALR_CONNECTION_STATUS = "signalr.connection.status";
  fU0.SIGNALR_CONNECTION_STATUS_VALUE_APP_SHUTDOWN = "app_shutdown";
  fU0.SIGNALR_CONNECTION_STATUS_VALUE_NORMAL_CLOSURE = "normal_closure";
  fU0.SIGNALR_CONNECTION_STATUS_VALUE_TIMEOUT = "timeout";
  fU0.ATTR_SIGNALR_TRANSPORT = "signalr.transport";
  fU0.SIGNALR_TRANSPORT_VALUE_LONG_POLLING = "long_polling";
  fU0.SIGNALR_TRANSPORT_VALUE_SERVER_SENT_EVENTS = "server_sent_events";
  fU0.SIGNALR_TRANSPORT_VALUE_WEB_SOCKETS = "web_sockets";
  fU0.ATTR_TELEMETRY_SDK_LANGUAGE = "telemetry.sdk.language";
  fU0.TELEMETRY_SDK_LANGUAGE_VALUE_CPP = "cpp";
  fU0.TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET = "dotnet";
  fU0.TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG = "erlang";
  fU0.TELEMETRY_SDK_LANGUAGE_VALUE_GO = "go";
  fU0.TELEMETRY_SDK_LANGUAGE_VALUE_JAVA = "java";
  fU0.TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS = "nodejs";
  fU0.TELEMETRY_SDK_LANGUAGE_VALUE_PHP = "php";
  fU0.TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON = "python";
  fU0.TELEMETRY_SDK_LANGUAGE_VALUE_RUBY = "ruby";
  fU0.TELEMETRY_SDK_LANGUAGE_VALUE_RUST = "rust";
  fU0.TELEMETRY_SDK_LANGUAGE_VALUE_SWIFT = "swift";
  fU0.TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS = "webjs";
  fU0.ATTR_TELEMETRY_SDK_NAME = "telemetry.sdk.name";
  fU0.ATTR_TELEMETRY_SDK_VERSION = "telemetry.sdk.version";
  fU0.ATTR_URL_FRAGMENT = "url.fragment";
  fU0.ATTR_URL_FULL = "url.full";
  fU0.ATTR_URL_PATH = "url.path";
  fU0.ATTR_URL_QUERY = "url.query";
  fU0.ATTR_URL_SCHEME = "url.scheme";
  fU0.ATTR_USER_AGENT_ORIGINAL = "user_agent.original"
})
// @from(Start 4160795, End 4166715)
uU0 = z((mU0) => {
  Object.defineProperty(mU0, "__esModule", {
    value: !0
  });
  mU0.METRIC_SIGNALR_SERVER_CONNECTION_DURATION = mU0.METRIC_SIGNALR_SERVER_ACTIVE_CONNECTIONS = mU0.METRIC_KESTREL_UPGRADED_CONNECTIONS = mU0.METRIC_KESTREL_TLS_HANDSHAKE_DURATION = mU0.METRIC_KESTREL_REJECTED_CONNECTIONS = mU0.METRIC_KESTREL_QUEUED_REQUESTS = mU0.METRIC_KESTREL_QUEUED_CONNECTIONS = mU0.METRIC_KESTREL_CONNECTION_DURATION = mU0.METRIC_KESTREL_ACTIVE_TLS_HANDSHAKES = mU0.METRIC_KESTREL_ACTIVE_CONNECTIONS = mU0.METRIC_JVM_THREAD_COUNT = mU0.METRIC_JVM_MEMORY_USED_AFTER_LAST_GC = mU0.METRIC_JVM_MEMORY_USED = mU0.METRIC_JVM_MEMORY_LIMIT = mU0.METRIC_JVM_MEMORY_COMMITTED = mU0.METRIC_JVM_GC_DURATION = mU0.METRIC_JVM_CPU_TIME = mU0.METRIC_JVM_CPU_RECENT_UTILIZATION = mU0.METRIC_JVM_CPU_COUNT = mU0.METRIC_JVM_CLASS_UNLOADED = mU0.METRIC_JVM_CLASS_LOADED = mU0.METRIC_JVM_CLASS_COUNT = mU0.METRIC_HTTP_SERVER_REQUEST_DURATION = mU0.METRIC_HTTP_CLIENT_REQUEST_DURATION = mU0.METRIC_DOTNET_TIMER_COUNT = mU0.METRIC_DOTNET_THREAD_POOL_WORK_ITEM_COUNT = mU0.METRIC_DOTNET_THREAD_POOL_THREAD_COUNT = mU0.METRIC_DOTNET_THREAD_POOL_QUEUE_LENGTH = mU0.METRIC_DOTNET_PROCESS_MEMORY_WORKING_SET = mU0.METRIC_DOTNET_PROCESS_CPU_TIME = mU0.METRIC_DOTNET_PROCESS_CPU_COUNT = mU0.METRIC_DOTNET_MONITOR_LOCK_CONTENTIONS = mU0.METRIC_DOTNET_JIT_COMPILED_METHODS = mU0.METRIC_DOTNET_JIT_COMPILED_IL_SIZE = mU0.METRIC_DOTNET_JIT_COMPILATION_TIME = mU0.METRIC_DOTNET_GC_PAUSE_TIME = mU0.METRIC_DOTNET_GC_LAST_COLLECTION_MEMORY_COMMITTED_SIZE = mU0.METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_SIZE = mU0.METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_FRAGMENTATION_SIZE = mU0.METRIC_DOTNET_GC_HEAP_TOTAL_ALLOCATED = mU0.METRIC_DOTNET_GC_COLLECTIONS = mU0.METRIC_DOTNET_EXCEPTIONS = mU0.METRIC_DOTNET_ASSEMBLY_COUNT = mU0.METRIC_ASPNETCORE_ROUTING_MATCH_ATTEMPTS = mU0.METRIC_ASPNETCORE_RATE_LIMITING_REQUESTS = mU0.METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_LEASE_DURATION = mU0.METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_TIME_IN_QUEUE = mU0.METRIC_ASPNETCORE_RATE_LIMITING_QUEUED_REQUESTS = mU0.METRIC_ASPNETCORE_RATE_LIMITING_ACTIVE_REQUEST_LEASES = mU0.METRIC_ASPNETCORE_DIAGNOSTICS_EXCEPTIONS = void 0;
  mU0.METRIC_ASPNETCORE_DIAGNOSTICS_EXCEPTIONS = "aspnetcore.diagnostics.exceptions";
  mU0.METRIC_ASPNETCORE_RATE_LIMITING_ACTIVE_REQUEST_LEASES = "aspnetcore.rate_limiting.active_request_leases";
  mU0.METRIC_ASPNETCORE_RATE_LIMITING_QUEUED_REQUESTS = "aspnetcore.rate_limiting.queued_requests";
  mU0.METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_TIME_IN_QUEUE = "aspnetcore.rate_limiting.request.time_in_queue";
  mU0.METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_LEASE_DURATION = "aspnetcore.rate_limiting.request_lease.duration";
  mU0.METRIC_ASPNETCORE_RATE_LIMITING_REQUESTS = "aspnetcore.rate_limiting.requests";
  mU0.METRIC_ASPNETCORE_ROUTING_MATCH_ATTEMPTS = "aspnetcore.routing.match_attempts";
  mU0.METRIC_DOTNET_ASSEMBLY_COUNT = "dotnet.assembly.count";
  mU0.METRIC_DOTNET_EXCEPTIONS = "dotnet.exceptions";
  mU0.METRIC_DOTNET_GC_COLLECTIONS = "dotnet.gc.collections";
  mU0.METRIC_DOTNET_GC_HEAP_TOTAL_ALLOCATED = "dotnet.gc.heap.total_allocated";
  mU0.METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_FRAGMENTATION_SIZE = "dotnet.gc.last_collection.heap.fragmentation.size";
  mU0.METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_SIZE = "dotnet.gc.last_collection.heap.size";
  mU0.METRIC_DOTNET_GC_LAST_COLLECTION_MEMORY_COMMITTED_SIZE = "dotnet.gc.last_collection.memory.committed_size";
  mU0.METRIC_DOTNET_GC_PAUSE_TIME = "dotnet.gc.pause.time";
  mU0.METRIC_DOTNET_JIT_COMPILATION_TIME = "dotnet.jit.compilation.time";
  mU0.METRIC_DOTNET_JIT_COMPILED_IL_SIZE = "dotnet.jit.compiled_il.size";
  mU0.METRIC_DOTNET_JIT_COMPILED_METHODS = "dotnet.jit.compiled_methods";
  mU0.METRIC_DOTNET_MONITOR_LOCK_CONTENTIONS = "dotnet.monitor.lock_contentions";
  mU0.METRIC_DOTNET_PROCESS_CPU_COUNT = "dotnet.process.cpu.count";
  mU0.METRIC_DOTNET_PROCESS_CPU_TIME = "dotnet.process.cpu.time";
  mU0.METRIC_DOTNET_PROCESS_MEMORY_WORKING_SET = "dotnet.process.memory.working_set";
  mU0.METRIC_DOTNET_THREAD_POOL_QUEUE_LENGTH = "dotnet.thread_pool.queue.length";
  mU0.METRIC_DOTNET_THREAD_POOL_THREAD_COUNT = "dotnet.thread_pool.thread.count";
  mU0.METRIC_DOTNET_THREAD_POOL_WORK_ITEM_COUNT = "dotnet.thread_pool.work_item.count";
  mU0.METRIC_DOTNET_TIMER_COUNT = "dotnet.timer.count";
  mU0.METRIC_HTTP_CLIENT_REQUEST_DURATION = "http.client.request.duration";
  mU0.METRIC_HTTP_SERVER_REQUEST_DURATION = "http.server.request.duration";
  mU0.METRIC_JVM_CLASS_COUNT = "jvm.class.count";
  mU0.METRIC_JVM_CLASS_LOADED = "jvm.class.loaded";
  mU0.METRIC_JVM_CLASS_UNLOADED = "jvm.class.unloaded";
  mU0.METRIC_JVM_CPU_COUNT = "jvm.cpu.count";
  mU0.METRIC_JVM_CPU_RECENT_UTILIZATION = "jvm.cpu.recent_utilization";
  mU0.METRIC_JVM_CPU_TIME = "jvm.cpu.time";
  mU0.METRIC_JVM_GC_DURATION = "jvm.gc.duration";
  mU0.METRIC_JVM_MEMORY_COMMITTED = "jvm.memory.committed";
  mU0.METRIC_JVM_MEMORY_LIMIT = "jvm.memory.limit";
  mU0.METRIC_JVM_MEMORY_USED = "jvm.memory.used";
  mU0.METRIC_JVM_MEMORY_USED_AFTER_LAST_GC = "jvm.memory.used_after_last_gc";
  mU0.METRIC_JVM_THREAD_COUNT = "jvm.thread.count";
  mU0.METRIC_KESTREL_ACTIVE_CONNECTIONS = "kestrel.active_connections";
  mU0.METRIC_KESTREL_ACTIVE_TLS_HANDSHAKES = "kestrel.active_tls_handshakes";
  mU0.METRIC_KESTREL_CONNECTION_DURATION = "kestrel.connection.duration";
  mU0.METRIC_KESTREL_QUEUED_CONNECTIONS = "kestrel.queued_connections";
  mU0.METRIC_KESTREL_QUEUED_REQUESTS = "kestrel.queued_requests";
  mU0.METRIC_KESTREL_REJECTED_CONNECTIONS = "kestrel.rejected_connections";
  mU0.METRIC_KESTREL_TLS_HANDSHAKE_DURATION = "kestrel.tls_handshake.duration";
  mU0.METRIC_KESTREL_UPGRADED_CONNECTIONS = "kestrel.upgraded_connections";
  mU0.METRIC_SIGNALR_SERVER_ACTIVE_CONNECTIONS = "signalr.server.active_connections";
  mU0.METRIC_SIGNALR_SERVER_CONNECTION_DURATION = "signalr.server.connection.duration"
})
// @from(Start 4166721, End 4167509)
LN = z((ez) => {
  var Gt4 = ez && ez.__createBinding || (Object.create ? function(A, B, Q, I) {
      if (I === void 0) I = Q;
      var G = Object.getOwnPropertyDescriptor(B, Q);
      if (!G || ("get" in G ? !B.__esModule : G.writable || G.configurable)) G = {
        enumerable: !0,
        get: function() {
          return B[Q]
        }
      };
      Object.defineProperty(A, I, G)
    } : function(A, B, Q, I) {
      if (I === void 0) I = Q;
      A[I] = B[Q]
    }),
    QZ1 = ez && ez.__exportStar || function(A, B) {
      for (var Q in A)
        if (Q !== "default" && !Object.prototype.hasOwnProperty.call(B, Q)) Gt4(B, A, Q)
    };
  Object.defineProperty(ez, "__esModule", {
    value: !0
  });
  QZ1(Gw0(), ez);
  QZ1(xU0(), ez);
  QZ1(hU0(), ez);
  QZ1(uU0(), ez)
})
// @from(Start 4167515, End 4167931)
lU0 = z((pU0) => {
  Object.defineProperty(pU0, "__esModule", {
    value: !0
  });
  pU0.SDK_INFO = void 0;
  var Zt4 = GV0(),
    Us = LN();
  pU0.SDK_INFO = {
    [Us.SEMRESATTRS_TELEMETRY_SDK_NAME]: "opentelemetry",
    [Us.SEMRESATTRS_PROCESS_RUNTIME_NAME]: "node",
    [Us.SEMRESATTRS_TELEMETRY_SDK_LANGUAGE]: Us.TELEMETRYSDKLANGUAGEVALUES_NODEJS,
    [Us.SEMRESATTRS_TELEMETRY_SDK_VERSION]: Zt4.VERSION
  }
})
// @from(Start 4167937, End 4168112)
aU0 = z((iU0) => {
  Object.defineProperty(iU0, "__esModule", {
    value: !0
  });
  iU0.unrefTimer = void 0;

  function Dt4(A) {
    A.unref()
  }
  iU0.unrefTimer = Dt4
})
// @from(Start 4168118, End 4169535)
sU0 = z((Aw) => {
  Object.defineProperty(Aw, "__esModule", {
    value: !0
  });
  Aw.unrefTimer = Aw.SDK_INFO = Aw.otperformance = Aw._globalThis = Aw.getStringListFromEnv = Aw.getNumberFromEnv = Aw.getBooleanFromEnv = Aw.getStringFromEnv = void 0;
  var IZ1 = sX0();
  Object.defineProperty(Aw, "getStringFromEnv", {
    enumerable: !0,
    get: function() {
      return IZ1.getStringFromEnv
    }
  });
  Object.defineProperty(Aw, "getBooleanFromEnv", {
    enumerable: !0,
    get: function() {
      return IZ1.getBooleanFromEnv
    }
  });
  Object.defineProperty(Aw, "getNumberFromEnv", {
    enumerable: !0,
    get: function() {
      return IZ1.getNumberFromEnv
    }
  });
  Object.defineProperty(Aw, "getStringListFromEnv", {
    enumerable: !0,
    get: function() {
      return IZ1.getStringListFromEnv
    }
  });
  var Yt4 = tX0();
  Object.defineProperty(Aw, "_globalThis", {
    enumerable: !0,
    get: function() {
      return Yt4._globalThis
    }
  });
  var Wt4 = BV0();
  Object.defineProperty(Aw, "otperformance", {
    enumerable: !0,
    get: function() {
      return Wt4.otperformance
    }
  });
  var Jt4 = lU0();
  Object.defineProperty(Aw, "SDK_INFO", {
    enumerable: !0,
    get: function() {
      return Jt4.SDK_INFO
    }
  });
  var Ft4 = aU0();
  Object.defineProperty(Aw, "unrefTimer", {
    enumerable: !0,
    get: function() {
      return Ft4.unrefTimer
    }
  })
})
// @from(Start 4169541, End 4170873)
jb1 = z((Bw) => {
  Object.defineProperty(Bw, "__esModule", {
    value: !0
  });
  Bw.getStringListFromEnv = Bw.getNumberFromEnv = Bw.getStringFromEnv = Bw.getBooleanFromEnv = Bw.unrefTimer = Bw.otperformance = Bw._globalThis = Bw.SDK_INFO = void 0;
  var eL = sU0();
  Object.defineProperty(Bw, "SDK_INFO", {
    enumerable: !0,
    get: function() {
      return eL.SDK_INFO
    }
  });
  Object.defineProperty(Bw, "_globalThis", {
    enumerable: !0,
    get: function() {
      return eL._globalThis
    }
  });
  Object.defineProperty(Bw, "otperformance", {
    enumerable: !0,
    get: function() {
      return eL.otperformance
    }
  });
  Object.defineProperty(Bw, "unrefTimer", {
    enumerable: !0,
    get: function() {
      return eL.unrefTimer
    }
  });
  Object.defineProperty(Bw, "getBooleanFromEnv", {
    enumerable: !0,
    get: function() {
      return eL.getBooleanFromEnv
    }
  });
  Object.defineProperty(Bw, "getStringFromEnv", {
    enumerable: !0,
    get: function() {
      return eL.getStringFromEnv
    }
  });
  Object.defineProperty(Bw, "getNumberFromEnv", {
    enumerable: !0,
    get: function() {
      return eL.getNumberFromEnv
    }
  });
  Object.defineProperty(Bw, "getStringListFromEnv", {
    enumerable: !0,
    get: function() {
      return eL.getStringListFromEnv
    }
  })
})
// @from(Start 4170879, End 4173223)
BN0 = z((eU0) => {
  Object.defineProperty(eU0, "__esModule", {
    value: !0
  });
  eU0.addHrTimes = eU0.isTimeInput = eU0.isTimeInputHrTime = eU0.hrTimeToMicroseconds = eU0.hrTimeToMilliseconds = eU0.hrTimeToNanoseconds = eU0.hrTimeToTimeStamp = eU0.hrTimeDuration = eU0.timeInputToHrTime = eU0.hrTime = eU0.getTimeOrigin = eU0.millisToHrTime = void 0;
  var yb1 = jb1(),
    rU0 = 9,
    Ct4 = 6,
    Kt4 = Math.pow(10, Ct4),
    GZ1 = Math.pow(10, rU0);

  function Ns(A) {
    let B = A / 1000,
      Q = Math.trunc(B),
      I = Math.round(A % 1000 * Kt4);
    return [Q, I]
  }
  eU0.millisToHrTime = Ns;

  function kb1() {
    let A = yb1.otperformance.timeOrigin;
    if (typeof A !== "number") {
      let B = yb1.otperformance;
      A = B.timing && B.timing.fetchStart
    }
    return A
  }
  eU0.getTimeOrigin = kb1;

  function oU0(A) {
    let B = Ns(kb1()),
      Q = Ns(typeof A === "number" ? A : yb1.otperformance.now());
    return tU0(B, Q)
  }
  eU0.hrTime = oU0;

  function Ht4(A) {
    if (xb1(A)) return A;
    else if (typeof A === "number")
      if (A < kb1()) return oU0(A);
      else return Ns(A);
    else if (A instanceof Date) return Ns(A.getTime());
    else throw TypeError("Invalid input type")
  }
  eU0.timeInputToHrTime = Ht4;

  function zt4(A, B) {
    let Q = B[0] - A[0],
      I = B[1] - A[1];
    if (I < 0) Q -= 1, I += GZ1;
    return [Q, I]
  }
  eU0.hrTimeDuration = zt4;

  function wt4(A) {
    let B = rU0,
      Q = `${"0".repeat(B)}${A[1]}Z`,
      I = Q.substring(Q.length - B - 1);
    return new Date(A[0] * 1000).toISOString().replace("000Z", I)
  }
  eU0.hrTimeToTimeStamp = wt4;

  function Et4(A) {
    return A[0] * GZ1 + A[1]
  }
  eU0.hrTimeToNanoseconds = Et4;

  function Ut4(A) {
    return A[0] * 1000 + A[1] / 1e6
  }
  eU0.hrTimeToMilliseconds = Ut4;

  function Nt4(A) {
    return A[0] * 1e6 + A[1] / 1000
  }
  eU0.hrTimeToMicroseconds = Nt4;

  function xb1(A) {
    return Array.isArray(A) && A.length === 2 && typeof A[0] === "number" && typeof A[1] === "number"
  }
  eU0.isTimeInputHrTime = xb1;

  function $t4(A) {
    return xb1(A) || typeof A === "number" || A instanceof Date
  }
  eU0.isTimeInput = $t4;

  function tU0(A, B) {
    let Q = [A[0] + B[0], A[1] + B[1]];
    if (Q[1] >= GZ1) Q[1] -= GZ1, Q[0] += 1;
    return Q
  }
  eU0.addHrTimes = tU0
})
// @from(Start 4173229, End 4173501)
IN0 = z((QN0) => {
  Object.defineProperty(QN0, "__esModule", {
    value: !0
  });
  QN0.ExportResultCode = void 0;
  var kt4;
  (function(A) {
    A[A.SUCCESS = 0] = "SUCCESS", A[A.FAILED = 1] = "FAILED"
  })(kt4 = QN0.ExportResultCode || (QN0.ExportResultCode = {}))
})
// @from(Start 4173507, End 4174507)
WN0 = z((DN0) => {
  Object.defineProperty(DN0, "__esModule", {
    value: !0
  });
  DN0.CompositePropagator = void 0;
  var GN0 = s9();
  class ZN0 {
    _propagators;
    _fields;
    constructor(A = {}) {
      this._propagators = A.propagators ?? [], this._fields = Array.from(new Set(this._propagators.map((B) => typeof B.fields === "function" ? B.fields() : []).reduce((B, Q) => B.concat(Q), [])))
    }
    inject(A, B, Q) {
      for (let I of this._propagators) try {
        I.inject(A, B, Q)
      } catch (G) {
        GN0.diag.warn(`Failed to inject with ${I.constructor.name}. Err: ${G.message}`)
      }
    }
    extract(A, B, Q) {
      return this._propagators.reduce((I, G) => {
        try {
          return G.extract(I, B, Q)
        } catch (Z) {
          GN0.diag.warn(`Failed to extract with ${G.constructor.name}. Err: ${Z.message}`)
        }
        return I
      }, A)
    }
    fields() {
      return this._fields.slice()
    }
  }
  DN0.CompositePropagator = ZN0
})
// @from(Start 4174513, End 4175018)
XN0 = z((JN0) => {
  Object.defineProperty(JN0, "__esModule", {
    value: !0
  });
  JN0.validateValue = JN0.validateKey = void 0;
  var vb1 = "[_0-9a-z-*/]",
    xt4 = `[a-z]${vb1}{0,255}`,
    ft4 = `[a-z0-9]${vb1}{0,240}@[a-z]${vb1}{0,13}`,
    vt4 = new RegExp(`^(?:${xt4}|${ft4})$`),
    bt4 = /^[ -~]{0,255}[!-~]$/,
    gt4 = /,|=/;

  function ht4(A) {
    return vt4.test(A)
  }
  JN0.validateKey = ht4;

  function mt4(A) {
    return bt4.test(A) && !gt4.test(A)
  }
  JN0.validateValue = mt4
})
// @from(Start 4175024, End 4176528)
gb1 = z((zN0) => {
  Object.defineProperty(zN0, "__esModule", {
    value: !0
  });
  zN0.TraceState = void 0;
  var VN0 = XN0(),
    CN0 = 32,
    ut4 = 512,
    KN0 = ",",
    HN0 = "=";
  class bb1 {
    _internalState = new Map;
    constructor(A) {
      if (A) this._parse(A)
    }
    set(A, B) {
      let Q = this._clone();
      if (Q._internalState.has(A)) Q._internalState.delete(A);
      return Q._internalState.set(A, B), Q
    }
    unset(A) {
      let B = this._clone();
      return B._internalState.delete(A), B
    }
    get(A) {
      return this._internalState.get(A)
    }
    serialize() {
      return this._keys().reduce((A, B) => {
        return A.push(B + HN0 + this.get(B)), A
      }, []).join(KN0)
    }
    _parse(A) {
      if (A.length > ut4) return;
      if (this._internalState = A.split(KN0).reverse().reduce((B, Q) => {
          let I = Q.trim(),
            G = I.indexOf(HN0);
          if (G !== -1) {
            let Z = I.slice(0, G),
              D = I.slice(G + 1, Q.length);
            if (VN0.validateKey(Z) && VN0.validateValue(D)) B.set(Z, D)
          }
          return B
        }, new Map), this._internalState.size > CN0) this._internalState = new Map(Array.from(this._internalState.entries()).reverse().slice(0, CN0))
    }
    _keys() {
      return Array.from(this._internalState.keys()).reverse()
    }
    _clone() {
      let A = new bb1;
      return A._internalState = new Map(this._internalState), A
    }
  }
  zN0.TraceState = bb1
})
// @from(Start 4176534, End 4178366)
qN0 = z((NN0) => {
  Object.defineProperty(NN0, "__esModule", {
    value: !0
  });
  NN0.W3CTraceContextPropagator = NN0.parseTraceParent = NN0.TRACE_STATE_HEADER = NN0.TRACE_PARENT_HEADER = void 0;
  var ZZ1 = s9(),
    pt4 = Es(),
    ct4 = gb1();
  NN0.TRACE_PARENT_HEADER = "traceparent";
  NN0.TRACE_STATE_HEADER = "tracestate";
  var lt4 = "00",
    it4 = "(?!ff)[\\da-f]{2}",
    nt4 = "(?![0]{32})[\\da-f]{32}",
    at4 = "(?![0]{16})[\\da-f]{16}",
    st4 = "[\\da-f]{2}",
    rt4 = new RegExp(`^\\s?(${it4})-(${nt4})-(${at4})-(${st4})(-.*)?\\s?$`);

  function EN0(A) {
    let B = rt4.exec(A);
    if (!B) return null;
    if (B[1] === "00" && B[5]) return null;
    return {
      traceId: B[2],
      spanId: B[3],
      traceFlags: parseInt(B[4], 16)
    }
  }
  NN0.parseTraceParent = EN0;
  class UN0 {
    inject(A, B, Q) {
      let I = ZZ1.trace.getSpanContext(A);
      if (!I || pt4.isTracingSuppressed(A) || !ZZ1.isSpanContextValid(I)) return;
      let G = `${lt4}-${I.traceId}-${I.spanId}-0${Number(I.traceFlags||ZZ1.TraceFlags.NONE).toString(16)}`;
      if (Q.set(B, NN0.TRACE_PARENT_HEADER, G), I.traceState) Q.set(B, NN0.TRACE_STATE_HEADER, I.traceState.serialize())
    }
    extract(A, B, Q) {
      let I = Q.get(B, NN0.TRACE_PARENT_HEADER);
      if (!I) return A;
      let G = Array.isArray(I) ? I[0] : I;
      if (typeof G !== "string") return A;
      let Z = EN0(G);
      if (!Z) return A;
      Z.isRemote = !0;
      let D = Q.get(B, NN0.TRACE_STATE_HEADER);
      if (D) {
        let Y = Array.isArray(D) ? D.join(",") : D;
        Z.traceState = new ct4.TraceState(typeof Y === "string" ? Y : void 0)
      }
      return ZZ1.trace.setSpanContext(A, Z)
    }
    fields() {
      return [NN0.TRACE_PARENT_HEADER, NN0.TRACE_STATE_HEADER]
    }
  }
  NN0.W3CTraceContextPropagator = UN0
})
// @from(Start 4178372, End 4178988)
ON0 = z((LN0) => {
  Object.defineProperty(LN0, "__esModule", {
    value: !0
  });
  LN0.getRPCMetadata = LN0.deleteRPCMetadata = LN0.setRPCMetadata = LN0.RPCType = void 0;
  var tt4 = s9(),
    hb1 = tt4.createContextKey("OpenTelemetry SDK Context Key RPC_METADATA"),
    et4;
  (function(A) {
    A.HTTP = "http"
  })(et4 = LN0.RPCType || (LN0.RPCType = {}));

  function Ae4(A, B) {
    return A.setValue(hb1, B)
  }
  LN0.setRPCMetadata = Ae4;

  function Be4(A) {
    return A.deleteValue(hb1)
  }
  LN0.deleteRPCMetadata = Be4;

  function Qe4(A) {
    return A.getValue(hb1)
  }
  LN0.getRPCMetadata = Qe4
})
// @from(Start 4178994, End 4180212)
kN0 = z((jN0) => {
  Object.defineProperty(jN0, "__esModule", {
    value: !0
  });
  jN0.isPlainObject = void 0;
  var Ze4 = "[object Object]",
    De4 = "[object Null]",
    Ye4 = "[object Undefined]",
    We4 = Function.prototype,
    TN0 = We4.toString,
    Je4 = TN0.call(Object),
    Fe4 = Object.getPrototypeOf,
    PN0 = Object.prototype,
    SN0 = PN0.hasOwnProperty,
    y_ = Symbol ? Symbol.toStringTag : void 0,
    _N0 = PN0.toString;

  function Xe4(A) {
    if (!Ve4(A) || Ce4(A) !== Ze4) return !1;
    let B = Fe4(A);
    if (B === null) return !0;
    let Q = SN0.call(B, "constructor") && B.constructor;
    return typeof Q == "function" && Q instanceof Q && TN0.call(Q) === Je4
  }
  jN0.isPlainObject = Xe4;

  function Ve4(A) {
    return A != null && typeof A == "object"
  }

  function Ce4(A) {
    if (A == null) return A === void 0 ? Ye4 : De4;
    return y_ && y_ in Object(A) ? Ke4(A) : He4(A)
  }

  function Ke4(A) {
    let B = SN0.call(A, y_),
      Q = A[y_],
      I = !1;
    try {
      A[y_] = void 0, I = !0
    } catch (Z) {}
    let G = _N0.call(A);
    if (I)
      if (B) A[y_] = Q;
      else delete A[y_];
    return G
  }

  function He4(A) {
    return _N0.call(A)
  }
})
// @from(Start 4180218, End 4182650)
mN0 = z((gN0) => {
  Object.defineProperty(gN0, "__esModule", {
    value: !0
  });
  gN0.merge = void 0;
  var xN0 = kN0(),
    ze4 = 20;

  function we4(...A) {
    let B = A.shift(),
      Q = new WeakMap;
    while (A.length > 0) B = vN0(B, A.shift(), 0, Q);
    return B
  }
  gN0.merge = we4;

  function mb1(A) {
    if (JZ1(A)) return A.slice();
    return A
  }

  function vN0(A, B, Q = 0, I) {
    let G;
    if (Q > ze4) return;
    if (Q++, WZ1(A) || WZ1(B) || bN0(B)) G = mb1(B);
    else if (JZ1(A)) {
      if (G = A.slice(), JZ1(B))
        for (let Z = 0, D = B.length; Z < D; Z++) G.push(mb1(B[Z]));
      else if ($s(B)) {
        let Z = Object.keys(B);
        for (let D = 0, Y = Z.length; D < Y; D++) {
          let W = Z[D];
          G[W] = mb1(B[W])
        }
      }
    } else if ($s(A))
      if ($s(B)) {
        if (!Ee4(A, B)) return B;
        G = Object.assign({}, A);
        let Z = Object.keys(B);
        for (let D = 0, Y = Z.length; D < Y; D++) {
          let W = Z[D],
            J = B[W];
          if (WZ1(J))
            if (typeof J === "undefined") delete G[W];
            else G[W] = J;
          else {
            let F = G[W],
              X = J;
            if (fN0(A, W, I) || fN0(B, W, I)) delete G[W];
            else {
              if ($s(F) && $s(X)) {
                let V = I.get(F) || [],
                  C = I.get(X) || [];
                V.push({
                  obj: A,
                  key: W
                }), C.push({
                  obj: B,
                  key: W
                }), I.set(F, V), I.set(X, C)
              }
              G[W] = vN0(G[W], J, Q, I)
            }
          }
        }
      } else G = B;
    return G
  }

  function fN0(A, B, Q) {
    let I = Q.get(A[B]) || [];
    for (let G = 0, Z = I.length; G < Z; G++) {
      let D = I[G];
      if (D.key === B && D.obj === A) return !0
    }
    return !1
  }

  function JZ1(A) {
    return Array.isArray(A)
  }

  function bN0(A) {
    return typeof A === "function"
  }

  function $s(A) {
    return !WZ1(A) && !JZ1(A) && !bN0(A) && typeof A === "object"
  }

  function WZ1(A) {
    return typeof A === "string" || typeof A === "number" || typeof A === "boolean" || typeof A === "undefined" || A instanceof Date || A instanceof RegExp || A === null
  }

  function Ee4(A, B) {
    if (!xN0.isPlainObject(A) || !xN0.isPlainObject(B)) return !1;
    return !0
  }
})
// @from(Start 4182656, End 4183276)
pN0 = z((dN0) => {
  Object.defineProperty(dN0, "__esModule", {
    value: !0
  });
  dN0.callWithTimeout = dN0.TimeoutError = void 0;
  class FZ1 extends Error {
    constructor(A) {
      super(A);
      Object.setPrototypeOf(this, FZ1.prototype)
    }
  }
  dN0.TimeoutError = FZ1;

  function Ue4(A, B) {
    let Q, I = new Promise(function G(Z, D) {
      Q = setTimeout(function Y() {
        D(new FZ1("Operation timed out."))
      }, B)
    });
    return Promise.race([A, I]).then((G) => {
      return clearTimeout(Q), G
    }, (G) => {
      throw clearTimeout(Q), G
    })
  }
  dN0.callWithTimeout = Ue4
})
// @from(Start 4183282, End 4183685)
nN0 = z((lN0) => {
  Object.defineProperty(lN0, "__esModule", {
    value: !0
  });
  lN0.isUrlIgnored = lN0.urlMatches = void 0;

  function cN0(A, B) {
    if (typeof B === "string") return A === B;
    else return !!A.match(B)
  }
  lN0.urlMatches = cN0;

  function $e4(A, B) {
    if (!B) return !1;
    for (let Q of B)
      if (cN0(A, Q)) return !0;
    return !1
  }
  lN0.isUrlIgnored = $e4
})
// @from(Start 4183691, End 4184150)
oN0 = z((sN0) => {
  Object.defineProperty(sN0, "__esModule", {
    value: !0
  });
  sN0.Deferred = void 0;
  class aN0 {
    _promise;
    _resolve;
    _reject;
    constructor() {
      this._promise = new Promise((A, B) => {
        this._resolve = A, this._reject = B
      })
    }
    get promise() {
      return this._promise
    }
    resolve(A) {
      this._resolve(A)
    }
    reject(A) {
      this._reject(A)
    }
  }
  sN0.Deferred = aN0
})
// @from(Start 4184156, End 4184948)
B$0 = z((eN0) => {
  Object.defineProperty(eN0, "__esModule", {
    value: !0
  });
  eN0.BindOnceFuture = void 0;
  var Me4 = oN0();
  class tN0 {
    _callback;
    _that;
    _isCalled = !1;
    _deferred = new Me4.Deferred;
    constructor(A, B) {
      this._callback = A, this._that = B
    }
    get isCalled() {
      return this._isCalled
    }
    get promise() {
      return this._deferred.promise
    }
    call(...A) {
      if (!this._isCalled) {
        this._isCalled = !0;
        try {
          Promise.resolve(this._callback.call(this._that, ...A)).then((B) => this._deferred.resolve(B), (B) => this._deferred.reject(B))
        } catch (B) {
          this._deferred.reject(B)
        }
      }
      return this._deferred.promise
    }
  }
  eN0.BindOnceFuture = tN0
})
// @from(Start 4184954, End 4185636)
Z$0 = z((I$0) => {
  Object.defineProperty(I$0, "__esModule", {
    value: !0
  });
  I$0.diagLogLevelFromString = void 0;
  var RN = s9(),
    Q$0 = {
      ALL: RN.DiagLogLevel.ALL,
      VERBOSE: RN.DiagLogLevel.VERBOSE,
      DEBUG: RN.DiagLogLevel.DEBUG,
      INFO: RN.DiagLogLevel.INFO,
      WARN: RN.DiagLogLevel.WARN,
      ERROR: RN.DiagLogLevel.ERROR,
      NONE: RN.DiagLogLevel.NONE
    };

  function Le4(A) {
    if (A == null) return;
    let B = Q$0[A.toUpperCase()];
    if (B == null) return RN.diag.warn(`Unknown log level "${A}", expected one of ${Object.keys(Q$0)}, using default`), RN.DiagLogLevel.INFO;
    return B
  }
  I$0.diagLogLevelFromString = Le4
})
// @from(Start 4185642, End 4186011)
J$0 = z((Y$0) => {
  Object.defineProperty(Y$0, "__esModule", {
    value: !0
  });
  Y$0._export = void 0;
  var D$0 = s9(),
    Re4 = Es();

  function Oe4(A, B) {
    return new Promise((Q) => {
      D$0.context.with(Re4.suppressTracing(D$0.context.active()), () => {
        A.export(B, (I) => {
          Q(I)
        })
      })
    })
  }
  Y$0._export = Oe4
})
// @from(Start 4186017, End 4194275)
p8 = z((p9) => {
  Object.defineProperty(p9, "__esModule", {
    value: !0
  });
  p9.internal = p9.diagLogLevelFromString = p9.BindOnceFuture = p9.urlMatches = p9.isUrlIgnored = p9.callWithTimeout = p9.TimeoutError = p9.merge = p9.TraceState = p9.unsuppressTracing = p9.suppressTracing = p9.isTracingSuppressed = p9.setRPCMetadata = p9.getRPCMetadata = p9.deleteRPCMetadata = p9.RPCType = p9.parseTraceParent = p9.W3CTraceContextPropagator = p9.TRACE_STATE_HEADER = p9.TRACE_PARENT_HEADER = p9.CompositePropagator = p9.unrefTimer = p9.otperformance = p9.getStringListFromEnv = p9.getNumberFromEnv = p9.getBooleanFromEnv = p9.getStringFromEnv = p9._globalThis = p9.SDK_INFO = p9.parseKeyPairsIntoRecord = p9.ExportResultCode = p9.timeInputToHrTime = p9.millisToHrTime = p9.isTimeInputHrTime = p9.isTimeInput = p9.hrTimeToTimeStamp = p9.hrTimeToNanoseconds = p9.hrTimeToMilliseconds = p9.hrTimeToMicroseconds = p9.hrTimeDuration = p9.hrTime = p9.getTimeOrigin = p9.addHrTimes = p9.loggingErrorHandler = p9.setGlobalErrorHandler = p9.globalErrorHandler = p9.sanitizeAttributes = p9.isAttributeValue = p9.AnchoredClock = p9.W3CBaggagePropagator = void 0;
  var Te4 = OX0();
  Object.defineProperty(p9, "W3CBaggagePropagator", {
    enumerable: !0,
    get: function() {
      return Te4.W3CBaggagePropagator
    }
  });
  var Pe4 = _X0();
  Object.defineProperty(p9, "AnchoredClock", {
    enumerable: !0,
    get: function() {
      return Pe4.AnchoredClock
    }
  });
  var F$0 = bX0();
  Object.defineProperty(p9, "isAttributeValue", {
    enumerable: !0,
    get: function() {
      return F$0.isAttributeValue
    }
  });
  Object.defineProperty(p9, "sanitizeAttributes", {
    enumerable: !0,
    get: function() {
      return F$0.sanitizeAttributes
    }
  });
  var X$0 = pX0();
  Object.defineProperty(p9, "globalErrorHandler", {
    enumerable: !0,
    get: function() {
      return X$0.globalErrorHandler
    }
  });
  Object.defineProperty(p9, "setGlobalErrorHandler", {
    enumerable: !0,
    get: function() {
      return X$0.setGlobalErrorHandler
    }
  });
  var Se4 = Sb1();
  Object.defineProperty(p9, "loggingErrorHandler", {
    enumerable: !0,
    get: function() {
      return Se4.loggingErrorHandler
    }
  });
  var MX = BN0();
  Object.defineProperty(p9, "addHrTimes", {
    enumerable: !0,
    get: function() {
      return MX.addHrTimes
    }
  });
  Object.defineProperty(p9, "getTimeOrigin", {
    enumerable: !0,
    get: function() {
      return MX.getTimeOrigin
    }
  });
  Object.defineProperty(p9, "hrTime", {
    enumerable: !0,
    get: function() {
      return MX.hrTime
    }
  });
  Object.defineProperty(p9, "hrTimeDuration", {
    enumerable: !0,
    get: function() {
      return MX.hrTimeDuration
    }
  });
  Object.defineProperty(p9, "hrTimeToMicroseconds", {
    enumerable: !0,
    get: function() {
      return MX.hrTimeToMicroseconds
    }
  });
  Object.defineProperty(p9, "hrTimeToMilliseconds", {
    enumerable: !0,
    get: function() {
      return MX.hrTimeToMilliseconds
    }
  });
  Object.defineProperty(p9, "hrTimeToNanoseconds", {
    enumerable: !0,
    get: function() {
      return MX.hrTimeToNanoseconds
    }
  });
  Object.defineProperty(p9, "hrTimeToTimeStamp", {
    enumerable: !0,
    get: function() {
      return MX.hrTimeToTimeStamp
    }
  });
  Object.defineProperty(p9, "isTimeInput", {
    enumerable: !0,
    get: function() {
      return MX.isTimeInput
    }
  });
  Object.defineProperty(p9, "isTimeInputHrTime", {
    enumerable: !0,
    get: function() {
      return MX.isTimeInputHrTime
    }
  });
  Object.defineProperty(p9, "millisToHrTime", {
    enumerable: !0,
    get: function() {
      return MX.millisToHrTime
    }
  });
  Object.defineProperty(p9, "timeInputToHrTime", {
    enumerable: !0,
    get: function() {
      return MX.timeInputToHrTime
    }
  });
  var _e4 = IN0();
  Object.defineProperty(p9, "ExportResultCode", {
    enumerable: !0,
    get: function() {
      return _e4.ExportResultCode
    }
  });
  var je4 = Ob1();
  Object.defineProperty(p9, "parseKeyPairsIntoRecord", {
    enumerable: !0,
    get: function() {
      return je4.parseKeyPairsIntoRecord
    }
  });
  var AR = jb1();
  Object.defineProperty(p9, "SDK_INFO", {
    enumerable: !0,
    get: function() {
      return AR.SDK_INFO
    }
  });
  Object.defineProperty(p9, "_globalThis", {
    enumerable: !0,
    get: function() {
      return AR._globalThis
    }
  });
  Object.defineProperty(p9, "getStringFromEnv", {
    enumerable: !0,
    get: function() {
      return AR.getStringFromEnv
    }
  });
  Object.defineProperty(p9, "getBooleanFromEnv", {
    enumerable: !0,
    get: function() {
      return AR.getBooleanFromEnv
    }
  });
  Object.defineProperty(p9, "getNumberFromEnv", {
    enumerable: !0,
    get: function() {
      return AR.getNumberFromEnv
    }
  });
  Object.defineProperty(p9, "getStringListFromEnv", {
    enumerable: !0,
    get: function() {
      return AR.getStringListFromEnv
    }
  });
  Object.defineProperty(p9, "otperformance", {
    enumerable: !0,
    get: function() {
      return AR.otperformance
    }
  });
  Object.defineProperty(p9, "unrefTimer", {
    enumerable: !0,
    get: function() {
      return AR.unrefTimer
    }
  });
  var ye4 = WN0();
  Object.defineProperty(p9, "CompositePropagator", {
    enumerable: !0,
    get: function() {
      return ye4.CompositePropagator
    }
  });
  var XZ1 = qN0();
  Object.defineProperty(p9, "TRACE_PARENT_HEADER", {
    enumerable: !0,
    get: function() {
      return XZ1.TRACE_PARENT_HEADER
    }
  });
  Object.defineProperty(p9, "TRACE_STATE_HEADER", {
    enumerable: !0,
    get: function() {
      return XZ1.TRACE_STATE_HEADER
    }
  });
  Object.defineProperty(p9, "W3CTraceContextPropagator", {
    enumerable: !0,
    get: function() {
      return XZ1.W3CTraceContextPropagator
    }
  });
  Object.defineProperty(p9, "parseTraceParent", {
    enumerable: !0,
    get: function() {
      return XZ1.parseTraceParent
    }
  });
  var VZ1 = ON0();
  Object.defineProperty(p9, "RPCType", {
    enumerable: !0,
    get: function() {
      return VZ1.RPCType
    }
  });
  Object.defineProperty(p9, "deleteRPCMetadata", {
    enumerable: !0,
    get: function() {
      return VZ1.deleteRPCMetadata
    }
  });
  Object.defineProperty(p9, "getRPCMetadata", {
    enumerable: !0,
    get: function() {
      return VZ1.getRPCMetadata
    }
  });
  Object.defineProperty(p9, "setRPCMetadata", {
    enumerable: !0,
    get: function() {
      return VZ1.setRPCMetadata
    }
  });
  var db1 = Es();
  Object.defineProperty(p9, "isTracingSuppressed", {
    enumerable: !0,
    get: function() {
      return db1.isTracingSuppressed
    }
  });
  Object.defineProperty(p9, "suppressTracing", {
    enumerable: !0,
    get: function() {
      return db1.suppressTracing
    }
  });
  Object.defineProperty(p9, "unsuppressTracing", {
    enumerable: !0,
    get: function() {
      return db1.unsuppressTracing
    }
  });
  var ke4 = gb1();
  Object.defineProperty(p9, "TraceState", {
    enumerable: !0,
    get: function() {
      return ke4.TraceState
    }
  });
  var xe4 = mN0();
  Object.defineProperty(p9, "merge", {
    enumerable: !0,
    get: function() {
      return xe4.merge
    }
  });
  var V$0 = pN0();
  Object.defineProperty(p9, "TimeoutError", {
    enumerable: !0,
    get: function() {
      return V$0.TimeoutError
    }
  });
  Object.defineProperty(p9, "callWithTimeout", {
    enumerable: !0,
    get: function() {
      return V$0.callWithTimeout
    }
  });
  var C$0 = nN0();
  Object.defineProperty(p9, "isUrlIgnored", {
    enumerable: !0,
    get: function() {
      return C$0.isUrlIgnored
    }
  });
  Object.defineProperty(p9, "urlMatches", {
    enumerable: !0,
    get: function() {
      return C$0.urlMatches
    }
  });
  var fe4 = B$0();
  Object.defineProperty(p9, "BindOnceFuture", {
    enumerable: !0,
    get: function() {
      return fe4.BindOnceFuture
    }
  });
  var ve4 = Z$0();
  Object.defineProperty(p9, "diagLogLevelFromString", {
    enumerable: !0,
    get: function() {
      return ve4.diagLogLevelFromString
    }
  });
  var be4 = J$0();
  p9.internal = {
    _export: be4._export
  }
})
// @from(Start 4194281, End 4195790)
E$0 = z((z$0) => {
  Object.defineProperty(z$0, "__esModule", {
    value: !0
  });
  z$0.LastValueAggregator = z$0.LastValueAccumulation = void 0;
  var ge4 = Sg(),
    qs = p8(),
    he4 = tL();
  class Ms {
    startTime;
    _current;
    sampleTime;
    constructor(A, B = 0, Q = [0, 0]) {
      this.startTime = A, this._current = B, this.sampleTime = Q
    }
    record(A) {
      this._current = A, this.sampleTime = qs.millisToHrTime(Date.now())
    }
    setStartTime(A) {
      this.startTime = A
    }
    toPointValue() {
      return this._current
    }
  }
  z$0.LastValueAccumulation = Ms;
  class H$0 {
    kind = ge4.AggregatorKind.LAST_VALUE;
    createAccumulation(A) {
      return new Ms(A)
    }
    merge(A, B) {
      let Q = qs.hrTimeToMicroseconds(B.sampleTime) >= qs.hrTimeToMicroseconds(A.sampleTime) ? B : A;
      return new Ms(A.startTime, Q.toPointValue(), Q.sampleTime)
    }
    diff(A, B) {
      let Q = qs.hrTimeToMicroseconds(B.sampleTime) >= qs.hrTimeToMicroseconds(A.sampleTime) ? B : A;
      return new Ms(B.startTime, Q.toPointValue(), Q.sampleTime)
    }
    toMetricData(A, B, Q, I) {
      return {
        descriptor: A,
        aggregationTemporality: B,
        dataPointType: he4.DataPointType.GAUGE,
        dataPoints: Q.map(([G, Z]) => {
          return {
            attributes: G,
            startTime: Z.startTime,
            endTime: I,
            value: Z.toPointValue()
          }
        })
      }
    }
  }
  z$0.LastValueAggregator = H$0
})
// @from(Start 4195796, End 4197459)
q$0 = z((N$0) => {
  Object.defineProperty(N$0, "__esModule", {
    value: !0
  });
  N$0.SumAggregator = N$0.SumAccumulation = void 0;
  var de4 = Sg(),
    ue4 = tL();
  class k_ {
    startTime;
    monotonic;
    _current;
    reset;
    constructor(A, B, Q = 0, I = !1) {
      this.startTime = A, this.monotonic = B, this._current = Q, this.reset = I
    }
    record(A) {
      if (this.monotonic && A < 0) return;
      this._current += A
    }
    setStartTime(A) {
      this.startTime = A
    }
    toPointValue() {
      return this._current
    }
  }
  N$0.SumAccumulation = k_;
  class U$0 {
    monotonic;
    kind = de4.AggregatorKind.SUM;
    constructor(A) {
      this.monotonic = A
    }
    createAccumulation(A) {
      return new k_(A, this.monotonic)
    }
    merge(A, B) {
      let Q = A.toPointValue(),
        I = B.toPointValue();
      if (B.reset) return new k_(B.startTime, this.monotonic, I, B.reset);
      return new k_(A.startTime, this.monotonic, Q + I)
    }
    diff(A, B) {
      let Q = A.toPointValue(),
        I = B.toPointValue();
      if (this.monotonic && Q > I) return new k_(B.startTime, this.monotonic, I, !0);
      return new k_(B.startTime, this.monotonic, I - Q)
    }
    toMetricData(A, B, Q, I) {
      return {
        descriptor: A,
        aggregationTemporality: B,
        dataPointType: ue4.DataPointType.SUM,
        dataPoints: Q.map(([G, Z]) => {
          return {
            attributes: G,
            startTime: Z.startTime,
            endTime: I,
            value: Z.toPointValue()
          }
        }),
        isMonotonic: this.monotonic
      }
    }
  }
  N$0.SumAggregator = U$0
})
// @from(Start 4197465, End 4199213)
T$0 = z((hC) => {
  Object.defineProperty(hC, "__esModule", {
    value: !0
  });
  hC.SumAggregator = hC.SumAccumulation = hC.LastValueAggregator = hC.LastValueAccumulation = hC.ExponentialHistogramAggregator = hC.ExponentialHistogramAccumulation = hC.HistogramAggregator = hC.HistogramAccumulation = hC.DropAggregator = void 0;
  var ce4 = kF0();
  Object.defineProperty(hC, "DropAggregator", {
    enumerable: !0,
    get: function() {
      return ce4.DropAggregator
    }
  });
  var M$0 = bF0();
  Object.defineProperty(hC, "HistogramAccumulation", {
    enumerable: !0,
    get: function() {
      return M$0.HistogramAccumulation
    }
  });
  Object.defineProperty(hC, "HistogramAggregator", {
    enumerable: !0,
    get: function() {
      return M$0.HistogramAggregator
    }
  });
  var L$0 = HX0();
  Object.defineProperty(hC, "ExponentialHistogramAccumulation", {
    enumerable: !0,
    get: function() {
      return L$0.ExponentialHistogramAccumulation
    }
  });
  Object.defineProperty(hC, "ExponentialHistogramAggregator", {
    enumerable: !0,
    get: function() {
      return L$0.ExponentialHistogramAggregator
    }
  });
  var R$0 = E$0();
  Object.defineProperty(hC, "LastValueAccumulation", {
    enumerable: !0,
    get: function() {
      return R$0.LastValueAccumulation
    }
  });
  Object.defineProperty(hC, "LastValueAggregator", {
    enumerable: !0,
    get: function() {
      return R$0.LastValueAggregator
    }
  });
  var O$0 = q$0();
  Object.defineProperty(hC, "SumAccumulation", {
    enumerable: !0,
    get: function() {
      return O$0.SumAccumulation
    }
  });
  Object.defineProperty(hC, "SumAggregator", {
    enumerable: !0,
    get: function() {
      return O$0.SumAggregator
    }
  })
})