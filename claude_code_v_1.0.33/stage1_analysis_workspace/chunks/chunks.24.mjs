
// @from(Start 2305579, End 2313240)
oT1 = z((jA8, CfA) => {
  var FA = TTA();
  FA.registerLanguage("1c", STA());
  FA.registerLanguage("abnf", jTA());
  FA.registerLanguage("accesslog", xTA());
  FA.registerLanguage("actionscript", vTA());
  FA.registerLanguage("ada", gTA());
  FA.registerLanguage("angelscript", mTA());
  FA.registerLanguage("apache", uTA());
  FA.registerLanguage("applescript", nTA());
  FA.registerLanguage("arcade", sTA());
  FA.registerLanguage("arduino", oTA());
  FA.registerLanguage("armasm", eTA());
  FA.registerLanguage("xml", IPA());
  FA.registerLanguage("asciidoc", DPA());
  FA.registerLanguage("aspectj", WPA());
  FA.registerLanguage("autohotkey", FPA());
  FA.registerLanguage("autoit", VPA());
  FA.registerLanguage("avrasm", KPA());
  FA.registerLanguage("awk", zPA());
  FA.registerLanguage("axapta", EPA());
  FA.registerLanguage("bash", NPA());
  FA.registerLanguage("basic", qPA());
  FA.registerLanguage("bnf", LPA());
  FA.registerLanguage("brainfuck", OPA());
  FA.registerLanguage("c-like", PPA());
  FA.registerLanguage("c", _PA());
  FA.registerLanguage("cal", yPA());
  FA.registerLanguage("capnproto", xPA());
  FA.registerLanguage("ceylon", vPA());
  FA.registerLanguage("clean", gPA());
  FA.registerLanguage("clojure", mPA());
  FA.registerLanguage("clojure-repl", uPA());
  FA.registerLanguage("cmake", cPA());
  FA.registerLanguage("coffeescript", iPA());
  FA.registerLanguage("coq", aPA());
  FA.registerLanguage("cos", rPA());
  FA.registerLanguage("cpp", tPA());
  FA.registerLanguage("crmsh", ASA());
  FA.registerLanguage("crystal", QSA());
  FA.registerLanguage("csharp", GSA());
  FA.registerLanguage("csp", DSA());
  FA.registerLanguage("css", WSA());
  FA.registerLanguage("d", FSA());
  FA.registerLanguage("markdown", VSA());
  FA.registerLanguage("dart", KSA());
  FA.registerLanguage("delphi", zSA());
  FA.registerLanguage("diff", ESA());
  FA.registerLanguage("django", NSA());
  FA.registerLanguage("dns", qSA());
  FA.registerLanguage("dockerfile", LSA());
  FA.registerLanguage("dos", OSA());
  FA.registerLanguage("dsconfig", PSA());
  FA.registerLanguage("dts", _SA());
  FA.registerLanguage("dust", ySA());
  FA.registerLanguage("ebnf", xSA());
  FA.registerLanguage("elixir", vSA());
  FA.registerLanguage("elm", gSA());
  FA.registerLanguage("ruby", dSA());
  FA.registerLanguage("erb", pSA());
  FA.registerLanguage("erlang-repl", lSA());
  FA.registerLanguage("erlang", nSA());
  FA.registerLanguage("excel", sSA());
  FA.registerLanguage("fix", oSA());
  FA.registerLanguage("flix", eSA());
  FA.registerLanguage("fortran", B_A());
  FA.registerLanguage("fsharp", I_A());
  FA.registerLanguage("gams", Z_A());
  FA.registerLanguage("gauss", Y_A());
  FA.registerLanguage("gcode", J_A());
  FA.registerLanguage("gherkin", X_A());
  FA.registerLanguage("glsl", C_A());
  FA.registerLanguage("gml", H_A());
  FA.registerLanguage("go", w_A());
  FA.registerLanguage("golo", U_A());
  FA.registerLanguage("gradle", $_A());
  FA.registerLanguage("groovy", M_A());
  FA.registerLanguage("haml", R_A());
  FA.registerLanguage("handlebars", P_A());
  FA.registerLanguage("haskell", __A());
  FA.registerLanguage("haxe", y_A());
  FA.registerLanguage("hsp", x_A());
  FA.registerLanguage("htmlbars", b_A());
  FA.registerLanguage("http", h_A());
  FA.registerLanguage("hy", d_A());
  FA.registerLanguage("inform7", p_A());
  FA.registerLanguage("ini", n_A());
  FA.registerLanguage("irpf90", s_A());
  FA.registerLanguage("isbl", o_A());
  FA.registerLanguage("java", e_A());
  FA.registerLanguage("javascript", QjA());
  FA.registerLanguage("jboss-cli", GjA());
  FA.registerLanguage("json", DjA());
  FA.registerLanguage("julia", WjA());
  FA.registerLanguage("julia-repl", FjA());
  FA.registerLanguage("kotlin", VjA());
  FA.registerLanguage("lasso", KjA());
  FA.registerLanguage("latex", zjA());
  FA.registerLanguage("ldif", EjA());
  FA.registerLanguage("leaf", NjA());
  FA.registerLanguage("less", LjA());
  FA.registerLanguage("lisp", OjA());
  FA.registerLanguage("livecodeserver", PjA());
  FA.registerLanguage("livescript", _jA());
  FA.registerLanguage("llvm", yjA());
  FA.registerLanguage("lsl", xjA());
  FA.registerLanguage("lua", vjA());
  FA.registerLanguage("makefile", gjA());
  FA.registerLanguage("mathematica", pjA());
  FA.registerLanguage("matlab", ljA());
  FA.registerLanguage("maxima", njA());
  FA.registerLanguage("mel", sjA());
  FA.registerLanguage("mercury", ojA());
  FA.registerLanguage("mipsasm", ejA());
  FA.registerLanguage("mizar", ByA());
  FA.registerLanguage("perl", ZyA());
  FA.registerLanguage("mojolicious", YyA());
  FA.registerLanguage("monkey", JyA());
  FA.registerLanguage("moonscript", XyA());
  FA.registerLanguage("n1ql", CyA());
  FA.registerLanguage("nginx", HyA());
  FA.registerLanguage("nim", wyA());
  FA.registerLanguage("nix", UyA());
  FA.registerLanguage("node-repl", $yA());
  FA.registerLanguage("nsis", MyA());
  FA.registerLanguage("objectivec", RyA());
  FA.registerLanguage("ocaml", TyA());
  FA.registerLanguage("openscad", SyA());
  FA.registerLanguage("oxygene", jyA());
  FA.registerLanguage("parser3", kyA());
  FA.registerLanguage("pf", fyA());
  FA.registerLanguage("pgsql", byA());
  FA.registerLanguage("php", hyA());
  FA.registerLanguage("php-template", dyA());
  FA.registerLanguage("plaintext", pyA());
  FA.registerLanguage("pony", lyA());
  FA.registerLanguage("powershell", nyA());
  FA.registerLanguage("processing", syA());
  FA.registerLanguage("profile", oyA());
  FA.registerLanguage("prolog", eyA());
  FA.registerLanguage("properties", BkA());
  FA.registerLanguage("protobuf", IkA());
  FA.registerLanguage("puppet", ZkA());
  FA.registerLanguage("purebasic", YkA());
  FA.registerLanguage("python", JkA());
  FA.registerLanguage("python-repl", XkA());
  FA.registerLanguage("q", CkA());
  FA.registerLanguage("qml", HkA());
  FA.registerLanguage("r", wkA());
  FA.registerLanguage("reasonml", UkA());
  FA.registerLanguage("rib", $kA());
  FA.registerLanguage("roboconf", MkA());
  FA.registerLanguage("routeros", RkA());
  FA.registerLanguage("rsl", TkA());
  FA.registerLanguage("ruleslanguage", SkA());
  FA.registerLanguage("rust", jkA());
  FA.registerLanguage("sas", kkA());
  FA.registerLanguage("scala", fkA());
  FA.registerLanguage("scheme", bkA());
  FA.registerLanguage("scilab", hkA());
  FA.registerLanguage("scss", dkA());
  FA.registerLanguage("shell", pkA());
  FA.registerLanguage("smali", lkA());
  FA.registerLanguage("smalltalk", nkA());
  FA.registerLanguage("sml", skA());
  FA.registerLanguage("sqf", okA());
  FA.registerLanguage("sql_more", ekA());
  FA.registerLanguage("sql", QxA());
  FA.registerLanguage("stan", GxA());
  FA.registerLanguage("stata", DxA());
  FA.registerLanguage("step21", WxA());
  FA.registerLanguage("stylus", FxA());
  FA.registerLanguage("subunit", VxA());
  FA.registerLanguage("swift", $xA());
  FA.registerLanguage("taggerscript", MxA());
  FA.registerLanguage("yaml", RxA());
  FA.registerLanguage("tap", TxA());
  FA.registerLanguage("tcl", _xA());
  FA.registerLanguage("thrift", yxA());
  FA.registerLanguage("tp", xxA());
  FA.registerLanguage("twig", vxA());
  FA.registerLanguage("typescript", uxA());
  FA.registerLanguage("vala", cxA());
  FA.registerLanguage("vbnet", nxA());
  FA.registerLanguage("vbscript", rxA());
  FA.registerLanguage("vbscript-html", txA());
  FA.registerLanguage("verilog", AfA());
  FA.registerLanguage("vhdl", QfA());
  FA.registerLanguage("vim", GfA());
  FA.registerLanguage("x86asm", DfA());
  FA.registerLanguage("xl", WfA());
  FA.registerLanguage("xquery", FfA());
  FA.registerLanguage("zephir", VfA());
  CfA.exports = FA
})
// @from(Start 2313246, End 2319412)
rvA = z((Ht9) => {
  function xP1(A, B) {
    var Q = A.length;
    A.push(B);
    A: for (; 0 < Q;) {
      var I = Q - 1 >>> 1,
        G = A[I];
      if (0 < e81(G, B)) A[I] = B, A[Q] = G, Q = I;
      else break A
    }
  }

  function zC(A) {
    return A.length === 0 ? null : A[0]
  }

  function IB1(A) {
    if (A.length === 0) return null;
    var B = A[0],
      Q = A.pop();
    if (Q !== B) {
      A[0] = Q;
      A: for (var I = 0, G = A.length, Z = G >>> 1; I < Z;) {
        var D = 2 * (I + 1) - 1,
          Y = A[D],
          W = D + 1,
          J = A[W];
        if (0 > e81(Y, Q)) W < G && 0 > e81(J, Y) ? (A[I] = J, A[W] = Q, I = W) : (A[I] = Y, A[D] = Q, I = D);
        else if (W < G && 0 > e81(J, Q)) A[I] = J, A[W] = Q, I = W;
        else break A
      }
    }
    return B
  }

  function e81(A, B) {
    var Q = A.sortIndex - B.sortIndex;
    return Q !== 0 ? Q : A.id - B.id
  }
  if (typeof performance === "object" && typeof performance.now === "function") fP1 = performance, Ht9.unstable_now = function() {
    return fP1.now()
  };
  else AB1 = Date, vP1 = AB1.now(), Ht9.unstable_now = function() {
    return AB1.now() - vP1
  };
  var fP1, AB1, vP1, Lz = [],
    QL = [],
    Kt9 = 1,
    BX = null,
    EZ = 3,
    GB1 = !1,
    RS = !1,
    Dn = !1,
    lvA = typeof setTimeout === "function" ? setTimeout : null,
    ivA = typeof clearTimeout === "function" ? clearTimeout : null,
    cvA = typeof setImmediate !== "undefined" ? setImmediate : null;
  typeof navigator !== "undefined" && navigator.scheduling !== void 0 && navigator.scheduling.isInputPending !== void 0 && navigator.scheduling.isInputPending.bind(navigator.scheduling);

  function bP1(A) {
    for (var B = zC(QL); B !== null;) {
      if (B.callback === null) IB1(QL);
      else if (B.startTime <= A) IB1(QL), B.sortIndex = B.expirationTime, xP1(Lz, B);
      else break;
      B = zC(QL)
    }
  }

  function hP1(A) {
    if (Dn = !1, bP1(A), !RS)
      if (zC(Lz) !== null) RS = !0, dP1(mP1);
      else {
        var B = zC(QL);
        B !== null && uP1(hP1, B.startTime - A)
      }
  }

  function mP1(A, B) {
    RS = !1, Dn && (Dn = !1, ivA(Yn), Yn = -1), GB1 = !0;
    var Q = EZ;
    try {
      bP1(B);
      for (BX = zC(Lz); BX !== null && (!(BX.expirationTime > B) || A && !svA());) {
        var I = BX.callback;
        if (typeof I === "function") {
          BX.callback = null, EZ = BX.priorityLevel;
          var G = I(BX.expirationTime <= B);
          B = Ht9.unstable_now(), typeof G === "function" ? BX.callback = G : BX === zC(Lz) && IB1(Lz), bP1(B)
        } else IB1(Lz);
        BX = zC(Lz)
      }
      if (BX !== null) var Z = !0;
      else {
        var D = zC(QL);
        D !== null && uP1(hP1, D.startTime - B), Z = !1
      }
      return Z
    } finally {
      BX = null, EZ = Q, GB1 = !1
    }
  }
  var ZB1 = !1,
    BB1 = null,
    Yn = -1,
    nvA = 5,
    avA = -1;

  function svA() {
    return Ht9.unstable_now() - avA < nvA ? !1 : !0
  }

  function kP1() {
    if (BB1 !== null) {
      var A = Ht9.unstable_now();
      avA = A;
      var B = !0;
      try {
        B = BB1(!0, A)
      } finally {
        B ? Zn() : (ZB1 = !1, BB1 = null)
      }
    } else ZB1 = !1
  }
  var Zn;
  if (typeof cvA === "function") Zn = function() {
    cvA(kP1)
  };
  else if (typeof MessageChannel !== "undefined") QB1 = new MessageChannel, gP1 = QB1.port2, QB1.port1.onmessage = kP1, Zn = function() {
    gP1.postMessage(null)
  };
  else Zn = function() {
    lvA(kP1, 0)
  };
  var QB1, gP1;

  function dP1(A) {
    BB1 = A, ZB1 || (ZB1 = !0, Zn())
  }

  function uP1(A, B) {
    Yn = lvA(function() {
      A(Ht9.unstable_now())
    }, B)
  }
  Ht9.unstable_IdlePriority = 5;
  Ht9.unstable_ImmediatePriority = 1;
  Ht9.unstable_LowPriority = 4;
  Ht9.unstable_NormalPriority = 3;
  Ht9.unstable_Profiling = null;
  Ht9.unstable_UserBlockingPriority = 2;
  Ht9.unstable_cancelCallback = function(A) {
    A.callback = null
  };
  Ht9.unstable_continueExecution = function() {
    RS || GB1 || (RS = !0, dP1(mP1))
  };
  Ht9.unstable_forceFrameRate = function(A) {
    0 > A || 125 < A ? console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported") : nvA = 0 < A ? Math.floor(1000 / A) : 5
  };
  Ht9.unstable_getCurrentPriorityLevel = function() {
    return EZ
  };
  Ht9.unstable_getFirstCallbackNode = function() {
    return zC(Lz)
  };
  Ht9.unstable_next = function(A) {
    switch (EZ) {
      case 1:
      case 2:
      case 3:
        var B = 3;
        break;
      default:
        B = EZ
    }
    var Q = EZ;
    EZ = B;
    try {
      return A()
    } finally {
      EZ = Q
    }
  };
  Ht9.unstable_pauseExecution = function() {};
  Ht9.unstable_requestPaint = function() {};
  Ht9.unstable_runWithPriority = function(A, B) {
    switch (A) {
      case 1:
      case 2:
      case 3:
      case 4:
      case 5:
        break;
      default:
        A = 3
    }
    var Q = EZ;
    EZ = A;
    try {
      return B()
    } finally {
      EZ = Q
    }
  };
  Ht9.unstable_scheduleCallback = function(A, B, Q) {
    var I = Ht9.unstable_now();
    switch (typeof Q === "object" && Q !== null ? (Q = Q.delay, Q = typeof Q === "number" && 0 < Q ? I + Q : I) : Q = I, A) {
      case 1:
        var G = -1;
        break;
      case 2:
        G = 250;
        break;
      case 5:
        G = 1073741823;
        break;
      case 4:
        G = 1e4;
        break;
      default:
        G = 5000
    }
    return G = Q + G, A = {
      id: Kt9++,
      callback: B,
      priorityLevel: A,
      startTime: Q,
      expirationTime: G,
      sortIndex: -1
    }, Q > I ? (A.sortIndex = Q, xP1(QL, A), zC(Lz) === null && A === zC(QL) && (Dn ? (ivA(Yn), Yn = -1) : Dn = !0, uP1(hP1, Q - I))) : (A.sortIndex = G, xP1(Lz, A), RS || GB1 || (RS = !0, dP1(mP1))), A
  };
  Ht9.unstable_shouldYield = svA;
  Ht9.unstable_wrapCallback = function(A) {
    var B = EZ;
    return function() {
      var Q = EZ;
      EZ = B;
      try {
        return A.apply(this, arguments)
      } finally {
        EZ = Q
      }
    }
  }
})