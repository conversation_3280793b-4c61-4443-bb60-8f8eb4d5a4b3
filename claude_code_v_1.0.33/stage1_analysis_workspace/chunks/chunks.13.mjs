
// @from(Start 1243285, End 1261156)
cOA = z((eA) => {
  Object.defineProperty(eA, "__esModule", {
    value: !0
  });
  eA.mergeAll = eA.merge = eA.max = eA.materialize = eA.mapTo = eA.map = eA.last = eA.isEmpty = eA.ignoreElements = eA.groupBy = eA.first = eA.findIndex = eA.find = eA.finalize = eA.filter = eA.expand = eA.exhaustMap = eA.exhaustAll = eA.exhaust = eA.every = eA.endWith = eA.elementAt = eA.distinctUntilKeyChanged = eA.distinctUntilChanged = eA.distinct = eA.dematerialize = eA.delayWhen = eA.delay = eA.defaultIfEmpty = eA.debounceTime = eA.debounce = eA.count = eA.connect = eA.concatWith = eA.concatMapTo = eA.concatMap = eA.concatAll = eA.concat = eA.combineLatestWith = eA.combineLatest = eA.combineLatestAll = eA.combineAll = eA.catchError = eA.bufferWhen = eA.bufferToggle = eA.bufferTime = eA.bufferCount = eA.buffer = eA.auditTime = eA.audit = void 0;
  eA.timeInterval = eA.throwIfEmpty = eA.throttleTime = eA.throttle = eA.tap = eA.takeWhile = eA.takeUntil = eA.takeLast = eA.take = eA.switchScan = eA.switchMapTo = eA.switchMap = eA.switchAll = eA.subscribeOn = eA.startWith = eA.skipWhile = eA.skipUntil = eA.skipLast = eA.skip = eA.single = eA.shareReplay = eA.share = eA.sequenceEqual = eA.scan = eA.sampleTime = eA.sample = eA.refCount = eA.retryWhen = eA.retry = eA.repeatWhen = eA.repeat = eA.reduce = eA.raceWith = eA.race = eA.publishReplay = eA.publishLast = eA.publishBehavior = eA.publish = eA.pluck = eA.partition = eA.pairwise = eA.onErrorResumeNext = eA.observeOn = eA.multicast = eA.min = eA.mergeWith = eA.mergeScan = eA.mergeMapTo = eA.mergeMap = eA.flatMap = void 0;
  eA.zipWith = eA.zipAll = eA.zip = eA.withLatestFrom = eA.windowWhen = eA.windowToggle = eA.windowTime = eA.windowCount = eA.window = eA.toArray = eA.timestamp = eA.timeoutWith = eA.timeout = void 0;
  var Km9 = p51();
  Object.defineProperty(eA, "audit", {
    enumerable: !0,
    get: function() {
      return Km9.audit
    }
  });
  var Hm9 = dR1();
  Object.defineProperty(eA, "auditTime", {
    enumerable: !0,
    get: function() {
      return Hm9.auditTime
    }
  });
  var zm9 = uR1();
  Object.defineProperty(eA, "buffer", {
    enumerable: !0,
    get: function() {
      return zm9.buffer
    }
  });
  var wm9 = cR1();
  Object.defineProperty(eA, "bufferCount", {
    enumerable: !0,
    get: function() {
      return wm9.bufferCount
    }
  });
  var Em9 = lR1();
  Object.defineProperty(eA, "bufferTime", {
    enumerable: !0,
    get: function() {
      return Em9.bufferTime
    }
  });
  var Um9 = nR1();
  Object.defineProperty(eA, "bufferToggle", {
    enumerable: !0,
    get: function() {
      return Um9.bufferToggle
    }
  });
  var Nm9 = aR1();
  Object.defineProperty(eA, "bufferWhen", {
    enumerable: !0,
    get: function() {
      return Nm9.bufferWhen
    }
  });
  var $m9 = sR1();
  Object.defineProperty(eA, "catchError", {
    enumerable: !0,
    get: function() {
      return $m9.catchError
    }
  });
  var qm9 = tR1();
  Object.defineProperty(eA, "combineAll", {
    enumerable: !0,
    get: function() {
      return qm9.combineAll
    }
  });
  var Mm9 = l51();
  Object.defineProperty(eA, "combineLatestAll", {
    enumerable: !0,
    get: function() {
      return Mm9.combineLatestAll
    }
  });
  var Lm9 = eR1();
  Object.defineProperty(eA, "combineLatest", {
    enumerable: !0,
    get: function() {
      return Lm9.combineLatest
    }
  });
  var Rm9 = AO1();
  Object.defineProperty(eA, "combineLatestWith", {
    enumerable: !0,
    get: function() {
      return Rm9.combineLatestWith
    }
  });
  var Om9 = QO1();
  Object.defineProperty(eA, "concat", {
    enumerable: !0,
    get: function() {
      return Om9.concat
    }
  });
  var Tm9 = Ui();
  Object.defineProperty(eA, "concatAll", {
    enumerable: !0,
    get: function() {
      return Tm9.concatAll
    }
  });
  var Pm9 = i51();
  Object.defineProperty(eA, "concatMap", {
    enumerable: !0,
    get: function() {
      return Pm9.concatMap
    }
  });
  var Sm9 = BO1();
  Object.defineProperty(eA, "concatMapTo", {
    enumerable: !0,
    get: function() {
      return Sm9.concatMapTo
    }
  });
  var _m9 = IO1();
  Object.defineProperty(eA, "concatWith", {
    enumerable: !0,
    get: function() {
      return _m9.concatWith
    }
  });
  var jm9 = qi();
  Object.defineProperty(eA, "connect", {
    enumerable: !0,
    get: function() {
      return jm9.connect
    }
  });
  var ym9 = GO1();
  Object.defineProperty(eA, "count", {
    enumerable: !0,
    get: function() {
      return ym9.count
    }
  });
  var km9 = ZO1();
  Object.defineProperty(eA, "debounce", {
    enumerable: !0,
    get: function() {
      return km9.debounce
    }
  });
  var xm9 = DO1();
  Object.defineProperty(eA, "debounceTime", {
    enumerable: !0,
    get: function() {
      return xm9.debounceTime
    }
  });
  var fm9 = rf();
  Object.defineProperty(eA, "defaultIfEmpty", {
    enumerable: !0,
    get: function() {
      return fm9.defaultIfEmpty
    }
  });
  var vm9 = YO1();
  Object.defineProperty(eA, "delay", {
    enumerable: !0,
    get: function() {
      return vm9.delay
    }
  });
  var bm9 = s51();
  Object.defineProperty(eA, "delayWhen", {
    enumerable: !0,
    get: function() {
      return bm9.delayWhen
    }
  });
  var gm9 = WO1();
  Object.defineProperty(eA, "dematerialize", {
    enumerable: !0,
    get: function() {
      return gm9.dematerialize
    }
  });
  var hm9 = JO1();
  Object.defineProperty(eA, "distinct", {
    enumerable: !0,
    get: function() {
      return hm9.distinct
    }
  });
  var mm9 = r51();
  Object.defineProperty(eA, "distinctUntilChanged", {
    enumerable: !0,
    get: function() {
      return mm9.distinctUntilChanged
    }
  });
  var dm9 = FO1();
  Object.defineProperty(eA, "distinctUntilKeyChanged", {
    enumerable: !0,
    get: function() {
      return dm9.distinctUntilKeyChanged
    }
  });
  var um9 = XO1();
  Object.defineProperty(eA, "elementAt", {
    enumerable: !0,
    get: function() {
      return um9.elementAt
    }
  });
  var pm9 = VO1();
  Object.defineProperty(eA, "endWith", {
    enumerable: !0,
    get: function() {
      return pm9.endWith
    }
  });
  var cm9 = CO1();
  Object.defineProperty(eA, "every", {
    enumerable: !0,
    get: function() {
      return cm9.every
    }
  });
  var lm9 = KO1();
  Object.defineProperty(eA, "exhaust", {
    enumerable: !0,
    get: function() {
      return lm9.exhaust
    }
  });
  var im9 = t51();
  Object.defineProperty(eA, "exhaustAll", {
    enumerable: !0,
    get: function() {
      return im9.exhaustAll
    }
  });
  var nm9 = o51();
  Object.defineProperty(eA, "exhaustMap", {
    enumerable: !0,
    get: function() {
      return nm9.exhaustMap
    }
  });
  var am9 = HO1();
  Object.defineProperty(eA, "expand", {
    enumerable: !0,
    get: function() {
      return am9.expand
    }
  });
  var sm9 = cU();
  Object.defineProperty(eA, "filter", {
    enumerable: !0,
    get: function() {
      return sm9.filter
    }
  });
  var rm9 = zO1();
  Object.defineProperty(eA, "finalize", {
    enumerable: !0,
    get: function() {
      return rm9.finalize
    }
  });
  var om9 = e51();
  Object.defineProperty(eA, "find", {
    enumerable: !0,
    get: function() {
      return om9.find
    }
  });
  var tm9 = wO1();
  Object.defineProperty(eA, "findIndex", {
    enumerable: !0,
    get: function() {
      return tm9.findIndex
    }
  });
  var em9 = EO1();
  Object.defineProperty(eA, "first", {
    enumerable: !0,
    get: function() {
      return em9.first
    }
  });
  var Ad9 = UO1();
  Object.defineProperty(eA, "groupBy", {
    enumerable: !0,
    get: function() {
      return Ad9.groupBy
    }
  });
  var Bd9 = n51();
  Object.defineProperty(eA, "ignoreElements", {
    enumerable: !0,
    get: function() {
      return Bd9.ignoreElements
    }
  });
  var Qd9 = NO1();
  Object.defineProperty(eA, "isEmpty", {
    enumerable: !0,
    get: function() {
      return Qd9.isEmpty
    }
  });
  var Id9 = $O1();
  Object.defineProperty(eA, "last", {
    enumerable: !0,
    get: function() {
      return Id9.last
    }
  });
  var Gd9 = pU();
  Object.defineProperty(eA, "map", {
    enumerable: !0,
    get: function() {
      return Gd9.map
    }
  });
  var Zd9 = a51();
  Object.defineProperty(eA, "mapTo", {
    enumerable: !0,
    get: function() {
      return Zd9.mapTo
    }
  });
  var Dd9 = MO1();
  Object.defineProperty(eA, "materialize", {
    enumerable: !0,
    get: function() {
      return Dd9.materialize
    }
  });
  var Yd9 = LO1();
  Object.defineProperty(eA, "max", {
    enumerable: !0,
    get: function() {
      return Yd9.max
    }
  });
  var Wd9 = PO1();
  Object.defineProperty(eA, "merge", {
    enumerable: !0,
    get: function() {
      return Wd9.merge
    }
  });
  var Jd9 = pf();
  Object.defineProperty(eA, "mergeAll", {
    enumerable: !0,
    get: function() {
      return Jd9.mergeAll
    }
  });
  var Fd9 = RO1();
  Object.defineProperty(eA, "flatMap", {
    enumerable: !0,
    get: function() {
      return Fd9.flatMap
    }
  });
  var Xd9 = zz();
  Object.defineProperty(eA, "mergeMap", {
    enumerable: !0,
    get: function() {
      return Xd9.mergeMap
    }
  });
  var Vd9 = OO1();
  Object.defineProperty(eA, "mergeMapTo", {
    enumerable: !0,
    get: function() {
      return Vd9.mergeMapTo
    }
  });
  var Cd9 = TO1();
  Object.defineProperty(eA, "mergeScan", {
    enumerable: !0,
    get: function() {
      return Cd9.mergeScan
    }
  });
  var Kd9 = SO1();
  Object.defineProperty(eA, "mergeWith", {
    enumerable: !0,
    get: function() {
      return Kd9.mergeWith
    }
  });
  var Hd9 = _O1();
  Object.defineProperty(eA, "min", {
    enumerable: !0,
    get: function() {
      return Hd9.min
    }
  });
  var zd9 = Mi();
  Object.defineProperty(eA, "multicast", {
    enumerable: !0,
    get: function() {
      return zd9.multicast
    }
  });
  var wd9 = df();
  Object.defineProperty(eA, "observeOn", {
    enumerable: !0,
    get: function() {
      return wd9.observeOn
    }
  });
  var Ed9 = jO1();
  Object.defineProperty(eA, "onErrorResumeNext", {
    enumerable: !0,
    get: function() {
      return Ed9.onErrorResumeNext
    }
  });
  var Ud9 = yO1();
  Object.defineProperty(eA, "pairwise", {
    enumerable: !0,
    get: function() {
      return Ud9.pairwise
    }
  });
  var Nd9 = uOA();
  Object.defineProperty(eA, "partition", {
    enumerable: !0,
    get: function() {
      return Nd9.partition
    }
  });
  var $d9 = kO1();
  Object.defineProperty(eA, "pluck", {
    enumerable: !0,
    get: function() {
      return $d9.pluck
    }
  });
  var qd9 = xO1();
  Object.defineProperty(eA, "publish", {
    enumerable: !0,
    get: function() {
      return qd9.publish
    }
  });
  var Md9 = fO1();
  Object.defineProperty(eA, "publishBehavior", {
    enumerable: !0,
    get: function() {
      return Md9.publishBehavior
    }
  });
  var Ld9 = vO1();
  Object.defineProperty(eA, "publishLast", {
    enumerable: !0,
    get: function() {
      return Ld9.publishLast
    }
  });
  var Rd9 = bO1();
  Object.defineProperty(eA, "publishReplay", {
    enumerable: !0,
    get: function() {
      return Rd9.publishReplay
    }
  });
  var Od9 = pOA();
  Object.defineProperty(eA, "race", {
    enumerable: !0,
    get: function() {
      return Od9.race
    }
  });
  var Td9 = B81();
  Object.defineProperty(eA, "raceWith", {
    enumerable: !0,
    get: function() {
      return Td9.raceWith
    }
  });
  var Pd9 = CS();
  Object.defineProperty(eA, "reduce", {
    enumerable: !0,
    get: function() {
      return Pd9.reduce
    }
  });
  var Sd9 = gO1();
  Object.defineProperty(eA, "repeat", {
    enumerable: !0,
    get: function() {
      return Sd9.repeat
    }
  });
  var _d9 = hO1();
  Object.defineProperty(eA, "repeatWhen", {
    enumerable: !0,
    get: function() {
      return _d9.repeatWhen
    }
  });
  var jd9 = mO1();
  Object.defineProperty(eA, "retry", {
    enumerable: !0,
    get: function() {
      return jd9.retry
    }
  });
  var yd9 = dO1();
  Object.defineProperty(eA, "retryWhen", {
    enumerable: !0,
    get: function() {
      return yd9.retryWhen
    }
  });
  var kd9 = _51();
  Object.defineProperty(eA, "refCount", {
    enumerable: !0,
    get: function() {
      return kd9.refCount
    }
  });
  var xd9 = Q81();
  Object.defineProperty(eA, "sample", {
    enumerable: !0,
    get: function() {
      return xd9.sample
    }
  });
  var fd9 = uO1();
  Object.defineProperty(eA, "sampleTime", {
    enumerable: !0,
    get: function() {
      return fd9.sampleTime
    }
  });
  var vd9 = pO1();
  Object.defineProperty(eA, "scan", {
    enumerable: !0,
    get: function() {
      return vd9.scan
    }
  });
  var bd9 = cO1();
  Object.defineProperty(eA, "sequenceEqual", {
    enumerable: !0,
    get: function() {
      return bd9.sequenceEqual
    }
  });
  var gd9 = I81();
  Object.defineProperty(eA, "share", {
    enumerable: !0,
    get: function() {
      return gd9.share
    }
  });
  var hd9 = iO1();
  Object.defineProperty(eA, "shareReplay", {
    enumerable: !0,
    get: function() {
      return hd9.shareReplay
    }
  });
  var md9 = nO1();
  Object.defineProperty(eA, "single", {
    enumerable: !0,
    get: function() {
      return md9.single
    }
  });
  var dd9 = aO1();
  Object.defineProperty(eA, "skip", {
    enumerable: !0,
    get: function() {
      return dd9.skip
    }
  });
  var ud9 = sO1();
  Object.defineProperty(eA, "skipLast", {
    enumerable: !0,
    get: function() {
      return ud9.skipLast
    }
  });
  var pd9 = rO1();
  Object.defineProperty(eA, "skipUntil", {
    enumerable: !0,
    get: function() {
      return pd9.skipUntil
    }
  });
  var cd9 = oO1();
  Object.defineProperty(eA, "skipWhile", {
    enumerable: !0,
    get: function() {
      return cd9.skipWhile
    }
  });
  var ld9 = tO1();
  Object.defineProperty(eA, "startWith", {
    enumerable: !0,
    get: function() {
      return ld9.startWith
    }
  });
  var id9 = uf();
  Object.defineProperty(eA, "subscribeOn", {
    enumerable: !0,
    get: function() {
      return id9.subscribeOn
    }
  });
  var nd9 = eO1();
  Object.defineProperty(eA, "switchAll", {
    enumerable: !0,
    get: function() {
      return nd9.switchAll
    }
  });
  var ad9 = Av();
  Object.defineProperty(eA, "switchMap", {
    enumerable: !0,
    get: function() {
      return ad9.switchMap
    }
  });
  var sd9 = AT1();
  Object.defineProperty(eA, "switchMapTo", {
    enumerable: !0,
    get: function() {
      return sd9.switchMapTo
    }
  });
  var rd9 = BT1();
  Object.defineProperty(eA, "switchScan", {
    enumerable: !0,
    get: function() {
      return rd9.switchScan
    }
  });
  var od9 = of();
  Object.defineProperty(eA, "take", {
    enumerable: !0,
    get: function() {
      return od9.take
    }
  });
  var td9 = A81();
  Object.defineProperty(eA, "takeLast", {
    enumerable: !0,
    get: function() {
      return td9.takeLast
    }
  });
  var ed9 = QT1();
  Object.defineProperty(eA, "takeUntil", {
    enumerable: !0,
    get: function() {
      return ed9.takeUntil
    }
  });
  var Au9 = IT1();
  Object.defineProperty(eA, "takeWhile", {
    enumerable: !0,
    get: function() {
      return Au9.takeWhile
    }
  });
  var Bu9 = GT1();
  Object.defineProperty(eA, "tap", {
    enumerable: !0,
    get: function() {
      return Bu9.tap
    }
  });
  var Qu9 = G81();
  Object.defineProperty(eA, "throttle", {
    enumerable: !0,
    get: function() {
      return Qu9.throttle
    }
  });
  var Iu9 = ZT1();
  Object.defineProperty(eA, "throttleTime", {
    enumerable: !0,
    get: function() {
      return Iu9.throttleTime
    }
  });
  var Gu9 = tf();
  Object.defineProperty(eA, "throwIfEmpty", {
    enumerable: !0,
    get: function() {
      return Gu9.throwIfEmpty
    }
  });
  var Zu9 = DT1();
  Object.defineProperty(eA, "timeInterval", {
    enumerable: !0,
    get: function() {
      return Zu9.timeInterval
    }
  });
  var Du9 = Ei();
  Object.defineProperty(eA, "timeout", {
    enumerable: !0,
    get: function() {
      return Du9.timeout
    }
  });
  var Yu9 = YT1();
  Object.defineProperty(eA, "timeoutWith", {
    enumerable: !0,
    get: function() {
      return Yu9.timeoutWith
    }
  });
  var Wu9 = WT1();
  Object.defineProperty(eA, "timestamp", {
    enumerable: !0,
    get: function() {
      return Wu9.timestamp
    }
  });
  var Ju9 = c51();
  Object.defineProperty(eA, "toArray", {
    enumerable: !0,
    get: function() {
      return Ju9.toArray
    }
  });
  var Fu9 = JT1();
  Object.defineProperty(eA, "window", {
    enumerable: !0,
    get: function() {
      return Fu9.window
    }
  });
  var Xu9 = FT1();
  Object.defineProperty(eA, "windowCount", {
    enumerable: !0,
    get: function() {
      return Xu9.windowCount
    }
  });
  var Vu9 = XT1();
  Object.defineProperty(eA, "windowTime", {
    enumerable: !0,
    get: function() {
      return Vu9.windowTime
    }
  });
  var Cu9 = CT1();
  Object.defineProperty(eA, "windowToggle", {
    enumerable: !0,
    get: function() {
      return Cu9.windowToggle
    }
  });
  var Ku9 = KT1();
  Object.defineProperty(eA, "windowWhen", {
    enumerable: !0,
    get: function() {
      return Ku9.windowWhen
    }
  });
  var Hu9 = HT1();
  Object.defineProperty(eA, "withLatestFrom", {
    enumerable: !0,
    get: function() {
      return Hu9.withLatestFrom
    }
  });
  var zu9 = wT1();
  Object.defineProperty(eA, "zip", {
    enumerable: !0,
    get: function() {
      return zu9.zip
    }
  });
  var wu9 = zT1();
  Object.defineProperty(eA, "zipAll", {
    enumerable: !0,
    get: function() {
      return wu9.zipAll
    }
  });
  var Eu9 = ET1();
  Object.defineProperty(eA, "zipWith", {
    enumerable: !0,
    get: function() {
      return Eu9.zipWith
    }
  })
})
// @from(Start 1261162, End 1269402)
NT1 = z((YJ) => {
  var __dirname = "/home/<USER>/work/claude-cli-internal/claude-cli-internal/node_modules/spawn-rx/lib/src",
    rF = YJ && YJ.__assign || function() {
      return rF = Object.assign || function(A) {
        for (var B, Q = 1, I = arguments.length; Q < I; Q++) {
          B = arguments[Q];
          for (var G in B)
            if (Object.prototype.hasOwnProperty.call(B, G)) A[G] = B[G]
        }
        return A
      }, rF.apply(this, arguments)
    },
    qu9 = YJ && YJ.__rest || function(A, B) {
      var Q = {};
      for (var I in A)
        if (Object.prototype.hasOwnProperty.call(A, I) && B.indexOf(I) < 0) Q[I] = A[I];
      if (A != null && typeof Object.getOwnPropertySymbols === "function") {
        for (var G = 0, I = Object.getOwnPropertySymbols(A); G < I.length; G++)
          if (B.indexOf(I[G]) < 0 && Object.prototype.propertyIsEnumerable.call(A, I[G])) Q[I[G]] = A[I[G]]
      }
      return Q
    },
    Mu9 = YJ && YJ.__spreadArray || function(A, B, Q) {
      if (Q || arguments.length === 2) {
        for (var I = 0, G = B.length, Z; I < G; I++)
          if (Z || !(I in B)) {
            if (!Z) Z = Array.prototype.slice.call(B, 0, I);
            Z[I] = B[I]
          }
      }
      return A.concat(Z || Array.prototype.slice.call(B))
    };
  Object.defineProperty(YJ, "__esModule", {
    value: !0
  });
  YJ.findActualExecutable = Z81;
  YJ.spawnDetached = UT1;
  YJ.spawn = Oi;
  YJ.spawnDetachedPromise = Tu9;
  YJ.spawnPromise = Pu9;
  var Li = Z1("path"),
    Lu9 = Z1("net"),
    Ri = Z1("fs"),
    dM = gOA(),
    lOA = cOA(),
    Ru9 = Z1("child_process"),
    Ou9 = _l(),
    aOA = process.platform === "win32",
    Iv = Ou9.default("spawn-rx");

  function iOA(A) {
    try {
      return Ri.statSync(A)
    } catch (B) {
      return null
    }
  }

  function nOA(A) {
    if (A.match(/[\\/]/)) return Iv("Path has slash in directory, bailing"), A;
    var B = Li.join(".", A);
    if (iOA(B)) return Iv("Found executable in currect directory: ".concat(B)), Ri.realpathSync(B);
    var Q = process.env.PATH.split(aOA ? ";" : ":");
    for (var I = 0, G = Q; I < G.length; I++) {
      var Z = G[I],
        D = Li.join(Z, A);
      if (iOA(D)) return Ri.realpathSync(D)
    }
    return Iv("Failed to find executable anywhere in path"), A
  }

  function Z81(A, B) {
    if (process.platform !== "win32") return {
      cmd: nOA(A),
      args: B
    };
    if (!Ri.existsSync(A)) {
      var Q = [".exe", ".bat", ".cmd", ".ps1"];
      for (var I = 0, G = Q; I < G.length; I++) {
        var Z = G[I],
          D = nOA("".concat(A).concat(Z));
        if (Ri.existsSync(D)) return Z81(D, B)
      }
    }
    if (A.match(/\.ps1$/i)) {
      var Y = Li.join(process.env.SYSTEMROOT, "System32", "WindowsPowerShell", "v1.0", "PowerShell.exe"),
        W = ["-ExecutionPolicy", "Unrestricted", "-NoLogo", "-NonInteractive", "-File", A];
      return {
        cmd: Y,
        args: W.concat(B)
      }
    }
    if (A.match(/\.(bat|cmd)$/i)) {
      var Y = Li.join(process.env.SYSTEMROOT, "System32", "cmd.exe"),
        J = Mu9(["/C", A], B, !0);
      return {
        cmd: Y,
        args: J
      }
    }
    if (A.match(/\.(js)$/i)) {
      var Y = process.execPath,
        F = [A];
      return {
        cmd: Y,
        args: F.concat(B)
      }
    }
    return {
      cmd: A,
      args: B
    }
  }

  function UT1(A, B, Q) {
    var I = Z81(A, B !== null && B !== void 0 ? B : []),
      G = I.cmd,
      Z = I.args;
    if (!aOA) return Oi(G, Z, Object.assign({}, Q || {}, {
      detached: !0
    }));
    var D = [G].concat(Z),
      Y = Li.join(__dirname, "..", "..", "vendor", "jobber", "Jobber.exe"),
      W = rF(rF({}, Q !== null && Q !== void 0 ? Q : {}), {
        detached: !0,
        jobber: !0
      });
    return Iv("spawnDetached: ".concat(Y, ", ").concat(D)), Oi(Y, D, W)
  }

  function Oi(A, B, Q) {
    Q = Q !== null && Q !== void 0 ? Q : {};
    var I = new dM.Observable(function(G) {
      var {
        stdin: Z,
        jobber: D,
        split: Y,
        encoding: W
      } = Q, J = qu9(Q, ["stdin", "jobber", "split", "encoding"]), F = Z81(A, B), X = F.cmd, V = F.args;
      Iv("spawning process: ".concat(X, " ").concat(V.join(), ", ").concat(JSON.stringify(J)));
      var C = Ru9.spawn(X, V, J),
        K = function(R) {
          return function(T) {
            if (T.length < 1) return;
            if (Q.echoOutput)(R === "stdout" ? process.stdout : process.stderr).write(T);
            var L = "<< String sent back was too long >>";
            try {
              if (typeof T === "string") L = T.toString();
              else L = T.toString(W || "utf8")
            } catch (_) {
              L = "<< Lost chunk of process output for ".concat(A, " - length was ").concat(T.length, ">>")
            }
            G.next({
              source: R,
              text: L
            })
          }
        },
        E = new dM.Subscription;
      if (Q.stdin)
        if (C.stdin) E.add(Q.stdin.subscribe({
          next: function(R) {
            return C.stdin.write(R)
          },
          error: G.error.bind(G),
          complete: function() {
            return C.stdin.end()
          }
        }));
        else G.error(new Error("opts.stdio conflicts with provided spawn opts.stdin observable, 'pipe' is required"));
      var N = null,
        q = null,
        O = !1;
      if (C.stdout) q = new dM.AsyncSubject, C.stdout.on("data", K("stdout")), C.stdout.on("close", function() {
        q.next(!0), q.complete()
      });
      else q = dM.of(!0);
      if (C.stderr) N = new dM.AsyncSubject, C.stderr.on("data", K("stderr")), C.stderr.on("close", function() {
        N.next(!0), N.complete()
      });
      else N = dM.of(!0);
      return C.on("error", function(R) {
        O = !0, G.error(R)
      }), C.on("close", function(R) {
        O = !0;
        var T = dM.merge(q, N).pipe(lOA.reduce(function(L) {
          return L
        }, !0));
        if (R === 0) T.subscribe(function() {
          return G.complete()
        });
        else T.subscribe(function() {
          var L = new Error("Failed with exit code: ".concat(R));
          L.exitCode = R, L.code = R, G.error(L)
        })
      }), E.add(new dM.Subscription(function() {
        if (O) return;
        if (Iv("Killing process: ".concat(X, " ").concat(V.join())), Q.jobber) Lu9.connect("\\\\.\\pipe\\jobber-".concat(C.pid)), setTimeout(function() {
          return C.kill()
        }, 5000);
        else C.kill()
      })), E
    });
    return Q.split ? I : I.pipe(lOA.map(function(G) {
      return G === null || G === void 0 ? void 0 : G.text
    }))
  }

  function sOA(A) {
    return new Promise(function(B, Q) {
      var I = "";
      A.subscribe({
        next: function(G) {
          return I += G
        },
        error: function(G) {
          var Z = new Error("".concat(I, `
`).concat(G.message));
          if ("exitCode" in G) Z.exitCode = G.exitCode, Z.code = G.exitCode;
          Q(Z)
        },
        complete: function() {
          return B(I)
        }
      })
    })
  }

  function rOA(A) {
    return new Promise(function(B, Q) {
      var I = "",
        G = "";
      A.subscribe({
        next: function(Z) {
          return Z.source === "stdout" ? I += Z.text : G += Z.text
        },
        error: function(Z) {
          var D = new Error("".concat(I, `
`).concat(Z.message));
          if ("exitCode" in Z) D.exitCode = Z.exitCode, D.code = Z.exitCode, D.stdout = I, D.stderr = G;
          Q(D)
        },
        complete: function() {
          return B([I, G])
        }
      })
    })
  }

  function Tu9(A, B, Q) {
    if (Q === null || Q === void 0 ? void 0 : Q.split) return rOA(UT1(A, B, rF(rF({}, Q !== null && Q !== void 0 ? Q : {}), {
      split: !0
    })));
    else return sOA(UT1(A, B, rF(rF({}, Q !== null && Q !== void 0 ? Q : {}), {
      split: !1
    })))
  }

  function Pu9(A, B, Q) {
    if (Q === null || Q === void 0 ? void 0 : Q.split) return rOA(Oi(A, B, rF(rF({}, Q !== null && Q !== void 0 ? Q : {}), {
      split: !0
    })));
    else return sOA(Oi(A, B, rF(rF({}, Q !== null && Q !== void 0 ? Q : {}), {
      split: !1
    })))
  }
})
// @from(Start 1269408, End 1275969)
J81 = z((to5, LT1) => {
  function ITA(A) {
    return Array.isArray(A) ? A : [A]
  }
  var xu9 = void 0,
    qT1 = "",
    BTA = " ",
    $T1 = "\\",
    fu9 = /^\s+$/,
    vu9 = /(?:[^\\]|^)\\$/,
    bu9 = /^\\!/,
    gu9 = /^\\#/,
    hu9 = /\r?\n/g,
    mu9 = /^\.*\/|^\.+$/,
    du9 = /\/$/,
    Dv = "/",
    GTA = "node-ignore";
  if (typeof Symbol !== "undefined") GTA = Symbol.for("node-ignore");
  var ZTA = GTA,
    Ti = (A, B, Q) => {
      return Object.defineProperty(A, B, {
        value: Q
      }), Q
    },
    uu9 = /([0-z])-([0-z])/g,
    DTA = () => !1,
    pu9 = (A) => A.replace(uu9, (B, Q, I) => Q.charCodeAt(0) <= I.charCodeAt(0) ? B : qT1),
    cu9 = (A) => {
      let {
        length: B
      } = A;
      return A.slice(0, B - B % 2)
    },
    lu9 = [
      [/^\uFEFF/, () => qT1],
      [/((?:\\\\)*?)(\\?\s+)$/, (A, B, Q) => B + (Q.indexOf("\\") === 0 ? BTA : qT1)],
      [/(\\+?)\s/g, (A, B) => {
        let {
          length: Q
        } = B;
        return B.slice(0, Q - Q % 2) + BTA
      }],
      [/[\\$.|*+(){^]/g, (A) => `\\${A}`],
      [/(?!\\)\?/g, () => "[^/]"],
      [/^\//, () => "^"],
      [/\//g, () => "\\/"],
      [/^\^*\\\*\\\*\\\//, () => "^(?:.*\\/)?"],
      [/^(?=[^^])/, function A() {
        return !/\/(?!$)/.test(this) ? "(?:^|\\/)" : "^"
      }],
      [/\\\/\\\*\\\*(?=\\\/|$)/g, (A, B, Q) => B + 6 < Q.length ? "(?:\\/[^\\/]+)*" : "\\/.+"],
      [/(^|[^\\]+)(\\\*)+(?=.+)/g, (A, B, Q) => {
        let I = Q.replace(/\\\*/g, "[^\\/]*");
        return B + I
      }],
      [/\\\\\\(?=[$.|*+(){^])/g, () => $T1],
      [/\\\\/g, () => $T1],
      [/(\\)?\[([^\]/]*?)(\\*)($|\])/g, (A, B, Q, I, G) => B === $T1 ? `\\[${Q}${cu9(I)}${G}` : G === "]" ? I.length % 2 === 0 ? `[${pu9(Q)}${I}]` : "[]" : "[]"],
      [/(?:[^*])$/, (A) => /\/$/.test(A) ? `${A}$` : `${A}(?=$|\\/$)`]
    ],
    iu9 = /(^|\\\/)?\\\*$/,
    Pi = "regex",
    Y81 = "checkRegex",
    QTA = "_",
    nu9 = {
      [Pi](A, B) {
        return `${B?`${B}[^/]+`:"[^/]*"}(?=$|\\/$)`
      },
      [Y81](A, B) {
        return `${B?`${B}[^/]*`:"[^/]*"}(?=$|\\/$)`
      }
    },
    au9 = (A) => lu9.reduce((B, [Q, I]) => B.replace(Q, I.bind(A)), A),
    W81 = (A) => typeof A === "string",
    su9 = (A) => A && W81(A) && !fu9.test(A) && !vu9.test(A) && A.indexOf("#") !== 0,
    ru9 = (A) => A.split(hu9).filter(Boolean);
  class YTA {
    constructor(A, B, Q, I, G, Z) {
      this.pattern = A, this.mark = B, this.negative = G, Ti(this, "body", Q), Ti(this, "ignoreCase", I), Ti(this, "regexPrefix", Z)
    }
    get regex() {
      let A = QTA + Pi;
      if (this[A]) return this[A];
      return this._make(Pi, A)
    }
    get checkRegex() {
      let A = QTA + Y81;
      if (this[A]) return this[A];
      return this._make(Y81, A)
    }
    _make(A, B) {
      let Q = this.regexPrefix.replace(iu9, nu9[A]),
        I = this.ignoreCase ? new RegExp(Q, "i") : new RegExp(Q);
      return Ti(this, B, I)
    }
  }
  var ou9 = ({
    pattern: A,
    mark: B
  }, Q) => {
    let I = !1,
      G = A;
    if (G.indexOf("!") === 0) I = !0, G = G.substr(1);
    G = G.replace(bu9, "!").replace(gu9, "#");
    let Z = au9(G);
    return new YTA(A, B, G, Q, I, Z)
  };
  class WTA {
    constructor(A) {
      this._ignoreCase = A, this._rules = []
    }
    _add(A) {
      if (A && A[ZTA]) {
        this._rules = this._rules.concat(A._rules._rules), this._added = !0;
        return
      }
      if (W81(A)) A = {
        pattern: A
      };
      if (su9(A.pattern)) {
        let B = ou9(A, this._ignoreCase);
        this._added = !0, this._rules.push(B)
      }
    }
    add(A) {
      return this._added = !1, ITA(W81(A) ? ru9(A) : A).forEach(this._add, this), this._added
    }
    test(A, B, Q) {
      let I = !1,
        G = !1,
        Z;
      this._rules.forEach((Y) => {
        let {
          negative: W
        } = Y;
        if (G === W && I !== G || W && !I && !G && !B) return;
        if (!Y[Q].test(A)) return;
        I = !W, G = W, Z = W ? xu9 : Y
      });
      let D = {
        ignored: I,
        unignored: G
      };
      if (Z) D.rule = Z;
      return D
    }
  }
  var tu9 = (A, B) => {
      throw new B(A)
    },
    iU = (A, B, Q) => {
      if (!W81(A)) return Q(`path must be a string, but got \`${B}\``, TypeError);
      if (!A) return Q("path must not be empty", TypeError);
      if (iU.isNotRelative(A)) return Q(`path should be a \`path.relative()\`d string, but got "${B}"`, RangeError);
      return !0
    },
    JTA = (A) => mu9.test(A);
  iU.isNotRelative = JTA;
  iU.convert = (A) => A;
  class FTA {
    constructor({
      ignorecase: A = !0,
      ignoreCase: B = A,
      allowRelativePaths: Q = !1
    } = {}) {
      Ti(this, ZTA, !0), this._rules = new WTA(B), this._strictPathCheck = !Q, this._initCache()
    }
    _initCache() {
      this._ignoreCache = Object.create(null), this._testCache = Object.create(null)
    }
    add(A) {
      if (this._rules.add(A)) this._initCache();
      return this
    }
    addPattern(A) {
      return this.add(A)
    }
    _test(A, B, Q, I) {
      let G = A && iU.convert(A);
      return iU(G, A, this._strictPathCheck ? tu9 : DTA), this._t(G, B, Q, I)
    }
    checkIgnore(A) {
      if (!du9.test(A)) return this.test(A);
      let B = A.split(Dv).filter(Boolean);
      if (B.pop(), B.length) {
        let Q = this._t(B.join(Dv) + Dv, this._testCache, !0, B);
        if (Q.ignored) return Q
      }
      return this._rules.test(A, !1, Y81)
    }
    _t(A, B, Q, I) {
      if (A in B) return B[A];
      if (!I) I = A.split(Dv).filter(Boolean);
      if (I.pop(), !I.length) return B[A] = this._rules.test(A, Q, Pi);
      let G = this._t(I.join(Dv) + Dv, B, Q, I);
      return B[A] = G.ignored ? G : this._rules.test(A, Q, Pi)
    }
    ignores(A) {
      return this._test(A, this._ignoreCache, !1).ignored
    }
    createFilter() {
      return (A) => !this.ignores(A)
    }
    filter(A) {
      return ITA(A).filter(this.createFilter())
    }
    test(A) {
      return this._test(A, this._testCache, !0)
    }
  }
  var MT1 = (A) => new FTA(A),
    eu9 = (A) => iU(A && iU.convert(A), A, DTA);
  if (typeof process !== "undefined" && (process.env && process.env.IGNORE_TEST_WIN32 || process.platform === "win32")) {
    let A = (Q) => /^\\\\\?\\/.test(Q) || /["<>|\u0000-\u001F]+/u.test(Q) ? Q : Q.replace(/\\/g, "/");
    iU.convert = A;
    let B = /^[a-z]:\//i;
    iU.isNotRelative = (Q) => B.test(Q) || JTA(Q)
  }
  LT1.exports = MT1;
  MT1.default = MT1;
  LT1.exports.isPathValid = eu9
})
// @from(Start 1275975, End 1309050)
TTA = z((Bt5, OTA) => {
  function ST1(A) {
    if (A instanceof Map) A.clear = A.delete = A.set = function() {
      throw new Error("map is read-only")
    };
    else if (A instanceof Set) A.add = A.clear = A.delete = function() {
      throw new Error("set is read-only")
    };
    return Object.freeze(A), Object.getOwnPropertyNames(A).forEach(function(B) {
      var Q = A[B];
      if (typeof Q == "object" && !Object.isFrozen(Q)) ST1(Q)
    }), A
  }
  var wTA = ST1,
    Ip9 = ST1;
  wTA.default = Ip9;
  class TT1 {
    constructor(A) {
      if (A.data === void 0) A.data = {};
      this.data = A.data, this.isMatchIgnored = !1
    }
    ignoreMatch() {
      this.isMatchIgnored = !0
    }
  }

  function Yv(A) {
    return A.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#x27;")
  }

  function uM(A, ...B) {
    let Q = Object.create(null);
    for (let I in A) Q[I] = A[I];
    return B.forEach(function(I) {
      for (let G in I) Q[G] = I[G]
    }), Q
  }
  var Gp9 = "</span>",
    XTA = (A) => {
      return !!A.kind
    };
  class ETA {
    constructor(A, B) {
      this.buffer = "", this.classPrefix = B.classPrefix, A.walk(this)
    }
    addText(A) {
      this.buffer += Yv(A)
    }
    openNode(A) {
      if (!XTA(A)) return;
      let B = A.kind;
      if (!A.sublanguage) B = `${this.classPrefix}${B}`;
      this.span(B)
    }
    closeNode(A) {
      if (!XTA(A)) return;
      this.buffer += Gp9
    }
    value() {
      return this.buffer
    }
    span(A) {
      this.buffer += `<span class="${A}">`
    }
  }
  class _T1 {
    constructor() {
      this.rootNode = {
        children: []
      }, this.stack = [this.rootNode]
    }
    get top() {
      return this.stack[this.stack.length - 1]
    }
    get root() {
      return this.rootNode
    }
    add(A) {
      this.top.children.push(A)
    }
    openNode(A) {
      let B = {
        kind: A,
        children: []
      };
      this.add(B), this.stack.push(B)
    }
    closeNode() {
      if (this.stack.length > 1) return this.stack.pop();
      return
    }
    closeAllNodes() {
      while (this.closeNode());
    }
    toJSON() {
      return JSON.stringify(this.rootNode, null, 4)
    }
    walk(A) {
      return this.constructor._walk(A, this.rootNode)
    }
    static _walk(A, B) {
      if (typeof B === "string") A.addText(B);
      else if (B.children) A.openNode(B), B.children.forEach((Q) => this._walk(A, Q)), A.closeNode(B);
      return A
    }
    static _collapse(A) {
      if (typeof A === "string") return;
      if (!A.children) return;
      if (A.children.every((B) => typeof B === "string")) A.children = [A.children.join("")];
      else A.children.forEach((B) => {
        _T1._collapse(B)
      })
    }
  }
  class UTA extends _T1 {
    constructor(A) {
      super();
      this.options = A
    }
    addKeyword(A, B) {
      if (A === "") return;
      this.openNode(B), this.addText(A), this.closeNode()
    }
    addText(A) {
      if (A === "") return;
      this.add(A)
    }
    addSublanguage(A, B) {
      let Q = A.root;
      Q.kind = B, Q.sublanguage = !0, this.add(Q)
    }
    toHTML() {
      return new ETA(this, this.options).value()
    }
    finalize() {
      return !0
    }
  }

  function Zp9(A) {
    return new RegExp(A.replace(/[-/\\^$*+?.()|[\]{}]/g, "\\$&"), "m")
  }

  function Si(A) {
    if (!A) return null;
    if (typeof A === "string") return A;
    return A.source
  }

  function Dp9(...A) {
    return A.map((Q) => Si(Q)).join("")
  }

  function Yp9(...A) {
    return "(" + A.map((Q) => Si(Q)).join("|") + ")"
  }

  function Wp9(A) {
    return new RegExp(A.toString() + "|").exec("").length - 1
  }

  function Jp9(A, B) {
    let Q = A && A.exec(B);
    return Q && Q.index === 0
  }
  var Fp9 = /\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;

  function Xp9(A, B = "|") {
    let Q = 0;
    return A.map((I) => {
      Q += 1;
      let G = Q,
        Z = Si(I),
        D = "";
      while (Z.length > 0) {
        let Y = Fp9.exec(Z);
        if (!Y) {
          D += Z;
          break
        }
        if (D += Z.substring(0, Y.index), Z = Z.substring(Y.index + Y[0].length), Y[0][0] === "\\" && Y[1]) D += "\\" + String(Number(Y[1]) + G);
        else if (D += Y[0], Y[0] === "(") Q++
      }
      return D
    }).map((I) => `(${I})`).join(B)
  }
  var Vp9 = /\b\B/,
    NTA = "[a-zA-Z]\\w*",
    jT1 = "[a-zA-Z_]\\w*",
    yT1 = "\\b\\d+(\\.\\d+)?",
    $TA = "(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",
    qTA = "\\b(0b[01]+)",
    Cp9 = "!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",
    Kp9 = (A = {}) => {
      let B = /^#![ ]*\//;
      if (A.binary) A.begin = Dp9(B, /.*\b/, A.binary, /\b.*/);
      return uM({
        className: "meta",
        begin: B,
        end: /$/,
        relevance: 0,
        "on:begin": (Q, I) => {
          if (Q.index !== 0) I.ignoreMatch()
        }
      }, A)
    },
    _i = {
      begin: "\\\\[\\s\\S]",
      relevance: 0
    },
    Hp9 = {
      className: "string",
      begin: "'",
      end: "'",
      illegal: "\\n",
      contains: [_i]
    },
    zp9 = {
      className: "string",
      begin: '"',
      end: '"',
      illegal: "\\n",
      contains: [_i]
    },
    MTA = {
      begin: /\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/
    },
    X81 = function(A, B, Q = {}) {
      let I = uM({
        className: "comment",
        begin: A,
        end: B,
        contains: []
      }, Q);
      return I.contains.push(MTA), I.contains.push({
        className: "doctag",
        begin: "(?:TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):",
        relevance: 0
      }), I
    },
    wp9 = X81("//", "$"),
    Ep9 = X81("/\\*", "\\*/"),
    Up9 = X81("#", "$"),
    Np9 = {
      className: "number",
      begin: yT1,
      relevance: 0
    },
    $p9 = {
      className: "number",
      begin: $TA,
      relevance: 0
    },
    qp9 = {
      className: "number",
      begin: qTA,
      relevance: 0
    },
    Mp9 = {
      className: "number",
      begin: yT1 + "(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",
      relevance: 0
    },
    Lp9 = {
      begin: /(?=\/[^/\n]*\/)/,
      contains: [{
        className: "regexp",
        begin: /\//,
        end: /\/[gimuy]*/,
        illegal: /\n/,
        contains: [_i, {
          begin: /\[/,
          end: /\]/,
          relevance: 0,
          contains: [_i]
        }]
      }]
    },
    Rp9 = {
      className: "title",
      begin: NTA,
      relevance: 0
    },
    Op9 = {
      className: "title",
      begin: jT1,
      relevance: 0
    },
    Tp9 = {
      begin: "\\.\\s*" + jT1,
      relevance: 0
    },
    Pp9 = function(A) {
      return Object.assign(A, {
        "on:begin": (B, Q) => {
          Q.data._beginMatch = B[1]
        },
        "on:end": (B, Q) => {
          if (Q.data._beginMatch !== B[1]) Q.ignoreMatch()
        }
      })
    },
    F81 = Object.freeze({
      __proto__: null,
      MATCH_NOTHING_RE: Vp9,
      IDENT_RE: NTA,
      UNDERSCORE_IDENT_RE: jT1,
      NUMBER_RE: yT1,
      C_NUMBER_RE: $TA,
      BINARY_NUMBER_RE: qTA,
      RE_STARTERS_RE: Cp9,
      SHEBANG: Kp9,
      BACKSLASH_ESCAPE: _i,
      APOS_STRING_MODE: Hp9,
      QUOTE_STRING_MODE: zp9,
      PHRASAL_WORDS_MODE: MTA,
      COMMENT: X81,
      C_LINE_COMMENT_MODE: wp9,
      C_BLOCK_COMMENT_MODE: Ep9,
      HASH_COMMENT_MODE: Up9,
      NUMBER_MODE: Np9,
      C_NUMBER_MODE: $p9,
      BINARY_NUMBER_MODE: qp9,
      CSS_NUMBER_MODE: Mp9,
      REGEXP_MODE: Lp9,
      TITLE_MODE: Rp9,
      UNDERSCORE_TITLE_MODE: Op9,
      METHOD_GUARD: Tp9,
      END_SAME_AS_BEGIN: Pp9
    });

  function Sp9(A, B) {
    if (A.input[A.index - 1] === ".") B.ignoreMatch()
  }

  function _p9(A, B) {
    if (!B) return;
    if (!A.beginKeywords) return;
    if (A.begin = "\\b(" + A.beginKeywords.split(" ").join("|") + ")(?!\\.)(?=\\b|\\s)", A.__beforeBegin = Sp9, A.keywords = A.keywords || A.beginKeywords, delete A.beginKeywords, A.relevance === void 0) A.relevance = 0
  }

  function jp9(A, B) {
    if (!Array.isArray(A.illegal)) return;
    A.illegal = Yp9(...A.illegal)
  }

  function yp9(A, B) {
    if (!A.match) return;
    if (A.begin || A.end) throw new Error("begin & end are not supported with match");
    A.begin = A.match, delete A.match
  }

  function kp9(A, B) {
    if (A.relevance === void 0) A.relevance = 1
  }
  var xp9 = ["of", "and", "for", "in", "not", "or", "if", "then", "parent", "list", "value"],
    fp9 = "keyword";

  function LTA(A, B, Q = fp9) {
    let I = {};
    if (typeof A === "string") G(Q, A.split(" "));
    else if (Array.isArray(A)) G(Q, A);
    else Object.keys(A).forEach(function(Z) {
      Object.assign(I, LTA(A[Z], B, Z))
    });
    return I;

    function G(Z, D) {
      if (B) D = D.map((Y) => Y.toLowerCase());
      D.forEach(function(Y) {
        let W = Y.split("|");
        I[W[0]] = [Z, vp9(W[0], W[1])]
      })
    }
  }

  function vp9(A, B) {
    if (B) return Number(B);
    return bp9(A) ? 0 : 1
  }

  function bp9(A) {
    return xp9.includes(A.toLowerCase())
  }

  function gp9(A, {
    plugins: B
  }) {
    function Q(Y, W) {
      return new RegExp(Si(Y), "m" + (A.case_insensitive ? "i" : "") + (W ? "g" : ""))
    }
    class I {
      constructor() {
        this.matchIndexes = {}, this.regexes = [], this.matchAt = 1, this.position = 0
      }
      addRule(Y, W) {
        W.position = this.position++, this.matchIndexes[this.matchAt] = W, this.regexes.push([W, Y]), this.matchAt += Wp9(Y) + 1
      }
      compile() {
        if (this.regexes.length === 0) this.exec = () => null;
        let Y = this.regexes.map((W) => W[1]);
        this.matcherRe = Q(Xp9(Y), !0), this.lastIndex = 0
      }
      exec(Y) {
        this.matcherRe.lastIndex = this.lastIndex;
        let W = this.matcherRe.exec(Y);
        if (!W) return null;
        let J = W.findIndex((X, V) => V > 0 && X !== void 0),
          F = this.matchIndexes[J];
        return W.splice(0, J), Object.assign(W, F)
      }
    }
    class G {
      constructor() {
        this.rules = [], this.multiRegexes = [], this.count = 0, this.lastIndex = 0, this.regexIndex = 0
      }
      getMatcher(Y) {
        if (this.multiRegexes[Y]) return this.multiRegexes[Y];
        let W = new I;
        return this.rules.slice(Y).forEach(([J, F]) => W.addRule(J, F)), W.compile(), this.multiRegexes[Y] = W, W
      }
      resumingScanAtSamePosition() {
        return this.regexIndex !== 0
      }
      considerAll() {
        this.regexIndex = 0
      }
      addRule(Y, W) {
        if (this.rules.push([Y, W]), W.type === "begin") this.count++
      }
      exec(Y) {
        let W = this.getMatcher(this.regexIndex);
        W.lastIndex = this.lastIndex;
        let J = W.exec(Y);
        if (this.resumingScanAtSamePosition())
          if (J && J.index === this.lastIndex);
          else {
            let F = this.getMatcher(0);
            F.lastIndex = this.lastIndex + 1, J = F.exec(Y)
          } if (J) {
          if (this.regexIndex += J.position + 1, this.regexIndex === this.count) this.considerAll()
        }
        return J
      }
    }

    function Z(Y) {
      let W = new G;
      if (Y.contains.forEach((J) => W.addRule(J.begin, {
          rule: J,
          type: "begin"
        })), Y.terminatorEnd) W.addRule(Y.terminatorEnd, {
        type: "end"
      });
      if (Y.illegal) W.addRule(Y.illegal, {
        type: "illegal"
      });
      return W
    }

    function D(Y, W) {
      let J = Y;
      if (Y.isCompiled) return J;
      [yp9].forEach((X) => X(Y, W)), A.compilerExtensions.forEach((X) => X(Y, W)), Y.__beforeBegin = null, [_p9, jp9, kp9].forEach((X) => X(Y, W)), Y.isCompiled = !0;
      let F = null;
      if (typeof Y.keywords === "object") F = Y.keywords.$pattern, delete Y.keywords.$pattern;
      if (Y.keywords) Y.keywords = LTA(Y.keywords, A.case_insensitive);
      if (Y.lexemes && F) throw new Error("ERR: Prefer `keywords.$pattern` to `mode.lexemes`, BOTH are not allowed. (see mode reference) ");
      if (F = F || Y.lexemes || /\w+/, J.keywordPatternRe = Q(F, !0), W) {
        if (!Y.begin) Y.begin = /\B|\b/;
        if (J.beginRe = Q(Y.begin), Y.endSameAsBegin) Y.end = Y.begin;
        if (!Y.end && !Y.endsWithParent) Y.end = /\B|\b/;
        if (Y.end) J.endRe = Q(Y.end);
        if (J.terminatorEnd = Si(Y.end) || "", Y.endsWithParent && W.terminatorEnd) J.terminatorEnd += (Y.end ? "|" : "") + W.terminatorEnd
      }
      if (Y.illegal) J.illegalRe = Q(Y.illegal);
      if (!Y.contains) Y.contains = [];
      if (Y.contains = [].concat(...Y.contains.map(function(X) {
          return hp9(X === "self" ? Y : X)
        })), Y.contains.forEach(function(X) {
          D(X, J)
        }), Y.starts) D(Y.starts, W);
      return J.matcher = Z(J), J
    }
    if (!A.compilerExtensions) A.compilerExtensions = [];
    if (A.contains && A.contains.includes("self")) throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");
    return A.classNameAliases = uM(A.classNameAliases || {}), D(A)
  }

  function RTA(A) {
    if (!A) return !1;
    return A.endsWithParent || RTA(A.starts)
  }

  function hp9(A) {
    if (A.variants && !A.cachedVariants) A.cachedVariants = A.variants.map(function(B) {
      return uM(A, {
        variants: null
      }, B)
    });
    if (A.cachedVariants) return A.cachedVariants;
    if (RTA(A)) return uM(A, {
      starts: A.starts ? uM(A.starts) : null
    });
    if (Object.isFrozen(A)) return uM(A);
    return A
  }
  var mp9 = "10.7.3";

  function dp9(A) {
    return Boolean(A || A === "")
  }

  function up9(A) {
    let B = {
      props: ["language", "code", "autodetect"],
      data: function() {
        return {
          detectedLanguage: "",
          unknownLanguage: !1
        }
      },
      computed: {
        className() {
          if (this.unknownLanguage) return "";
          return "hljs " + this.detectedLanguage
        },
        highlighted() {
          if (!this.autoDetect && !A.getLanguage(this.language)) return console.warn(`The language "${this.language}" you specified could not be found.`), this.unknownLanguage = !0, Yv(this.code);
          let I = {};
          if (this.autoDetect) I = A.highlightAuto(this.code), this.detectedLanguage = I.language;
          else I = A.highlight(this.language, this.code, this.ignoreIllegals), this.detectedLanguage = this.language;
          return I.value
        },
        autoDetect() {
          return !this.language || dp9(this.autodetect)
        },
        ignoreIllegals() {
          return !0
        }
      },
      render(I) {
        return I("pre", {}, [I("code", {
          class: this.className,
          domProps: {
            innerHTML: this.highlighted
          }
        })])
      }
    };
    return {
      Component: B,
      VuePlugin: {
        install(I) {
          I.component("highlightjs", B)
        }
      }
    }
  }
  var pp9 = {
    "after:highlightElement": ({
      el: A,
      result: B,
      text: Q
    }) => {
      let I = VTA(A);
      if (!I.length) return;
      let G = document.createElement("div");
      G.innerHTML = B.value, B.value = cp9(I, VTA(G), Q)
    }
  };

  function PT1(A) {
    return A.nodeName.toLowerCase()
  }

  function VTA(A) {
    let B = [];
    return function Q(I, G) {
      for (let Z = I.firstChild; Z; Z = Z.nextSibling)
        if (Z.nodeType === 3) G += Z.nodeValue.length;
        else if (Z.nodeType === 1) {
        if (B.push({
            event: "start",
            offset: G,
            node: Z
          }), G = Q(Z, G), !PT1(Z).match(/br|hr|img|input/)) B.push({
          event: "stop",
          offset: G,
          node: Z
        })
      }
      return G
    }(A, 0), B
  }

  function cp9(A, B, Q) {
    let I = 0,
      G = "",
      Z = [];

    function D() {
      if (!A.length || !B.length) return A.length ? A : B;
      if (A[0].offset !== B[0].offset) return A[0].offset < B[0].offset ? A : B;
      return B[0].event === "start" ? A : B
    }

    function Y(F) {
      function X(V) {
        return " " + V.nodeName + '="' + Yv(V.value) + '"'
      }
      G += "<" + PT1(F) + [].map.call(F.attributes, X).join("") + ">"
    }

    function W(F) {
      G += "</" + PT1(F) + ">"
    }

    function J(F) {
      (F.event === "start" ? Y : W)(F.node)
    }
    while (A.length || B.length) {
      let F = D();
      if (G += Yv(Q.substring(I, F[0].offset)), I = F[0].offset, F === A) {
        Z.reverse().forEach(W);
        do J(F.splice(0, 1)[0]), F = D(); while (F === A && F.length && F[0].offset === I);
        Z.reverse().forEach(Y)
      } else {
        if (F[0].event === "start") Z.push(F[0].node);
        else Z.pop();
        J(F.splice(0, 1)[0])
      }
    }
    return G + Yv(Q.substr(I))
  }
  var CTA = {},
    RT1 = (A) => {
      console.error(A)
    },
    KTA = (A, ...B) => {
      console.log(`WARN: ${A}`, ...B)
    },
    oF = (A, B) => {
      if (CTA[`${A}/${B}`]) return;
      console.log(`Deprecated as of ${A}. ${B}`), CTA[`${A}/${B}`] = !0
    },
    OT1 = Yv,
    HTA = uM,
    zTA = Symbol("nomatch"),
    lp9 = function(A) {
      let B = Object.create(null),
        Q = Object.create(null),
        I = [],
        G = !0,
        Z = /(^(<[^>]+>|\t|)+|\n)/gm,
        D = "Could not find the language '{}', did you forget to load/include a language module?",
        Y = {
          disableAutodetect: !0,
          name: "Plain text",
          contains: []
        },
        W = {
          noHighlightRe: /^(no-?highlight)$/i,
          languageDetectRe: /\blang(?:uage)?-([\w-]+)\b/i,
          classPrefix: "hljs-",
          tabReplace: null,
          useBR: !1,
          languages: null,
          __emitter: UTA
        };

      function J(Q1) {
        return W.noHighlightRe.test(Q1)
      }

      function F(Q1) {
        let v1 = Q1.className + " ";
        v1 += Q1.parentNode ? Q1.parentNode.className : "";
        let L1 = W.languageDetectRe.exec(v1);
        if (L1) {
          let BA = D1(L1[1]);
          if (!BA) KTA(D.replace("{}", L1[1])), KTA("Falling back to no-highlight mode for this block.", Q1);
          return BA ? L1[1] : "no-highlight"
        }
        return v1.split(/\s+/).find((BA) => J(BA) || D1(BA))
      }

      function X(Q1, v1, L1, BA) {
        let HA = "",
          MA = "";
        if (typeof v1 === "object") HA = Q1, L1 = v1.ignoreIllegals, MA = v1.language, BA = void 0;
        else oF("10.7.0", "highlight(lang, code, ...args) has been deprecated."), oF("10.7.0", `Please use highlight(code, options) instead.
https://github.com/highlightjs/highlight.js/issues/2277`), MA = Q1, HA = v1;
        let t = {
          code: HA,
          language: MA
        };
        bA("before:highlight", t);
        let B1 = t.result ? t.result : V(t.language, t.code, L1, BA);
        return B1.code = t.code, bA("after:highlight", B1), B1
      }

      function V(Q1, v1, L1, BA) {
        function HA(E0, g0) {
          let y0 = QA.case_insensitive ? g0[0].toLowerCase() : g0[0];
          return Object.prototype.hasOwnProperty.call(E0.keywords, y0) && E0.keywords[y0]
        }

        function MA() {
          if (!fA.keywords) {
            k2.addText(s0);
            return
          }
          let E0 = 0;
          fA.keywordPatternRe.lastIndex = 0;
          let g0 = fA.keywordPatternRe.exec(s0),
            y0 = "";
          while (g0) {
            y0 += s0.substring(E0, g0.index);
            let T0 = HA(fA, g0);
            if (T0) {
              let [V0, N2] = T0;
              if (k2.addText(y0), y0 = "", q2 += N2, V0.startsWith("_")) y0 += g0[0];
              else {
                let h9 = QA.classNameAliases[V0] || V0;
                k2.addKeyword(g0[0], h9)
              }
            } else y0 += g0[0];
            E0 = fA.keywordPatternRe.lastIndex, g0 = fA.keywordPatternRe.exec(s0)
          }
          y0 += s0.substr(E0), k2.addText(y0)
        }

        function t() {
          if (s0 === "") return;
          let E0 = null;
          if (typeof fA.subLanguage === "string") {
            if (!B[fA.subLanguage]) {
              k2.addText(s0);
              return
            }
            E0 = V(fA.subLanguage, s0, !0, H0[fA.subLanguage]), H0[fA.subLanguage] = E0.top
          } else E0 = K(s0, fA.subLanguage.length ? fA.subLanguage : null);
          if (fA.relevance > 0) q2 += E0.relevance;
          k2.addSublanguage(E0.emitter, E0.language)
        }

        function B1() {
          if (fA.subLanguage != null) t();
          else MA();
          s0 = ""
        }

        function W1(E0) {
          if (E0.className) k2.openNode(QA.classNameAliases[E0.className] || E0.className);
          return fA = Object.create(E0, {
            parent: {
              value: fA
            }
          }), fA
        }

        function w1(E0, g0, y0) {
          let T0 = Jp9(E0.endRe, y0);
          if (T0) {
            if (E0["on:end"]) {
              let V0 = new TT1(E0);
              if (E0["on:end"](g0, V0), V0.isMatchIgnored) T0 = !1
            }
            if (T0) {
              while (E0.endsParent && E0.parent) E0 = E0.parent;
              return E0
            }
          }
          if (E0.endsWithParent) return w1(E0.parent, g0, y0)
        }

        function P1(E0) {
          if (fA.matcher.regexIndex === 0) return s0 += E0[0], 1;
          else return w6 = !0, 0
        }

        function e(E0) {
          let g0 = E0[0],
            y0 = E0.rule,
            T0 = new TT1(y0),
            V0 = [y0.__beforeBegin, y0["on:begin"]];
          for (let N2 of V0) {
            if (!N2) continue;
            if (N2(E0, T0), T0.isMatchIgnored) return P1(g0)
          }
          if (y0 && y0.endSameAsBegin) y0.endRe = Zp9(g0);
          if (y0.skip) s0 += g0;
          else {
            if (y0.excludeBegin) s0 += g0;
            if (B1(), !y0.returnBegin && !y0.excludeBegin) s0 = g0
          }
          return W1(y0), y0.returnBegin ? 0 : g0.length
        }

        function y1(E0) {
          let g0 = E0[0],
            y0 = v1.substr(E0.index),
            T0 = w1(fA, E0, y0);
          if (!T0) return zTA;
          let V0 = fA;
          if (V0.skip) s0 += g0;
          else {
            if (!(V0.returnEnd || V0.excludeEnd)) s0 += g0;
            if (B1(), V0.excludeEnd) s0 = g0
          }
          do {
            if (fA.className) k2.closeNode();
            if (!fA.skip && !fA.subLanguage) q2 += fA.relevance;
            fA = fA.parent
          } while (fA !== T0.parent);
          if (T0.starts) {
            if (T0.endSameAsBegin) T0.starts.endRe = T0.endRe;
            W1(T0.starts)
          }
          return V0.returnEnd ? 0 : g0.length
        }

        function O1() {
          let E0 = [];
          for (let g0 = fA; g0 !== QA; g0 = g0.parent)
            if (g0.className) E0.unshift(g0.className);
          E0.forEach((g0) => k2.openNode(g0))
        }
        let h1 = {};

        function o1(E0, g0) {
          let y0 = g0 && g0[0];
          if (s0 += E0, y0 == null) return B1(), 0;
          if (h1.type === "begin" && g0.type === "end" && h1.index === g0.index && y0 === "") {
            if (s0 += v1.slice(g0.index, g0.index + 1), !G) {
              let T0 = new Error("0 width match regex");
              throw T0.languageName = Q1, T0.badRule = h1.rule, T0
            }
            return 1
          }
          if (h1 = g0, g0.type === "begin") return e(g0);
          else if (g0.type === "illegal" && !L1) {
            let T0 = new Error('Illegal lexeme "' + y0 + '" for mode "' + (fA.className || "<unnamed>") + '"');
            throw T0.mode = fA, T0
          } else if (g0.type === "end") {
            let T0 = y1(g0);
            if (T0 !== zTA) return T0
          }
          if (g0.type === "illegal" && y0 === "") return 1;
          if (j9 > 1e5 && j9 > g0.index * 3) throw new Error("potential infinite loop, way more iterations than matches");
          return s0 += y0, y0.length
        }
        let QA = D1(Q1);
        if (!QA) throw RT1(D.replace("{}", Q1)), new Error('Unknown language: "' + Q1 + '"');
        let zA = gp9(QA, {
            plugins: I
          }),
          Y0 = "",
          fA = BA || zA,
          H0 = {},
          k2 = new W.__emitter(W);
        O1();
        let s0 = "",
          q2 = 0,
          h2 = 0,
          j9 = 0,
          w6 = !1;
        try {
          fA.matcher.considerAll();
          for (;;) {
            if (j9++, w6) w6 = !1;
            else fA.matcher.considerAll();
            fA.matcher.lastIndex = h2;
            let E0 = fA.matcher.exec(v1);
            if (!E0) break;
            let g0 = v1.substring(h2, E0.index),
              y0 = o1(g0, E0);
            h2 = E0.index + y0
          }
          return o1(v1.substr(h2)), k2.closeAllNodes(), k2.finalize(), Y0 = k2.toHTML(), {
            relevance: Math.floor(q2),
            value: Y0,
            language: Q1,
            illegal: !1,
            emitter: k2,
            top: fA
          }
        } catch (E0) {
          if (E0.message && E0.message.includes("Illegal")) return {
            illegal: !0,
            illegalBy: {
              msg: E0.message,
              context: v1.slice(h2 - 100, h2 + 100),
              mode: E0.mode
            },
            sofar: Y0,
            relevance: 0,
            value: OT1(v1),
            emitter: k2
          };
          else if (G) return {
            illegal: !1,
            relevance: 0,
            value: OT1(v1),
            emitter: k2,
            language: Q1,
            top: fA,
            errorRaised: E0
          };
          else throw E0
        }
      }

      function C(Q1) {
        let v1 = {
          relevance: 0,
          emitter: new W.__emitter(W),
          value: OT1(Q1),
          illegal: !1,
          top: Y
        };
        return v1.emitter.addText(Q1), v1
      }

      function K(Q1, v1) {
        v1 = v1 || W.languages || Object.keys(B);
        let L1 = C(Q1),
          BA = v1.filter(D1).filter(u1).map((W1) => V(W1, Q1, !1));
        BA.unshift(L1);
        let HA = BA.sort((W1, w1) => {
            if (W1.relevance !== w1.relevance) return w1.relevance - W1.relevance;
            if (W1.language && w1.language) {
              if (D1(W1.language).supersetOf === w1.language) return 1;
              else if (D1(w1.language).supersetOf === W1.language) return -1
            }
            return 0
          }),
          [MA, t] = HA,
          B1 = MA;
        return B1.second_best = t, B1
      }

      function E(Q1) {
        if (!(W.tabReplace || W.useBR)) return Q1;
        return Q1.replace(Z, (v1) => {
          if (v1 === `
`) return W.useBR ? "<br>" : v1;
          else if (W.tabReplace) return v1.replace(/\t/g, W.tabReplace);
          return v1
        })
      }

      function N(Q1, v1, L1) {
        let BA = v1 ? Q[v1] : L1;
        if (Q1.classList.add("hljs"), BA) Q1.classList.add(BA)
      }
      let q = {
          "before:highlightElement": ({
            el: Q1
          }) => {
            if (W.useBR) Q1.innerHTML = Q1.innerHTML.replace(/\n/g, "").replace(/<br[ /]*>/g, `
`)
          },
          "after:highlightElement": ({
            result: Q1
          }) => {
            if (W.useBR) Q1.value = Q1.value.replace(/\n/g, "<br>")
          }
        },
        O = /^(<[^>]+>|\t)+/gm,
        R = {
          "after:highlightElement": ({
            result: Q1
          }) => {
            if (W.tabReplace) Q1.value = Q1.value.replace(O, (v1) => v1.replace(/\t/g, W.tabReplace))
          }
        };

      function T(Q1) {
        let v1 = null,
          L1 = F(Q1);
        if (J(L1)) return;
        bA("before:highlightElement", {
          el: Q1,
          language: L1
        }), v1 = Q1;
        let BA = v1.textContent,
          HA = L1 ? X(BA, {
            language: L1,
            ignoreIllegals: !0
          }) : K(BA);
        if (bA("after:highlightElement", {
            el: Q1,
            result: HA,
            text: BA
          }), Q1.innerHTML = HA.value, N(Q1, L1, HA.language), Q1.result = {
            language: HA.language,
            re: HA.relevance,
            relavance: HA.relevance
          }, HA.second_best) Q1.second_best = {
          language: HA.second_best.language,
          re: HA.second_best.relevance,
          relavance: HA.second_best.relevance
        }
      }

      function L(Q1) {
        if (Q1.useBR) oF("10.3.0", "'useBR' will be removed entirely in v11.0"), oF("10.3.0", "Please see https://github.com/highlightjs/highlight.js/issues/2559");
        W = HTA(W, Q1)
      }
      let _ = () => {
        if (_.called) return;
        _.called = !0, oF("10.6.0", "initHighlighting() is deprecated.  Use highlightAll() instead."), document.querySelectorAll("pre code").forEach(T)
      };

      function k() {
        oF("10.6.0", "initHighlightingOnLoad() is deprecated.  Use highlightAll() instead."), i = !0
      }
      let i = !1;

      function x() {
        if (document.readyState === "loading") {
          i = !0;
          return
        }
        document.querySelectorAll("pre code").forEach(T)
      }

      function s() {
        if (i) x()
      }
      if (typeof window !== "undefined" && window.addEventListener) window.addEventListener("DOMContentLoaded", s, !1);

      function d(Q1, v1) {
        let L1 = null;
        try {
          L1 = v1(A)
        } catch (BA) {
          if (RT1("Language definition for '{}' could not be registered.".replace("{}", Q1)), !G) throw BA;
          else RT1(BA);
          L1 = Y
        }
        if (!L1.name) L1.name = Q1;
        if (B[Q1] = L1, L1.rawDefinition = v1.bind(null, A), L1.aliases) N1(L1.aliases, {
          languageName: Q1
        })
      }

      function F1(Q1) {
        delete B[Q1];
        for (let v1 of Object.keys(Q))
          if (Q[v1] === Q1) delete Q[v1]
      }

      function X1() {
        return Object.keys(B)
      }

      function v(Q1) {
        oF("10.4.0", "requireLanguage will be removed entirely in v11."), oF("10.4.0", "Please see https://github.com/highlightjs/highlight.js/pull/2844");
        let v1 = D1(Q1);
        if (v1) return v1;
        throw new Error("The '{}' language is required, but not loaded.".replace("{}", Q1))
      }

      function D1(Q1) {
        return Q1 = (Q1 || "").toLowerCase(), B[Q1] || B[Q[Q1]]
      }

      function N1(Q1, {
        languageName: v1
      }) {
        if (typeof Q1 === "string") Q1 = [Q1];
        Q1.forEach((L1) => {
          Q[L1.toLowerCase()] = v1
        })
      }

      function u1(Q1) {
        let v1 = D1(Q1);
        return v1 && !v1.disableAutodetect
      }

      function d1(Q1) {
        if (Q1["before:highlightBlock"] && !Q1["before:highlightElement"]) Q1["before:highlightElement"] = (v1) => {
          Q1["before:highlightBlock"](Object.assign({
            block: v1.el
          }, v1))
        };
        if (Q1["after:highlightBlock"] && !Q1["after:highlightElement"]) Q1["after:highlightElement"] = (v1) => {
          Q1["after:highlightBlock"](Object.assign({
            block: v1.el
          }, v1))
        }
      }

      function YA(Q1) {
        d1(Q1), I.push(Q1)
      }

      function bA(Q1, v1) {
        let L1 = Q1;
        I.forEach(function(BA) {
          if (BA[L1]) BA[L1](v1)
        })
      }

      function e1(Q1) {
        return oF("10.2.0", "fixMarkup will be removed entirely in v11.0"), oF("10.2.0", "Please see https://github.com/highlightjs/highlight.js/issues/2534"), E(Q1)
      }

      function k1(Q1) {
        return oF("10.7.0", "highlightBlock will be removed entirely in v12.0"), oF("10.7.0", "Please use highlightElement now."), T(Q1)
      }
      Object.assign(A, {
        highlight: X,
        highlightAuto: K,
        highlightAll: x,
        fixMarkup: e1,
        highlightElement: T,
        highlightBlock: k1,
        configure: L,
        initHighlighting: _,
        initHighlightingOnLoad: k,
        registerLanguage: d,
        unregisterLanguage: F1,
        listLanguages: X1,
        getLanguage: D1,
        registerAliases: N1,
        requireLanguage: v,
        autoDetection: u1,
        inherit: HTA,
        addPlugin: YA,
        vuePlugin: up9(A).VuePlugin
      }), A.debugMode = function() {
        G = !1
      }, A.safeMode = function() {
        G = !0
      }, A.versionString = mp9;
      for (let Q1 in F81)
        if (typeof F81[Q1] === "object") wTA(F81[Q1]);
      return Object.assign(A, F81), A.addPlugin(q), A.addPlugin(pp9), A.addPlugin(R), A
    },
    ip9 = lp9({});
  OTA.exports = ip9
})
// @from(Start 1309056, End 1340636)
STA = z((Qt5, PTA) => {
  function np9(A) {
    var B = "[A-Za-zА-Яа-яёЁ_][A-Za-zА-Яа-яёЁ_0-9]+",
      Q = "далее ",
      I = "возврат вызватьисключение выполнить для если и из или иначе иначеесли исключение каждого конецесли " + "конецпопытки конеццикла не новый перейти перем по пока попытка прервать продолжить тогда цикл экспорт ",
      G = Q + I,
      Z = "загрузитьизфайла ",
      D = "вебклиент вместо внешнеесоединение клиент конецобласти мобильноеприложениеклиент мобильноеприложениесервер " + "наклиенте наклиентенасервере наклиентенасерверебезконтекста насервере насерверебезконтекста область перед " + "после сервер толстыйклиентобычноеприложение толстыйклиентуправляемоеприложение тонкийклиент ",
      Y = Z + D,
      W = "разделительстраниц разделительстрок символтабуляции ",
      J = "ansitooem oemtoansi ввестивидсубконто ввестиперечисление ввестипериод ввестиплансчетов выбранныйплансчетов " + "датагод датамесяц датачисло заголовоксистемы значениевстроку значениеизстроки каталогиб каталогпользователя " + "кодсимв конгода конецпериодаби конецрассчитанногопериодаби конецстандартногоинтервала конквартала конмесяца " + "коннедели лог лог10 максимальноеколичествосубконто названиеинтерфейса названиенабораправ назначитьвид " + "назначитьсчет найтиссылки началопериодаби началостандартногоинтервала начгода начквартала начмесяца " + "начнедели номерднягода номерднянедели номернеделигода обработкаожидания основнойжурналрасчетов " + "основнойплансчетов основнойязык очиститьокносообщений периодстр получитьвремята получитьдатута " + "получитьдокументта получитьзначенияотбора получитьпозициюта получитьпустоезначение получитьта " + "префиксавтонумерации пропись пустоезначение разм разобратьпозициюдокумента рассчитатьрегистрына " + "рассчитатьрегистрыпо симв создатьобъект статусвозврата стрколичествострок сформироватьпозициюдокумента " + "счетпокоду текущеевремя типзначения типзначениястр установитьтана установитьтапо фиксшаблон шаблон ",
      F = "acos asin atan base64значение base64строка cos exp log log10 pow sin sqrt tan xmlзначение xmlстрока " + "xmlтип xmlтипзнч активноеокно безопасныйрежим безопасныйрежимразделенияданных булево ввестидату ввестизначение " + "ввестистроку ввестичисло возможностьчтенияxml вопрос восстановитьзначение врег выгрузитьжурналрегистрации " + "выполнитьобработкуоповещения выполнитьпроверкуправдоступа вычислить год данныеформывзначение дата день деньгода " + "деньнедели добавитьмесяц заблокироватьданныедляредактирования заблокироватьработупользователя завершитьработусистемы " + "загрузитьвнешнююкомпоненту закрытьсправку записатьjson записатьxml записатьдатуjson записьжурналарегистрации " + "заполнитьзначениясвойств запроситьразрешениепользователя запуститьприложение запуститьсистему зафиксироватьтранзакцию " + "значениевданныеформы значениевстрокувнутр значениевфайл значениезаполнено значениеизстрокивнутр значениеизфайла " + "изxmlтипа импортмоделиxdto имякомпьютера имяпользователя инициализироватьпредопределенныеданные информацияобошибке " + "каталогбиблиотекимобильногоустройства каталогвременныхфайлов каталогдокументов каталогпрограммы кодироватьстроку " + "кодлокализацииинформационнойбазы кодсимвола командасистемы конецгода конецдня конецквартала конецмесяца конецминуты " + "конецнедели конецчаса конфигурациябазыданныхизмененадинамически конфигурацияизменена копироватьданныеформы " + "копироватьфайл краткоепредставлениеошибки лев макс местноевремя месяц мин минута монопольныйрежим найти " + "найтинедопустимыесимволыxml найтиокнопонавигационнойссылке найтипомеченныенаудаление найтипоссылкам найтифайлы " + "началогода началодня началоквартала началомесяца началоминуты началонедели началочаса начатьзапросразрешенияпользователя " + "начатьзапускприложения начатькопированиефайла начатьперемещениефайла начатьподключениевнешнейкомпоненты " + "начатьподключениерасширенияработыскриптографией начатьподключениерасширенияработысфайлами начатьпоискфайлов " + "начатьполучениекаталогавременныхфайлов начатьполучениекаталогадокументов начатьполучениерабочегокаталогаданныхпользователя " + "начатьполучениефайлов начатьпомещениефайла начатьпомещениефайлов начатьсозданиедвоичныхданныхизфайла начатьсозданиекаталога " + "начатьтранзакцию начатьудалениефайлов начатьустановкувнешнейкомпоненты начатьустановкурасширенияработыскриптографией " + "начатьустановкурасширенияработысфайлами неделягода необходимостьзавершениясоединения номерсеансаинформационнойбазы " + "номерсоединенияинформационнойбазы нрег нстр обновитьинтерфейс обновитьнумерациюобъектов обновитьповторноиспользуемыезначения " + "обработкапрерыванияпользователя объединитьфайлы окр описаниеошибки оповестить оповеститьобизменении " + "отключитьобработчикзапросанастроекклиенталицензирования отключитьобработчикожидания отключитьобработчикоповещения " + "открытьзначение открытьиндекссправки открытьсодержаниесправки открытьсправку открытьформу открытьформумодально " + "отменитьтранзакцию очиститьжурналрегистрации очиститьнастройкипользователя очиститьсообщения параметрыдоступа " + "перейтипонавигационнойссылке переместитьфайл подключитьвнешнююкомпоненту " + "подключитьобработчикзапросанастроекклиенталицензирования подключитьобработчикожидания подключитьобработчикоповещения " + "подключитьрасширениеработыскриптографией подключитьрасширениеработысфайлами подробноепредставлениеошибки " + "показатьвводдаты показатьвводзначения показатьвводстроки показатьвводчисла показатьвопрос показатьзначение " + "показатьинформациюобошибке показатьнакарте показатьоповещениепользователя показатьпредупреждение полноеимяпользователя " + "получитьcomобъект получитьxmlтип получитьадреспоместоположению получитьблокировкусеансов получитьвремязавершенияспящегосеанса " + "получитьвремязасыпанияпассивногосеанса получитьвремяожиданияблокировкиданных получитьданныевыбора " + "получитьдополнительныйпараметрклиенталицензирования получитьдопустимыекодылокализации получитьдопустимыечасовыепояса " + "получитьзаголовокклиентскогоприложения получитьзаголовоксистемы получитьзначенияотборажурналарегистрации " + "получитьидентификаторконфигурации получитьизвременногохранилища получитьимявременногофайла " + "получитьимяклиенталицензирования получитьинформациюэкрановклиента получитьиспользованиежурналарегистрации " + "получитьиспользованиесобытияжурналарегистрации получитькраткийзаголовокприложения получитьмакетоформления " + "получитьмаскувсефайлы получитьмаскувсефайлыклиента получитьмаскувсефайлысервера получитьместоположениепоадресу " + "получитьминимальнуюдлинупаролейпользователей получитьнавигационнуюссылку получитьнавигационнуюссылкуинформационнойбазы " + "получитьобновлениеконфигурациибазыданных получитьобновлениепредопределенныхданныхинформационнойбазы получитьобщиймакет " + "получитьобщуюформу получитьокна получитьоперативнуюотметкувремени получитьотключениебезопасногорежима " + "получитьпараметрыфункциональныхопцийинтерфейса получитьполноеимяпредопределенногозначения " + "получитьпредставлениянавигационныхссылок получитьпроверкусложностипаролейпользователей получитьразделительпути " + "получитьразделительпутиклиента получитьразделительпутисервера получитьсеансыинформационнойбазы " + "получитьскоростьклиентскогосоединения получитьсоединенияинформационнойбазы получитьсообщенияпользователю " + "получитьсоответствиеобъектаиформы получитьсоставстандартногоинтерфейсаodata получитьструктурухранениябазыданных " + "получитьтекущийсеансинформационнойбазы получитьфайл получитьфайлы получитьформу получитьфункциональнуюопцию " + "получитьфункциональнуюопциюинтерфейса получитьчасовойпоясинформационнойбазы пользователиос поместитьвовременноехранилище " + "поместитьфайл поместитьфайлы прав праводоступа предопределенноезначение представлениекодалокализации представлениепериода " + "представлениеправа представлениеприложения представлениесобытияжурналарегистрации представлениечасовогопояса предупреждение " + "прекратитьработусистемы привилегированныйрежим продолжитьвызов прочитатьjson прочитатьxml прочитатьдатуjson пустаястрока " + "рабочийкаталогданныхпользователя разблокироватьданныедляредактирования разделитьфайл разорватьсоединениесвнешнимисточникомданных " + "раскодироватьстроку рольдоступна секунда сигнал символ скопироватьжурналрегистрации смещениелетнеговремени " + "смещениестандартноговремени соединитьбуферыдвоичныхданных создатькаталог создатьфабрикуxdto сокрл сокрлп сокрп сообщить " + "состояние сохранитьзначение сохранитьнастройкипользователя сред стрдлина стрзаканчиваетсяна стрзаменить стрнайти стрначинаетсяс " + "строка строкасоединенияинформационнойбазы стрполучитьстроку стрразделить стрсоединить стрсравнить стрчисловхождений " + "стрчислострок стршаблон текущаядата текущаядатасеанса текущаяуниверсальнаядата текущаяуниверсальнаядатавмиллисекундах " + "текущийвариантинтерфейсаклиентскогоприложения текущийвариантосновногошрифтаклиентскогоприложения текущийкодлокализации " + "текущийрежимзапуска текущийязык текущийязыксистемы тип типзнч транзакцияактивна трег удалитьданныеинформационнойбазы " + "удалитьизвременногохранилища удалитьобъекты удалитьфайлы универсальноевремя установитьбезопасныйрежим " + "установитьбезопасныйрежимразделенияданных установитьблокировкусеансов установитьвнешнююкомпоненту " + "установитьвремязавершенияспящегосеанса установитьвремязасыпанияпассивногосеанса установитьвремяожиданияблокировкиданных " + "установитьзаголовокклиентскогоприложения установитьзаголовоксистемы установитьиспользованиежурналарегистрации " + "установитьиспользованиесобытияжурналарегистрации установитькраткийзаголовокприложения " + "установитьминимальнуюдлинупаролейпользователей установитьмонопольныйрежим установитьнастройкиклиенталицензирования " + "установитьобновлениепредопределенныхданныхинформационнойбазы установитьотключениебезопасногорежима " + "установитьпараметрыфункциональныхопцийинтерфейса установитьпривилегированныйрежим " + "установитьпроверкусложностипаролейпользователей установитьрасширениеработыскриптографией " + "установитьрасширениеработысфайлами установитьсоединениесвнешнимисточникомданных установитьсоответствиеобъектаиформы " + "установитьсоставстандартногоинтерфейсаodata установитьчасовойпоясинформационнойбазы установитьчасовойпояссеанса " + "формат цел час часовойпояс часовойпояссеанса число числопрописью этоадресвременногохранилища ",
      X = "wsссылки библиотекакартинок библиотекамакетовоформлениякомпоновкиданных библиотекастилей бизнеспроцессы " + "внешниеисточникиданных внешниеобработки внешниеотчеты встроенныепокупки главныйинтерфейс главныйстиль " + "документы доставляемыеуведомления журналыдокументов задачи информацияобинтернетсоединении использованиерабочейдаты " + "историяработыпользователя константы критерииотбора метаданные обработки отображениерекламы отправкадоставляемыхуведомлений " + "отчеты панельзадачос параметрзапуска параметрысеанса перечисления планывидоврасчета планывидовхарактеристик " + "планыобмена планысчетов полнотекстовыйпоиск пользователиинформационнойбазы последовательности проверкавстроенныхпокупок " + "рабочаядата расширенияконфигурации регистрыбухгалтерии регистрынакопления регистрырасчета регистрысведений " + "регламентныезадания сериализаторxdto справочники средствагеопозиционирования средствакриптографии средствамультимедиа " + "средстваотображениярекламы средствапочты средствателефонии фабрикаxdto файловыепотоки фоновыезадания хранилищанастроек " + "хранилищевариантовотчетов хранилищенастроекданныхформ хранилищеобщихнастроек хранилищепользовательскихнастроекдинамическихсписков " + "хранилищепользовательскихнастроекотчетов хранилищесистемныхнастроек ",
      V = W + J + F + X,
      C = "webцвета windowsцвета windowsшрифты библиотекакартинок рамкистиля символы цветастиля шрифтыстиля ",
      K = "автоматическоесохранениеданныхформывнастройках автонумерациявформе автораздвижениесерий " + "анимациядиаграммы вариантвыравниванияэлементовизаголовков вариантуправлениявысотойтаблицы " + "вертикальнаяпрокруткаформы вертикальноеположение вертикальноеположениеэлемента видгруппыформы " + "виддекорацииформы виддополненияэлементаформы видизмененияданных видкнопкиформы видпереключателя " + "видподписейкдиаграмме видполяформы видфлажка влияниеразмеранапузырекдиаграммы горизонтальноеположение " + "горизонтальноеположениеэлемента группировкаколонок группировкаподчиненныхэлементовформы " + "группыиэлементы действиеперетаскивания дополнительныйрежимотображения допустимыедействияперетаскивания " + "интервалмеждуэлементамиформы использованиевывода использованиеполосыпрокрутки " + "используемоезначениеточкибиржевойдиаграммы историявыборапривводе источникзначенийоситочекдиаграммы " + "источникзначенияразмерапузырькадиаграммы категориягруппыкоманд максимумсерий начальноеотображениедерева " + "начальноеотображениесписка обновлениетекстаредактирования ориентациядендрограммы ориентациядиаграммы " + "ориентацияметокдиаграммы ориентацияметоксводнойдиаграммы ориентацияэлементаформы отображениевдиаграмме " + "отображениевлегендедиаграммы отображениегруппыкнопок отображениезаголовкашкалыдиаграммы " + "отображениезначенийсводнойдиаграммы отображениезначенияизмерительнойдиаграммы " + "отображениеинтерваладиаграммыганта отображениекнопки отображениекнопкивыбора отображениеобсужденийформы " + "отображениеобычнойгруппы отображениеотрицательныхзначенийпузырьковойдиаграммы отображениепанелипоиска " + "отображениеподсказки отображениепредупрежденияприредактировании отображениеразметкиполосырегулирования " + "отображениестраницформы отображениетаблицы отображениетекстазначениядиаграммыганта " + "отображениеуправленияобычнойгруппы отображениефигурыкнопки палитрацветовдиаграммы поведениеобычнойгруппы " + "поддержкамасштабадендрограммы поддержкамасштабадиаграммыганта поддержкамасштабасводнойдиаграммы " + "поисквтаблицепривводе положениезаголовкаэлементаформы положениекартинкикнопкиформы " + "положениекартинкиэлементаграфическойсхемы положениекоманднойпанелиформы положениекоманднойпанелиэлементаформы " + "положениеопорнойточкиотрисовки положениеподписейкдиаграмме положениеподписейшкалызначенийизмерительнойдиаграммы " + "положениесостоянияпросмотра положениестрокипоиска положениетекстасоединительнойлинии положениеуправленияпоиском " + "положениешкалывремени порядокотображенияточекгоризонтальнойгистограммы порядоксерийвлегендедиаграммы " + "размеркартинки расположениезаголовкашкалыдиаграммы растягиваниеповертикалидиаграммыганта " + "режимавтоотображениясостояния режимвводастроктаблицы режимвыборанезаполненного режимвыделениядаты " + "режимвыделениястрокитаблицы режимвыделениятаблицы режимизмененияразмера режимизменениясвязанногозначения " + "режимиспользованиядиалогапечати режимиспользованияпараметракоманды режиммасштабированияпросмотра " + "режимосновногоокнаклиентскогоприложения режимоткрытияокнаформы режимотображениявыделения " + "режимотображениягеографическойсхемы режимотображениязначенийсерии режимотрисовкисеткиграфическойсхемы " + "режимполупрозрачностидиаграммы режимпробеловдиаграммы режимразмещениянастранице режимредактированияколонки " + "режимсглаживаниядиаграммы режимсглаживанияиндикатора режимсписказадач сквозноевыравнивание " + "сохранениеданныхформывнастройках способзаполнениятекстазаголовкашкалыдиаграммы " + "способопределенияограничивающегозначениядиаграммы стандартнаягруппакоманд стандартноеоформление " + "статусоповещенияпользователя стильстрелки типаппроксимациилиниитрендадиаграммы типдиаграммы " + "типединицышкалывремени типимпортасерийслоягеографическойсхемы типлиниигеографическойсхемы типлиниидиаграммы " + "типмаркерагеографическойсхемы типмаркерадиаграммы типобластиоформления " + "типорганизацииисточникаданныхгеографическойсхемы типотображениясериислоягеографическойсхемы " + "типотображенияточечногообъектагеографическойсхемы типотображенияшкалыэлементалегендыгеографическойсхемы " + "типпоискаобъектовгеографическойсхемы типпроекциигеографическойсхемы типразмещенияизмерений " + "типразмещенияреквизитовизмерений типрамкиэлементауправления типсводнойдиаграммы " + "типсвязидиаграммыганта типсоединениязначенийпосериямдиаграммы типсоединенияточекдиаграммы " + "типсоединительнойлинии типстороныэлементаграфическойсхемы типформыотчета типшкалырадарнойдиаграммы " + "факторлиниитрендадиаграммы фигуракнопки фигурыграфическойсхемы фиксациявтаблице форматдняшкалывремени " + "форматкартинки ширинаподчиненныхэлементовформы ",
      E = "виддвижениябухгалтерии виддвижениянакопления видпериодарегистрарасчета видсчета видточкимаршрутабизнеспроцесса " + "использованиеагрегатарегистранакопления использованиегруппиэлементов использованиережимапроведения " + "использованиесреза периодичностьагрегатарегистранакопления режимавтовремя режимзаписидокумента режимпроведениядокумента ",
      N = "авторегистрацияизменений допустимыйномерсообщения отправкаэлементаданных получениеэлементаданных ",
      q = "использованиерасшифровкитабличногодокумента ориентациястраницы положениеитоговколоноксводнойтаблицы " + "положениеитоговстроксводнойтаблицы положениетекстаотносительнокартинки расположениезаголовкагруппировкитабличногодокумента " + "способчтениязначенийтабличногодокумента типдвустороннейпечати типзаполненияобластитабличногодокумента " + "типкурсоровтабличногодокумента типлиниирисункатабличногодокумента типлинииячейкитабличногодокумента " + "типнаправленияпереходатабличногодокумента типотображениявыделениятабличногодокумента типотображениялинийсводнойтаблицы " + "типразмещениятекстатабличногодокумента типрисункатабличногодокумента типсмещениятабличногодокумента " + "типузоратабличногодокумента типфайлатабличногодокумента точностьпечати чередованиерасположениястраниц ",
      O = "отображениевремениэлементовпланировщика ",
      R = "типфайлаформатированногодокумента ",
      T = "обходрезультатазапроса типзаписизапроса ",
      L = "видзаполнениярасшифровкипостроителяотчета типдобавленияпредставлений типизмеренияпостроителяотчета типразмещенияитогов ",
      _ = "доступкфайлу режимдиалогавыборафайла режимоткрытияфайла ",
      k = "типизмеренияпостроителязапроса ",
      i = "видданныханализа методкластеризации типединицыинтервалавременианализаданных типзаполнениятаблицырезультатаанализаданных " + "типиспользованиячисловыхзначенийанализаданных типисточникаданныхпоискаассоциаций типколонкианализаданныхдереворешений " + "типколонкианализаданныхкластеризация типколонкианализаданныхобщаястатистика типколонкианализаданныхпоискассоциаций " + "типколонкианализаданныхпоискпоследовательностей типколонкимоделипрогноза типмерырасстоянияанализаданных " + "типотсеченияправилассоциации типполяанализаданных типстандартизациианализаданных типупорядочиванияправилассоциациианализаданных " + "типупорядочиванияшаблоновпоследовательностейанализаданных типупрощениядереварешений ",
      x = "wsнаправлениепараметра вариантxpathxs вариантзаписидатыjson вариантпростоготипаxs видгруппымоделиxs видфасетаxdto " + "действиепостроителяdom завершенностьпростоготипаxs завершенностьсоставноготипаxs завершенностьсхемыxs запрещенныеподстановкиxs " + "исключениягруппподстановкиxs категорияиспользованияатрибутаxs категорияограниченияидентичностиxs категорияограниченияпространствименxs " + "методнаследованияxs модельсодержимогоxs назначениетипаxml недопустимыеподстановкиxs обработкапробельныхсимволовxs обработкасодержимогоxs " + "ограничениезначенияxs параметрыотбораузловdom переносстрокjson позициявдокументеdom пробельныесимволыxml типатрибутаxml типзначенияjson " + "типканоническогоxml типкомпонентыxs типпроверкиxml типрезультатаdomxpath типузлаdom типузлаxml формаxml формапредставленияxs " + "форматдатыjson экранированиесимволовjson ",
      s = "видсравнениякомпоновкиданных действиеобработкирасшифровкикомпоновкиданных направлениесортировкикомпоновкиданных " + "расположениевложенныхэлементоврезультатакомпоновкиданных расположениеитоговкомпоновкиданных расположениегруппировкикомпоновкиданных " + "расположениеполейгруппировкикомпоновкиданных расположениеполякомпоновкиданных расположениереквизитовкомпоновкиданных " + "расположениересурсовкомпоновкиданных типбухгалтерскогоостаткакомпоновкиданных типвыводатекстакомпоновкиданных " + "типгруппировкикомпоновкиданных типгруппыэлементовотборакомпоновкиданных типдополненияпериодакомпоновкиданных " + "типзаголовкаполейкомпоновкиданных типмакетагруппировкикомпоновкиданных типмакетаобластикомпоновкиданных типостаткакомпоновкиданных " + "типпериодакомпоновкиданных типразмещениятекстакомпоновкиданных типсвязинаборовданныхкомпоновкиданных типэлементарезультатакомпоновкиданных " + "расположениелегендыдиаграммыкомпоновкиданных типпримененияотборакомпоновкиданных режимотображенияэлементанастройкикомпоновкиданных " + "режимотображениянастроеккомпоновкиданных состояниеэлементанастройкикомпоновкиданных способвосстановлениянастроеккомпоновкиданных " + "режимкомпоновкирезультата использованиепараметракомпоновкиданных автопозицияресурсовкомпоновкиданных " + "вариантиспользованиягруппировкикомпоновкиданных расположениересурсоввдиаграммекомпоновкиданных фиксациякомпоновкиданных " + "использованиеусловногооформлениякомпоновкиданных ",
      d = "важностьинтернетпочтовогосообщения обработкатекстаинтернетпочтовогосообщения способкодированияинтернетпочтовоговложения " + "способкодированиянеasciiсимволовинтернетпочтовогосообщения типтекстапочтовогосообщения протоколинтернетпочты " + "статусразборапочтовогосообщения ",
      F1 = "режимтранзакциизаписижурналарегистрации статустранзакциизаписижурналарегистрации уровеньжурналарегистрации ",
      X1 = "расположениехранилищасертификатовкриптографии режимвключениясертификатовкриптографии режимпроверкисертификатакриптографии " + "типхранилищасертификатовкриптографии ",
      v = "кодировкаименфайловвzipфайле методсжатияzip методшифрованияzip режимвосстановленияпутейфайловzip режимобработкиподкаталоговzip " + "режимсохраненияпутейzip уровеньсжатияzip ",
      D1 = "звуковоеоповещение направлениепереходакстроке позициявпотоке порядокбайтов режимблокировкиданных режимуправленияблокировкойданных " + "сервисвстроенныхпокупок состояниефоновогозадания типподписчикадоставляемыхуведомлений уровеньиспользованиязащищенногосоединенияftp ",
      N1 = "направлениепорядкасхемызапроса типдополненияпериодамисхемызапроса типконтрольнойточкисхемызапроса типобъединениясхемызапроса " + "типпараметрадоступнойтаблицысхемызапроса типсоединениясхемызапроса ",
      u1 = "httpметод автоиспользованиеобщегореквизита автопрефиксномеразадачи вариантвстроенногоязыка видиерархии видрегистранакопления " + "видтаблицывнешнегоисточникаданных записьдвиженийприпроведении заполнениепоследовательностей индексирование " + "использованиебазыпланавидоврасчета использованиебыстроговыбора использованиеобщегореквизита использованиеподчинения " + "использованиеполнотекстовогопоиска использованиеразделяемыхданныхобщегореквизита использованиереквизита " + "назначениеиспользованияприложения назначениерасширенияконфигурации направлениепередачи обновлениепредопределенныхданных " + "оперативноепроведение основноепредставлениевидарасчета основноепредставлениевидахарактеристики основноепредставлениезадачи " + "основноепредставлениепланаобмена основноепредставлениесправочника основноепредставлениесчета перемещениеграницыприпроведении " + "периодичностьномерабизнеспроцесса периодичностьномерадокумента периодичностьрегистрарасчета периодичностьрегистрасведений " + "повторноеиспользованиевозвращаемыхзначений полнотекстовыйпоискпривводепостроке принадлежностьобъекта проведение " + "разделениеаутентификацииобщегореквизита разделениеданныхобщегореквизита разделениерасширенийконфигурацииобщегореквизита " + "режимавтонумерацииобъектов режимзаписирегистра режимиспользованиямодальности " + "режимиспользованиясинхронныхвызововрасширенийплатформыивнешнихкомпонент режимповторногоиспользованиясеансов " + "режимполученияданныхвыборапривводепостроке режимсовместимости режимсовместимостиинтерфейса " + "режимуправленияблокировкойданныхпоумолчанию сериикодовпланавидовхарактеристик сериикодовпланасчетов " + "сериикодовсправочника созданиепривводе способвыбора способпоискастрокипривводепостроке способредактирования " + "типданныхтаблицывнешнегоисточникаданных типкодапланавидоврасчета типкодасправочника типмакета типномерабизнеспроцесса " + "типномерадокумента типномеразадачи типформы удалениедвижений ",
      d1 = "важностьпроблемыприменениярасширенияконфигурации вариантинтерфейсаклиентскогоприложения вариантмасштабаформклиентскогоприложения " + "вариантосновногошрифтаклиентскогоприложения вариантстандартногопериода вариантстандартнойдатыначала видграницы видкартинки " + "видотображенияполнотекстовогопоиска видрамки видсравнения видцвета видчисловогозначения видшрифта допустимаядлина допустимыйзнак " + "использованиеbyteordermark использованиеметаданныхполнотекстовогопоиска источникрасширенийконфигурации клавиша кодвозвратадиалога " + "кодировкаxbase кодировкатекста направлениепоиска направлениесортировки обновлениепредопределенныхданных обновлениеприизмененииданных " + "отображениепанелиразделов проверказаполнения режимдиалогавопрос режимзапускаклиентскогоприложения режимокругления режимоткрытияформприложения " + "режимполнотекстовогопоиска скоростьклиентскогосоединения состояниевнешнегоисточникаданных состояниеобновленияконфигурациибазыданных " + "способвыборасертификатаwindows способкодированиястроки статуссообщения типвнешнейкомпоненты типплатформы типповеденияклавишиenter " + "типэлементаинформацииовыполненииобновленияконфигурациибазыданных уровеньизоляциитранзакций хешфункция частидаты",
      YA = C + K + E + N + q + O + R + T + L + _ + k + i + x + s + d + F1 + X1 + v + D1 + N1 + u1 + d1,
      bA = "comобъект ftpсоединение httpзапрос httpсервисответ httpсоединение wsопределения wsпрокси xbase анализданных аннотацияxs " + "блокировкаданных буфердвоичныхданных включениеxs выражениекомпоновкиданных генераторслучайныхчисел географическаясхема " + "географическиекоординаты графическаясхема группамоделиxs данныерасшифровкикомпоновкиданных двоичныеданные дендрограмма " + "диаграмма диаграммаганта диалогвыборафайла диалогвыборацвета диалогвыборашрифта диалограсписаниярегламентногозадания " + "диалогредактированиястандартногопериода диапазон документdom документhtml документацияxs доставляемоеуведомление " + "записьdom записьfastinfoset записьhtml записьjson записьxml записьzipфайла записьданных записьтекста записьузловdom " + "запрос защищенноесоединениеopenssl значенияполейрасшифровкикомпоновкиданных извлечениетекста импортxs интернетпочта " + "интернетпочтовоесообщение интернетпочтовыйпрофиль интернетпрокси интернетсоединение информациядляприложенияxs " + "использованиеатрибутаxs использованиесобытияжурналарегистрации источникдоступныхнастроеккомпоновкиданных " + "итераторузловdom картинка квалификаторыдаты квалификаторыдвоичныхданных квалификаторыстроки квалификаторычисла " + "компоновщикмакетакомпоновкиданных компоновщикнастроеккомпоновкиданных конструктормакетаоформлениякомпоновкиданных " + "конструкторнастроеккомпоновкиданных конструкторформатнойстроки линия макеткомпоновкиданных макетобластикомпоновкиданных " + "макетоформлениякомпоновкиданных маскаxs менеджеркриптографии наборсхемxml настройкикомпоновкиданных настройкисериализацииjson " + "обработкакартинок обработкарасшифровкикомпоновкиданных обходдереваdom объявлениеатрибутаxs объявлениенотацииxs " + "объявлениеэлементаxs описаниеиспользованиясобытиядоступжурналарегистрации " + "описаниеиспользованиясобытияотказвдоступежурналарегистрации описаниеобработкирасшифровкикомпоновкиданных " + "описаниепередаваемогофайла описаниетипов определениегруппыатрибутовxs определениегруппымоделиxs " + "определениеограниченияидентичностиxs определениепростоготипаxs определениесоставноготипаxs определениетипадокументаdom " + "определенияxpathxs отборкомпоновкиданных пакетотображаемыхдокументов параметрвыбора параметркомпоновкиданных " + "параметрызаписиjson параметрызаписиxml параметрычтенияxml переопределениеxs планировщик полеанализаданных " + "полекомпоновкиданных построительdom построительзапроса построительотчета построительотчетаанализаданных " + "построительсхемxml поток потоквпамяти почта почтовоесообщение преобразованиеxsl преобразованиекканоническомуxml " + "процессорвыводарезультатакомпоновкиданныхвколлекциюзначений процессорвыводарезультатакомпоновкиданныхвтабличныйдокумент " + "процессоркомпоновкиданных разыменовательпространствименdom рамка расписаниерегламентногозадания расширенноеимяxml " + "результатчтенияданных своднаядиаграмма связьпараметравыбора связьпотипу связьпотипукомпоновкиданных сериализаторxdto " + "сертификатклиентаwindows сертификатклиентафайл сертификаткриптографии сертификатыудостоверяющихцентровwindows " + "сертификатыудостоверяющихцентровфайл сжатиеданных системнаяинформация сообщениепользователю сочетаниеклавиш " + "сравнениезначений стандартнаядатаначала стандартныйпериод схемаxml схемакомпоновкиданных табличныйдокумент " + "текстовыйдокумент тестируемоеприложение типданныхxml уникальныйидентификатор фабрикаxdto файл файловыйпоток " + "фасетдлиныxs фасетколичестваразрядовдробнойчастиxs фасетмаксимальноговключающегозначенияxs " + "фасетмаксимальногоисключающегозначенияxs фасетмаксимальнойдлиныxs фасетминимальноговключающегозначенияxs " + "фасетминимальногоисключающегозначенияxs фасетминимальнойдлиныxs фасетобразцаxs фасетобщегоколичестваразрядовxs " + "фасетперечисленияxs фасетпробельныхсимволовxs фильтрузловdom форматированнаястрока форматированныйдокумент " + "фрагментxs хешированиеданных хранилищезначения цвет чтениеfastinfoset чтениеhtml чтениеjson чтениеxml чтениеzipфайла " + "чтениеданных чтениетекста чтениеузловdom шрифт элементрезультатакомпоновкиданных ",
      e1 = "comsafearray деревозначений массив соответствие списокзначений структура таблицазначений фиксированнаяструктура " + "фиксированноесоответствие фиксированныймассив ",
      k1 = bA + e1,
      Q1 = "null истина ложь неопределено",
      v1 = A.inherit(A.NUMBER_MODE),
      L1 = {
        className: "string",
        begin: '"|\\|',
        end: '"|$',
        contains: [{
          begin: '""'
        }]
      },
      BA = {
        begin: "'",
        end: "'",
        excludeBegin: !0,
        excludeEnd: !0,
        contains: [{
          className: "number",
          begin: "\\d{4}([\\.\\\\/:-]?\\d{2}){0,5}"
        }]
      },
      HA = A.inherit(A.C_LINE_COMMENT_MODE),
      MA = {
        className: "meta",
        begin: "#|&",
        end: "$",
        keywords: {
          $pattern: B,
          "meta-keyword": G + Y
        },
        contains: [HA]
      },
      t = {
        className: "symbol",
        begin: "~",
        end: ";|:",
        excludeEnd: !0
      },
      B1 = {
        className: "function",
        variants: [{
          begin: "процедура|функция",
          end: "\\)",
          keywords: "процедура функция"
        }, {
          begin: "конецпроцедуры|конецфункции",
          keywords: "конецпроцедуры конецфункции"
        }],
        contains: [{
          begin: "\\(",
          end: "\\)",
          endsParent: !0,
          contains: [{
            className: "params",
            begin: B,
            end: ",",
            excludeEnd: !0,
            endsWithParent: !0,
            keywords: {
              $pattern: B,
              keyword: "знач",
              literal: Q1
            },
            contains: [v1, L1, BA]
          }, HA]
        }, A.inherit(A.TITLE_MODE, {
          begin: B
        })]
      };
    return {
      name: "1C:Enterprise",
      case_insensitive: !0,
      keywords: {
        $pattern: B,
        keyword: G,
        built_in: V,
        class: YA,
        type: k1,
        literal: Q1
      },
      contains: [MA, B1, HA, t, v1, L1, BA]
    }
  }
  PTA.exports = np9
})
// @from(Start 1340642, End 1341856)
jTA = z((It5, _TA) => {
  function ap9(A) {
    if (!A) return null;
    if (typeof A === "string") return A;
    return A.source
  }

  function sp9(...A) {
    return A.map((Q) => ap9(Q)).join("")
  }

  function rp9(A) {
    let B = {
        ruleDeclaration: /^[a-zA-Z][a-zA-Z0-9-]*/,
        unexpectedChars: /[!@#$^&',?+~`|:]/
      },
      Q = ["ALPHA", "BIT", "CHAR", "CR", "CRLF", "CTL", "DIGIT", "DQUOTE", "HEXDIG", "HTAB", "LF", "LWSP", "OCTET", "SP", "VCHAR", "WSP"],
      I = A.COMMENT(/;/, /$/),
      G = {
        className: "symbol",
        begin: /%b[0-1]+(-[0-1]+|(\.[0-1]+)+){0,1}/
      },
      Z = {
        className: "symbol",
        begin: /%d[0-9]+(-[0-9]+|(\.[0-9]+)+){0,1}/
      },
      D = {
        className: "symbol",
        begin: /%x[0-9A-F]+(-[0-9A-F]+|(\.[0-9A-F]+)+){0,1}/
      },
      Y = {
        className: "symbol",
        begin: /%[si]/
      },
      W = {
        className: "attribute",
        begin: sp9(B.ruleDeclaration, /(?=\s*=)/)
      };
    return {
      name: "Augmented Backus-Naur Form",
      illegal: B.unexpectedChars,
      keywords: Q,
      contains: [W, I, G, Z, D, Y, A.QUOTE_STRING_MODE, A.NUMBER_MODE]
    }
  }
  _TA.exports = rp9
})