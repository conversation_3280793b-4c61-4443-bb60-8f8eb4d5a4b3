import React from 'react'
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  MessageSquare, 
  Zap, 
  TrendingUp, 
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { MetricCard } from '../components/MetricCard'
import { StatusCard } from '../components/StatusCard'
import { PerformanceChart } from '../components/PerformanceChart'

export function Dashboard() {
  // Mock data - in real app, this would come from API
  const metrics = {
    totalAgents: 5,
    activeAgents: 3,
    messagesPerSecond: 4322773,
    averageLatency: 0.001,
    successRate: 100,
    uptime: '99.9%'
  }

  const systemStatus = {
    framework: 'online',
    messageQueue: 'online',
    agents: 'online',
    localLLM: 'online',
    api: 'online'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">
          Monitor your Claude Code 3.0 system performance and status
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Active Agents"
          value={metrics.activeAgents}
          total={metrics.totalAgents}
          icon={Bot}
          trend={{ value: 0, label: 'No change' }}
          color="primary"
        />
        
        <MetricCard
          title="Messages/Second"
          value="4.3M"
          icon={MessageSquare}
          trend={{ value: 15, label: '+15% from last hour' }}
          color="success"
        />
        
        <MetricCard
          title="Average Latency"
          value="0.001ms"
          icon={Zap}
          trend={{ value: -5, label: '5% faster' }}
          color="warning"
        />
        
        <MetricCard
          title="Success Rate"
          value="100%"
          icon={CheckCircle}
          trend={{ value: 0, label: 'Perfect score' }}
          color="success"
        />
      </div>

      {/* System Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">System Status</h3>
          </div>
          <div className="card-content space-y-4">
            <StatusCard
              title="Framework Core"
              status={systemStatus.framework}
              description="Main system components"
              icon={Activity}
            />
            <StatusCard
              title="h2A Message Queue"
              status={systemStatus.messageQueue}
              description="Zero-latency dual-buffer system"
              icon={MessageSquare}
            />
            <StatusCard
              title="Multi-Agent System"
              status={systemStatus.agents}
              description="3 of 5 agents active"
              icon={Bot}
            />
            <StatusCard
              title="Local LLM (qwen2.5:3b)"
              status={systemStatus.localLLM}
              description="116+ tokens/sec performance"
              icon={Zap}
            />
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">Performance Overview</h3>
          </div>
          <div className="card-content">
            <PerformanceChart />
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
        </div>
        <div className="card-content">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <CheckCircle className="w-5 h-5 text-success-500" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-gray-900">Agent spawned: agent-specialized</p>
                <p className="text-xs text-gray-500">2 minutes ago</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <TrendingUp className="w-5 h-5 text-primary-500" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-gray-900">Performance benchmark completed</p>
                <p className="text-xs text-gray-500">5 minutes ago</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <MessageSquare className="w-5 h-5 text-warning-500" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-gray-900">Message queue processed 1M messages</p>
                <p className="text-xs text-gray-500">10 minutes ago</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <Bot className="w-5 h-5 text-success-500" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-gray-900">Multi-agent system initialized</p>
                <p className="text-xs text-gray-500">15 minutes ago</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
        </div>
        <div className="card-content">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button className="btn-primary p-4 text-left">
              <Bot className="w-6 h-6 mb-2" />
              <div className="text-sm font-medium">Spawn New Agent</div>
              <div className="text-xs opacity-75">Create a new AI agent</div>
            </button>
            
            <button className="btn-secondary p-4 text-left">
              <BarChart3 className="w-6 h-6 mb-2" />
              <div className="text-sm font-medium">Run Benchmark</div>
              <div className="text-xs opacity-75">Test system performance</div>
            </button>
            
            <button className="btn-secondary p-4 text-left">
              <Settings className="w-6 h-6 mb-2" />
              <div className="text-sm font-medium">System Settings</div>
              <div className="text-xs opacity-75">Configure the system</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
