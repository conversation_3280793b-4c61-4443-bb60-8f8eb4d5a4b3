import React from 'react'
import { BookOpen, ExternalLink, Download } from 'lucide-react'

export function Documentation() {
  const docs = [
    {
      title: 'Quick Start Guide',
      description: 'Get up and running with Claude Code 3.0 in 5 minutes',
      link: '/docs/guides/quick-start.md',
      category: 'Getting Started'
    },
    {
      title: 'Local LLM Setup',
      description: 'Complete guide to setting up Ollama with qwen2.5:3b',
      link: '/docs/guides/local-llm-setup.md',
      category: 'Setup'
    },
    {
      title: 'Multi-Agent Framework',
      description: 'Learn about concurrent agent orchestration',
      link: '/docs/guides/multi-agent-framework.md',
      category: 'Architecture'
    },
    {
      title: 'Architecture Overview',
      description: '7-layer event-driven architecture explained',
      link: '/docs/guides/architecture-overview.md',
      category: 'Architecture'
    },
    {
      title: 'Performance Guide',
      description: 'Optimization and tuning for maximum performance',
      link: '/docs/guides/performance-guide.md',
      category: 'Performance'
    },
    {
      title: 'API Reference',
      description: 'Complete API documentation',
      link: '/docs/api/core-api.md',
      category: 'Reference'
    }
  ]

  const categories = [...new Set(docs.map(doc => doc.category))]

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 pb-4">
        <h1 className="text-2xl font-bold text-gray-900">Documentation</h1>
        <p className="text-gray-600">
          Comprehensive guides and API reference for Claude Code 3.0
        </p>
      </div>

      {/* Quick Links */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card p-6 text-center">
          <BookOpen className="w-12 h-12 text-primary-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Quick Start</h3>
          <p className="text-gray-600 mb-4">Get started in 5 minutes</p>
          <button className="btn-primary">
            Read Guide
          </button>
        </div>
        
        <div className="card p-6 text-center">
          <ExternalLink className="w-12 h-12 text-success-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">API Reference</h3>
          <p className="text-gray-600 mb-4">Complete API documentation</p>
          <button className="btn-secondary">
            View API Docs
          </button>
        </div>
        
        <div className="card p-6 text-center">
          <Download className="w-12 h-12 text-warning-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Examples</h3>
          <p className="text-gray-600 mb-4">Code examples and tutorials</p>
          <button className="btn-secondary">
            Browse Examples
          </button>
        </div>
      </div>

      {/* Documentation by Category */}
      {categories.map(category => (
        <div key={category} className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">{category}</h3>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              {docs.filter(doc => doc.category === category).map(doc => (
                <div key={doc.title} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900">{doc.title}</h4>
                    <p className="text-sm text-gray-600">{doc.description}</p>
                  </div>
                  <button className="btn-secondary text-sm">
                    <ExternalLink className="w-4 h-4 mr-1" />
                    View
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      ))}

      {/* System Information */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">System Information</h3>
        </div>
        <div className="card-content">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Framework Version</h4>
              <p className="text-gray-600">Claude Code 3.0.0</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Architecture</h4>
              <p className="text-gray-600">7-layer event-driven system</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Performance</h4>
              <p className="text-gray-600">0.001ms latency, 4.3M msg/sec</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Local LLM</h4>
              <p className="text-gray-600">Ollama with qwen2.5:3b support</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
