import React from 'react'
import { Bo<PERSON>, <PERSON>, Square, Trash2, Plus } from 'lucide-react'

// Mock agent data
const agents = [
  {
    id: 'agent-1',
    name: 'General Agent',
    status: 'active',
    capabilities: ['general', 'text-processing'],
    currentLoad: 2,
    totalRequests: 1247,
    lastActivity: '2 minutes ago',
    model: 'qwen2.5:3b'
  },
  {
    id: 'agent-2', 
    name: 'Code Agent',
    status: 'active',
    capabilities: ['code-generation', 'typescript'],
    currentLoad: 1,
    totalRequests: 892,
    lastActivity: '5 minutes ago',
    model: 'codellama:7b'
  },
  {
    id: 'agent-3',
    name: 'Specialized Agent',
    status: 'idle',
    capabilities: ['specialized', 'analysis'],
    currentLoad: 0,
    totalRequests: 456,
    lastActivity: '15 minutes ago',
    model: 'qwen2.5:3b'
  }
]

export function Agents() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-200 pb-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Multi-Agent System</h1>
          <p className="text-gray-600">
            Manage and monitor your AI agents with load balancing
          </p>
        </div>
        <button className="btn-primary">
          <Plus className="w-4 h-4 mr-2" />
          Spawn Agent
        </button>
      </div>

      {/* Agent Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Total Agents</p>
              <p className="metric-value">3</p>
            </div>
            <Bot className="w-8 h-8 text-primary-600" />
          </div>
        </div>
        
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Active Agents</p>
              <p className="metric-value">2</p>
            </div>
            <div className="w-8 h-8 bg-success-100 rounded-lg flex items-center justify-center">
              <div className="w-3 h-3 bg-success-500 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>
        
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Total Requests</p>
              <p className="metric-value">2,595</p>
            </div>
            <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
              <span className="text-primary-600 font-bold text-sm">R</span>
            </div>
          </div>
        </div>
        
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Avg Load</p>
              <p className="metric-value">1.0</p>
            </div>
            <div className="w-8 h-8 bg-warning-100 rounded-lg flex items-center justify-center">
              <span className="text-warning-600 font-bold text-sm">L</span>
            </div>
          </div>
        </div>
      </div>

      {/* Agents List */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">Active Agents</h3>
        </div>
        <div className="card-content p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Agent
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Capabilities
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Load
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Requests
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Model
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {agents.map((agent) => (
                  <tr key={agent.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <Bot className="w-8 h-8 text-primary-600" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {agent.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {agent.id}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`status-indicator ${
                        agent.status === 'active' ? 'status-success' : 'status-info'
                      }`}>
                        {agent.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-wrap gap-1">
                        {agent.capabilities.map((cap) => (
                          <span key={cap} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            {cap}
                          </span>
                        ))}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm text-gray-900">{agent.currentLoad}</div>
                        <div className="ml-2 w-16 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-primary-600 h-2 rounded-full" 
                            style={{ width: `${(agent.currentLoad / 5) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {agent.totalRequests.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {agent.model}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        {agent.status === 'active' ? (
                          <button className="text-warning-600 hover:text-warning-900">
                            <Square className="w-4 h-4" />
                          </button>
                        ) : (
                          <button className="text-success-600 hover:text-success-900">
                            <Play className="w-4 h-4" />
                          </button>
                        )}
                        <button className="text-error-600 hover:text-error-900">
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}
