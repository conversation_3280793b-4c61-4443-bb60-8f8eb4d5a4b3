@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }
  
  .btn-secondary {
    @apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 active:bg-success-800;
  }
  
  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 active:bg-warning-800;
  }
  
  .btn-error {
    @apply btn bg-error-600 text-white hover:bg-error-700 active:bg-error-800;
  }
  
  .card {
    @apply bg-white rounded-lg border border-gray-200 shadow-sm;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-content {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200;
  }
  
  .status-indicator {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-success {
    @apply status-indicator bg-success-100 text-success-800;
  }
  
  .status-warning {
    @apply status-indicator bg-warning-100 text-warning-800;
  }
  
  .status-error {
    @apply status-indicator bg-error-100 text-error-800;
  }
  
  .status-info {
    @apply status-indicator bg-primary-100 text-primary-800;
  }
  
  .metric-card {
    @apply card p-6;
  }
  
  .metric-value {
    @apply text-2xl font-bold text-gray-900;
  }
  
  .metric-label {
    @apply text-sm font-medium text-gray-500;
  }
  
  .metric-change {
    @apply text-sm font-medium;
  }
  
  .metric-change.positive {
    @apply text-success-600;
  }
  
  .metric-change.negative {
    @apply text-error-600;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
