import React from 'react'
import { LucideIcon } from 'lucide-react'
import { cn } from '../utils/cn'

interface MetricCardProps {
  title: string
  value: string | number
  total?: number
  icon: LucideIcon
  trend?: {
    value: number
    label: string
  }
  color?: 'primary' | 'success' | 'warning' | 'error'
}

export function MetricCard({ 
  title, 
  value, 
  total, 
  icon: Icon, 
  trend, 
  color = 'primary' 
}: MetricCardProps) {
  const colorClasses = {
    primary: 'text-primary-600 bg-primary-50',
    success: 'text-success-600 bg-success-50',
    warning: 'text-warning-600 bg-warning-50',
    error: 'text-error-600 bg-error-50'
  }

  return (
    <div className="metric-card">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="metric-label">{title}</p>
          <div className="flex items-baseline space-x-2">
            <p className="metric-value">
              {value}
              {total && <span className="text-lg text-gray-500">/{total}</span>}
            </p>
          </div>
          {trend && (
            <p className={cn(
              'metric-change',
              trend.value > 0 ? 'positive' : trend.value < 0 ? 'negative' : 'text-gray-500'
            )}>
              {trend.value > 0 && '+'}
              {trend.value !== 0 && `${trend.value}% `}
              {trend.label}
            </p>
          )}
        </div>
        <div className={cn('p-3 rounded-lg', colorClasses[color])}>
          <Icon className="w-6 h-6" />
        </div>
      </div>
    </div>
  )
}
