import React from 'react'
import { <PERSON>Chart, Line, XAxis, <PERSON>A<PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'

// Mock performance data
const performanceData = [
  { time: '00:00', latency: 0.001, throughput: 4200000, agents: 3 },
  { time: '00:05', latency: 0.001, throughput: 4250000, agents: 3 },
  { time: '00:10', latency: 0.001, throughput: 4300000, agents: 4 },
  { time: '00:15', latency: 0.001, throughput: 4350000, agents: 4 },
  { time: '00:20', latency: 0.001, throughput: 4320000, agents: 5 },
  { time: '00:25', latency: 0.001, throughput: 4380000, agents: 5 },
  { time: '00:30', latency: 0.001, throughput: 4400000, agents: 5 },
]

export function PerformanceChart() {
  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={performanceData}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="time" 
            stroke="#6b7280"
            fontSize={12}
          />
          <YAxis 
            stroke="#6b7280"
            fontSize={12}
            tickFormatter={(value) => `${(value / 1000000).toFixed(1)}M`}
          />
          <Tooltip 
            contentStyle={{
              backgroundColor: 'white',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
            formatter={(value: number, name: string) => {
              if (name === 'throughput') {
                return [`${(value / 1000000).toFixed(1)}M msg/sec`, 'Throughput']
              }
              if (name === 'latency') {
                return [`${value}ms`, 'Latency']
              }
              return [value, name]
            }}
          />
          <Line 
            type="monotone" 
            dataKey="throughput" 
            stroke="#3b82f6" 
            strokeWidth={2}
            dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  )
}
