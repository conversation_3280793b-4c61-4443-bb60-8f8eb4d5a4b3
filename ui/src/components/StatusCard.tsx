import React from 'react'
import { LucideIcon, CheckCircle, AlertCircle, XCircle } from 'lucide-react'
import { cn } from '../utils/cn'

interface StatusCardProps {
  title: string
  status: 'online' | 'warning' | 'offline'
  description: string
  icon: LucideIcon
}

export function StatusCard({ title, status, description, icon: Icon }: StatusCardProps) {
  const statusConfig = {
    online: {
      icon: CheckCircle,
      color: 'text-success-600',
      bgColor: 'bg-success-50',
      label: 'Online'
    },
    warning: {
      icon: AlertCircle,
      color: 'text-warning-600',
      bgColor: 'bg-warning-50',
      label: 'Warning'
    },
    offline: {
      icon: XCircle,
      color: 'text-error-600',
      bgColor: 'bg-error-50',
      label: 'Offline'
    }
  }

  const config = statusConfig[status]
  const StatusIcon = config.icon

  return (
    <div className="flex items-center space-x-3 p-3 rounded-lg border border-gray-100">
      <div className={cn('p-2 rounded-lg', config.bgColor)}>
        <Icon className={cn('w-5 h-5', config.color)} />
      </div>
      <div className="flex-1">
        <div className="flex items-center space-x-2">
          <h4 className="text-sm font-medium text-gray-900">{title}</h4>
          <div className="flex items-center space-x-1">
            <StatusIcon className={cn('w-4 h-4', config.color)} />
            <span className={cn('text-xs font-medium', config.color)}>
              {config.label}
            </span>
          </div>
        </div>
        <p className="text-xs text-gray-500">{description}</p>
      </div>
    </div>
  )
}
