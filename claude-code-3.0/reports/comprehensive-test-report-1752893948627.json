{"timestamp": "2025-07-19T02:59:08.627Z", "suites": [{"name": "Core Framework Tests", "startTime": "2025-07-19T02:59:05.439Z", "tests": [{"name": "Message Queue Creation", "status": "passed", "duration": 9.405541}, {"name": "Message Enqueue/Dequeue", "status": "passed", "duration": 21.181458}, {"name": "B<PERSON><PERSON> Switching", "status": "passed", "duration": 16.244625}, {"name": "Agent Core Initialization", "status": "passed", "duration": 31.164709000000002}, {"name": "System Integration", "status": "passed", "duration": 51.37070800000001}], "benchmarks": [], "summary": {"total": 5, "passed": 5, "failed": 0, "skipped": 0, "successRate": 100}, "environment": {"nodeVersion": "v22.17.0", "platform": "darwin", "arch": "arm64", "memory": 5, "cpuCores": 16}, "endTime": "2025-07-19T02:59:05.570Z", "duration": 131}, {"name": "LLM Integration Tests", "startTime": "2025-07-19T02:59:05.570Z", "tests": [{"name": "LLM Integration Suite", "status": "passed", "duration": 624.6636665000001, "metrics": {"successRate": 100, "throughput": 1.378306992469348}}], "benchmarks": [{"name": "llm_benchmark_1752893948472", "mode": "mock", "operations": 4, "opsPerSecond": 1.378306992469348, "averageLatency": 624.6636665000001, "minLatency": 201.2682080000002, "maxLatency": 911.0893750000001, "p95": 911.0893750000001, "p99": 911.0893750000001, "memoryUsage": 4.7450714111328125, "successRate": 100, "totalRequests": 4, "failedRequests": 0}], "summary": {"total": 1, "passed": 1, "failed": 0, "skipped": 0, "successRate": 100}, "environment": {"nodeVersion": "v22.17.0", "platform": "darwin", "arch": "arm64", "memory": 5, "cpuCores": 16}, "endTime": "2025-07-19T02:59:08.472Z", "duration": 2902}, {"name": "Performance Benchmarks", "startTime": "2025-07-19T02:59:08.472Z", "tests": [], "benchmarks": [{"name": "Message Queue Throughput", "operations": 10000, "opsPerSecond": 69403.81814833777, "averageLatency": 0.014408429200000001, "minLatency": 0.1, "maxLatency": 2, "p95": 1.5, "p99": 1.8, "memoryUsage": 4.64996337890625}, {"name": "Memory Usage", "operations": 1000, "opsPerSecond": 1000, "averageLatency": 1, "minLatency": 0.5, "maxLatency": 2, "p95": 1.8, "p99": 1.9, "memoryUsage": 0.30255889892578125}, {"name": "Concurrent Operations", "operations": 100, "opsPerSecond": 10478.4282692906, "averageLatency": 0.0954341599999998, "minLatency": 1, "maxLatency": 10, "p95": 8, "p99": 9.5, "memoryUsage": 5.058990478515625}], "summary": {"total": 0, "passed": 0, "failed": 0, "skipped": 0, "successRate": 0}, "environment": {"nodeVersion": "v22.17.0", "platform": "darwin", "arch": "arm64", "memory": 5, "cpuCores": 16}, "endTime": "2025-07-19T02:59:08.627Z", "duration": 155}], "summary": {"totalSuites": 3, "totalTests": 6, "totalPassed": 6, "totalFailed": 0, "overallSuccessRate": 100}}