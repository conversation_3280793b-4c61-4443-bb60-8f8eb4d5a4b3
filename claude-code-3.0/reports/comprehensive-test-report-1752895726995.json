{"timestamp": "2025-07-19T03:28:46.995Z", "suites": [{"name": "Core Framework Tests", "startTime": "2025-07-19T03:28:44.367Z", "tests": [{"name": "Message Queue Creation", "status": "passed", "duration": 11.199000000000002}, {"name": "Message Enqueue/Dequeue", "status": "passed", "duration": 21.122249999999998}, {"name": "B<PERSON><PERSON> Switching", "status": "passed", "duration": 15.740666999999995}, {"name": "Agent Core Initialization", "status": "passed", "duration": 31.147625000000005}, {"name": "System Integration", "status": "passed", "duration": 51.43154100000001}], "benchmarks": [], "summary": {"total": 5, "passed": 5, "failed": 0, "skipped": 0, "successRate": 100}, "environment": {"nodeVersion": "v22.17.0", "platform": "darwin", "arch": "arm64", "memory": 5, "cpuCores": 16}, "endTime": "2025-07-19T03:28:44.499Z", "duration": 132}, {"name": "LLM Integration Tests", "startTime": "2025-07-19T03:28:44.499Z", "tests": [{"name": "LLM Integration Suite", "status": "passed", "duration": 482.51071874999997, "metrics": {"successRate": 100, "throughput": 1.7133750246193786}}], "benchmarks": [{"name": "llm_benchmark_1752895726834", "mode": "mock", "operations": 4, "opsPerSecond": 1.7133750246193786, "averageLatency": 482.51071874999997, "minLatency": 225.2165, "maxLatency": 743.3858749999997, "p95": 743.3858749999997, "p99": 743.3858749999997, "memoryUsage": 4.5604705810546875, "successRate": 100, "totalRequests": 4, "failedRequests": 0}], "summary": {"total": 1, "passed": 1, "failed": 0, "skipped": 0, "successRate": 100}, "environment": {"nodeVersion": "v22.17.0", "platform": "darwin", "arch": "arm64", "memory": 5, "cpuCores": 16}, "endTime": "2025-07-19T03:28:46.835Z", "duration": 2336}, {"name": "Performance Benchmarks", "startTime": "2025-07-19T03:28:46.835Z", "tests": [], "benchmarks": [{"name": "Message Queue Throughput", "operations": 10000, "opsPerSecond": 66959.44684800955, "averageLatency": 0.014934412500000008, "minLatency": 0.1, "maxLatency": 2, "p95": 1.5, "p99": 1.8, "memoryUsage": 4.436851501464844}, {"name": "Memory Usage", "operations": 1000, "opsPerSecond": 1000, "averageLatency": 1, "minLatency": 0.5, "maxLatency": 2, "p95": 1.8, "p99": 1.9, "memoryUsage": 0.30272674560546875}, {"name": "Concurrent Operations", "operations": 100, "opsPerSecond": 10321.559942768927, "averageLatency": 0.09688458000000083, "minLatency": 1, "maxLatency": 10, "p95": 8, "p99": 9.5, "memoryUsage": 4.846275329589844}], "summary": {"total": 0, "passed": 0, "failed": 0, "skipped": 0, "successRate": 0}, "environment": {"nodeVersion": "v22.17.0", "platform": "darwin", "arch": "arm64", "memory": 5, "cpuCores": 16}, "endTime": "2025-07-19T03:28:46.995Z", "duration": 160}], "summary": {"totalSuites": 3, "totalTests": 6, "totalPassed": 6, "totalFailed": 0, "overallSuccessRate": 100}}