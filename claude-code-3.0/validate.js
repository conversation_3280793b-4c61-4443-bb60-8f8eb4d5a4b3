#!/usr/bin/env node

/**
 * Claude Code 3.0 - Validation Script
 * 
 * Simple validation script to test core functionality without external dependencies.
 */

import { readdir, stat } from 'fs/promises';
import { join } from 'path';

// ANSI color codes
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(color, message) {
  console.log(`${color}${message}${colors.reset}`);
}

function success(message) {
  log(colors.green, `✓ ${message}`);
}

function error(message) {
  log(colors.red, `✗ ${message}`);
}

function info(message) {
  log(colors.blue, `ℹ ${message}`);
}

function warning(message) {
  log(colors.yellow, `⚠ ${message}`);
}

async function validateFileStructure() {
  info('Validating project structure...');
  
  const requiredFiles = [
    'package.json',
    'tsconfig.json',
    'README.md',
    'src/index.ts',
    'src/core/system.ts',
    'src/layers/steering/message-queue.ts',
    'src/layers/steering/steering-manager.ts',
    'src/layers/agent/agent-core.ts',
    'src/types/system.ts',
    'src/types/message-queue.ts',
    'src/types/agent.ts',
    'tests/unit/steering/message-queue.test.ts',
    'tests/integration/system.test.ts',
    'tests/benchmarks/performance.benchmark.ts'
  ];
  
  let allFilesExist = true;
  
  for (const file of requiredFiles) {
    try {
      const filePath = join(process.cwd(), file);
      const stats = await stat(filePath);
      
      if (stats.isFile()) {
        success(`Found: ${file}`);
      } else {
        error(`Not a file: ${file}`);
        allFilesExist = false;
      }
    } catch (err) {
      error(`Missing: ${file}`);
      allFilesExist = false;
    }
  }
  
  return allFilesExist;
}

async function validateDirectoryStructure() {
  info('Validating directory structure...');
  
  const requiredDirs = [
    'src',
    'src/core',
    'src/layers',
    'src/layers/steering',
    'src/layers/agent',
    'src/types',
    'src/cli',
    'tests',
    'tests/unit',
    'tests/integration',
    'tests/benchmarks'
  ];
  
  let allDirsExist = true;
  
  for (const dir of requiredDirs) {
    try {
      const dirPath = join(process.cwd(), dir);
      const stats = await stat(dirPath);
      
      if (stats.isDirectory()) {
        success(`Found directory: ${dir}`);
      } else {
        error(`Not a directory: ${dir}`);
        allDirsExist = false;
      }
    } catch (err) {
      error(`Missing directory: ${dir}`);
      allDirsExist = false;
    }
  }
  
  return allDirsExist;
}

async function validatePackageJson() {
  info('Validating package.json...');

  try {
    const packagePath = join(process.cwd(), 'package.json');
    const { readFile } = await import('fs/promises');
    const packageContent = await readFile(packagePath, 'utf-8');
    const pkg = JSON.parse(packageContent);
    
    const requiredFields = ['name', 'version', 'description', 'main', 'scripts'];
    const requiredScripts = ['build', 'test', 'start'];
    
    let isValid = true;
    
    for (const field of requiredFields) {
      if (pkg[field]) {
        success(`Package has ${field}: ${typeof pkg[field] === 'string' ? pkg[field] : 'defined'}`);
      } else {
        error(`Package missing ${field}`);
        isValid = false;
      }
    }
    
    if (pkg.scripts) {
      for (const script of requiredScripts) {
        if (pkg.scripts[script]) {
          success(`Script defined: ${script}`);
        } else {
          warning(`Script missing: ${script}`);
        }
      }
    }
    
    return isValid;
  } catch (err) {
    error(`Failed to validate package.json: ${err.message}`);
    return false;
  }
}

async function validateTypeScriptConfig() {
  info('Validating TypeScript configuration...');

  try {
    const tsconfigPath = join(process.cwd(), 'tsconfig.json');
    const { readFile } = await import('fs/promises');
    const tsconfigContent = await readFile(tsconfigPath, 'utf-8');
    const tsconfig = JSON.parse(tsconfigContent);
    
    if (tsconfig.compilerOptions) {
      success('TypeScript compiler options defined');
      
      const importantOptions = ['target', 'module', 'outDir', 'rootDir', 'strict'];
      for (const option of importantOptions) {
        if (tsconfig.compilerOptions[option] !== undefined) {
          success(`Compiler option ${option}: ${tsconfig.compilerOptions[option]}`);
        } else {
          warning(`Compiler option ${option} not set`);
        }
      }
    } else {
      error('No compiler options in tsconfig.json');
      return false;
    }
    
    return true;
  } catch (err) {
    error(`Failed to validate tsconfig.json: ${err.message}`);
    return false;
  }
}

async function validateSourceFiles() {
  info('Validating source file syntax...');
  
  const sourceFiles = [
    'src/index.ts',
    'src/core/system.ts',
    'src/layers/steering/message-queue.ts',
    'src/layers/agent/agent-core.ts'
  ];
  
  let allValid = true;
  
  for (const file of sourceFiles) {
    try {
      const filePath = join(process.cwd(), file);
      const { readFile } = await import('fs/promises');
      const content = await readFile(filePath, 'utf-8');
      
      // Basic syntax validation
      if (content.includes('export')) {
        success(`${file} has exports`);
      } else {
        warning(`${file} has no exports`);
      }
      
      if (content.includes('import')) {
        success(`${file} has imports`);
      } else {
        info(`${file} has no imports`);
      }
      
      // Check for TypeScript syntax
      if (content.includes('interface') || content.includes('type') || content.includes(': ')) {
        success(`${file} uses TypeScript syntax`);
      } else {
        warning(`${file} may not be using TypeScript syntax`);
      }
      
    } catch (err) {
      error(`Failed to validate ${file}: ${err.message}`);
      allValid = false;
    }
  }
  
  return allValid;
}

async function validateArchitecture() {
  info('Validating architecture implementation...');
  
  const architectureChecks = [
    {
      name: 'Message Queue System',
      file: 'src/layers/steering/message-queue.ts',
      patterns: ['h2AMessageQueue', 'enqueue', 'dequeue', 'dual-buffer']
    },
    {
      name: 'Agent Core',
      file: 'src/layers/agent/agent-core.ts',
      patterns: ['AgentCore', 'async*', 'nOMainLoop', 'processMessage']
    },
    {
      name: 'System Integration',
      file: 'src/core/system.ts',
      patterns: ['ClaudeCodeSystem', 'initialize', 'start', 'stop']
    },
    {
      name: 'Type Definitions',
      file: 'src/types/system.ts',
      patterns: ['SystemConfig', 'SystemState', 'interface']
    }
  ];
  
  let allValid = true;
  
  for (const check of architectureChecks) {
    try {
      const filePath = join(process.cwd(), check.file);
      const { readFile } = await import('fs/promises');
      const content = await readFile(filePath, 'utf-8');
      
      let foundPatterns = 0;
      for (const pattern of check.patterns) {
        if (content.includes(pattern)) {
          foundPatterns++;
        }
      }
      
      if (foundPatterns >= check.patterns.length - 1) { // Allow one missing pattern
        success(`${check.name} implementation looks good (${foundPatterns}/${check.patterns.length} patterns found)`);
      } else {
        warning(`${check.name} may be incomplete (${foundPatterns}/${check.patterns.length} patterns found)`);
      }
      
    } catch (err) {
      error(`Failed to validate ${check.name}: ${err.message}`);
      allValid = false;
    }
  }
  
  return allValid;
}

async function generateReport() {
  console.log('\n' + '='.repeat(60));
  log(colors.bold + colors.blue, '🚀 Claude Code 3.0 Framework Validation Report');
  console.log('='.repeat(60));
  
  const results = {
    fileStructure: await validateFileStructure(),
    directoryStructure: await validateDirectoryStructure(),
    packageJson: await validatePackageJson(),
    typeScriptConfig: await validateTypeScriptConfig(),
    sourceFiles: await validateSourceFiles(),
    architecture: await validateArchitecture()
  };
  
  console.log('\n' + '='.repeat(60));
  log(colors.bold, '📊 Summary');
  console.log('='.repeat(60));
  
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  for (const [check, result] of Object.entries(results)) {
    const status = result ? '✓' : '✗';
    const color = result ? colors.green : colors.red;
    log(color, `${status} ${check.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}`);
  }
  
  console.log('\n' + '='.repeat(60));
  
  if (passed === total) {
    log(colors.bold + colors.green, `🎉 All checks passed! (${passed}/${total})`);
    log(colors.green, '✅ Claude Code 3.0 framework is ready for development!');
  } else {
    log(colors.bold + colors.yellow, `⚠️  ${passed}/${total} checks passed`);
    log(colors.yellow, '🔧 Some issues need attention before the framework is fully ready.');
  }
  
  console.log('='.repeat(60));
  
  // Additional information
  console.log('\n' + colors.bold + '📋 Next Steps:' + colors.reset);
  
  if (passed === total) {
    info('1. Install dependencies: npm install');
    info('2. Build the project: npm run build');
    info('3. Run tests: npm test');
    info('4. Start development: npm run dev');
  } else {
    info('1. Fix the issues identified above');
    info('2. Re-run this validation script');
    info('3. Once all checks pass, proceed with npm install');
  }
  
  return passed === total;
}

// Run validation
generateReport()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(err => {
    error(`Validation failed: ${err.message}`);
    process.exit(1);
  });
