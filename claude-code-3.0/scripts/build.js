#!/usr/bin/env node

/**
 * Production Build Script for Claude Code 3.0
 * 
 * This script handles the complete build process for both backend and frontend
 */

import { execSync } from 'child_process';
import { existsSync, rmSync } from 'fs';
import { join } from 'path';
import chalk from 'chalk';

const log = {
  info: (msg) => console.log(chalk.blue('ℹ'), msg),
  success: (msg) => console.log(chalk.green('✓'), msg),
  error: (msg) => console.log(chalk.red('✗'), msg),
  warn: (msg) => console.log(chalk.yellow('⚠'), msg)
};

function runCommand(command, cwd = process.cwd()) {
  try {
    log.info(`Running: ${command}`);
    execSync(command, { 
      cwd, 
      stdio: 'inherit',
      env: { ...process.env, NODE_ENV: 'production' }
    });
    return true;
  } catch (error) {
    log.error(`Failed to run: ${command}`);
    log.error(error.message);
    return false;
  }
}

function cleanDist() {
  log.info('Cleaning previous build artifacts...');
  
  const distPaths = [
    join(process.cwd(), 'dist'),
    join(process.cwd(), 'ui', 'dist')
  ];
  
  distPaths.forEach(path => {
    if (existsSync(path)) {
      rmSync(path, { recursive: true, force: true });
      log.success(`Cleaned: ${path}`);
    }
  });
}

function buildBackend() {
  log.info('Building backend (TypeScript compilation)...');
  return runCommand('npx tsc');
}

function buildFrontend() {
  log.info('Building frontend (React + Vite)...');
  return runCommand('npm run build', join(process.cwd(), 'ui'));
}

function validateBuild() {
  log.info('Validating build artifacts...');
  
  const requiredFiles = [
    join(process.cwd(), 'dist', 'api-server.js'),
    join(process.cwd(), 'ui', 'dist', 'index.html')
  ];
  
  let valid = true;
  requiredFiles.forEach(file => {
    if (existsSync(file)) {
      log.success(`Found: ${file}`);
    } else {
      log.error(`Missing: ${file}`);
      valid = false;
    }
  });
  
  return valid;
}

async function main() {
  console.log(chalk.bold.blue('\n🚀 Claude Code 3.0 Production Build\n'));
  
  const startTime = Date.now();
  
  try {
    // Step 1: Clean previous builds
    cleanDist();
    
    // Step 2: Build backend
    if (!buildBackend()) {
      throw new Error('Backend build failed');
    }
    log.success('Backend build completed');
    
    // Step 3: Build frontend
    if (!buildFrontend()) {
      throw new Error('Frontend build failed');
    }
    log.success('Frontend build completed');
    
    // Step 4: Validate build
    if (!validateBuild()) {
      throw new Error('Build validation failed');
    }
    
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    console.log(chalk.bold.green(`\n✅ Build completed successfully in ${duration}s\n`));
    
    log.info('Build artifacts:');
    log.info('  Backend: ./dist/');
    log.info('  Frontend: ./ui/dist/');
    log.info('\nTo start production server:');
    log.info('  npm start');
    
  } catch (error) {
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    console.log(chalk.bold.red(`\n❌ Build failed after ${duration}s\n`));
    log.error(error.message);
    process.exit(1);
  }
}

main().catch(console.error);
