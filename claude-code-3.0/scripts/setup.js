#!/usr/bin/env node

/**
 * Development Setup Script for Claude Code 3.0
 * 
 * This script sets up the development environment
 */

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { join } from 'path';
import chalk from 'chalk';

const log = {
  info: (msg) => console.log(chalk.blue('ℹ'), msg),
  success: (msg) => console.log(chalk.green('✓'), msg),
  error: (msg) => console.log(chalk.red('✗'), msg),
  warn: (msg) => console.log(chalk.yellow('⚠'), msg)
};

function runCommand(command, cwd = process.cwd()) {
  try {
    log.info(`Running: ${command}`);
    execSync(command, { 
      cwd, 
      stdio: 'inherit'
    });
    return true;
  } catch (error) {
    log.error(`Failed to run: ${command}`);
    log.error(error.message);
    return false;
  }
}

function checkNodeVersion() {
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 18) {
    log.error(`Node.js ${nodeVersion} is not supported. Please use Node.js 18 or higher.`);
    return false;
  }
  
  log.success(`Node.js ${nodeVersion} is supported`);
  return true;
}

function installDependencies() {
  log.info('Installing root dependencies...');
  if (!runCommand('npm install')) {
    return false;
  }
  
  log.info('Installing UI dependencies...');
  if (!runCommand('npm install', join(process.cwd(), 'ui'))) {
    return false;
  }
  
  return true;
}

function checkOllama() {
  try {
    execSync('ollama --version', { stdio: 'pipe' });
    log.success('Ollama is installed');
    
    try {
      const models = execSync('ollama list', { encoding: 'utf8' });
      if (models.includes('qwen2.5:3b')) {
        log.success('qwen2.5:3b model is available');
      } else {
        log.warn('qwen2.5:3b model not found. Run: ollama pull qwen2.5:3b');
      }
    } catch {
      log.warn('Could not check Ollama models');
    }
  } catch {
    log.warn('Ollama not found. Install from: https://ollama.ai');
    log.info('Ollama is optional but recommended for local LLM support');
  }
}

function createEnvFile() {
  const envPath = join(process.cwd(), '.env');
  if (!existsSync(envPath)) {
    const envContent = `# Claude Code 3.0 Environment Configuration
# Server Configuration
PORT=8080
UI_PORT=3001
NODE_ENV=development

# Optional: Claude API Key for enhanced LLM integration
# CLAUDE_API_KEY=your_api_key_here

# Optional: Custom Ollama configuration
# OLLAMA_HOST=http://localhost:11434
# OLLAMA_MODEL=qwen2.5:3b
`;
    
    try {
      require('fs').writeFileSync(envPath, envContent);
      log.success('Created .env file with default configuration');
    } catch (error) {
      log.warn('Could not create .env file');
    }
  } else {
    log.info('.env file already exists');
  }
}

async function main() {
  console.log(chalk.bold.blue('\n🛠️  Claude Code 3.0 Development Setup\n'));
  
  try {
    // Step 1: Check Node.js version
    if (!checkNodeVersion()) {
      throw new Error('Node.js version check failed');
    }
    
    // Step 2: Install dependencies
    if (!installDependencies()) {
      throw new Error('Dependency installation failed');
    }
    log.success('All dependencies installed');
    
    // Step 3: Check Ollama (optional)
    checkOllama();
    
    // Step 4: Create environment file
    createEnvFile();
    
    console.log(chalk.bold.green('\n✅ Development setup completed!\n'));
    
    log.info('Next steps:');
    log.info('  1. Start the backend: npm run dev');
    log.info('  2. Start the frontend: npm run dev:ui');
    log.info('  3. Or start both: npm run dev:full');
    log.info('\nAccess points:');
    log.info('  • Main Dashboard: http://localhost:3001');
    log.info('  • API Server: http://localhost:8080');
    
  } catch (error) {
    console.log(chalk.bold.red('\n❌ Setup failed\n'));
    log.error(error.message);
    process.exit(1);
  }
}

main().catch(console.error);
