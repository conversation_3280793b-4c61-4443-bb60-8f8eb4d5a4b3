#!/usr/bin/env node

/**
 * Claude Code 3.0 - Quality Validation Runner
 * 
 * Script to run comprehensive quality validation tests for the multi-agent system
 */

import { ValidationTestSuite } from '../src/quality/validation-test-suite.js';
import { getQualityConfig, qualityPresets } from '../src/quality/quality-config.js';
import { writeFileSync } from 'fs';
import { join } from 'path';

interface ValidationOptions {
  environment: 'development' | 'production' | 'default';
  preset?: 'quick' | 'comprehensive' | 'performance' | 'accuracy';
  outputFile?: string;
  verbose?: boolean;
  categories?: string[];
}

class QualityValidationRunner {
  private options: ValidationOptions;

  constructor(options: ValidationOptions) {
    this.options = options;
  }

  async run(): Promise<void> {
    console.log('🚀 Claude Code 3.0 - Quality Validation Runner');
    console.log('=' .repeat(60));
    
    // Get configuration
    const config = this.getValidationConfig();
    
    console.log(`📋 Configuration:`);
    console.log(`   Environment: ${this.options.environment}`);
    console.log(`   Preset: ${this.options.preset || 'none'}`);
    console.log(`   Categories: ${config.testCaseCategories.join(', ')}`);
    console.log(`   Sample Size: ${config.sampleSize}`);
    console.log(`   Quality Threshold: ${(config.minimumQualityThreshold * 100).toFixed(1)}%`);
    console.log(`   Improvement Threshold: ${(config.improvementThreshold * 100).toFixed(1)}%`);
    
    // Initialize test suite
    const testSuite = new ValidationTestSuite(config);
    
    // Set up event listeners
    this.setupEventListeners(testSuite);
    
    try {
      // Run validation suite
      console.log('\n🧪 Starting validation tests...');
      const report = await testSuite.runValidationSuite();
      
      // Display results
      this.displayResults(report);
      
      // Save results if requested
      if (this.options.outputFile) {
        this.saveResults(report);
      }
      
      // Exit with appropriate code
      const exitCode = this.determineExitCode(report);
      process.exit(exitCode);
      
    } catch (error) {
      console.error('❌ Validation failed:', error);
      process.exit(1);
    }
  }

  private getValidationConfig() {
    let config = getQualityConfig(this.options.environment);
    
    // Apply preset if specified
    if (this.options.preset) {
      const preset = qualityPresets[this.options.preset];
      config = { ...config, ...preset };
    }
    
    // Override categories if specified
    if (this.options.categories && this.options.categories.length > 0) {
      config.testCaseCategories = this.options.categories;
    }
    
    return config;
  }

  private setupEventListeners(testSuite: ValidationTestSuite): void {
    testSuite.on('validation_completed', (report) => {
      if (this.options.verbose) {
        console.log('\n📊 Validation completed');
      }
    });
    
    // Add more event listeners as needed
  }

  private displayResults(report: any): void {
    console.log('\n📊 VALIDATION RESULTS');
    console.log('=' .repeat(60));
    
    // Overall statistics
    console.log(`📈 Overall Statistics:`);
    console.log(`   Total Tests: ${report.totalTests}`);
    console.log(`   Passed: ${report.passedTests} (${(report.passedTests/report.totalTests*100).toFixed(1)}%)`);
    console.log(`   Failed: ${report.failedTests} (${(report.failedTests/report.totalTests*100).toFixed(1)}%)`);
    console.log(`   Overall Improvement: ${(report.overallImprovement*100).toFixed(1)}%`);
    
    // Category breakdown
    console.log(`\n📋 Category Results:`);
    for (const [category, results] of report.categoryResults) {
      const passRate = (results.passed / results.tests * 100).toFixed(1);
      const improvement = (results.averageImprovement * 100).toFixed(1);
      const significant = results.significantImprovements;
      
      console.log(`   ${category}:`);
      console.log(`     Tests: ${results.tests}, Passed: ${results.passed} (${passRate}%)`);
      console.log(`     Avg Improvement: ${improvement}%, Significant: ${significant}`);
    }
    
    // Critical issues
    if (report.criticalIssues.length > 0) {
      console.log(`\n⚠️  Critical Issues:`);
      for (const issue of report.criticalIssues) {
        console.log(`   • ${issue}`);
      }
    }
    
    // Recommendations
    if (report.recommendations.length > 0) {
      console.log(`\n💡 Recommendations:`);
      for (const recommendation of report.recommendations) {
        console.log(`   • ${recommendation}`);
      }
    }
    
    // Detailed results (if verbose)
    if (this.options.verbose && report.detailedResults) {
      console.log(`\n🔍 Detailed Results:`);
      for (const result of report.detailedResults.slice(0, 5)) { // Show first 5
        console.log(`   Test: ${result.testCase.id}`);
        console.log(`     Question: ${result.testCase.question.substring(0, 60)}...`);
        console.log(`     Passed: ${result.passed ? '✅' : '❌'}`);
        console.log(`     Improvement: ${(result.comparison.improvement * 100).toFixed(1)}%`);
        if (result.issues.length > 0) {
          console.log(`     Issues: ${result.issues.join(', ')}`);
        }
      }
      
      if (report.detailedResults.length > 5) {
        console.log(`   ... and ${report.detailedResults.length - 5} more tests`);
      }
    }
  }

  private saveResults(report: any): void {
    const outputPath = this.options.outputFile || 'validation-report.json';
    const fullPath = join(process.cwd(), outputPath);
    
    try {
      writeFileSync(fullPath, JSON.stringify(report, null, 2));
      console.log(`\n💾 Results saved to: ${fullPath}`);
    } catch (error) {
      console.error(`❌ Failed to save results: ${error}`);
    }
  }

  private determineExitCode(report: any): number {
    // Exit with error if validation failed significantly
    if (report.failedTests / report.totalTests > 0.5) {
      return 1; // High failure rate
    }
    
    if (report.overallImprovement < -0.1) {
      return 1; // Significant degradation
    }
    
    if (report.criticalIssues.length > 0) {
      return 1; // Critical issues found
    }
    
    return 0; // Success
  }
}

// Command line interface
function parseArguments(): ValidationOptions {
  const args = process.argv.slice(2);
  const options: ValidationOptions = {
    environment: 'default',
    verbose: false
  };
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--environment':
      case '-e':
        options.environment = args[++i] as any;
        break;
      case '--preset':
      case '-p':
        options.preset = args[++i] as any;
        break;
      case '--output':
      case '-o':
        options.outputFile = args[++i];
        break;
      case '--verbose':
      case '-v':
        options.verbose = true;
        break;
      case '--categories':
      case '-c':
        options.categories = args[++i].split(',');
        break;
      case '--help':
      case '-h':
        printHelp();
        process.exit(0);
        break;
      default:
        console.error(`Unknown argument: ${arg}`);
        printHelp();
        process.exit(1);
    }
  }
  
  return options;
}

function printHelp(): void {
  console.log(`
Claude Code 3.0 - Quality Validation Runner

Usage: npm run validate [options]

Options:
  -e, --environment <env>     Environment: development, production, default
  -p, --preset <preset>       Preset: quick, comprehensive, performance, accuracy
  -o, --output <file>         Output file for results (JSON format)
  -v, --verbose               Verbose output
  -c, --categories <list>     Comma-separated list of test categories
  -h, --help                  Show this help message

Examples:
  npm run validate                                    # Default validation
  npm run validate -e development -p quick           # Quick dev validation
  npm run validate -e production -p comprehensive    # Full production validation
  npm run validate -c conceptual,coding -v           # Specific categories with verbose output
  npm run validate -o results.json                   # Save results to file
`);
}

// Main execution
async function main(): Promise<void> {
  try {
    const options = parseArguments();
    const runner = new QualityValidationRunner(options);
    await runner.run();
  } catch (error) {
    console.error('❌ Validation runner failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { QualityValidationRunner, ValidationOptions };
