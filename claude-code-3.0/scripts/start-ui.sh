#!/bin/bash

# Claude Code 3.0 - UI Startup Script
# Starts both the API server and React UI

set -e

echo "🚀 Starting Claude Code 3.0 UI System..."
echo "========================================"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js v18 or higher."
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Please run this script from the claude-code-3.0 root directory"
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing backend dependencies..."
    npm install
fi

if [ ! -d "ui/node_modules" ]; then
    echo "📦 Installing UI dependencies..."
    cd ui && npm install && cd ..
fi

# Start the API server in the background
echo "🔧 Starting API server on port 8080..."
npm run dev &
API_PID=$!

# Wait a moment for the API server to start
sleep 3

# Start the UI development server
echo "🎨 Starting UI development server on port 3000..."
cd ui && npm run dev &
UI_PID=$!

echo ""
echo "✅ Claude Code 3.0 UI System is starting up!"
echo ""
echo "📊 API Server: http://localhost:8080"
echo "🎨 UI Interface: http://localhost:3000"
echo ""
echo "Press Ctrl+C to stop both servers"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down servers..."
    kill $API_PID 2>/dev/null || true
    kill $UI_PID 2>/dev/null || true
    echo "✅ Servers stopped"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for both processes
wait
