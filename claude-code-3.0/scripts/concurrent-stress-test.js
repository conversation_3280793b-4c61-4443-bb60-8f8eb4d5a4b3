#!/usr/bin/env node

/**
 * Claude Code 3.0 - Concurrent Stress Test Script
 * 
 * This script sends multiple concurrent requests to stress test the multi-agent system
 * and demonstrate automatic agent spawning when load exceeds thresholds.
 */

import http from 'http';
import { performance } from 'perf_hooks';

// Configuration
const CONFIG = {
  baseUrl: 'http://localhost:8080',
  maxConcurrentRequests: 8, // More than current 3 agents to trigger spawning
  requestDelay: 100, // ms between request batches
  monitoringInterval: 2000, // ms between status checks
  testDuration: 60000, // 1 minute test
};

// Complex test scenarios designed to stress different agent types
const STRESS_TEST_SCENARIOS = [
  {
    id: 'quantum-ml-advanced',
    content: `Design and implement a quantum-enhanced neural network architecture that uses quantum entanglement for weight optimization. Include:
    1. Mathematical derivation of quantum gradient descent
    2. Complete TypeScript implementation with quantum state management
    3. Integration with TensorFlow.js for hybrid classical-quantum training
    4. Performance benchmarking against classical approaches
    5. Error correction mechanisms for quantum noise
    6. Scalability analysis for large neural networks`,
    expectedAgent: 'code-generation',
    complexity: 'extreme'
  },
  {
    id: 'distributed-consensus-theory',
    content: `Explain the theoretical foundations of Byzantine fault tolerance in distributed systems. Cover:
    1. Mathematical proofs of safety and liveness properties
    2. Game-theoretic analysis of rational vs malicious actors
    3. Information-theoretic bounds on consensus protocols
    4. Relationship to computational complexity theory
    5. Implications for blockchain and cryptocurrency systems
    6. Future research directions in distributed consensus`,
    expectedAgent: 'general',
    complexity: 'extreme'
  },
  {
    id: 'bioinformatics-protein-folding',
    content: `Develop a comprehensive protein folding prediction system combining AI and molecular dynamics:
    1. Implement AlphaFold-style attention mechanisms in TypeScript
    2. Create molecular dynamics simulation engine
    3. Design energy minimization algorithms
    4. Build 3D visualization components
    5. Integrate with protein databases (PDB, UniProt)
    6. Validate against experimental structures`,
    expectedAgent: 'code-generation',
    complexity: 'extreme'
  },
  {
    id: 'quantum-cryptography-analysis',
    content: `Analyze the security implications of quantum computing on modern cryptography:
    1. Mathematical analysis of Shor's algorithm impact on RSA
    2. Post-quantum cryptography alternatives evaluation
    3. Quantum key distribution protocols comparison
    4. Implementation vulnerabilities in quantum systems
    5. Timeline for quantum supremacy in cryptography
    6. Migration strategies for existing systems`,
    expectedAgent: 'specialized',
    complexity: 'extreme'
  },
  {
    id: 'ml-optimization-algorithms',
    content: `Create advanced machine learning optimization algorithms with mathematical foundations:
    1. Derive novel second-order optimization methods
    2. Implement adaptive learning rate schedules
    3. Design momentum-based gradient methods
    4. Create distributed optimization for large models
    5. Analyze convergence properties mathematically
    6. Benchmark against state-of-the-art optimizers`,
    expectedAgent: 'code-generation',
    complexity: 'extreme'
  },
  {
    id: 'complexity-theory-analysis',
    content: `Explore the relationship between computational complexity theory and machine learning:
    1. P vs NP implications for learning algorithms
    2. Sample complexity bounds for neural networks
    3. Approximation algorithms for NP-hard ML problems
    4. Quantum complexity classes and quantum ML
    5. Information-theoretic learning bounds
    6. Connections to statistical physics and phase transitions`,
    expectedAgent: 'specialized',
    complexity: 'extreme'
  },
  {
    id: 'advanced-data-structures',
    content: `Design and implement advanced data structures for high-performance computing:
    1. Lock-free concurrent data structures in TypeScript
    2. Cache-oblivious algorithms implementation
    3. Persistent data structures with structural sharing
    4. Probabilistic data structures (Bloom filters, HyperLogLog)
    5. Geometric data structures for spatial queries
    6. Performance analysis and benchmarking framework`,
    expectedAgent: 'code-generation',
    complexity: 'extreme'
  },
  {
    id: 'systems-theory-emergence',
    content: `Analyze emergence and complexity in multi-agent systems:
    1. Mathematical models of emergent behavior
    2. Phase transitions in complex systems
    3. Information theory and collective intelligence
    4. Network effects and scale-free properties
    5. Applications to AI coordination and swarm intelligence
    6. Philosophical implications for consciousness and intelligence`,
    expectedAgent: 'general',
    complexity: 'extreme'
  }
];

class ConcurrentStressTest {
  constructor() {
    this.activeRequests = new Map();
    this.completedRequests = [];
    this.agentStatus = new Map();
    this.testStartTime = null;
    this.monitoringTimer = null;
  }

  async runStressTest() {
    console.log('🚀 Starting Concurrent Multi-Agent Stress Test');
    console.log('=' .repeat(60));
    console.log(`📊 Configuration:`);
    console.log(`   Max Concurrent Requests: ${CONFIG.maxConcurrentRequests}`);
    console.log(`   Test Duration: ${CONFIG.testDuration / 1000}s`);
    console.log(`   Monitoring Interval: ${CONFIG.monitoringInterval}ms`);
    console.log(`   Available Scenarios: ${STRESS_TEST_SCENARIOS.length}`);
    console.log('');

    this.testStartTime = performance.now();
    
    // Start monitoring
    this.startMonitoring();
    
    // Launch concurrent requests in waves
    await this.launchConcurrentWaves();
    
    // Wait for test completion
    await this.waitForCompletion();
    
    // Generate final report
    this.generateFinalReport();
  }

  async launchConcurrentWaves() {
    const totalWaves = Math.ceil(CONFIG.testDuration / (CONFIG.requestDelay * CONFIG.maxConcurrentRequests));
    
    console.log(`🌊 Launching ${totalWaves} waves of concurrent requests...`);
    
    for (let wave = 0; wave < totalWaves; wave++) {
      console.log(`\n🔄 Wave ${wave + 1}/${totalWaves} - Launching ${CONFIG.maxConcurrentRequests} concurrent requests`);
      
      // Launch batch of concurrent requests
      const promises = [];
      for (let i = 0; i < CONFIG.maxConcurrentRequests; i++) {
        const scenario = STRESS_TEST_SCENARIOS[i % STRESS_TEST_SCENARIOS.length];
        promises.push(this.sendRequest(scenario, `${wave}-${i}`));
      }
      
      // Don't wait for completion, let them run concurrently
      Promise.allSettled(promises).then(results => {
        console.log(`✅ Wave ${wave + 1} completed: ${results.filter(r => r.status === 'fulfilled').length}/${results.length} successful`);
      });
      
      // Small delay between waves to allow monitoring
      await this.sleep(CONFIG.requestDelay);
    }
  }

  async sendRequest(scenario, requestId) {
    const startTime = performance.now();
    const fullRequestId = `${scenario.id}-${requestId}`;
    
    console.log(`   🎯 ${fullRequestId}: ${scenario.content.substring(0, 50)}...`);
    
    this.activeRequests.set(fullRequestId, {
      scenario,
      startTime,
      status: 'active'
    });

    try {
      const response = await this.makeHttpRequest(scenario.content);
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.completedRequests.push({
        requestId: fullRequestId,
        scenario,
        duration,
        success: true,
        agentUsed: response.agentId,
        tokenCount: response.content?.length || 0
      });
      
      console.log(`   ✅ ${fullRequestId}: Completed in ${Math.round(duration)}ms by ${response.agentId}`);
      
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.completedRequests.push({
        requestId: fullRequestId,
        scenario,
        duration,
        success: false,
        error: error.message
      });
      
      console.log(`   ❌ ${fullRequestId}: Failed after ${Math.round(duration)}ms - ${error.message}`);
    } finally {
      this.activeRequests.delete(fullRequestId);
    }
  }

  makeHttpRequest(content) {
    return new Promise((resolve, reject) => {
      const postData = JSON.stringify({ content });
      
      const options = {
        hostname: 'localhost',
        port: 8080,
        path: '/api/messages',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        }
      };

      const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            resolve(response);
          } catch (error) {
            reject(new Error(`Failed to parse response: ${error.message}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.setTimeout(30000, () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      req.write(postData);
      req.end();
    });
  }

  async startMonitoring() {
    console.log('\n📊 Starting real-time monitoring...\n');
    
    this.monitoringTimer = setInterval(async () => {
      try {
        await this.checkSystemStatus();
      } catch (error) {
        console.log(`⚠️  Monitoring error: ${error.message}`);
      }
    }, CONFIG.monitoringInterval);
  }

  async checkSystemStatus() {
    try {
      const agentStatus = await this.getAgentStatus();
      const currentTime = performance.now();
      const elapsedTime = Math.round((currentTime - this.testStartTime) / 1000);
      
      console.log(`\n⏱️  Time: ${elapsedTime}s | Active Requests: ${this.activeRequests.size} | Completed: ${this.completedRequests.length}`);
      console.log('📊 Agent Status:');
      
      agentStatus.forEach(agent => {
        const status = agent.status === 'active' ? '🟢' : agent.status === 'busy' ? '🟡' : '⚪';
        const load = agent.currentLoad.toFixed(2);
        console.log(`   ${status} ${agent.name}: Load ${load} | Requests ${agent.totalRequests}`);
      });
      
      // Check if new agents were spawned
      const totalAgents = agentStatus.length;
      if (totalAgents > 3) {
        console.log(`🆕 AGENT SCALING DETECTED: ${totalAgents} agents now active (was 3)`);
      }
      
      // Check for high load conditions
      const highLoadAgents = agentStatus.filter(agent => agent.currentLoad > 3.0);
      if (highLoadAgents.length > 0) {
        console.log(`⚡ HIGH LOAD: ${highLoadAgents.length} agents over 3.0 load threshold`);
      }
      
    } catch (error) {
      console.log(`❌ Status check failed: ${error.message}`);
    }
  }

  async getAgentStatus() {
    return new Promise((resolve, reject) => {
      const req = http.request({
        hostname: 'localhost',
        port: 8080,
        path: '/api/agents',
        method: 'GET'
      }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            resolve(JSON.parse(data));
          } catch (error) {
            reject(error);
          }
        });
      });
      
      req.on('error', reject);
      req.setTimeout(5000, () => {
        req.destroy();
        reject(new Error('Status request timeout'));
      });
      req.end();
    });
  }

  async waitForCompletion() {
    console.log('\n⏳ Waiting for all requests to complete...');
    
    // Wait for all active requests to finish
    while (this.activeRequests.size > 0) {
      await this.sleep(1000);
      console.log(`   Still waiting for ${this.activeRequests.size} active requests...`);
    }
    
    // Stop monitoring
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
    }
    
    console.log('✅ All requests completed!');
  }

  generateFinalReport() {
    const totalTime = performance.now() - this.testStartTime;
    const successfulRequests = this.completedRequests.filter(r => r.success);
    const failedRequests = this.completedRequests.filter(r => !r.success);
    
    console.log('\n' + '=' .repeat(60));
    console.log('📋 FINAL STRESS TEST REPORT');
    console.log('=' .repeat(60));
    
    console.log(`⏱️  Total Test Duration: ${Math.round(totalTime / 1000)}s`);
    console.log(`📊 Total Requests: ${this.completedRequests.length}`);
    console.log(`✅ Successful: ${successfulRequests.length} (${Math.round(successfulRequests.length / this.completedRequests.length * 100)}%)`);
    console.log(`❌ Failed: ${failedRequests.length} (${Math.round(failedRequests.length / this.completedRequests.length * 100)}%)`);
    
    if (successfulRequests.length > 0) {
      const avgDuration = successfulRequests.reduce((sum, r) => sum + r.duration, 0) / successfulRequests.length;
      const throughput = successfulRequests.length / (totalTime / 1000);
      
      console.log(`⚡ Average Response Time: ${Math.round(avgDuration)}ms`);
      console.log(`🚀 Throughput: ${throughput.toFixed(2)} requests/second`);
      
      // Agent utilization analysis
      const agentUsage = {};
      successfulRequests.forEach(r => {
        if (r.agentUsed) {
          agentUsage[r.agentUsed] = (agentUsage[r.agentUsed] || 0) + 1;
        }
      });
      
      console.log('\n🤖 Agent Utilization:');
      Object.entries(agentUsage).forEach(([agent, count]) => {
        const percentage = Math.round(count / successfulRequests.length * 100);
        console.log(`   ${agent}: ${count} requests (${percentage}%)`);
      });
    }
    
    console.log('\n🎯 Test completed successfully!');
    console.log('Check the UI at http://localhost:3004/ to see real-time agent coordination graph');
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run the stress test
async function main() {
  const stressTest = new ConcurrentStressTest();
  await stressTest.runStressTest();
  process.exit(0);
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('❌ Stress test failed:', error);
    process.exit(1);
  });
}

export { ConcurrentStressTest };
