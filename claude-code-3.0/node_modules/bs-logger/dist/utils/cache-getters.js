"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) if (e.indexOf(p[i]) < 0)
            t[p[i]] = s[p[i]];
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
var cacheGetters = function (target) {
    var props = [];
    for (var _i = 1; _i < arguments.length; _i++) {
        props[_i - 1] = arguments[_i];
    }
    props.forEach(function (prop) {
        var desc = Object.getOwnPropertyDescriptor(target, prop);
        var set = desc.set, previousGet = desc.get, partial = __rest(desc, ["set", "get"]);
        desc.get = function get() {
            var value = previousGet.call(this);
            Object.defineProperty(this, prop, __assign({}, partial, { value: value }));
            return value;
        };
        Object.defineProperty(target, prop, desc);
    });
    return target;
};
exports.cacheGetters = cacheGetters;
