{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,YAAY,MAAM,aAAa,CAAC;AACvC,OAAO,MAAM,MAAM,UAAU,CAAC;AAC9B,OAAO,MAAM,MAAM,aAAa,CAAC;AACjC,OAAO,MAAM,EAAE,EAAC,WAAW,IAAI,iBAAiB,EAAC,MAAM,aAAa,CAAC;AAErE,OAAO,YAAY,MAAM,eAAe,CAAC;AACzC,OAAO,SAAS,MAAM,YAAY,CAAC;AACnC,OAAO,WAAW,MAAM,sBAAsB,CAAC;AAC/C,OAAO,QAAQ,MAAM,cAAc,CAAC;AACpC,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,aAAa,MAAM,gBAAgB,CAAC;AAC3C,OAAO,EAAoF,UAAU,EAAE,YAAY,EAAoC,MAAM,YAAY,CAAC;AAI1K,MAAM,gBAAgB;IAIrB,YAAY,YAAuB,EAAE,YAAsC;QAD3E,UAAK,GAAsB,IAAI,GAAG,EAAgB,CAAC;QAoBnD,YAAO,GAAG,GAAG,EAAE,CAAC,CAAC,OAAyB,EACzC,EAAsC,EAAW,EAAE;YACnD,IAAI,GAAG,CAAC;YACR,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;gBAChC,GAAG,GAAG,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;gBAChD,OAAO,GAAG,EAAE,CAAC;aACb;iBAAM,IAAI,OAAO,YAAY,MAAM,CAAC,GAAG,EAAE;gBACzC,GAAG,GAAG,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;gBAC3D,OAAO,GAAG,EAAE,CAAC;aACb;iBAAM;gBACN,MAAM,CAAC,QAAQ,EAAE,GAAG,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACnE,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC;oBACpC,CAAC,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;oBAC7B,CAAC,CAAC,EAAE,CAAC;gBACN,GAAG,GAAG,kBAAkB,CAAC,EAAC,GAAG,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAC,CAAC,CAAC;aACzD;YAED,OAAO,GAAG;gBACT,OAAO,EAAE,EAAE;gBACX,MAAM,EAAE,KAAK;gBACb,KAAK,EAAE,IAAI;gBACX,SAAS,EAAE,KAAK;gBAChB,iBAAiB,EAAE,KAAK;gBACxB,GAAG,OAAO;gBACV,GAAG,yBAAyB,CAAC,GAAG,CAAC;aACjC,CAAC;YACF,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAE,GAAc,CAAC,WAAW,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;YAC7H,MAAM,EAAE,GAAY,IAAI,YAAY,EAAa,CAAC;YAClD,MAAM,mBAAmB,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;gBAC5D,QAAQ,EAAE,KAAK;gBACf,mBAAmB,EAAE,KAAK;gBAC1B,mBAAmB,EAAE,KAAK;aAC1B,CAAC,CAAC;YACH,IAAI,GAAG,GAAG,GAAG,OAAO,CAAC,MAAM,IAAI,mBAAmB,EAAE,CAAC;YACrD,yEAAyE;YACzE,wEAAwE;YACxE,uDAAuD;YACvD,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACtG,IAAI,OAAO,CAAC,IAAI,YAAY,MAAM,CAAC,QAAQ,EAAE;oBAC5C,oEAAoE;oBACpE,qEAAqE;oBACrE,iDAAiD;oBACjD,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;iBACtB;qBAAM;oBACN,GAAG,IAAI,IAAI,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;iBACzE;aACD;YAED,IAAI,UAAU,GAAQ,KAAK,CAAC;YAC5B,IAAI,WAAW,GAAG,KAAK,CAAC;YACxB,MAAM,WAAW,GAAG,CAAC,QAAa,EAAE,EAAE;gBACrC,WAAW,GAAG,IAAI,CAAC;gBACnB,IAAI,cAAc,GAAG,KAAK,CAAC;gBAC3B,IAAI,oBAAoB,GAA6B,GAAG,EAAE,GAAkB,CAAC,CAAC;gBAE9E,MAAM,mBAAmB,GAAG,IAAI,OAAO,CAAO,OAAO,CAAC,EAAE;oBACvD,oBAAoB,GAAG,GAAG,EAAE;wBAC3B,IAAI,CAAC,cAAc,EAAE;4BACpB,cAAc,GAAG,IAAI,CAAC;4BACtB,OAAO,EAAE,CAAC;yBACV;oBACF,CAAC,CAAC;gBACH,CAAC,CAAC,CAAC;gBACH,MAAM,OAAO,GAAG,KAAK,EAAE,QAAa,EAAE,EAAE;oBACvC,IAAI,UAAU,EAAE;wBACf,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC;wBACtC,MAAM,iBAAiB,GAAG,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;wBAC/G,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE;4BAChC,QAAQ,CAAC,MAAM,EAAE,CAAC;4BAClB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;gCAC3B,oFAAoF;gCACpF,QAAQ;qCACN,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;4BACxB,CAAC,CAAC,CAAC;4BACH,MAAM,OAAO,GAAG,cAAc,CAAC,iBAAiB,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC;4BAC3E,QAAQ,GAAG,IAAI,QAAQ,CAAC,EAAC,UAAU,EAAE,UAAU,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,UAAU,CAAC,GAAG,EAAC,CAAC,CAAC;4BAClH,QAAQ,CAAC,WAAW,GAAG,iBAAiB,CAAC,MAAM,CAAC;4BAChD,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;yBAC1B;qBACD;oBAED,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;wBACxB,QAAQ,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;wBACrE,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;qBAC3B;oBAED,IAAI,cAAc,CAAC;oBACnB,IAAI,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE;wBACtD,cAAc,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC;wBACzC,CAAC,KAAK,IAAI,EAAE;4BACX,IAAI;gCACH,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gCAC/C,MAAM,OAAO,CAAC,IAAI,CAAC;oCAClB,mBAAmB;oCACnB,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;oCACrD,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE,iDAAiD;iCAC1G,CAAC,CAAC;gCACH,MAAM,IAAI,GAAG,MAAM,WAAW,CAAC;gCAC/B,IAAI,KAAK,GAAe;oCACvB,GAAG,EAAE,QAAQ,CAAC,GAAG;oCACjB,UAAU,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU;oCAC5E,IAAI;oCACJ,WAAW,EAAE,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE;iCAC5C,CAAC;gCACF,IAAI,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;gCAC7E,IAAI,QAAQ,CAAC,MAAM,EAAE;oCACpB,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;iCAC7D;gCAED,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE;oCACxB,qCAAqC;oCACrC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE;wCACrC,KAAK,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;qCAClD;oCACD,oCAAoC;iCACpC;gCAED,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;6BACtC;4BAAC,OAAO,KAAU,EAAE;gCACpB,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;6BACxC;wBACF,CAAC,CAAC,EAAE,CAAC;qBACL;yBAAM,IAAI,QAAQ,CAAC,KAAK,IAAI,UAAU,EAAE;wBACxC,CAAC,KAAK,IAAI,EAAE;4BACX,IAAI;gCACH,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;6BAC7B;4BAAC,OAAO,KAAU,EAAE;gCACpB,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;6BACxC;wBACF,CAAC,CAAC,EAAE,CAAC;qBACL;oBAED,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,IAAI,QAAQ,CAAC,CAAC;oBAChD,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;wBAC7B,EAAE,CAAC,cAAc,IAAI,QAAQ,CAAC,CAAC;qBAC/B;gBACF,CAAC,CAAC;gBAEF,IAAI;oBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;oBACtD,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;oBAC7C,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;oBAC7C,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC;oBAC/C,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;iBAC7B;gBAAC,OAAO,KAAU,EAAE;oBACpB,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;iBAC1C;YACF,CAAC,CAAC;YAEF,CAAC,KAAK,IAAI,EAAE;gBACX,MAAM,GAAG,GAAG,KAAK,EAAE,QAAa,EAAE,EAAE;oBACnC,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;oBACxB,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;oBAE1E,IAAI,UAAU,KAAK,SAAS,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;wBACvD,WAAW,CAAC,QAAQ,CAAC,CAAC;wBACtB,OAAO;qBACP;oBAED,MAAM,MAAM,GAAG,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;oBAC9D,IAAI,MAAM,CAAC,4BAA4B,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;wBAC5E,MAAM,OAAO,GAAG,cAAc,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC;wBACzD,MAAM,QAAQ,GAAQ,IAAI,QAAQ,CAAC,EAAC,UAAU,EAAE,UAAU,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,UAAU,CAAC,GAAG,EAAC,CAAC,CAAC;wBAC7H,QAAQ,CAAC,WAAW,GAAG,MAAM,CAAC;wBAC9B,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;wBAC1B,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;wBAC9B,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;4BAC7B,EAAE,CAAC,QAAQ,CAAC,CAAC;yBACb;qBACD;yBAAM,IAAI,MAAM,CAAC,4BAA4B,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,MAAM,CAAC,UAAU,EAAE,IAAI,QAAQ,CAAC,YAAY,EAAE;wBACvH,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;wBAC7B,QAAQ,CAAC,OAAO,GAAG,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;wBACxD,WAAW,CAAC,QAAQ,CAAC,CAAC;qBACtB;yBAAM;wBACN,UAAU,GAAG,UAAU,CAAC;wBACxB,QAAQ,CAAC,OAAO,GAAG,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;wBACxD,WAAW,CAAC,QAAQ,CAAC,CAAC;qBACtB;gBACF,CAAC,CAAC;gBAEF,MAAM,YAAY,GAAG,CAAC,KAAY,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC/E,IAAI,IAAI,CAAC,KAAK,YAAY,IAAI,EAAE;oBAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC;oBAC1B,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;oBACnC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC;oBACnE,EAAE,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC;iBACtE;gBAED,IAAI;oBACH,MAAM,GAAG,CAAC,OAAO,CAAC,CAAC;iBACnB;gBAAC,OAAO,KAAU,EAAE;oBACpB,IAAI,OAAO,CAAC,iBAAiB,IAAI,CAAC,WAAW,EAAE;wBAC9C,WAAW,CAAC,OAAO,CAAC,CAAC;qBACrB;oBAED,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;iBACxC;YACF,CAAC,CAAC,EAAE,CAAC;YAEL,OAAO,EAAE,CAAC;QACX,CAAC,CAAC;QAEF,YAAO,GAAG,CAAC,IAAY,EAAE,EAAQ,EAAE,EAAE;YACpC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBAC1B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;aACzB;QACF,CAAC,CAAC;QAEF,eAAU,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEvD,YAAO,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEjD,YAAO,GAAG,KAAK,EAAE,IAAY,EAAE,GAAG,IAAW,EAAuB,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;QAtOtG,IAAI,YAAY,YAAY,IAAI,EAAE;YACjC,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC;SAC1B;aAAM,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;YAC5C,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC;gBACrB,GAAG,EAAE,YAAY;gBACjB,SAAS,EAAE,mBAAmB;aAC9B,CAAC,CAAC;SACH;aAAM;YACN,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC;gBACrB,KAAK,EAAE,YAAY;gBACnB,SAAS,EAAE,mBAAmB;aAC9B,CAAC,CAAC;SACH;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IAClC,CAAC;CAuND;AAED,MAAM,OAAO,GAAG,MAAM,CAAC,OAAyD,CAAC;AAEjF,MAAM,aAAa,GAAG,CAAC,QAAyB,EAAE,EAAE;IACnD,MAAM,KAAK,GAAG,IAAI,iBAAiB,CAAC,EAAC,WAAW,EAAE,KAAK,EAAC,CAAC,CAAC;IAC1D,aAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAE/B,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7B,CAAC,CAAC;AAEF,MAAM,yBAAyB,GAAG,CAAC,GAAQ,EAAE,EAAE;IAC9C,MAAM,OAAO,GAAc,EAAC,GAAG,GAAG,EAAC,CAAC;IACpC,OAAO,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC,QAAQ,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;IAC3D,OAAO,OAAO,CAAC,QAAQ,CAAC;IACxB,OAAO,OAAO,CAAC,MAAM,CAAC;IACtB,OAAO,OAAO,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,kBAAkB,GAAG,CAAC,GAAQ,EAAE,EAAE;AACvC,6CAA6C;AAC7C,yBAAyB;AACzB,iCAAiC;AACjC,6DAA6D;AAC7D,2CAA2C;AAC3C,gCAAgC;AAChC,qCAAqC;AACrC,CAAC;IACA,QAAQ,EAAE,GAAG,CAAC,QAAQ;IACtB,IAAI,EAAE,GAAG,CAAC,IAAI;IACd,QAAQ,EAAE,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,IAAI,IAAI,WAAW;IACjD,IAAI,EAAE,GAAG,CAAC,IAAI;IACd,QAAQ,EAAE,GAAG,CAAC,QAAQ;IACtB,MAAM,EAAE,GAAG,CAAC,MAAM;CAClB,CAAC,CAAC;AAEJ,MAAM,cAAc,GAAG,CAAC,OAA4B,EAAE,EAAE;IACvD,MAAM,MAAM,GAAQ,EAAE,CAAC;IACvB,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;QACxC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;KAC3C;IAED,OAAO,MAAM,CAAC;AACf,CAAC,CAAC;AAEF,eAAe,gBAAgB,CAAC;AAChC,cAAc,YAAY,CAAC;AAC3B,MAAM,CAAC,MAAM,UAAU,GAAG,YAAY,CAAC", "sourcesContent": ["import EventEmitter from 'node:events';\nimport urlLib from 'node:url';\nimport crypto from 'node:crypto';\nimport stream, {PassThrough as PassThroughStream} from 'node:stream';\nimport {IncomingMessage} from 'node:http';\nimport normalizeUrl from 'normalize-url';\nimport getStream from 'get-stream';\nimport CachePolicy from 'http-cache-semantics';\nimport Response from 'responselike';\nimport Keyv from 'keyv';\nimport mimicResponse from 'mimic-response';\nimport {RequestFn, StorageAdapter, CacheResponse, CacheValue, CacheableOptions, UrlOption, CacheError, RequestError, Emitter, CacheableRequestFunction} from './types.js';\n\ntype Func = (...args: any[]) => any;\n\nclass CacheableRequest {\n\tcache: StorageAdapter;\n\tcacheRequest: RequestFn;\n\thooks: Map<string, Func> = new Map<string, Func>();\n\tconstructor(cacheRequest: RequestFn, cacheAdapter?: StorageAdapter | string) {\n\t\tif (cacheAdapter instanceof Keyv) {\n\t\t\tthis.cache = cacheAdapter;\n\t\t} else if (typeof cacheAdapter === 'string') {\n\t\t\tthis.cache = new Keyv({\n\t\t\t\turi: cacheAdapter,\n\t\t\t\tnamespace: 'cacheable-request',\n\t\t\t});\n\t\t} else {\n\t\t\tthis.cache = new Keyv({\n\t\t\t\tstore: cacheAdapter,\n\t\t\t\tnamespace: 'cacheable-request',\n\t\t\t});\n\t\t}\n\n\t\tthis.request = this.request.bind(this);\n\t\tthis.cacheRequest = cacheRequest;\n\t}\n\n\trequest = () => (options: CacheableOptions,\n\t\tcb?: (response: CacheResponse) => void): Emitter => {\n\t\tlet url;\n\t\tif (typeof options === 'string') {\n\t\t\turl = normalizeUrlObject(urlLib.parse(options));\n\t\t\toptions = {};\n\t\t} else if (options instanceof urlLib.URL) {\n\t\t\turl = normalizeUrlObject(urlLib.parse(options.toString()));\n\t\t\toptions = {};\n\t\t} else {\n\t\t\tconst [pathname, ...searchParts] = (options.path ?? '').split('?');\n\t\t\tconst search = searchParts.length > 0\n\t\t\t\t? `?${searchParts.join('?')}`\n\t\t\t\t: '';\n\t\t\turl = normalizeUrlObject({...options, pathname, search});\n\t\t}\n\n\t\toptions = {\n\t\t\theaders: {},\n\t\t\tmethod: 'GET',\n\t\t\tcache: true,\n\t\t\tstrictTtl: false,\n\t\t\tautomaticFailover: false,\n\t\t\t...options,\n\t\t\t...urlObjectToRequestOptions(url),\n\t\t};\n\t\toptions.headers = Object.fromEntries(entries(options.headers).map(([key, value]) => [(key as string).toLowerCase(), value]));\n\t\tconst ee: Emitter = new EventEmitter() as Emitter;\n\t\tconst normalizedUrlString = normalizeUrl(urlLib.format(url), {\n\t\t\tstripWWW: false, // eslint-disable-line @typescript-eslint/naming-convention\n\t\t\tremoveTrailingSlash: false,\n\t\t\tstripAuthentication: false,\n\t\t});\n\t\tlet key = `${options.method}:${normalizedUrlString}`;\n\t\t// POST, PATCH, and PUT requests may be cached, depending on the response\n\t\t// cache-control headers. As a result, the body of the request should be\n\t\t// added to the cache key in order to avoid collisions.\n\t\tif (options.body && options.method !== undefined && ['POST', 'PATCH', 'PUT'].includes(options.method)) {\n\t\t\tif (options.body instanceof stream.Readable) {\n\t\t\t\t// Streamed bodies should completely skip the cache because they may\n\t\t\t\t// or may not be hashable and in either case the stream would need to\n\t\t\t\t// close before the cache key could be generated.\n\t\t\t\toptions.cache = false;\n\t\t\t} else {\n\t\t\t\tkey += `:${crypto.createHash('md5').update(options.body).digest('hex')}`;\n\t\t\t}\n\t\t}\n\n\t\tlet revalidate: any = false;\n\t\tlet madeRequest = false;\n\t\tconst makeRequest = (options_: any) => {\n\t\t\tmadeRequest = true;\n\t\t\tlet requestErrored = false;\n\t\t\tlet requestErrorCallback: (...args: any[]) => void = () => {/* do nothing */};\n\n\t\t\tconst requestErrorPromise = new Promise<void>(resolve => {\n\t\t\t\trequestErrorCallback = () => {\n\t\t\t\t\tif (!requestErrored) {\n\t\t\t\t\t\trequestErrored = true;\n\t\t\t\t\t\tresolve();\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t});\n\t\t\tconst handler = async (response: any) => {\n\t\t\t\tif (revalidate) {\n\t\t\t\t\tresponse.status = response.statusCode;\n\t\t\t\t\tconst revalidatedPolicy = CachePolicy.fromObject(revalidate.cachePolicy).revalidatedPolicy(options_, response);\n\t\t\t\t\tif (!revalidatedPolicy.modified) {\n\t\t\t\t\t\tresponse.resume();\n\t\t\t\t\t\tawait new Promise(resolve => {\n\t\t\t\t\t\t\t// Skipping 'error' handler cause 'error' event should't be emitted for 304 response\n\t\t\t\t\t\t\tresponse\n\t\t\t\t\t\t\t\t.once('end', resolve);\n\t\t\t\t\t\t});\n\t\t\t\t\t\tconst headers = convertHeaders(revalidatedPolicy.policy.responseHeaders());\n\t\t\t\t\t\tresponse = new Response({statusCode: revalidate.statusCode, headers, body: revalidate.body, url: revalidate.url});\n\t\t\t\t\t\tresponse.cachePolicy = revalidatedPolicy.policy;\n\t\t\t\t\t\tresponse.fromCache = true;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (!response.fromCache) {\n\t\t\t\t\tresponse.cachePolicy = new CachePolicy(options_, response, options_);\n\t\t\t\t\tresponse.fromCache = false;\n\t\t\t\t}\n\n\t\t\t\tlet clonedResponse;\n\t\t\t\tif (options_.cache && response.cachePolicy.storable()) {\n\t\t\t\t\tclonedResponse = cloneResponse(response);\n\t\t\t\t\t(async () => {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst bodyPromise = getStream.buffer(response);\n\t\t\t\t\t\t\tawait Promise.race([\n\t\t\t\t\t\t\t\trequestErrorPromise,\n\t\t\t\t\t\t\t\tnew Promise(resolve => response.once('end', resolve)), // eslint-disable-line no-promise-executor-return\n\t\t\t\t\t\t\t\tnew Promise(resolve => response.once('close', resolve)), // eslint-disable-line no-promise-executor-return\n\t\t\t\t\t\t\t]);\n\t\t\t\t\t\t\tconst body = await bodyPromise;\n\t\t\t\t\t\t\tlet value: CacheValue = {\n\t\t\t\t\t\t\t\turl: response.url,\n\t\t\t\t\t\t\t\tstatusCode: response.fromCache ? revalidate.statusCode : response.statusCode,\n\t\t\t\t\t\t\t\tbody,\n\t\t\t\t\t\t\t\tcachePolicy: response.cachePolicy.toObject(),\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tlet ttl = options_.strictTtl ? response.cachePolicy.timeToLive() : undefined;\n\t\t\t\t\t\t\tif (options_.maxTtl) {\n\t\t\t\t\t\t\t\tttl = ttl ? Math.min(ttl, options_.maxTtl) : options_.maxTtl;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (this.hooks.size > 0) {\n\t\t\t\t\t\t\t\t/* eslint-disable no-await-in-loop */\n\t\t\t\t\t\t\t\tfor (const key_ of this.hooks.keys()) {\n\t\t\t\t\t\t\t\t\tvalue = await this.runHook(key_, value, response);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t/* eslint-enable no-await-in-loop */\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tawait this.cache.set(key, value, ttl);\n\t\t\t\t\t\t} catch (error: any) {\n\t\t\t\t\t\t\tee.emit('error', new CacheError(error));\n\t\t\t\t\t\t}\n\t\t\t\t\t})();\n\t\t\t\t} else if (options_.cache && revalidate) {\n\t\t\t\t\t(async () => {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tawait this.cache.delete(key);\n\t\t\t\t\t\t} catch (error: any) {\n\t\t\t\t\t\t\tee.emit('error', new CacheError(error));\n\t\t\t\t\t\t}\n\t\t\t\t\t})();\n\t\t\t\t}\n\n\t\t\t\tee.emit('response', clonedResponse ?? response);\n\t\t\t\tif (typeof cb === 'function') {\n\t\t\t\t\tcb(clonedResponse ?? response);\n\t\t\t\t}\n\t\t\t};\n\n\t\t\ttry {\n\t\t\t\tconst request_ = this.cacheRequest(options_, handler);\n\t\t\t\trequest_.once('error', requestErrorCallback);\n\t\t\t\trequest_.once('abort', requestErrorCallback);\n\t\t\t\trequest_.once('destroy', requestErrorCallback);\n\t\t\t\tee.emit('request', request_);\n\t\t\t} catch (error: any) {\n\t\t\t\tee.emit('error', new RequestError(error));\n\t\t\t}\n\t\t};\n\n\t\t(async () => {\n\t\t\tconst get = async (options_: any) => {\n\t\t\t\tawait Promise.resolve();\n\t\t\t\tconst cacheEntry = options_.cache ? await this.cache.get(key) : undefined;\n\n\t\t\t\tif (cacheEntry === undefined && !options_.forceRefresh) {\n\t\t\t\t\tmakeRequest(options_);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tconst policy = CachePolicy.fromObject(cacheEntry.cachePolicy);\n\t\t\t\tif (policy.satisfiesWithoutRevalidation(options_) && !options_.forceRefresh) {\n\t\t\t\t\tconst headers = convertHeaders(policy.responseHeaders());\n\t\t\t\t\tconst response: any = new Response({statusCode: cacheEntry.statusCode, headers, body: cacheEntry.body, url: cacheEntry.url});\n\t\t\t\t\tresponse.cachePolicy = policy;\n\t\t\t\t\tresponse.fromCache = true;\n\t\t\t\t\tee.emit('response', response);\n\t\t\t\t\tif (typeof cb === 'function') {\n\t\t\t\t\t\tcb(response);\n\t\t\t\t\t}\n\t\t\t\t} else if (policy.satisfiesWithoutRevalidation(options_) && Date.now() >= policy.timeToLive() && options_.forceRefresh) {\n\t\t\t\t\tawait this.cache.delete(key);\n\t\t\t\t\toptions_.headers = policy.revalidationHeaders(options_);\n\t\t\t\t\tmakeRequest(options_);\n\t\t\t\t} else {\n\t\t\t\t\trevalidate = cacheEntry;\n\t\t\t\t\toptions_.headers = policy.revalidationHeaders(options_);\n\t\t\t\t\tmakeRequest(options_);\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tconst errorHandler = (error: Error) => ee.emit('error', new CacheError(error));\n\t\t\tif (this.cache instanceof Keyv) {\n\t\t\t\tconst cachek = this.cache;\n\t\t\t\tcachek.once('error', errorHandler);\n\t\t\t\tee.on('error', () => cachek.removeListener('error', errorHandler));\n\t\t\t\tee.on('response', () => cachek.removeListener('error', errorHandler));\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\tawait get(options);\n\t\t\t} catch (error: any) {\n\t\t\t\tif (options.automaticFailover && !madeRequest) {\n\t\t\t\t\tmakeRequest(options);\n\t\t\t\t}\n\n\t\t\t\tee.emit('error', new CacheError(error));\n\t\t\t}\n\t\t})();\n\n\t\treturn ee;\n\t};\n\n\taddHook = (name: string, fn: Func) => {\n\t\tif (!this.hooks.has(name)) {\n\t\t\tthis.hooks.set(name, fn);\n\t\t}\n\t};\n\n\tremoveHook = (name: string) => this.hooks.delete(name);\n\n\tgetHook = (name: string) => this.hooks.get(name);\n\n\trunHook = async (name: string, ...args: any[]): Promise<CacheValue> => this.hooks.get(name)?.(...args);\n}\n\nconst entries = Object.entries as <T>(object: T) => Array<[keyof T, T[keyof T]]>;\n\nconst cloneResponse = (response: IncomingMessage) => {\n\tconst clone = new PassThroughStream({autoDestroy: false});\n\tmimicResponse(response, clone);\n\n\treturn response.pipe(clone);\n};\n\nconst urlObjectToRequestOptions = (url: any) => {\n\tconst options: UrlOption = {...url};\n\toptions.path = `${url.pathname || '/'}${url.search || ''}`;\n\tdelete options.pathname;\n\tdelete options.search;\n\treturn options;\n};\n\nconst normalizeUrlObject = (url: any) =>\n\t// If url was parsed by url.parse or new URL:\n\t// - hostname will be set\n\t// - host will be hostname[:port]\n\t// - port will be set if it was explicit in the parsed string\n\t// Otherwise, url was from request options:\n\t// - hostname or host may be set\n\t// - host shall not have port encoded\n\t({\n\t\tprotocol: url.protocol,\n\t\tauth: url.auth,\n\t\thostname: url.hostname || url.host || 'localhost',\n\t\tport: url.port,\n\t\tpathname: url.pathname,\n\t\tsearch: url.search,\n\t});\n\nconst convertHeaders = (headers: CachePolicy.Headers) => {\n\tconst result: any = [];\n\tfor (const name of Object.keys(headers)) {\n\t\tresult[name.toLowerCase()] = headers[name];\n\t}\n\n\treturn result;\n};\n\nexport default CacheableRequest;\nexport * from './types.js';\nexport const onResponse = 'onResponse';\n"]}