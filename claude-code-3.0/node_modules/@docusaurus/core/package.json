{"name": "@docusaurus/core", "description": "Easy to Maintain Open Source Documentation Websites", "version": "3.8.1", "license": "MIT", "publishConfig": {"access": "public"}, "keywords": ["react", "static site generator", "webpack", "documentation", "websites", "open source", "<PERSON>cusaurus"], "repository": {"type": "git", "url": "https://github.com/facebook/docusaurus.git", "directory": "packages/docusaurus"}, "bin": {"docusaurus": "bin/docusaurus.mjs"}, "scripts": {"build": "tsc --build && node ../../admin/scripts/copyUntypedFiles.js", "watch": "run-p -c copy:watch build:watch", "build:watch": "tsc --build --watch", "copy:watch": "node ../../admin/scripts/copyUntypedFiles.js --watch"}, "bugs": {"url": "https://github.com/facebook/docusaurus/issues"}, "dependencies": {"@docusaurus/babel": "3.8.1", "@docusaurus/bundler": "3.8.1", "@docusaurus/logger": "3.8.1", "@docusaurus/mdx-loader": "3.8.1", "@docusaurus/utils": "3.8.1", "@docusaurus/utils-common": "3.8.1", "@docusaurus/utils-validation": "3.8.1", "boxen": "^6.2.1", "chalk": "^4.1.2", "chokidar": "^3.5.3", "cli-table3": "^0.6.3", "combine-promises": "^1.1.0", "commander": "^5.1.0", "core-js": "^3.31.1", "detect-port": "^1.5.1", "escape-html": "^1.0.3", "eta": "^2.2.0", "eval": "^0.1.8", "execa": "5.1.1", "fs-extra": "^11.1.1", "html-tags": "^3.3.1", "html-webpack-plugin": "^5.6.0", "leven": "^3.1.0", "lodash": "^4.17.21", "open": "^8.4.0", "p-map": "^4.0.0", "prompts": "^2.4.2", "react-helmet-async": "npm:@slorber/react-helmet-async@1.3.0", "react-loadable": "npm:@docusaurus/react-loadable@6.0.0", "react-loadable-ssr-addon-v5-slorber": "^1.0.1", "react-router": "^5.3.4", "react-router-config": "^5.1.1", "react-router-dom": "^5.3.4", "semver": "^7.5.4", "serve-handler": "^6.1.6", "tinypool": "^1.0.2", "tslib": "^2.6.0", "update-notifier": "^6.0.2", "webpack": "^5.95.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-dev-server": "^4.15.2", "webpack-merge": "^6.0.1"}, "devDependencies": {"@docusaurus/module-type-aliases": "3.8.1", "@docusaurus/types": "3.8.1", "@total-typescript/shoehorn": "^0.1.2", "@types/detect-port": "^1.3.3", "@types/react-dom": "^18.2.7", "@types/react-router-config": "^5.0.7", "@types/serve-handler": "^6.1.4", "@types/update-notifier": "^6.0.4", "@types/webpack-bundle-analyzer": "^4.7.0", "react-test-renderer": "^18.0.0", "tmp-promise": "^3.0.3", "tree-node-cli": "^1.6.0"}, "peerDependencies": {"@mdx-js/react": "^3.0.0", "react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "engines": {"node": ">=18.0"}, "gitHead": "fa8ae13e668fcbc0481ce10c0a734e2a5b397293"}