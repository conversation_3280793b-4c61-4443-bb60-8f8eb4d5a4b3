/**
 * Claude Code 3.0 - System Type Definitions
 * 
 * Core system types and interfaces for the 7-layer architecture.
 * Based on the "Documentation as Software" philosophy.
 */

// ============================================================================
// System Configuration
// ============================================================================

export interface SystemConfig {
  // Core System Settings
  version: string;
  environment: 'development' | 'production' | 'test';
  enableDebugMode: boolean;
  enableMetrics: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  
  // Performance Settings
  maxConcurrentOperations: number;
  messageQueueSize: number;
  enableHotReload: boolean;
  optimizeForProduction: boolean;
  
  // Layer Configuration
  layers: {
    cli: CLILayerConfig;
    ui: UILayerConfig;
    steering: SteeringLayerConfig;
    event: EventLayerConfig;
    message: MessageLayerConfig;
    agent: AgentLayerConfig;
    tool: ToolLayerConfig;
    api: APILayerConfig;
  };
  
  // Security Settings
  security: SecurityConfig;
  
  // Integration Settings
  integrations: IntegrationConfig;
}

// ============================================================================
// Layer Configurations
// ============================================================================

export interface CLILayerConfig {
  enabled: boolean;
  defaultCommand: string;
  enableAutoComplete: boolean;
  enableColorOutput: boolean;
}

export interface UILayerConfig {
  enabled: boolean;
  port: number;
  theme: 'light' | 'dark' | 'auto';
  enableHotReload: boolean;
  enableDevTools: boolean;
}

export interface SteeringLayerConfig {
  enabled: boolean;
  bufferSize: number;
  backpressureStrategy: 'drop_oldest' | 'drop_newest' | 'block';
  enableMetrics: boolean;
  flushInterval: number;
}

export interface EventLayerConfig {
  enabled: boolean;
  maxListeners: number;
  enableEventHistory: boolean;
  eventHistorySize: number;
}

export interface MessageLayerConfig {
  enabled: boolean;
  maxMessageSize: number;
  compressionEnabled: boolean;
  enableMessageHistory: boolean;
}

export interface AgentLayerConfig {
  enabled: boolean;
  maxConcurrentAgents: number;
  defaultModel: string;
  enableContextCompression: boolean;
  contextWindowSize: number;
}

export interface ToolLayerConfig {
  enabled: boolean;
  maxConcurrentTools: number;
  enableToolMetrics: boolean;
  toolTimeout: number;
}

export interface APILayerConfig {
  enabled: boolean;
  claudeApiKey?: string;
  rateLimitConfig: RateLimitConfig;
  enableRetry: boolean;
  maxRetries: number;
}

// ============================================================================
// System State
// ============================================================================

export interface SystemState {
  status: SystemStatus;
  startTime: Date;
  uptime: number;
  
  // Layer States
  layers: {
    cli: LayerState;
    ui: LayerState;
    steering: LayerState;
    event: LayerState;
    message: LayerState;
    agent: LayerState;
    tool: LayerState;
    api: LayerState;
  };
  
  // Performance Metrics
  metrics: SystemMetrics;
  
  // Error State
  errors: SystemError[];
  
  // Resource Usage
  resources: ResourceUsage;
}

export type SystemStatus = 
  | 'initializing'
  | 'running'
  | 'paused'
  | 'stopping'
  | 'stopped'
  | 'error';

export interface LayerState {
  status: SystemStatus;
  initialized: boolean;
  lastActivity: Date;
  errorCount: number;
  metrics?: Record<string, any>;
}

// ============================================================================
// Supporting Types
// ============================================================================

export interface SecurityConfig {
  enableSandbox: boolean;
  allowedFileOperations: string[];
  maxFileSize: number;
  enableAuditLog: boolean;
}

export interface IntegrationConfig {
  enableClaudeAPI: boolean;
  enableWebSocket: boolean;
  enableFileWatcher: boolean;
  enableGitIntegration: boolean;
}

export interface RateLimitConfig {
  requestsPerMinute: number;
  requestsPerHour: number;
  enableBurst: boolean;
  burstSize: number;
}

export interface SystemMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  memoryUsage: number;
  cpuUsage: number;
  activeConnections: number;
}

export interface SystemError {
  id: string;
  timestamp: Date;
  level: 'warning' | 'error' | 'critical';
  layer: string;
  message: string;
  stack?: string;
  context?: Record<string, any>;
}

export interface ResourceUsage {
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  cpu: {
    usage: number;
    cores: number;
  };
  disk: {
    used: number;
    available: number;
    percentage: number;
  };
}

// ============================================================================
// Default Configuration
// ============================================================================

export const DEFAULT_SYSTEM_CONFIG: SystemConfig = {
  version: '3.0.0',
  environment: 'development',
  enableDebugMode: true,
  enableMetrics: true,
  logLevel: 'info',
  maxConcurrentOperations: 10,
  messageQueueSize: 1000,
  enableHotReload: true,
  optimizeForProduction: false,
  
  layers: {
    cli: {
      enabled: true,
      defaultCommand: 'help',
      enableAutoComplete: true,
      enableColorOutput: true
    },
    ui: {
      enabled: true,
      port: 3000,
      theme: 'auto',
      enableHotReload: true,
      enableDevTools: true
    },
    steering: {
      enabled: true,
      bufferSize: 1000,
      backpressureStrategy: 'drop_oldest',
      enableMetrics: true,
      flushInterval: 100
    },
    event: {
      enabled: true,
      maxListeners: 100,
      enableEventHistory: true,
      eventHistorySize: 1000
    },
    message: {
      enabled: true,
      maxMessageSize: 1024 * 1024, // 1MB
      compressionEnabled: true,
      enableMessageHistory: true
    },
    agent: {
      enabled: true,
      maxConcurrentAgents: 5,
      defaultModel: 'claude-3-sonnet',
      enableContextCompression: true,
      contextWindowSize: 200000
    },
    tool: {
      enabled: true,
      maxConcurrentTools: 10,
      enableToolMetrics: true,
      toolTimeout: 30000 // 30 seconds
    },
    api: {
      enabled: true,
      rateLimitConfig: {
        requestsPerMinute: 60,
        requestsPerHour: 1000,
        enableBurst: true,
        burstSize: 10
      },
      enableRetry: true,
      maxRetries: 3
    }
  },
  
  security: {
    enableSandbox: true,
    allowedFileOperations: ['read', 'write', 'create', 'delete'],
    maxFileSize: 10 * 1024 * 1024, // 10MB
    enableAuditLog: true
  },
  
  integrations: {
    enableClaudeAPI: true,
    enableWebSocket: true,
    enableFileWatcher: true,
    enableGitIntegration: true
  }
};
