/**
 * Claude Code 3.0 - h2A Message Queue Type Definitions
 * 
 * Type definitions for the h2A dual-buffer async message queue system.
 * Implements real-time steering mechanism for Claude Code.
 */

// ============================================================================
// Core Message Types
// ============================================================================

export interface Message<T = any> {
  id: string;
  timestamp: Date;
  type: string;
  payload: T;
  priority: MessagePriority;
  metadata?: MessageMetadata;
}

export interface MessageMetadata {
  source: string;
  destination?: string;
  correlationId?: string;
  retryCount?: number;
  maxRetries?: number;
  timeout?: number;
  tags?: string[];
}

export enum MessagePriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  CRITICAL = 3
}

// ============================================================================
// Queue Configuration
// ============================================================================

export interface QueueConfig {
  // Buffer Configuration
  maxBufferSize: number;
  enableDualBuffer: boolean;
  bufferSwitchThreshold: number;
  
  // Backpressure Strategy
  backpressureStrategy: BackpressureStrategy;
  backpressureThreshold: number;
  
  // Performance Settings
  batchSize: number;
  flushInterval: number;
  enableMetrics: boolean;
  
  // Error Handling
  enableRetry: boolean;
  maxRetries: number;
  retryDelay: number;
  
  // Monitoring
  enableHealthCheck: boolean;
  healthCheckInterval: number;
}

export enum BackpressureStrategy {
  DROP_OLDEST = 'drop_oldest',
  DROP_NEWEST = 'drop_newest',
  BLOCK = 'block',
  EXPAND_BUFFER = 'expand_buffer'
}

// ============================================================================
// Queue State and Metrics
// ============================================================================

export interface QueueState {
  status: QueueStatus;
  activeBuffer: 'primary' | 'secondary';
  primaryBufferSize: number;
  secondaryBufferSize: number;
  totalMessages: number;
  processingRate: number;
  lastActivity: Date;
}

export enum QueueStatus {
  IDLE = 'idle',
  PROCESSING = 'processing',
  BACKPRESSURE = 'backpressure',
  ERROR = 'error',
  SHUTDOWN = 'shutdown'
}

export interface QueueMetrics {
  // Throughput Metrics
  messagesProcessed: number;
  messagesDropped: number;
  averageProcessingTime: number;
  throughputPerSecond: number;
  
  // Buffer Metrics
  bufferUtilization: number;
  bufferSwitches: number;
  maxBufferSizeReached: number;
  
  // Error Metrics
  errorCount: number;
  retryCount: number;
  timeoutCount: number;
  
  // Performance Metrics
  memoryUsage: number;
  cpuUsage: number;
  latency: {
    p50: number;
    p95: number;
    p99: number;
  };
}

// ============================================================================
// Event Types
// ============================================================================

export interface QueueEvent<T = any> {
  type: QueueEventType;
  timestamp: Date;
  data: T;
}

export enum QueueEventType {
  MESSAGE_ENQUEUED = 'message_enqueued',
  MESSAGE_DEQUEUED = 'message_dequeued',
  MESSAGE_PROCESSED = 'message_processed',
  MESSAGE_DROPPED = 'message_dropped',
  MESSAGE_RETRY = 'message_retry',
  MESSAGE_TIMEOUT = 'message_timeout',
  BUFFER_SWITCH = 'buffer_switch',
  BACKPRESSURE_ACTIVATED = 'backpressure_activated',
  BACKPRESSURE_DEACTIVATED = 'backpressure_deactivated',
  QUEUE_ERROR = 'queue_error',
  QUEUE_SHUTDOWN = 'queue_shutdown'
}

// ============================================================================
// Handler Types
// ============================================================================

export type MessageHandler<T = any> = (message: Message<T>) => Promise<void> | void;
export type ErrorHandler = (error: Error, message?: Message) => Promise<void> | void;
export type EventHandler<T = any> = (event: QueueEvent<T>) => Promise<void> | void;

// ============================================================================
// Queue Interface
// ============================================================================

export interface IMessageQueue<T = any> {
  // Core Operations
  enqueue(message: Message<T>): Promise<boolean>;
  dequeue(): Promise<Message<T> | null>;
  peek(): Promise<Message<T> | null>;
  clear(): Promise<void>;
  
  // Batch Operations
  enqueueBatch(messages: Message<T>[]): Promise<number>;
  dequeueBatch(count: number): Promise<Message<T>[]>;
  
  // State Management
  getState(): QueueState;
  getMetrics(): QueueMetrics;
  isHealthy(): boolean;
  
  // Event Management
  on(event: QueueEventType, handler: EventHandler): void;
  off(event: QueueEventType, handler: EventHandler): void;
  emit(event: QueueEventType, data?: any): void;
  
  // Lifecycle
  start(): Promise<void>;
  stop(): Promise<void>;
  pause(): Promise<void>;
  resume(): Promise<void>;
}

// ============================================================================
// Steering Layer Types
// ============================================================================

export interface SteeringConfig {
  // Queue Configuration
  queueConfig: QueueConfig;
  
  // Steering Settings
  enableRealTimeProcessing: boolean;
  steeringInterval: number;
  adaptiveThrottling: boolean;
  
  // Flow Control
  enableFlowControl: boolean;
  flowControlThreshold: number;
  
  // Integration Settings
  enableAgentIntegration: boolean;
  enableUIIntegration: boolean;
  enableToolIntegration: boolean;
}

export interface SteeringState {
  isActive: boolean;
  currentLoad: number;
  throttleLevel: number;
  lastSteeringAction: Date;
  queueState: QueueState;
}

// ============================================================================
// Default Configurations
// ============================================================================

export const DEFAULT_QUEUE_CONFIG: QueueConfig = {
  maxBufferSize: 1000,
  enableDualBuffer: true,
  bufferSwitchThreshold: 0.8,
  backpressureStrategy: BackpressureStrategy.DROP_OLDEST,
  backpressureThreshold: 0.9,
  batchSize: 10,
  flushInterval: 100,
  enableMetrics: true,
  enableRetry: true,
  maxRetries: 3,
  retryDelay: 1000,
  enableHealthCheck: true,
  healthCheckInterval: 5000
};

export const DEFAULT_STEERING_CONFIG: SteeringConfig = {
  queueConfig: DEFAULT_QUEUE_CONFIG,
  enableRealTimeProcessing: true,
  steeringInterval: 50,
  adaptiveThrottling: true,
  enableFlowControl: true,
  flowControlThreshold: 0.8,
  enableAgentIntegration: true,
  enableUIIntegration: true,
  enableToolIntegration: true
};
