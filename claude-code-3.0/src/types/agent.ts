/**
 * Claude Code 3.0 - Agent Type Definitions
 * 
 * Type definitions for the Agent layer, including the nO async generator
 * functions and event-driven processing system.
 */

// ============================================================================
// Core Agent Types
// ============================================================================

export interface AgentConfig {
  // Model Configuration
  model: string;
  apiKey?: string;
  baseURL?: string;
  
  // Processing Configuration
  maxConcurrentRequests: number;
  contextWindowSize: number;
  enableContextCompression: boolean;
  compressionThreshold: number;
  
  // Streaming Configuration
  enableStreaming: boolean;
  streamingChunkSize: number;
  streamingTimeout: number;
  
  // Error Handling
  maxRetries: number;
  retryDelay: number;
  enableFallback: boolean;
  fallbackModel?: string;
  
  // Performance
  enableCaching: boolean;
  cacheSize: number;
  enableMetrics: boolean;
}

export interface AgentState {
  status: AgentStatus;
  activeRequests: number;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  lastActivity: Date;
  contextSize: number;
  isStreaming: boolean;
}

export enum AgentStatus {
  IDLE = 'idle',
  PROCESSING = 'processing',
  STREAMING = 'streaming',
  ERROR = 'error',
  PAUSED = 'paused'
}

// ============================================================================
// Message and Context Types
// ============================================================================

export interface AgentMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: MessageMetadata;
}

export interface MessageMetadata {
  toolCalls?: ToolCall[];
  attachments?: Attachment[];
  context?: ContextData;
  priority?: number;
  tags?: string[];
}

export interface ToolCall {
  id: string;
  name: string;
  parameters: Record<string, any>;
  result?: any;
  error?: string;
}

export interface Attachment {
  id: string;
  type: 'file' | 'image' | 'document';
  name: string;
  content: string | Buffer;
  mimeType: string;
}

export interface ContextData {
  workspaceRoot?: string;
  currentFile?: string;
  selectedText?: string;
  gitBranch?: string;
  projectType?: string;
  dependencies?: string[];
}

// ============================================================================
// Conversation and Session Types
// ============================================================================

export interface Conversation {
  id: string;
  title: string;
  messages: AgentMessage[];
  createdAt: Date;
  updatedAt: Date;
  metadata: ConversationMetadata;
}

export interface ConversationMetadata {
  model: string;
  totalTokens: number;
  estimatedCost: number;
  tags: string[];
  archived: boolean;
}

export interface AgentSession {
  id: string;
  conversationId: string;
  startTime: Date;
  endTime?: Date;
  config: AgentConfig;
  state: AgentState;
  context: ContextData;
}

// ============================================================================
// Streaming Types
// ============================================================================

export interface StreamChunk {
  id: string;
  type: StreamChunkType;
  content: string;
  timestamp: Date;
  metadata?: any;
}

export enum StreamChunkType {
  TEXT = 'text',
  TOOL_CALL = 'tool_call',
  TOOL_RESULT = 'tool_result',
  ERROR = 'error',
  DONE = 'done'
}

export interface StreamingResponse {
  id: string;
  chunks: AsyncIterable<StreamChunk>;
  metadata: {
    model: string;
    startTime: Date;
    estimatedTokens?: number;
  };
}

// ============================================================================
// nO Async Generator Types
// ============================================================================

export interface nOGeneratorConfig {
  // Generator Configuration
  enableYielding: boolean;
  yieldInterval: number;
  maxIterations: number;
  
  // Context Management
  enableContextCompression: boolean;
  compressionRatio: number;
  
  // Error Handling
  enableErrorRecovery: boolean;
  maxErrors: number;
  
  // Performance
  enableBatching: boolean;
  batchSize: number;
}

export interface nOGeneratorState {
  isRunning: boolean;
  currentIteration: number;
  totalIterations: number;
  lastYield: Date;
  errors: Error[];
  performance: {
    averageIterationTime: number;
    totalProcessingTime: number;
    memoryUsage: number;
  };
}

export type nOAsyncGenerator<T = any> = AsyncGenerator<T, void, unknown>;

// ============================================================================
// Event Types
// ============================================================================

export interface AgentEvent<T = any> {
  type: AgentEventType;
  timestamp: Date;
  agentId: string;
  data: T;
}

export enum AgentEventType {
  AGENT_STARTED = 'agent_started',
  AGENT_STOPPED = 'agent_stopped',
  MESSAGE_RECEIVED = 'message_received',
  MESSAGE_PROCESSED = 'message_processed',
  STREAMING_STARTED = 'streaming_started',
  STREAMING_CHUNK = 'streaming_chunk',
  STREAMING_COMPLETED = 'streaming_completed',
  TOOL_CALL_STARTED = 'tool_call_started',
  TOOL_CALL_COMPLETED = 'tool_call_completed',
  CONTEXT_COMPRESSED = 'context_compressed',
  ERROR_OCCURRED = 'error_occurred',
  PERFORMANCE_METRIC = 'performance_metric'
}

// ============================================================================
// Tool Integration Types
// ============================================================================

export interface ToolDefinition {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
  };
}

export interface ToolExecutionContext {
  agentId: string;
  conversationId: string;
  messageId: string;
  toolCall: ToolCall;
  context: ContextData;
}

export interface ToolExecutionResult {
  success: boolean;
  result?: any;
  error?: string;
  executionTime: number;
  metadata?: any;
}

// ============================================================================
// Default Configurations
// ============================================================================

export const DEFAULT_AGENT_CONFIG: AgentConfig = {
  model: 'claude-3-sonnet-20240229',
  maxConcurrentRequests: 5,
  contextWindowSize: 200000,
  enableContextCompression: true,
  compressionThreshold: 150000,
  enableStreaming: true,
  streamingChunkSize: 1024,
  streamingTimeout: 30000,
  maxRetries: 3,
  retryDelay: 1000,
  enableFallback: true,
  fallbackModel: 'claude-3-haiku-20240307',
  enableCaching: true,
  cacheSize: 100,
  enableMetrics: true
};

export const DEFAULT_NO_GENERATOR_CONFIG: nOGeneratorConfig = {
  enableYielding: true,
  yieldInterval: 100,
  maxIterations: 10000,
  enableContextCompression: true,
  compressionRatio: 0.7,
  enableErrorRecovery: true,
  maxErrors: 5,
  enableBatching: true,
  batchSize: 10
};
