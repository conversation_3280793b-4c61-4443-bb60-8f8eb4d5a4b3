/**
 * Claude Code 3.0 - Type Definitions Index
 * 
 * Central export point for all type definitions.
 */

// System types
export * from './system.js';

// Message queue types
export * from './message-queue.js';

// Agent types
export * from './agent.js';

// Common utility types
export interface BaseConfig {
  enabled: boolean;
  [key: string]: any;
}

export interface BaseState {
  status: string;
  lastActivity: Date;
  [key: string]: any;
}

export interface BaseMetrics {
  [key: string]: number | string | boolean;
}

// Event types
export interface BaseEvent<T = any> {
  type: string;
  timestamp: Date;
  data: T;
}

// Error types
export interface BaseError {
  id: string;
  timestamp: Date;
  level: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  stack?: string;
  context?: Record<string, any>;
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Function types
export type AsyncFunction<T = void> = () => Promise<T>;
export type EventHandler<T = any> = (event: T) => void | Promise<void>;
export type ErrorHandler = (error: Error) => void | Promise<void>;

// Generic response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
}

export interface PaginatedResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

// Configuration validation types
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

// Plugin system types (for future extensibility)
export interface PluginConfig {
  name: string;
  version: string;
  enabled: boolean;
  dependencies?: string[];
  config?: Record<string, any>;
}

export interface PluginInterface {
  name: string;
  version: string;
  initialize(config: any): Promise<void>;
  start(): Promise<void>;
  stop(): Promise<void>;
  getState(): any;
}

// Logging types
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: Date;
  context?: Record<string, any>;
  source?: string;
}

// Performance monitoring types
export interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  tags?: Record<string, string>;
}

export interface PerformanceReport {
  metrics: PerformanceMetric[];
  summary: {
    averageResponseTime: number;
    throughput: number;
    errorRate: number;
    uptime: number;
  };
  period: {
    start: Date;
    end: Date;
  };
}
