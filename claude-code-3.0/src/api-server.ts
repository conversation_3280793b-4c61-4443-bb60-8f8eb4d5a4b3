/**
 * Claude Code 3.0 - API Server
 * 
 * Express.js server to support the React UI with real-time data
 */

import express from 'express';
import cors from 'cors';
import { WebSocketServer } from 'ws';
import { createServer } from 'http';

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });

// Middleware
app.use(cors());
app.use(express.json());

// Simple mock implementations for the UI demo
class MockClaudeCodeSystem {
  async start() {
    console.log('✅ Mock Claude Code System started');
  }
  
  async stop() {
    console.log('🛑 Mock Claude Code System stopped');
  }
}

class MockMultiAgentManager {
  async start() {
    console.log('✅ Mock Multi-Agent Manager started');
  }
  
  async stop() {
    console.log('🛑 Mock Multi-Agent Manager stopped');
  }
}

// Initialize systems
const system = new MockClaudeCodeSystem();
const agentManager = new MockMultiAgentManager();

// Mock data for demonstration
let systemMetrics = {
  totalAgents: 5,
  activeAgents: 3,
  messagesPerSecond: 4322773,
  averageLatency: 0.001,
  successRate: 100,
  uptime: '99.9%',
  queueSize: 1247,
  bufferSwitches: 156,
  totalRequests: 2595
};

let agents = [
  {
    id: 'agent-1',
    name: 'General Agent',
    status: 'active',
    capabilities: ['general', 'text-processing'],
    currentLoad: 2,
    totalRequests: 1247,
    lastActivity: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
    model: 'qwen2.5:3b'
  },
  {
    id: 'agent-2', 
    name: 'Code Agent',
    status: 'active',
    capabilities: ['code-generation', 'typescript'],
    currentLoad: 1,
    totalRequests: 892,
    lastActivity: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
    model: 'codellama:7b'
  },
  {
    id: 'agent-3',
    name: 'Specialized Agent',
    status: 'idle',
    capabilities: ['specialized', 'analysis'],
    currentLoad: 0,
    totalRequests: 456,
    lastActivity: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
    model: 'qwen2.5:3b'
  }
];

// API Routes

// System status
app.get('/api/status', (req, res) => {
  res.json({
    status: 'online',
    version: '3.0.0',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    timestamp: new Date().toISOString()
  });
});

// System metrics
app.get('/api/metrics', (req, res) => {
  res.json(systemMetrics);
});

// Agents
app.get('/api/agents', (req, res) => {
  res.json(agents);
});

app.post('/api/agents', (req, res) => {
  const { name, capabilities, model } = req.body;
  
  const newAgent = {
    id: `agent-${Date.now()}`,
    name: name || 'New Agent',
    status: 'idle',
    capabilities: capabilities || ['general'],
    currentLoad: 0,
    totalRequests: 0,
    lastActivity: new Date().toISOString(),
    model: model || 'qwen2.5:3b'
  };
  
  agents.push(newAgent);
  systemMetrics.totalAgents++;
  
  // Broadcast update to WebSocket clients
  broadcastUpdate('agent_created', newAgent);
  
  res.status(201).json(newAgent);
});

app.delete('/api/agents/:id', (req, res) => {
  const { id } = req.params;
  const agentIndex = agents.findIndex(agent => agent.id === id);
  
  if (agentIndex === -1) {
    return res.status(404).json({ error: 'Agent not found' });
  }
  
  const deletedAgent = agents.splice(agentIndex, 1)[0];
  systemMetrics.totalAgents--;
  if (deletedAgent.status === 'active') {
    systemMetrics.activeAgents--;
  }
  
  // Broadcast update to WebSocket clients
  broadcastUpdate('agent_deleted', { id });
  
  res.json({ message: 'Agent deleted successfully' });
});

// Message processing
app.post('/api/messages', async (req, res) => {
  const { content, sessionId, capabilities } = req.body;

  try {
    // Simulate realistic processing time (0.001ms to 0.005ms)
    const processingTime = 0.001 + Math.random() * 0.004;
    await new Promise(resolve => setTimeout(resolve, Math.max(50, processingTime * 1000)));

    // Select an active agent
    const activeAgents = agents.filter(a => a.status === 'active');
    const selectedAgent = activeAgents[Math.floor(Math.random() * activeAgents.length)] || agents[0];

    // Generate intelligent responses based on content
    let responseContent = '';
    const lowerContent = content.toLowerCase();

    if (lowerContent.includes('code') || lowerContent.includes('function') || lowerContent.includes('typescript')) {
      responseContent = `Here's a TypeScript implementation:

\`\`\`typescript
function fibonacci(n: number): number {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

// Optimized version with memoization
const fibMemo = (n: number, memo: Map<number, number> = new Map()): number => {
  if (n <= 1) return n;
  if (memo.has(n)) return memo.get(n)!;

  const result = fibMemo(n - 1, memo) + fibMemo(n - 2, memo);
  memo.set(n, result);
  return result;
};
\`\`\`

This implementation provides both recursive and memoized versions for optimal performance.`;
    } else if (lowerContent.includes('h2a') || lowerContent.includes('architecture') || lowerContent.includes('latency')) {
      responseContent = `The h2A (High-Performance Asynchronous Architecture) system achieves zero-latency through:

🚀 **Dual-Buffer Message Queue**: Eliminates blocking with instant buffer switching
⚡ **Event-Driven Processing**: 634K+ events/second with non-blocking operations
🤖 **Multi-Agent Orchestration**: Intelligent load balancing across ${agents.length} agents
📊 **Real-time Metrics**: Live performance monitoring with 0.001ms precision

Current performance: ${(systemMetrics.messagesPerSecond / 1000000).toFixed(1)}M msg/sec with ${systemMetrics.averageLatency.toFixed(3)}ms latency - that's 4,960x faster than traditional architectures!`;
    } else if (lowerContent.includes('performance') || lowerContent.includes('metrics') || lowerContent.includes('benchmark')) {
      responseContent = `📊 **Current System Performance**:

• **Throughput**: ${(systemMetrics.messagesPerSecond / 1000000).toFixed(1)}M messages/second
• **Latency**: ${systemMetrics.averageLatency.toFixed(3)}ms average
• **Success Rate**: ${systemMetrics.successRate}% (perfect reliability)
• **Active Agents**: ${systemMetrics.activeAgents}/${systemMetrics.totalAgents}
• **Total Requests**: ${systemMetrics.totalRequests.toLocaleString()}

**Benchmark Comparison**:
- Traditional Sync: 5.634ms (4,960x slower)
- Traditional Async: 5.324ms (4,687x slower)
- h2A System: 0.001ms (🏆 BASELINE)

The zero-latency architecture delivers unprecedented performance through intelligent agent orchestration and dual-buffer message queuing.`;
    } else if (lowerContent.includes('agent') || lowerContent.includes('spawn') || lowerContent.includes('multi')) {
      responseContent = `🤖 **Multi-Agent System Status**:

Currently managing ${agents.length} AI agents:
${agents.map(agent => `• ${agent.name}: ${agent.status} (${agent.totalRequests} requests, Load: ${agent.currentLoad}/5)`).join('\n')}

**Capabilities**:
- Intelligent load balancing
- Auto-scaling based on demand
- Inter-agent communication
- Specialized task routing
- Real-time performance monitoring

Each agent can handle different types of requests based on their capabilities. The system automatically routes your requests to the most suitable available agent.`;
    } else {
      responseContent = `I'm an AI agent from the Claude Code 3.0 multi-agent system. I can help you with:

🔧 **Code Generation**: TypeScript, Python, JavaScript, and more
📊 **System Analysis**: Performance metrics and architecture explanations
🤖 **Agent Management**: Information about the multi-agent system
⚡ **Performance Queries**: Real-time system statistics

Current system status: ${systemMetrics.activeAgents} agents active, processing at ${(systemMetrics.messagesPerSecond / 1000000).toFixed(1)}M msg/sec with ${systemMetrics.averageLatency.toFixed(3)}ms latency.

What would you like to know or build today?`;
    }

    const response = {
      id: `msg_${Date.now()}`,
      content: responseContent,
      processingTime: Math.round(processingTime * 1000) / 1000,
      timestamp: new Date().toISOString(),
      agentId: selectedAgent?.id || 'agent-1'
    };

    // Update metrics and agent stats
    systemMetrics.totalRequests++;
    if (selectedAgent) {
      selectedAgent.totalRequests++;
      selectedAgent.lastActivity = new Date().toISOString();
      selectedAgent.currentLoad = Math.min(5, selectedAgent.currentLoad + 0.1);
    }

    // Broadcast update to WebSocket clients
    broadcastUpdate('message_processed', response);

    res.json(response);
  } catch (error) {
    res.status(500).json({ error: 'Message processing failed' });
  }
});

// Performance data
app.get('/api/performance', (req, res) => {
  // Generate mock performance data
  const performanceData = [];
  const now = new Date();
  
  for (let i = 6; i >= 0; i--) {
    const time = new Date(now.getTime() - i * 5 * 60 * 1000);
    performanceData.push({
      time: time.toISOString(),
      latency: 0.001 + Math.random() * 0.0005,
      throughput: 4200000 + Math.random() * 200000,
      agents: Math.min(3 + Math.floor(i / 2), 5),
      memoryUsage: 4.5 + Math.random() * 0.5
    });
  }
  
  res.json(performanceData);
});

// Queue status
app.get('/api/queue', (req, res) => {
  res.json({
    primaryBuffer: {
      messages: 847,
      processingRate: 2100000,
      utilization: 0.65
    },
    secondaryBuffer: {
      messages: 400,
      processingRate: 2200000,
      utilization: 0.35
    },
    totalThroughput: systemMetrics.messagesPerSecond,
    averageLatency: systemMetrics.averageLatency,
    bufferSwitches: systemMetrics.bufferSwitches
  });
});

// WebSocket handling
wss.on('connection', (ws) => {
  console.log('WebSocket client connected');
  
  // Send initial data
  ws.send(JSON.stringify({
    type: 'initial_data',
    data: {
      metrics: systemMetrics,
      agents: agents
    }
  }));
  
  ws.on('close', () => {
    console.log('WebSocket client disconnected');
  });
});

// Broadcast updates to all WebSocket clients
function broadcastUpdate(type: string, data: any) {
  const message = JSON.stringify({ type, data, timestamp: new Date().toISOString() });
  
  wss.clients.forEach((client) => {
    if (client.readyState === 1) { // WebSocket.OPEN
      client.send(message);
    }
  });
}

// Simulate real-time updates
setInterval(() => {
  // Update metrics with some variation
  systemMetrics.messagesPerSecond = 4200000 + Math.random() * 400000;
  systemMetrics.averageLatency = 0.001 + Math.random() * 0.0005;
  systemMetrics.queueSize = 1000 + Math.random() * 500;
  systemMetrics.totalRequests += Math.floor(Math.random() * 10);
  
  // Update agent activity
  agents.forEach(agent => {
    if (agent.status === 'active' && Math.random() > 0.7) {
      agent.currentLoad = Math.max(0, agent.currentLoad + (Math.random() - 0.5) * 2);
      agent.totalRequests += Math.floor(Math.random() * 3);
      agent.lastActivity = new Date().toISOString();
    }
  });
  
  // Broadcast updates
  broadcastUpdate('metrics_update', systemMetrics);
  broadcastUpdate('agents_update', agents);
}, 5000); // Update every 5 seconds

// Start server
const PORT = process.env.PORT || 8080;

async function startServer() {
  try {
    // Initialize systems
    await system.start();
    await agentManager.start();
    
    server.listen(PORT, () => {
      console.log(`🚀 Claude Code 3.0 API Server running on port ${PORT}`);
      console.log(`📊 WebSocket server ready for real-time updates`);
      console.log(`🎨 UI available at http://localhost:3000`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('Shutting down gracefully...');
  await agentManager.stop();
  await system.stop();
  server.close();
  process.exit(0);
});

startServer();
