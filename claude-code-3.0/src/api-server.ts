/**
 * Claude Code 3.0 - API Server
 * 
 * Express.js server to support the React UI with real-time data
 */

import express from 'express';
import cors from 'cors';
import { WebSocketServer } from 'ws';
import { createServer } from 'http';

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });

// Middleware
app.use(cors());
app.use(express.json());

// Simple mock implementations for the UI demo
class MockClaudeCodeSystem {
  async start() {
    console.log('✅ Mock Claude Code System started');
  }
  
  async stop() {
    console.log('🛑 Mock Claude Code System stopped');
  }
}

class MockMultiAgentManager {
  async start() {
    console.log('✅ Mock Multi-Agent Manager started');
  }
  
  async stop() {
    console.log('🛑 Mock Multi-Agent Manager stopped');
  }
}

// Initialize systems
const system = new MockClaudeCodeSystem();
const agentManager = new MockMultiAgentManager();

// Mock data for demonstration
let systemMetrics = {
  totalAgents: 5,
  activeAgents: 3,
  messagesPerSecond: 4322773,
  averageLatency: 0.001,
  successRate: 100,
  uptime: '99.9%',
  queueSize: 1247,
  bufferSwitches: 156,
  totalRequests: 2595
};

let agents = [
  {
    id: 'agent-1',
    name: 'General Agent',
    status: 'active',
    capabilities: ['general', 'text-processing'],
    currentLoad: 2,
    totalRequests: 1247,
    lastActivity: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
    model: 'qwen2.5:3b'
  },
  {
    id: 'agent-2', 
    name: 'Code Agent',
    status: 'active',
    capabilities: ['code-generation', 'typescript'],
    currentLoad: 1,
    totalRequests: 892,
    lastActivity: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
    model: 'codellama:7b'
  },
  {
    id: 'agent-3',
    name: 'Specialized Agent',
    status: 'idle',
    capabilities: ['specialized', 'analysis'],
    currentLoad: 0,
    totalRequests: 456,
    lastActivity: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
    model: 'qwen2.5:3b'
  }
];

// API Routes

// System status
app.get('/api/status', (req, res) => {
  res.json({
    status: 'online',
    version: '3.0.0',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    timestamp: new Date().toISOString()
  });
});

// System metrics
app.get('/api/metrics', (req, res) => {
  res.json(systemMetrics);
});

// Agents
app.get('/api/agents', (req, res) => {
  res.json(agents);
});

app.post('/api/agents', (req, res) => {
  const { name, capabilities, model } = req.body;
  
  const newAgent = {
    id: `agent-${Date.now()}`,
    name: name || 'New Agent',
    status: 'idle',
    capabilities: capabilities || ['general'],
    currentLoad: 0,
    totalRequests: 0,
    lastActivity: new Date().toISOString(),
    model: model || 'qwen2.5:3b'
  };
  
  agents.push(newAgent);
  systemMetrics.totalAgents++;
  
  // Broadcast update to WebSocket clients
  broadcastUpdate('agent_created', newAgent);
  
  res.status(201).json(newAgent);
});

app.delete('/api/agents/:id', (req, res) => {
  const { id } = req.params;
  const agentIndex = agents.findIndex(agent => agent.id === id);
  
  if (agentIndex === -1) {
    return res.status(404).json({ error: 'Agent not found' });
  }
  
  const deletedAgent = agents.splice(agentIndex, 1)[0];
  systemMetrics.totalAgents--;
  if (deletedAgent.status === 'active') {
    systemMetrics.activeAgents--;
  }
  
  // Broadcast update to WebSocket clients
  broadcastUpdate('agent_deleted', { id });
  
  res.json({ message: 'Agent deleted successfully' });
});

// LLM Integration with Ollama
async function callLocalLLM(prompt: string, agentCapabilities: string[] = ['general']): Promise<string> {
  try {
    // Enhance prompt based on agent capabilities
    let enhancedPrompt = prompt;

    if (agentCapabilities.includes('code-generation')) {
      enhancedPrompt = `You are a code generation specialist. ${prompt}. Please provide practical, working code examples with explanations.`;
    } else if (agentCapabilities.includes('text-processing')) {
      enhancedPrompt = `You are a text processing and analysis expert. ${prompt}. Please provide detailed analysis and insights.`;
    } else if (agentCapabilities.includes('specialized')) {
      enhancedPrompt = `You are a specialized AI assistant with advanced capabilities. ${prompt}. Please provide comprehensive and technical responses.`;
    } else {
      enhancedPrompt = `You are a helpful AI assistant from the Claude Code 3.0 multi-agent system. ${prompt}`;
    }

    const response = await fetch('http://localhost:11434/api/generate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: 'qwen2.5:3b',
        prompt: enhancedPrompt,
        stream: false,
        options: {
          temperature: 0.7,
          top_p: 0.9,
          max_tokens: 1000
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Ollama API error: ${response.status}`);
    }

    const data = await response.json();
    return data.response || 'I apologize, but I was unable to generate a response.';
  } catch (error) {
    console.error('LLM call failed:', error);
    return `I'm experiencing technical difficulties connecting to the local AI model. Error: ${error instanceof Error ? error.message : 'Unknown error'}`;
  }
}

// Message processing with real LLM integration
app.post('/api/messages', async (req, res) => {
  const { content, sessionId, capabilities = ['general'] } = req.body;

  try {
    const startTime = Date.now();

    // Select an active agent based on capabilities
    const activeAgents = agents.filter(a => a.status === 'active');
    let selectedAgent = activeAgents[0]; // Default fallback

    // Intelligent agent selection based on request content and capabilities
    const lowerContent = content.toLowerCase();

    if (lowerContent.includes('code') || lowerContent.includes('function') || lowerContent.includes('typescript') || lowerContent.includes('javascript')) {
      selectedAgent = activeAgents.find(a => a.capabilities.includes('code-generation')) || activeAgents[0];
    } else if (lowerContent.includes('analyze') || lowerContent.includes('explain') || lowerContent.includes('text')) {
      selectedAgent = activeAgents.find(a => a.capabilities.includes('text-processing')) || activeAgents[0];
    } else if (lowerContent.includes('advanced') || lowerContent.includes('complex') || lowerContent.includes('technical')) {
      selectedAgent = activeAgents.find(a => a.capabilities.includes('specialized')) || activeAgents[0];
    } else {
      // Round-robin for general queries
      selectedAgent = activeAgents[systemMetrics.totalRequests % activeAgents.length] || activeAgents[0];
    }

    // Call the local LLM model
    const responseContent = await callLocalLLM(content, selectedAgent?.capabilities || ['general']);

    const endTime = Date.now();
    const processingTime = (endTime - startTime) / 1000; // Convert to seconds

    const response = {
      id: `msg_${Date.now()}`,
      content: responseContent,
      processingTime: Math.round(processingTime * 1000) / 1000, // Round to 3 decimal places
      timestamp: new Date().toISOString(),
      agentId: selectedAgent?.id || 'agent-1',
      model: 'qwen2.5:3b',
      isRealLLM: true // Flag to indicate this is from real LLM
    };

    // Update metrics and agent stats
    systemMetrics.totalRequests++;
    systemMetrics.averageLatency = (systemMetrics.averageLatency * (systemMetrics.totalRequests - 1) + processingTime) / systemMetrics.totalRequests;

    if (selectedAgent) {
      selectedAgent.totalRequests++;
      selectedAgent.lastActivity = new Date().toISOString();
      selectedAgent.currentLoad = Math.min(5, selectedAgent.currentLoad + 0.2);

      // Gradually reduce load over time
      setTimeout(() => {
        selectedAgent.currentLoad = Math.max(0, selectedAgent.currentLoad - 0.1);
      }, 5000);
    }

    // Broadcast update to WebSocket clients
    broadcastUpdate('message_processed', response);

    res.json(response);
  } catch (error) {
    console.error('Message processing failed:', error);
    res.status(500).json({
      error: 'Message processing failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Performance data
app.get('/api/performance', (req, res) => {
  // Generate mock performance data
  const performanceData = [];
  const now = new Date();
  
  for (let i = 6; i >= 0; i--) {
    const time = new Date(now.getTime() - i * 5 * 60 * 1000);
    performanceData.push({
      time: time.toISOString(),
      latency: 0.001 + Math.random() * 0.0005,
      throughput: 4200000 + Math.random() * 200000,
      agents: Math.min(3 + Math.floor(i / 2), 5),
      memoryUsage: 4.5 + Math.random() * 0.5
    });
  }
  
  res.json(performanceData);
});

// Queue status
app.get('/api/queue', (req, res) => {
  res.json({
    primaryBuffer: {
      messages: 847,
      processingRate: 2100000,
      utilization: 0.65
    },
    secondaryBuffer: {
      messages: 400,
      processingRate: 2200000,
      utilization: 0.35
    },
    totalThroughput: systemMetrics.messagesPerSecond,
    averageLatency: systemMetrics.averageLatency,
    bufferSwitches: systemMetrics.bufferSwitches
  });
});

// WebSocket handling
wss.on('connection', (ws) => {
  console.log('WebSocket client connected');
  
  // Send initial data
  ws.send(JSON.stringify({
    type: 'initial_data',
    data: {
      metrics: systemMetrics,
      agents: agents
    }
  }));
  
  ws.on('close', () => {
    console.log('WebSocket client disconnected');
  });
});

// Broadcast updates to all WebSocket clients
function broadcastUpdate(type: string, data: any) {
  const message = JSON.stringify({ type, data, timestamp: new Date().toISOString() });
  
  wss.clients.forEach((client) => {
    if (client.readyState === 1) { // WebSocket.OPEN
      client.send(message);
    }
  });
}

// Simulate real-time updates
setInterval(() => {
  // Update metrics with some variation
  systemMetrics.messagesPerSecond = 4200000 + Math.random() * 400000;
  systemMetrics.averageLatency = 0.001 + Math.random() * 0.0005;
  systemMetrics.queueSize = 1000 + Math.random() * 500;
  systemMetrics.totalRequests += Math.floor(Math.random() * 10);
  
  // Update agent activity
  agents.forEach(agent => {
    if (agent.status === 'active' && Math.random() > 0.7) {
      agent.currentLoad = Math.max(0, agent.currentLoad + (Math.random() - 0.5) * 2);
      agent.totalRequests += Math.floor(Math.random() * 3);
      agent.lastActivity = new Date().toISOString();
    }
  });
  
  // Broadcast updates
  broadcastUpdate('metrics_update', systemMetrics);
  broadcastUpdate('agents_update', agents);
}, 5000); // Update every 5 seconds

// Start server
const PORT = process.env.PORT || 8080;

async function startServer() {
  try {
    // Initialize systems
    await system.start();
    await agentManager.start();
    
    server.listen(PORT, () => {
      console.log(`🚀 Claude Code 3.0 API Server running on port ${PORT}`);
      console.log(`📊 WebSocket server ready for real-time updates`);
      console.log(`🎨 UI available at http://localhost:3000`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('Shutting down gracefully...');
  await agentManager.stop();
  await system.stop();
  server.close();
  process.exit(0);
});

startServer();
