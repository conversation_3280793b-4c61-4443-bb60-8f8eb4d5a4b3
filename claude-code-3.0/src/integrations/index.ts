/**
 * Claude Code 3.0 - Integrations
 * 
 * External service integrations and LLM clients.
 */

// LLM Clients
export * from './claude-api-client.js';
export * from './ollama-client.js';

// Integration utilities
export interface IntegrationConfig {
  enabled: boolean;
  timeout: number;
  retries: number;
  enableMetrics: boolean;
}

export const DEFAULT_INTEGRATION_CONFIG: IntegrationConfig = {
  enabled: true,
  timeout: 30000,
  retries: 3,
  enableMetrics: true
};

/**
 * Integration Manager
 * Manages external service integrations
 */
export class IntegrationManager {
  private config: IntegrationConfig;
  private integrations = new Map<string, any>();
  
  constructor(config: Partial<IntegrationConfig> = {}) {
    this.config = { ...DEFAULT_INTEGRATION_CONFIG, ...config };
  }
  
  /**
   * Register integration
   */
  registerIntegration(name: string, integration: any): void {
    this.integrations.set(name, integration);
  }
  
  /**
   * Get integration
   */
  getIntegration(name: string): any {
    return this.integrations.get(name);
  }
  
  /**
   * List integrations
   */
  listIntegrations(): string[] {
    return Array.from(this.integrations.keys());
  }
  
  /**
   * Test integration connectivity
   */
  async testIntegration(name: string): Promise<boolean> {
    const integration = this.integrations.get(name);
    if (!integration) return false;
    
    try {
      if (typeof integration.testConnection === 'function') {
        const result = await integration.testConnection();
        return result.success || false;
      }
      return true;
    } catch (error) {
      return false;
    }
  }
}

/**
 * Create integration manager
 */
export function createIntegrationManager(config: Partial<IntegrationConfig> = {}): IntegrationManager {
  return new IntegrationManager(config);
}
