/**
 * Claude Code 3.0 - Core Components
 * 
 * Central export point for all core system components.
 */

// Main system
export { ClaudeCodeSystem } from './system.js';

// Core managers (placeholders for future implementation)
export class FileStateManager {
  private fileStates = new Map<string, any>();
  
  getFileState(path: string): any {
    return this.fileStates.get(path);
  }
  
  setFileState(path: string, state: any): void {
    this.fileStates.set(path, state);
  }
  
  removeFileState(path: string): boolean {
    return this.fileStates.delete(path);
  }
  
  getAllFileStates(): Map<string, any> {
    return new Map(this.fileStates);
  }
}

export class ConfigManager {
  private config: any = {};
  
  get(key: string): any {
    return this.config[key];
  }
  
  set(key: string, value: any): void {
    this.config[key] = value;
  }
  
  getAll(): any {
    return { ...this.config };
  }
  
  merge(newConfig: any): void {
    this.config = { ...this.config, ...newConfig };
  }
}

export class LoggingManager {
  private logs: any[] = [];
  
  log(level: string, message: string, context?: any): void {
    const entry = {
      level,
      message,
      context,
      timestamp: new Date()
    };
    
    this.logs.push(entry);
    
    // Keep only recent logs
    if (this.logs.length > 1000) {
      this.logs = this.logs.slice(-1000);
    }
    
    // Output to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[${level.toUpperCase()}] ${message}`, context || '');
    }
  }
  
  getLogs(level?: string): any[] {
    if (level) {
      return this.logs.filter(log => log.level === level);
    }
    return [...this.logs];
  }
  
  clearLogs(): void {
    this.logs = [];
  }
}

export class ErrorRecoveryManager {
  private errorHistory: Error[] = [];
  private recoveryStrategies = new Map<string, Function>();
  
  recordError(error: Error): void {
    this.errorHistory.push(error);
    
    // Keep only recent errors
    if (this.errorHistory.length > 100) {
      this.errorHistory = this.errorHistory.slice(-100);
    }
  }
  
  registerRecoveryStrategy(errorType: string, strategy: Function): void {
    this.recoveryStrategies.set(errorType, strategy);
  }
  
  async attemptRecovery(error: Error): Promise<boolean> {
    const strategy = this.recoveryStrategies.get(error.constructor.name);
    
    if (strategy) {
      try {
        await strategy(error);
        return true;
      } catch (recoveryError) {
        console.error('Recovery strategy failed:', recoveryError);
        return false;
      }
    }
    
    return false;
  }
  
  getErrorHistory(): Error[] {
    return [...this.errorHistory];
  }
  
  getErrorStats(): any {
    const stats = new Map<string, number>();
    
    for (const error of this.errorHistory) {
      const type = error.constructor.name;
      stats.set(type, (stats.get(type) || 0) + 1);
    }
    
    return Object.fromEntries(stats);
  }
}
