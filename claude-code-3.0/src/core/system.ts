/**
 * Claude Code 3.0 - Main System Class
 * 
 * Central orchestrator for the entire Claude Code 3.0 system.
 * Manages all layers and provides the main system interface.
 */

import { EventEmitter } from 'eventemitter3';
import { SteeringManager, createSteeringEnvironment } from '../layers/steering/index.js';
import {
  SystemConfig,
  SystemState,
  SystemStatus,
  LayerState,
  SystemMetrics,
  SystemError,
  DEFAULT_SYSTEM_CONFIG
} from '../types/system.js';

/**
 * Claude Code 3.0 System
 * 
 * Main system class that orchestrates all layers:
 * 1. CLI Layer - Command line interface
 * 2. UI Layer - React-based user interface  
 * 3. Steering Layer - h2A message queue system
 * 4. Event Layer - Event-driven processing
 * 5. Message Layer - Message handling
 * 6. Agent Layer - AI agent processing
 * 7. Tool Layer - Tool execution
 * 8. API Layer - External integrations
 */
export class ClaudeCodeSystem extends EventEmitter {
  private config: SystemConfig;
  private state: SystemState;
  
  // Core Components
  private steeringManager: SteeringManager;
  
  // Layer Managers (to be implemented)
  private cliManager?: any;
  private uiManager?: any;
  private eventManager?: any;
  private messageManager?: any;
  private agentManager?: any;
  private toolManager?: any;
  private apiManager?: any;
  
  // System Control
  private initializationPromise?: Promise<void>;
  private shutdownPromise?: Promise<void>;
  private healthCheckInterval?: NodeJS.Timeout;
  
  constructor(config: Partial<SystemConfig> = {}) {
    super();
    
    this.config = { ...DEFAULT_SYSTEM_CONFIG, ...config };
    this.state = this.initializeState();
    
    // Initialize core components
    this.steeringManager = createSteeringEnvironment(this.config.layers.steering);
    
    this.setupEventHandlers();
  }
  
  // ============================================================================
  // Public Interface
  // ============================================================================
  
  /**
   * Initialize the entire system
   */
  async initialize(): Promise<void> {
    if (this.initializationPromise) {
      return this.initializationPromise;
    }
    
    this.initializationPromise = this.performInitialization();
    return this.initializationPromise;
  }
  
  /**
   * Start the system
   */
  async start(): Promise<void> {
    if (this.state.status === SystemStatus.RUNNING) {
      return;
    }
    
    await this.initialize();
    
    this.state.status = SystemStatus.RUNNING;
    this.state.startTime = new Date();
    
    // Start core components
    await this.steeringManager.start();
    
    // Start health monitoring
    this.startHealthMonitoring();
    
    this.emit('system_started', { timestamp: new Date() });
  }
  
  /**
   * Stop the system gracefully
   */
  async stop(): Promise<void> {
    if (this.shutdownPromise) {
      return this.shutdownPromise;
    }
    
    this.shutdownPromise = this.performShutdown();
    return this.shutdownPromise;
  }
  
  /**
   * Pause the system
   */
  async pause(): Promise<void> {
    this.state.status = SystemStatus.PAUSED;
    this.stopHealthMonitoring();
    this.emit('system_paused', { timestamp: new Date() });
  }
  
  /**
   * Resume the system
   */
  async resume(): Promise<void> {
    this.state.status = SystemStatus.RUNNING;
    this.startHealthMonitoring();
    this.emit('system_resumed', { timestamp: new Date() });
  }
  
  /**
   * Get current system state
   */
  getState(): SystemState {
    this.updateState();
    return { ...this.state };
  }
  
  /**
   * Get system configuration
   */
  getConfig(): SystemConfig {
    return { ...this.config };
  }
  
  /**
   * Get comprehensive system metrics
   */
  getMetrics(): SystemMetrics {
    return {
      totalRequests: 0, // To be implemented
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      memoryUsage: process.memoryUsage().heapUsed,
      cpuUsage: 0, // To be implemented with proper CPU monitoring
      activeConnections: 0
    };
  }
  
  /**
   * Check if system is healthy
   */
  isHealthy(): boolean {
    return (
      this.state.status === SystemStatus.RUNNING &&
      this.state.errors.length < 10 &&
      this.steeringManager.getState().isActive
    );
  }
  
  /**
   * Get steering manager for direct access
   */
  getSteering(): SteeringManager {
    return this.steeringManager;
  }
  
  /**
   * Send a message through the system
   */
  async sendMessage<T>(message: any): Promise<boolean> {
    return await this.steeringManager.routeMessage(message);
  }
  
  // ============================================================================
  // Private Implementation
  // ============================================================================
  
  private initializeState(): SystemState {
    const now = new Date();
    
    return {
      status: SystemStatus.INITIALIZING,
      startTime: now,
      uptime: 0,
      layers: {
        cli: this.createLayerState(),
        ui: this.createLayerState(),
        steering: this.createLayerState(),
        event: this.createLayerState(),
        message: this.createLayerState(),
        agent: this.createLayerState(),
        tool: this.createLayerState(),
        api: this.createLayerState()
      },
      metrics: {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0,
        memoryUsage: 0,
        cpuUsage: 0,
        activeConnections: 0
      },
      errors: [],
      resources: {
        memory: {
          used: 0,
          total: 0,
          percentage: 0
        },
        cpu: {
          usage: 0,
          cores: 0
        },
        disk: {
          used: 0,
          available: 0,
          percentage: 0
        }
      }
    };
  }
  
  private createLayerState(): LayerState {
    return {
      status: SystemStatus.INITIALIZING,
      initialized: false,
      lastActivity: new Date(),
      errorCount: 0
    };
  }
  
  private setupEventHandlers(): void {
    // Handle steering events
    this.steeringManager.on('steering_started', () => {
      this.state.layers.steering.status = SystemStatus.RUNNING;
      this.state.layers.steering.initialized = true;
      this.emit('layer_started', { layer: 'steering' });
    });
    
    this.steeringManager.on('steering_stopped', () => {
      this.state.layers.steering.status = SystemStatus.STOPPED;
      this.emit('layer_stopped', { layer: 'steering' });
    });
    
    this.steeringManager.on('steering_error', (error) => {
      this.handleSystemError('steering', error.error);
    });
    
    // Handle system errors
    this.on('error', (error) => {
      this.handleSystemError('system', error);
    });
  }
  
  private async performInitialization(): Promise<void> {
    try {
      this.state.status = SystemStatus.INITIALIZING;
      
      // Initialize layers in dependency order
      await this.initializeLayer('steering', async () => {
        // Steering layer is already initialized in constructor
        this.state.layers.steering.initialized = true;
      });
      
      // TODO: Initialize other layers
      // await this.initializeLayer('event', () => this.initializeEventLayer());
      // await this.initializeLayer('message', () => this.initializeMessageLayer());
      // await this.initializeLayer('agent', () => this.initializeAgentLayer());
      // await this.initializeLayer('tool', () => this.initializeToolLayer());
      // await this.initializeLayer('api', () => this.initializeAPILayer());
      // await this.initializeLayer('ui', () => this.initializeUILayer());
      // await this.initializeLayer('cli', () => this.initializeCLILayer());
      
      this.state.status = SystemStatus.STOPPED; // Ready to start
      this.emit('system_initialized', { timestamp: new Date() });
      
    } catch (error) {
      this.state.status = SystemStatus.ERROR;
      this.handleSystemError('system', error as Error);
      throw error;
    }
  }
  
  private async initializeLayer(layerName: string, initializer: () => Promise<void>): Promise<void> {
    try {
      await initializer();
      this.state.layers[layerName as keyof typeof this.state.layers].initialized = true;
      this.emit('layer_initialized', { layer: layerName });
    } catch (error) {
      this.handleSystemError(layerName, error as Error);
      throw error;
    }
  }
  
  private async performShutdown(): Promise<void> {
    try {
      this.state.status = SystemStatus.STOPPING;
      
      this.stopHealthMonitoring();
      
      // Stop components in reverse order
      await this.steeringManager.stop();
      
      // TODO: Stop other layers
      
      this.state.status = SystemStatus.STOPPED;
      this.emit('system_stopped', { timestamp: new Date() });
      
    } catch (error) {
      this.state.status = SystemStatus.ERROR;
      this.handleSystemError('system', error as Error);
      throw error;
    }
  }
  
  private updateState(): void {
    const now = new Date();
    this.state.uptime = now.getTime() - this.state.startTime.getTime();
    
    // Update resource usage
    const memUsage = process.memoryUsage();
    this.state.resources.memory.used = memUsage.heapUsed;
    this.state.resources.memory.total = memUsage.heapTotal;
    this.state.resources.memory.percentage = (memUsage.heapUsed / memUsage.heapTotal) * 100;
    
    // Update layer states
    if (this.steeringManager.getState().isActive) {
      this.state.layers.steering.status = SystemStatus.RUNNING;
      this.state.layers.steering.lastActivity = now;
    }
  }
  
  private startHealthMonitoring(): void {
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, 5000); // Check every 5 seconds
  }
  
  private stopHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = undefined;
    }
  }
  
  private performHealthCheck(): void {
    const isHealthy = this.isHealthy();
    
    if (!isHealthy && this.state.status === SystemStatus.RUNNING) {
      this.emit('health_check_failed', {
        timestamp: new Date(),
        errors: this.state.errors.slice(-5) // Last 5 errors
      });
    }
    
    this.emit('health_check', {
      timestamp: new Date(),
      isHealthy,
      metrics: this.getMetrics()
    });
  }
  
  private handleSystemError(layer: string, error: Error): void {
    const systemError: SystemError = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      level: 'error',
      layer,
      message: error.message,
      stack: error.stack
    };
    
    this.state.errors.push(systemError);
    
    // Keep only recent errors
    if (this.state.errors.length > 100) {
      this.state.errors = this.state.errors.slice(-100);
    }
    
    // Update layer error count
    if (this.state.layers[layer as keyof typeof this.state.layers]) {
      this.state.layers[layer as keyof typeof this.state.layers].errorCount++;
    }
    
    this.emit('system_error', systemError);
  }
}
