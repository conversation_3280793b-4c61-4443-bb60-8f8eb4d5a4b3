/**
 * Claude Code 3.0 - Quality Metrics Engine
 * 
 * Advanced metrics calculation engine for evaluating response quality
 * using NLP techniques, heuristics, and statistical analysis.
 */

import { QualityMetrics } from './multi-agent-quality-framework.js';

export interface TextAnalysis {
  wordCount: number;
  sentenceCount: number;
  paragraphCount: number;
  averageSentenceLength: number;
  readabilityScore: number;
  complexityScore: number;
  structureScore: number;
}

export interface ContentAnalysis {
  topicCoverage: number;        // 0-1: How well the response covers the topic
  factualDensity: number;       // 0-1: Density of factual information
  exampleQuality: number;       // 0-1: Quality of examples provided
  definitionClarity: number;    // 0-1: Clarity of definitions
  logicalFlow: number;          // 0-1: Logical progression of ideas
}

export interface CoordinationAnalysis {
  agentContributions: Map<string, number>;  // Agent ID -> contribution percentage
  informationOverlap: number;               // 0-1: Redundancy between agents
  complementarity: number;                  // 0-1: How well agents complement each other
  coordinationEfficiency: number;           // 0-1: Efficiency of coordination
  responseCoherence: number;                // 0-1: Overall response coherence
}

export class QualityMetricsEngine {
  private stopWords: Set<string>;
  private technicalTerms: Set<string>;
  private conceptualIndicators: Set<string>;

  constructor() {
    this.initializeWordSets();
  }

  /**
   * Calculate comprehensive quality metrics for a response
   */
  async calculateQualityMetrics(
    question: string,
    response: string,
    metadata: {
      responseTime: number;
      agentId?: string;
      agentContributions?: Map<string, string>;
      isMultiAgent?: boolean;
    }
  ): Promise<QualityMetrics> {
    // Perform various analyses
    const textAnalysis = this.analyzeText(response);
    const contentAnalysis = this.analyzeContent(question, response);
    const coordinationAnalysis = metadata.isMultiAgent 
      ? this.analyzeCoordination(metadata.agentContributions || new Map())
      : this.getDefaultCoordinationAnalysis();

    // Calculate core quality measures
    const accuracy = this.calculateAccuracy(question, response, contentAnalysis);
    const completeness = this.calculateCompleteness(question, response, contentAnalysis);
    const clarity = this.calculateClarity(response, textAnalysis);
    const relevance = this.calculateRelevance(question, response, contentAnalysis);

    // Calculate reasoning quality
    const logicalCoherence = this.calculateLogicalCoherence(response, textAnalysis, contentAnalysis);
    const depthOfAnalysis = this.calculateDepthOfAnalysis(response, contentAnalysis);
    const evidenceSupport = this.calculateEvidenceSupport(response, contentAnalysis);

    // Calculate multi-agent specific metrics
    const coordinationEfficiency = coordinationAnalysis.coordinationEfficiency;
    const responseConsistency = coordinationAnalysis.responseCoherence;
    const redundancyLevel = coordinationAnalysis.informationOverlap;

    // Calculate performance metrics
    const tokenCount = this.estimateTokenCount(response);
    const agentUtilization = this.calculateAgentUtilization(coordinationAnalysis);

    return {
      accuracy,
      completeness,
      clarity,
      relevance,
      logicalCoherence,
      depthOfAnalysis,
      evidenceSupport,
      coordinationEfficiency,
      responseConsistency,
      redundancyLevel,
      responseTime: metadata.responseTime,
      tokenCount,
      agentUtilization
    };
  }

  /**
   * Analyze text structure and readability
   */
  private analyzeText(text: string): TextAnalysis {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = text.split(/\s+/).filter(w => w.length > 0);
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);

    const wordCount = words.length;
    const sentenceCount = sentences.length;
    const paragraphCount = paragraphs.length;
    const averageSentenceLength = wordCount / Math.max(sentenceCount, 1);

    // Calculate readability (simplified Flesch Reading Ease)
    const averageWordsPerSentence = averageSentenceLength;
    const averageSyllablesPerWord = this.estimateAverageSyllables(words);
    const readabilityScore = Math.max(0, Math.min(1, 
      (206.835 - (1.015 * averageWordsPerSentence) - (84.6 * averageSyllablesPerWord)) / 100
    ));

    // Calculate complexity based on technical terms and sentence structure
    const technicalTermCount = words.filter(word => 
      this.technicalTerms.has(word.toLowerCase())
    ).length;
    const complexityScore = Math.min(1, technicalTermCount / Math.max(wordCount * 0.1, 1));

    // Calculate structure score based on formatting and organization
    const structureScore = this.calculateStructureScore(text);

    return {
      wordCount,
      sentenceCount,
      paragraphCount,
      averageSentenceLength,
      readabilityScore,
      complexityScore,
      structureScore
    };
  }

  /**
   * Analyze content quality and coverage
   */
  private analyzeContent(question: string, response: string): ContentAnalysis {
    const questionWords = this.extractKeywords(question);
    const responseWords = this.extractKeywords(response);

    // Calculate topic coverage
    const coveredKeywords = questionWords.filter(word => 
      responseWords.includes(word)
    );
    const topicCoverage = coveredKeywords.length / Math.max(questionWords.length, 1);

    // Calculate factual density (based on specific patterns and structures)
    const factualDensity = this.calculateFactualDensity(response);

    // Calculate example quality
    const exampleQuality = this.calculateExampleQuality(response);

    // Calculate definition clarity
    const definitionClarity = this.calculateDefinitionClarity(response);

    // Calculate logical flow
    const logicalFlow = this.calculateLogicalFlow(response);

    return {
      topicCoverage,
      factualDensity,
      exampleQuality,
      definitionClarity,
      logicalFlow
    };
  }

  /**
   * Analyze multi-agent coordination
   */
  private analyzeCoordination(agentContributions: Map<string, string>): CoordinationAnalysis {
    if (agentContributions.size === 0) {
      return this.getDefaultCoordinationAnalysis();
    }

    // Calculate contribution percentages
    const contributions = new Map<string, number>();
    const totalLength = Array.from(agentContributions.values())
      .reduce((sum, content) => sum + content.length, 0);

    for (const [agentId, content] of agentContributions) {
      contributions.set(agentId, content.length / totalLength);
    }

    // Calculate information overlap (redundancy)
    const informationOverlap = this.calculateInformationOverlap(agentContributions);

    // Calculate complementarity
    const complementarity = this.calculateComplementarity(agentContributions);

    // Calculate coordination efficiency
    const coordinationEfficiency = this.calculateCoordinationEfficiency(
      agentContributions, informationOverlap, complementarity
    );

    // Calculate response coherence
    const responseCoherence = this.calculateResponseCoherence(agentContributions);

    return {
      agentContributions: contributions,
      informationOverlap,
      complementarity,
      coordinationEfficiency,
      responseCoherence
    };
  }

  // Core quality calculation methods
  private calculateAccuracy(question: string, response: string, contentAnalysis: ContentAnalysis): number {
    // Combine multiple accuracy indicators
    const topicAccuracy = contentAnalysis.topicCoverage;
    const factualAccuracy = contentAnalysis.factualDensity;
    const definitionAccuracy = contentAnalysis.definitionClarity;
    
    return (topicAccuracy * 0.4 + factualAccuracy * 0.4 + definitionAccuracy * 0.2);
  }

  private calculateCompleteness(question: string, response: string, contentAnalysis: ContentAnalysis): number {
    // Assess how completely the response addresses the question
    const topicCoverage = contentAnalysis.topicCoverage;
    const responseLength = response.length;
    const questionComplexity = this.assessQuestionComplexity(question);
    
    // Adjust expected length based on question complexity
    const expectedLength = questionComplexity * 500; // Base expectation
    const lengthScore = Math.min(1, responseLength / expectedLength);
    
    return (topicCoverage * 0.7 + lengthScore * 0.3);
  }

  private calculateClarity(response: string, textAnalysis: TextAnalysis): number {
    // Combine readability, structure, and organization
    const readabilityScore = textAnalysis.readabilityScore;
    const structureScore = textAnalysis.structureScore;
    const organizationScore = this.calculateOrganizationScore(response);
    
    return (readabilityScore * 0.4 + structureScore * 0.3 + organizationScore * 0.3);
  }

  private calculateRelevance(question: string, response: string, contentAnalysis: ContentAnalysis): number {
    // Assess how relevant the response is to the question
    return contentAnalysis.topicCoverage;
  }

  private calculateLogicalCoherence(response: string, textAnalysis: TextAnalysis, contentAnalysis: ContentAnalysis): number {
    // Assess logical flow and consistency
    return contentAnalysis.logicalFlow;
  }

  private calculateDepthOfAnalysis(response: string, contentAnalysis: ContentAnalysis): number {
    // Assess depth based on examples, explanations, and detail level
    const exampleScore = contentAnalysis.exampleQuality;
    const detailScore = this.calculateDetailLevel(response);
    const insightScore = this.calculateInsightLevel(response);
    
    return (exampleScore * 0.3 + detailScore * 0.4 + insightScore * 0.3);
  }

  private calculateEvidenceSupport(response: string, contentAnalysis: ContentAnalysis): number {
    // Assess quality of supporting evidence and examples
    return contentAnalysis.exampleQuality;
  }

  // Helper methods for detailed calculations
  private initializeWordSets(): void {
    this.stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were'
    ]);

    this.technicalTerms = new Set([
      'algorithm', 'function', 'variable', 'array', 'object', 'class', 'method', 'api', 'database',
      'machine learning', 'neural network', 'artificial intelligence', 'statistical', 'probability',
      'thermodynamics', 'quantum', 'molecular', 'cellular', 'genetic', 'evolution'
    ]);

    this.conceptualIndicators = new Set([
      'concept', 'principle', 'theory', 'mechanism', 'process', 'phenomenon', 'relationship'
    ]);
  }

  private extractKeywords(text: string): string[] {
    return text.toLowerCase()
      .split(/\s+/)
      .filter(word => word.length > 3 && !this.stopWords.has(word))
      .slice(0, 20); // Top 20 keywords
  }

  private estimateAverageSyllables(words: string[]): number {
    // Simplified syllable estimation
    return words.reduce((sum, word) => {
      const syllables = Math.max(1, word.replace(/[^aeiouAEIOU]/g, '').length);
      return sum + syllables;
    }, 0) / Math.max(words.length, 1);
  }

  private calculateStructureScore(text: string): number {
    // Check for headers, bullet points, numbered lists, etc.
    const hasHeaders = /^#+\s/.test(text) || /^[A-Z][^.]*:/.test(text);
    const hasBullets = /^\s*[-*•]\s/.test(text);
    const hasNumbers = /^\s*\d+\.\s/.test(text);
    const hasParagraphs = text.includes('\n\n');
    
    let score = 0;
    if (hasHeaders) score += 0.3;
    if (hasBullets || hasNumbers) score += 0.3;
    if (hasParagraphs) score += 0.4;
    
    return Math.min(1, score);
  }

  private calculateFactualDensity(response: string): number {
    // Look for factual patterns: numbers, dates, specific terms, citations
    const factualPatterns = [
      /\d+%/g,           // Percentages
      /\d{4}/g,          // Years
      /\d+\.\d+/g,       // Decimal numbers
      /\([^)]*\)/g,      // Parenthetical information
      /\b[A-Z][a-z]+ et al\./g, // Citations
    ];
    
    let factualCount = 0;
    for (const pattern of factualPatterns) {
      factualCount += (response.match(pattern) || []).length;
    }
    
    const wordCount = response.split(/\s+/).length;
    return Math.min(1, factualCount / Math.max(wordCount * 0.1, 1));
  }

  private calculateExampleQuality(response: string): number {
    // Look for examples, illustrations, analogies
    const exampleIndicators = [
      /for example/gi,
      /such as/gi,
      /like/gi,
      /consider/gi,
      /imagine/gi,
      /suppose/gi
    ];
    
    let exampleCount = 0;
    for (const indicator of exampleIndicators) {
      exampleCount += (response.match(indicator) || []).length;
    }
    
    return Math.min(1, exampleCount / 3); // Normalize to 0-1
  }

  private calculateDefinitionClarity(response: string): number {
    // Look for clear definitions and explanations
    const definitionPatterns = [
      /is defined as/gi,
      /refers to/gi,
      /means/gi,
      /is a/gi,
      /are/gi
    ];
    
    let definitionCount = 0;
    for (const pattern of definitionPatterns) {
      definitionCount += (response.match(pattern) || []).length;
    }
    
    return Math.min(1, definitionCount / 5);
  }

  private calculateLogicalFlow(response: string): number {
    // Look for logical connectors and transitions
    const logicalConnectors = [
      /therefore/gi,
      /however/gi,
      /furthermore/gi,
      /moreover/gi,
      /consequently/gi,
      /first/gi,
      /second/gi,
      /finally/gi
    ];
    
    let connectorCount = 0;
    for (const connector of logicalConnectors) {
      connectorCount += (response.match(connector) || []).length;
    }
    
    const paragraphCount = response.split(/\n\s*\n/).length;
    return Math.min(1, connectorCount / Math.max(paragraphCount, 1));
  }

  private getDefaultCoordinationAnalysis(): CoordinationAnalysis {
    return {
      agentContributions: new Map([['single-agent', 1.0]]),
      informationOverlap: 0,
      complementarity: 1,
      coordinationEfficiency: 1,
      responseCoherence: 1
    };
  }

  private calculateInformationOverlap(agentContributions: Map<string, string>): number {
    // Calculate semantic overlap between agent contributions
    // This is a simplified implementation
    const contributions = Array.from(agentContributions.values());
    if (contributions.length < 2) return 0;
    
    let totalOverlap = 0;
    let comparisons = 0;
    
    for (let i = 0; i < contributions.length; i++) {
      for (let j = i + 1; j < contributions.length; j++) {
        const overlap = this.calculateTextSimilarity(contributions[i], contributions[j]);
        totalOverlap += overlap;
        comparisons++;
      }
    }
    
    return comparisons > 0 ? totalOverlap / comparisons : 0;
  }

  private calculateComplementarity(agentContributions: Map<string, string>): number {
    // Calculate how well agents complement each other
    // Higher complementarity means less overlap and more diverse contributions
    const overlap = this.calculateInformationOverlap(agentContributions);
    return 1 - overlap;
  }

  private calculateCoordinationEfficiency(
    agentContributions: Map<string, string>,
    overlap: number,
    complementarity: number
  ): number {
    // Efficiency is high when there's good complementarity and low redundancy
    const balanceScore = this.calculateContributionBalance(agentContributions);
    return (complementarity * 0.5 + balanceScore * 0.3 + (1 - overlap) * 0.2);
  }

  private calculateResponseCoherence(agentContributions: Map<string, string>): number {
    // Assess how well the combined response flows together
    const combinedResponse = Array.from(agentContributions.values()).join(' ');
    const textAnalysis = this.analyzeText(combinedResponse);
    return textAnalysis.structureScore;
  }

  private calculateTextSimilarity(text1: string, text2: string): number {
    // Simplified Jaccard similarity
    const words1 = new Set(this.extractKeywords(text1));
    const words2 = new Set(this.extractKeywords(text2));
    
    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);
    
    return union.size > 0 ? intersection.size / union.size : 0;
  }

  private calculateContributionBalance(agentContributions: Map<string, string>): number {
    // Calculate how balanced the contributions are
    const lengths = Array.from(agentContributions.values()).map(content => content.length);
    const totalLength = lengths.reduce((sum, len) => sum + len, 0);
    const averageLength = totalLength / lengths.length;
    
    const variance = lengths.reduce((sum, len) => sum + Math.pow(len - averageLength, 2), 0) / lengths.length;
    const standardDeviation = Math.sqrt(variance);
    
    // Lower standard deviation means better balance
    const normalizedStdDev = standardDeviation / Math.max(averageLength, 1);
    return Math.max(0, 1 - normalizedStdDev);
  }

  private assessQuestionComplexity(question: string): number {
    // Assess question complexity based on various factors
    const wordCount = question.split(/\s+/).length;
    const technicalTermCount = this.extractKeywords(question)
      .filter(word => this.technicalTerms.has(word)).length;
    
    const baseComplexity = Math.min(1, wordCount / 20);
    const technicalComplexity = Math.min(1, technicalTermCount / 5);
    
    return (baseComplexity * 0.6 + technicalComplexity * 0.4);
  }

  private calculateOrganizationScore(response: string): number {
    // Assess overall organization and structure
    return this.calculateStructureScore(response);
  }

  private calculateDetailLevel(response: string): number {
    // Assess level of detail in the response
    const wordCount = response.split(/\s+/).length;
    const sentenceCount = response.split(/[.!?]+/).length;
    const averageSentenceLength = wordCount / Math.max(sentenceCount, 1);
    
    // Longer sentences and more words generally indicate more detail
    const lengthScore = Math.min(1, wordCount / 500);
    const sentenceScore = Math.min(1, averageSentenceLength / 20);
    
    return (lengthScore * 0.6 + sentenceScore * 0.4);
  }

  private calculateInsightLevel(response: string): number {
    // Look for insights, analysis, and deeper understanding
    const insightIndicators = [
      /this suggests/gi,
      /this implies/gi,
      /this means/gi,
      /the significance/gi,
      /importantly/gi,
      /notably/gi,
      /interestingly/gi
    ];
    
    let insightCount = 0;
    for (const indicator of insightIndicators) {
      insightCount += (response.match(indicator) || []).length;
    }
    
    return Math.min(1, insightCount / 3);
  }

  private estimateTokenCount(text: string): number {
    // Rough estimation: 1 token ≈ 0.75 words
    const wordCount = text.split(/\s+/).length;
    return Math.round(wordCount / 0.75);
  }

  private calculateAgentUtilization(coordinationAnalysis: CoordinationAnalysis): number {
    // How effectively multiple agents were utilized
    const agentCount = coordinationAnalysis.agentContributions.size;
    if (agentCount === 1) return 1; // Single agent is 100% utilized
    
    // Multi-agent utilization based on balance and efficiency
    const balance = this.calculateContributionBalance(new Map(
      Array.from(coordinationAnalysis.agentContributions.entries()).map(([id, percentage]) => 
        [id, 'x'.repeat(Math.round(percentage * 100))]
      )
    ));
    
    return (balance * 0.5 + coordinationAnalysis.coordinationEfficiency * 0.5);
  }
}
