/**
 * Claude Code 3.0 - Quality Validation Configuration
 * 
 * Configuration settings for the multi-agent quality validation framework
 */

import { ValidationConfig } from './multi-agent-quality-framework.js';

export const defaultQualityConfig: ValidationConfig = {
  // Assessment Methods
  enableAutomatedAssessment: true,
  enableHumanValidation: false,  // Can be enabled for critical validations
  enableCrossValidation: true,
  
  // Quality Thresholds
  minimumQualityThreshold: 0.6,    // 60% minimum quality score
  significanceThreshold: 0.1,      // 10% difference considered significant
  improvementThreshold: 0.05,      // 5% minimum improvement expected
  
  // Sampling and Testing
  sampleSize: 50,                  // Number of test cases per validation run
  testCaseCategories: [
    'conceptual',
    'coding', 
    'research',
    'mixed'
  ],
  validationFrequency: 24,         // Run validation every 24 hours
  
  // Weights for Overall Score Calculation
  weights: {
    accuracy: 0.20,              // 20% - Factual correctness
    completeness: 0.15,          // 15% - Coverage of question aspects
    clarity: 0.15,               // 15% - Response clarity and structure
    relevance: 0.10,             // 10% - Relevance to the question
    logicalCoherence: 0.15,      // 15% - Logical flow and consistency
    depthOfAnalysis: 0.10,       // 10% - Depth of reasoning and insight
    evidenceSupport: 0.05,       // 5% - Quality of supporting evidence
    coordinationEfficiency: 0.05, // 5% - Agent coordination quality
    responseConsistency: 0.03,   // 3% - Consistency across agents
    redundancyLevel: 0.02        // 2% - Penalty for redundancy (inverted)
  }
};

export const productionQualityConfig: ValidationConfig = {
  ...defaultQualityConfig,
  
  // Stricter thresholds for production
  minimumQualityThreshold: 0.75,
  significanceThreshold: 0.05,
  improvementThreshold: 0.1,
  
  // More comprehensive testing
  sampleSize: 100,
  validationFrequency: 12,  // Every 12 hours
  
  // Enable human validation for production
  enableHumanValidation: true,
  
  // Adjusted weights for production focus
  weights: {
    accuracy: 0.25,              // Higher weight on accuracy
    completeness: 0.20,          // Higher weight on completeness
    clarity: 0.15,
    relevance: 0.15,             // Higher weight on relevance
    logicalCoherence: 0.10,
    depthOfAnalysis: 0.08,
    evidenceSupport: 0.04,
    coordinationEfficiency: 0.02,
    responseConsistency: 0.01,
    redundancyLevel: 0.00        // Zero tolerance for redundancy in production
  }
};

export const developmentQualityConfig: ValidationConfig = {
  ...defaultQualityConfig,
  
  // More lenient for development
  minimumQualityThreshold: 0.5,
  significanceThreshold: 0.15,
  improvementThreshold: 0.0,   // Any improvement is good in development
  
  // Faster iteration
  sampleSize: 20,
  validationFrequency: 6,      // Every 6 hours
  
  // Focus on development metrics
  weights: {
    accuracy: 0.15,
    completeness: 0.10,
    clarity: 0.10,
    relevance: 0.10,
    logicalCoherence: 0.15,
    depthOfAnalysis: 0.15,
    evidenceSupport: 0.10,
    coordinationEfficiency: 0.10,  // Higher focus on coordination in dev
    responseConsistency: 0.03,
    redundancyLevel: 0.02
  }
};

/**
 * Get quality configuration based on environment
 */
export function getQualityConfig(environment: 'development' | 'production' | 'default' = 'default'): ValidationConfig {
  switch (environment) {
    case 'development':
      return developmentQualityConfig;
    case 'production':
      return productionQualityConfig;
    default:
      return defaultQualityConfig;
  }
}

/**
 * Quality validation presets for different use cases
 */
export const qualityPresets = {
  // Quick validation for development
  quick: {
    ...developmentQualityConfig,
    sampleSize: 10,
    testCaseCategories: ['conceptual', 'coding'],
    validationFrequency: 0  // Manual only
  },
  
  // Comprehensive validation for releases
  comprehensive: {
    ...productionQualityConfig,
    sampleSize: 200,
    enableHumanValidation: true,
    enableCrossValidation: true,
    testCaseCategories: ['conceptual', 'coding', 'research', 'mixed', 'edge-cases']
  },
  
  // Performance-focused validation
  performance: {
    ...defaultQualityConfig,
    weights: {
      accuracy: 0.15,
      completeness: 0.15,
      clarity: 0.10,
      relevance: 0.10,
      logicalCoherence: 0.10,
      depthOfAnalysis: 0.05,
      evidenceSupport: 0.05,
      coordinationEfficiency: 0.20,  // High focus on efficiency
      responseConsistency: 0.05,
      redundancyLevel: 0.05          // Penalty for redundancy
    }
  },
  
  // Accuracy-focused validation
  accuracy: {
    ...defaultQualityConfig,
    weights: {
      accuracy: 0.40,              // Very high focus on accuracy
      completeness: 0.20,
      clarity: 0.15,
      relevance: 0.15,
      logicalCoherence: 0.05,
      depthOfAnalysis: 0.03,
      evidenceSupport: 0.02,
      coordinationEfficiency: 0.00,
      responseConsistency: 0.00,
      redundancyLevel: 0.00
    }
  }
};

/**
 * Test case categories and their characteristics
 */
export const testCategoryDefinitions = {
  conceptual: {
    description: 'Questions requiring conceptual understanding and explanation',
    expectedAgents: ['general-agent', 'research-agent'],
    minResponseLength: 200,
    maxResponseLength: 800,
    keyMetrics: ['accuracy', 'completeness', 'clarity'],
    examples: [
      'What is quantum entanglement?',
      'Explain the concept of emergence in complex systems',
      'How does photosynthesis work at the molecular level?'
    ]
  },
  
  coding: {
    description: 'Questions requiring code generation or programming help',
    expectedAgents: ['code-agent'],
    minResponseLength: 100,
    maxResponseLength: 1000,
    keyMetrics: ['accuracy', 'completeness', 'evidenceSupport'],
    examples: [
      'Write a function to implement binary search',
      'Create a React component for data visualization',
      'Debug this Python code snippet'
    ]
  },
  
  research: {
    description: 'Questions requiring research and analysis',
    expectedAgents: ['research-agent', 'specialized-agent'],
    minResponseLength: 300,
    maxResponseLength: 1200,
    keyMetrics: ['accuracy', 'depthOfAnalysis', 'evidenceSupport'],
    examples: [
      'Compare different approaches to quantum computing',
      'Analyze the impact of climate change on biodiversity',
      'Review recent advances in machine learning'
    ]
  },
  
  mixed: {
    description: 'Questions requiring both conceptual understanding and practical implementation',
    expectedAgents: ['general-agent', 'code-agent'],
    minResponseLength: 400,
    maxResponseLength: 1500,
    keyMetrics: ['completeness', 'coordinationEfficiency', 'responseConsistency'],
    examples: [
      'Explain machine learning and implement a neural network',
      'Describe REST APIs and create an example endpoint',
      'Discuss database design principles and write SQL queries'
    ]
  }
};

/**
 * Validation thresholds for different quality aspects
 */
export const qualityThresholds = {
  // Core quality thresholds
  excellent: 0.9,
  good: 0.75,
  acceptable: 0.6,
  poor: 0.4,
  unacceptable: 0.2,
  
  // Improvement thresholds
  significantImprovement: 0.15,
  moderateImprovement: 0.1,
  minimalImprovement: 0.05,
  noImprovement: 0.0,
  degradation: -0.05,
  significantDegradation: -0.15,
  
  // Performance thresholds (in milliseconds)
  fastResponse: 1000,
  acceptableResponse: 3000,
  slowResponse: 5000,
  unacceptableResponse: 10000,
  
  // Coordination thresholds
  excellentCoordination: 0.9,
  goodCoordination: 0.75,
  acceptableCoordination: 0.6,
  poorCoordination: 0.4,
  
  // Redundancy thresholds
  minimalRedundancy: 0.1,
  acceptableRedundancy: 0.3,
  highRedundancy: 0.5,
  excessiveRedundancy: 0.7
};

/**
 * Alert configurations for quality monitoring
 */
export const alertConfig = {
  // When to trigger alerts
  triggers: {
    qualityDropBelow: 0.6,
    improvementDropBelow: 0.0,
    failureRateAbove: 0.3,
    responseTimeAbove: 5000,
    redundancyAbove: 0.5
  },
  
  // Alert severity levels
  severity: {
    critical: {
      qualityBelow: 0.4,
      improvementBelow: -0.1,
      failureRateAbove: 0.5
    },
    warning: {
      qualityBelow: 0.6,
      improvementBelow: 0.05,
      failureRateAbove: 0.3
    },
    info: {
      qualityBelow: 0.75,
      improvementBelow: 0.1,
      failureRateAbove: 0.2
    }
  }
};
