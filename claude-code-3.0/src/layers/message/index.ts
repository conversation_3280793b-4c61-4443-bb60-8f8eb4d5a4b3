/**
 * Claude Code 3.0 - Message Layer
 * 
 * Layer 5: Message Processing and Transformation
 * Message handling, transformation, and routing logic.
 */

export interface MessageConfig {
  enabled: boolean;
  enableTransformation: boolean;
  enableValidation: boolean;
  enableCompression: boolean;
  maxMessageSize: number;
}

export const DEFAULT_MESSAGE_CONFIG: MessageConfig = {
  enabled: true,
  enableTransformation: true,
  enableValidation: true,
  enableCompression: false,
  maxMessageSize: 1024 * 1024 // 1MB
};

export interface MessageTransformer {
  name: string;
  transform(message: any): Promise<any>;
}

export interface MessageValidator {
  name: string;
  validate(message: any): Promise<boolean>;
}

/**
 * Message Processor
 * Handles message transformation, validation, and processing
 */
export class MessageProcessor {
  private config: MessageConfig;
  private transformers: MessageTransformer[] = [];
  private validators: MessageValidator[] = [];
  
  constructor(config: Partial<MessageConfig> = {}) {
    this.config = { ...DEFAULT_MESSAGE_CONFIG, ...config };
  }
  
  /**
   * Add message transformer
   */
  addTransformer(transformer: MessageTransformer): void {
    this.transformers.push(transformer);
  }
  
  /**
   * Add message validator
   */
  addValidator(validator: MessageValidator): void {
    this.validators.push(validator);
  }
  
  /**
   * Process a message through the pipeline
   */
  async processMessage(message: any): Promise<any> {
    let processedMessage = message;
    
    // Validation
    if (this.config.enableValidation) {
      for (const validator of this.validators) {
        const isValid = await validator.validate(processedMessage);
        if (!isValid) {
          throw new Error(`Message validation failed: ${validator.name}`);
        }
      }
    }
    
    // Size check
    const messageSize = JSON.stringify(processedMessage).length;
    if (messageSize > this.config.maxMessageSize) {
      throw new Error(`Message size ${messageSize} exceeds limit ${this.config.maxMessageSize}`);
    }
    
    // Transformation
    if (this.config.enableTransformation) {
      for (const transformer of this.transformers) {
        processedMessage = await transformer.transform(processedMessage);
      }
    }
    
    // Compression (if enabled)
    if (this.config.enableCompression) {
      processedMessage = await this.compressMessage(processedMessage);
    }
    
    return processedMessage;
  }
  
  /**
   * Get processor configuration
   */
  getConfig(): MessageConfig {
    return { ...this.config };
  }
  
  /**
   * Get registered transformers
   */
  getTransformers(): MessageTransformer[] {
    return [...this.transformers];
  }
  
  /**
   * Get registered validators
   */
  getValidators(): MessageValidator[] {
    return [...this.validators];
  }
  
  private async compressMessage(message: any): Promise<any> {
    // Simple compression simulation
    // In real implementation, would use actual compression
    return {
      ...message,
      _compressed: true,
      _originalSize: JSON.stringify(message).length
    };
  }
}

/**
 * Built-in message transformers
 */
export const builtInTransformers = {
  timestampTransformer: {
    name: 'timestamp',
    async transform(message: any) {
      return {
        ...message,
        timestamp: message.timestamp || new Date().toISOString()
      };
    }
  },
  
  idTransformer: {
    name: 'id',
    async transform(message: any) {
      return {
        ...message,
        id: message.id || `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };
    }
  }
};

/**
 * Built-in message validators
 */
export const builtInValidators = {
  requiredFieldsValidator: {
    name: 'required-fields',
    async validate(message: any) {
      return message && typeof message === 'object' && message.type;
    }
  },
  
  typeValidator: {
    name: 'type',
    async validate(message: any) {
      return typeof message.type === 'string' && message.type.length > 0;
    }
  }
};

/**
 * Create message processor instance
 */
export function createMessageProcessor(config: Partial<MessageConfig> = {}): MessageProcessor {
  const processor = new MessageProcessor(config);
  
  // Add built-in transformers
  processor.addTransformer(builtInTransformers.timestampTransformer);
  processor.addTransformer(builtInTransformers.idTransformer);
  
  // Add built-in validators
  processor.addValidator(builtInValidators.requiredFieldsValidator);
  processor.addValidator(builtInValidators.typeValidator);
  
  return processor;
}
