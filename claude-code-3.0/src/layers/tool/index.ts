/**
 * Claude Code 3.0 - Tool Layer
 * 
 * Layer 7: Tool Execution and Management
 * Extensible tool system for agent capabilities.
 */

export interface ToolConfig {
  enabled: boolean;
  maxConcurrentExecutions: number;
  executionTimeout: number;
  enableSandbox: boolean;
  enableMetrics: boolean;
}

export const DEFAULT_TOOL_CONFIG: ToolConfig = {
  enabled: true,
  maxConcurrentExecutions: 5,
  executionTimeout: 30000, // 30 seconds
  enableSandbox: true,
  enableMetrics: true
};

export interface ToolDefinition {
  name: string;
  description: string;
  parameters: Record<string, any>;
  execute: (parameters: any, context?: ToolExecutionContext) => Promise<any>;
}

export interface ToolExecutionContext {
  sessionId?: string;
  userId?: string;
  agentId?: string;
  metadata?: Record<string, any>;
}

export interface ToolExecutionResult {
  success: boolean;
  result?: any;
  error?: string;
  executionTime: number;
  metadata?: Record<string, any>;
}

/**
 * Tool Manager
 * Manages tool registration, execution, and lifecycle
 */
export class ToolManager {
  private config: ToolConfig;
  private tools = new Map<string, ToolDefinition>();
  private activeExecutions = new Set<string>();
  private metrics = {
    totalExecutions: 0,
    successfulExecutions: 0,
    failedExecutions: 0,
    averageExecutionTime: 0
  };
  
  constructor(config: Partial<ToolConfig> = {}) {
    this.config = { ...DEFAULT_TOOL_CONFIG, ...config };
  }
  
  /**
   * Register a tool
   */
  registerTool(tool: ToolDefinition): void {
    this.tools.set(tool.name, tool);
  }
  
  /**
   * Unregister a tool
   */
  unregisterTool(name: string): boolean {
    return this.tools.delete(name);
  }
  
  /**
   * Get registered tool
   */
  getTool(name: string): ToolDefinition | undefined {
    return this.tools.get(name);
  }
  
  /**
   * List all registered tools
   */
  listTools(): ToolDefinition[] {
    return Array.from(this.tools.values());
  }
  
  /**
   * Execute a tool
   */
  async executeTool(
    name: string, 
    parameters: any, 
    context?: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    const tool = this.tools.get(name);
    if (!tool) {
      return {
        success: false,
        error: `Tool '${name}' not found`,
        executionTime: 0
      };
    }
    
    // Check concurrent execution limit
    if (this.activeExecutions.size >= this.config.maxConcurrentExecutions) {
      return {
        success: false,
        error: 'Maximum concurrent executions reached',
        executionTime: 0
      };
    }
    
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.activeExecutions.add(executionId);
    
    const startTime = Date.now();
    
    try {
      // Create execution timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Tool execution timeout')), this.config.executionTimeout);
      });
      
      // Execute tool with timeout
      const result = await Promise.race([
        tool.execute(parameters, context),
        timeoutPromise
      ]);
      
      const executionTime = Date.now() - startTime;
      
      // Update metrics
      if (this.config.enableMetrics) {
        this.updateMetrics(executionTime, true);
      }
      
      return {
        success: true,
        result,
        executionTime,
        metadata: {
          toolName: name,
          executionId
        }
      };
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      // Update metrics
      if (this.config.enableMetrics) {
        this.updateMetrics(executionTime, false);
      }
      
      return {
        success: false,
        error: (error as Error).message,
        executionTime,
        metadata: {
          toolName: name,
          executionId
        }
      };
      
    } finally {
      this.activeExecutions.delete(executionId);
    }
  }
  
  /**
   * Get tool execution metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Get active executions count
   */
  getActiveExecutionsCount(): number {
    return this.activeExecutions.size;
  }
  
  private updateMetrics(executionTime: number, success: boolean): void {
    this.metrics.totalExecutions++;
    
    if (success) {
      this.metrics.successfulExecutions++;
    } else {
      this.metrics.failedExecutions++;
    }
    
    // Update average execution time
    const totalTime = this.metrics.averageExecutionTime * (this.metrics.totalExecutions - 1) + executionTime;
    this.metrics.averageExecutionTime = totalTime / this.metrics.totalExecutions;
  }
}

/**
 * Built-in tools
 */
export const builtInTools = {
  echo: {
    name: 'echo',
    description: 'Echo back the input parameters',
    parameters: {
      message: { type: 'string', required: true }
    },
    async execute(parameters: any) {
      return { echo: parameters.message };
    }
  },
  
  timestamp: {
    name: 'timestamp',
    description: 'Get current timestamp',
    parameters: {},
    async execute() {
      return { timestamp: new Date().toISOString() };
    }
  },
  
  random: {
    name: 'random',
    description: 'Generate random number',
    parameters: {
      min: { type: 'number', default: 0 },
      max: { type: 'number', default: 100 }
    },
    async execute(parameters: any) {
      const min = parameters.min || 0;
      const max = parameters.max || 100;
      return { random: Math.floor(Math.random() * (max - min + 1)) + min };
    }
  }
};

/**
 * Create tool manager instance
 */
export function createToolManager(config: Partial<ToolConfig> = {}): ToolManager {
  const manager = new ToolManager(config);
  
  // Register built-in tools
  Object.values(builtInTools).forEach(tool => {
    manager.registerTool(tool);
  });
  
  return manager;
}
