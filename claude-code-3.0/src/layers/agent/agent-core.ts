/**
 * Claude Code 3.0 - Agent Core Engine
 * 
 * Core implementation of the AI agent with nO async generator functions
 * and event-driven processing system.
 */

import { EventEmitter } from 'eventemitter3';
import { v4 as uuidv4 } from 'uuid';
import {
  AgentConfig,
  AgentState,
  AgentStatus,
  AgentMessage,
  AgentSession,
  Conversation,
  StreamChunk,
  StreamChunkType,
  StreamingResponse,
  nOAsyncGenerator,
  nOGeneratorConfig,
  nOGeneratorState,
  AgentEvent,
  AgentEventType,
  ToolCall,
  ToolExecutionContext,
  ToolExecutionResult,
  DEFAULT_AGENT_CONFIG,
  DEFAULT_NO_GENERATOR_CONFIG
} from '../../types/agent.js';

/**
 * Agent Core Engine
 * 
 * Features:
 * - nO async generator-based processing
 * - Event-driven architecture
 * - Streaming response support
 * - Context compression
 * - Tool integration
 * - Error recovery
 * - Performance monitoring
 */
export class AgentCore extends EventEmitter {
  private config: AgentConfig;
  private state: AgentState;
  private generatorConfig: nOGeneratorConfig;
  private generatorState: nOGeneratorState;
  
  // Session Management
  private activeSessions = new Map<string, AgentSession>();
  private conversations = new Map<string, Conversation>();
  
  // Processing Control
  private abortController?: AbortController;
  private processingQueue: AgentMessage[] = [];
  private isProcessing = false;
  
  // Performance Tracking
  private performanceMetrics: number[] = [];
  private lastProcessingTime = Date.now();
  
  constructor(
    config: Partial<AgentConfig> = {},
    generatorConfig: Partial<nOGeneratorConfig> = {}
  ) {
    super();
    
    this.config = { ...DEFAULT_AGENT_CONFIG, ...config };
    this.generatorConfig = { ...DEFAULT_NO_GENERATOR_CONFIG, ...generatorConfig };
    
    this.state = this.initializeState();
    this.generatorState = this.initializeGeneratorState();
    
    this.setupEventHandlers();
  }
  
  // ============================================================================
  // Public Interface
  // ============================================================================
  
  /**
   * Start the agent core
   */
  async start(): Promise<void> {
    if (this.state.status !== AgentStatus.IDLE) {
      return;
    }
    
    this.state.status = AgentStatus.IDLE;
    this.state.lastActivity = new Date();
    
    this.emit('agent_started', { agentId: this.getId(), timestamp: new Date() });
  }
  
  /**
   * Stop the agent core
   */
  async stop(): Promise<void> {
    if (this.abortController) {
      this.abortController.abort();
    }
    
    this.state.status = AgentStatus.IDLE;
    this.isProcessing = false;
    
    // Clean up active sessions
    for (const session of this.activeSessions.values()) {
      session.endTime = new Date();
    }
    
    this.emit('agent_stopped', { agentId: this.getId(), timestamp: new Date() });
  }
  
  /**
   * Process a message using the nO async generator
   */
  async processMessage(message: AgentMessage, sessionId?: string): Promise<StreamingResponse> {
    const session = sessionId ? this.getOrCreateSession(sessionId) : this.createSession();
    
    this.state.activeRequests++;
    this.state.totalRequests++;
    this.state.status = AgentStatus.PROCESSING;
    
    const startTime = Date.now();
    
    try {
      // Add message to conversation
      const conversation = this.getOrCreateConversation(session.conversationId);
      conversation.messages.push(message);
      
      // Create streaming response
      const response: StreamingResponse = {
        id: uuidv4(),
        chunks: this.nOMainLoop(message, session),
        metadata: {
          model: this.config.model,
          startTime: new Date(),
          estimatedTokens: this.estimateTokens(message.content)
        }
      };
      
      this.emit('message_received', {
        agentId: this.getId(),
        messageId: message.id,
        sessionId: session.id
      });
      
      return response;
      
    } catch (error) {
      this.state.failedRequests++;
      this.handleError(error as Error, session.id);
      throw error;
    } finally {
      this.state.activeRequests--;
      
      const processingTime = Date.now() - startTime;
      this.updatePerformanceMetrics(processingTime);
      
      if (this.state.activeRequests === 0) {
        this.state.status = AgentStatus.IDLE;
      }
    }
  }
  
  /**
   * Get agent state
   */
  getState(): AgentState {
    this.updateState();
    return { ...this.state };
  }
  
  /**
   * Get generator state
   */
  getGeneratorState(): nOGeneratorState {
    return { ...this.generatorState };
  }
  
  /**
   * Get active sessions
   */
  getActiveSessions(): AgentSession[] {
    return Array.from(this.activeSessions.values());
  }
  
  /**
   * Get conversation by ID
   */
  getConversation(conversationId: string): Conversation | undefined {
    return this.conversations.get(conversationId);
  }
  
  // ============================================================================
  // nO Async Generator Implementation
  // ============================================================================
  
  /**
   * Main nO async generator loop
   * 
   * This is the core processing loop that handles:
   * - Message processing
   * - Context management
   * - Tool execution
   * - Streaming responses
   * - Error recovery
   */
  private async* nOMainLoop(
    message: AgentMessage,
    session: AgentSession
  ): nOAsyncGenerator<StreamChunk> {
    this.generatorState.isRunning = true;
    this.generatorState.currentIteration = 0;
    
    const startTime = Date.now();
    
    try {
      // Initialize abort controller for this processing session
      this.abortController = new AbortController();
      
      // Get conversation context
      const conversation = this.getOrCreateConversation(session.conversationId);
      
      // Check if context compression is needed
      if (this.shouldCompressContext(conversation)) {
        yield* this.compressContext(conversation);
      }
      
      // Process the message through multiple iterations
      while (
        this.generatorState.currentIteration < this.generatorConfig.maxIterations &&
        this.generatorState.isRunning
      ) {
        const iterationStart = Date.now();
        
        // Check for abort signal
        if (this.abortController.signal.aborted) {
          break;
        }
        
        // Yield control periodically
        if (this.shouldYield()) {
          await this.yieldControl();
        }
        
        // Process current iteration
        yield* this.processIteration(message, conversation, session);
        
        // Update iteration metrics
        this.generatorState.currentIteration++;
        const iterationTime = Date.now() - iterationStart;
        this.updateIterationMetrics(iterationTime);
        
        // Check if processing is complete
        if (this.isProcessingComplete(message, conversation)) {
          break;
        }
      }
      
      // Emit completion chunk
      yield {
        id: uuidv4(),
        type: StreamChunkType.DONE,
        content: '',
        timestamp: new Date(),
        metadata: {
          totalIterations: this.generatorState.currentIteration,
          processingTime: Date.now() - startTime
        }
      };
      
    } catch (error) {
      this.generatorState.errors.push(error as Error);
      
      yield {
        id: uuidv4(),
        type: StreamChunkType.ERROR,
        content: (error as Error).message,
        timestamp: new Date(),
        metadata: { error: error as Error }
      };
      
      if (this.generatorConfig.enableErrorRecovery) {
        yield* this.recoverFromError(error as Error, session);
      }
      
    } finally {
      this.generatorState.isRunning = false;
      this.generatorState.totalIterations = this.generatorState.currentIteration;
      
      const totalTime = Date.now() - startTime;
      this.generatorState.performance.totalProcessingTime = totalTime;
      this.generatorState.performance.averageIterationTime = 
        totalTime / Math.max(this.generatorState.currentIteration, 1);
    }
  }
  
  /**
   * Process a single iteration of the nO loop
   */
  private async* processIteration(
    message: AgentMessage,
    conversation: Conversation,
    session: AgentSession
  ): nOAsyncGenerator<StreamChunk> {
    // Simulate AI processing (in real implementation, this would call Claude API)
    const response = await this.simulateAIResponse(message, conversation);
    
    // Check for tool calls in the response
    const toolCalls = this.extractToolCalls(response);
    
    if (toolCalls.length > 0) {
      // Execute tool calls
      for (const toolCall of toolCalls) {
        yield* this.executeToolCall(toolCall, session);
      }
    }
    
    // Yield response chunks
    const chunks = this.chunkResponse(response);
    for (const chunk of chunks) {
      yield chunk;
    }
    
    // Add response to conversation
    const assistantMessage: AgentMessage = {
      id: uuidv4(),
      role: 'assistant',
      content: response,
      timestamp: new Date(),
      metadata: {
        toolCalls: toolCalls.length > 0 ? toolCalls : undefined
      }
    };
    
    conversation.messages.push(assistantMessage);
    conversation.updatedAt = new Date();
  }
  
  /**
   * Execute a tool call
   */
  private async* executeToolCall(
    toolCall: ToolCall,
    session: AgentSession
  ): nOAsyncGenerator<StreamChunk> {
    yield {
      id: uuidv4(),
      type: StreamChunkType.TOOL_CALL,
      content: `Executing tool: ${toolCall.name}`,
      timestamp: new Date(),
      metadata: { toolCall }
    };
    
    try {
      // Simulate tool execution (in real implementation, this would call actual tools)
      const result = await this.simulateToolExecution(toolCall, session);
      
      yield {
        id: uuidv4(),
        type: StreamChunkType.TOOL_RESULT,
        content: `Tool result: ${JSON.stringify(result)}`,
        timestamp: new Date(),
        metadata: { toolCall, result }
      };
      
      toolCall.result = result;
      
    } catch (error) {
      toolCall.error = (error as Error).message;
      
      yield {
        id: uuidv4(),
        type: StreamChunkType.ERROR,
        content: `Tool error: ${(error as Error).message}`,
        timestamp: new Date(),
        metadata: { toolCall, error }
      };
    }
  }
  
  // ============================================================================
  // Private Implementation
  // ============================================================================
  
  private initializeState(): AgentState {
    return {
      status: AgentStatus.IDLE,
      activeRequests: 0,
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      lastActivity: new Date(),
      contextSize: 0,
      isStreaming: false
    };
  }
  
  private initializeGeneratorState(): nOGeneratorState {
    return {
      isRunning: false,
      currentIteration: 0,
      totalIterations: 0,
      lastYield: new Date(),
      errors: [],
      performance: {
        averageIterationTime: 0,
        totalProcessingTime: 0,
        memoryUsage: 0
      }
    };
  }
  
  private setupEventHandlers(): void {
    this.on('error', (error) => {
      this.state.status = AgentStatus.ERROR;
      console.error('Agent error:', error);
    });
  }
  
  private getId(): string {
    return 'agent-core-' + Math.random().toString(36).substr(2, 9);
  }
  
  private createSession(): AgentSession {
    const session: AgentSession = {
      id: uuidv4(),
      conversationId: uuidv4(),
      startTime: new Date(),
      config: this.config,
      state: this.state,
      context: {}
    };
    
    this.activeSessions.set(session.id, session);
    return session;
  }
  
  private getOrCreateSession(sessionId: string): AgentSession {
    let session = this.activeSessions.get(sessionId);
    if (!session) {
      session = this.createSession();
      session.id = sessionId;
      this.activeSessions.set(sessionId, session);
    }
    return session;
  }
  
  private getOrCreateConversation(conversationId: string): Conversation {
    let conversation = this.conversations.get(conversationId);
    if (!conversation) {
      conversation = {
        id: conversationId,
        title: 'New Conversation',
        messages: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        metadata: {
          model: this.config.model,
          totalTokens: 0,
          estimatedCost: 0,
          tags: [],
          archived: false
        }
      };
      this.conversations.set(conversationId, conversation);
    }
    return conversation;
  }
  
  private shouldCompressContext(conversation: Conversation): boolean {
    const totalTokens = this.estimateConversationTokens(conversation);
    return this.config.enableContextCompression && 
           totalTokens > this.config.compressionThreshold;
  }
  
  private async* compressContext(conversation: Conversation): nOAsyncGenerator<StreamChunk> {
    yield {
      id: uuidv4(),
      type: StreamChunkType.TEXT,
      content: 'Compressing conversation context...',
      timestamp: new Date()
    };
    
    // Simulate context compression
    const originalLength = conversation.messages.length;
    const targetLength = Math.floor(originalLength * this.generatorConfig.compressionRatio);
    
    // Keep system messages and recent messages
    const systemMessages = conversation.messages.filter(m => m.role === 'system');
    const recentMessages = conversation.messages.slice(-targetLength);
    
    conversation.messages = [...systemMessages, ...recentMessages];
    
    this.emit('context_compressed', {
      conversationId: conversation.id,
      originalLength,
      compressedLength: conversation.messages.length
    });
  }
  
  private shouldYield(): boolean {
    if (!this.generatorConfig.enableYielding) return false;
    
    const now = Date.now();
    const timeSinceLastYield = now - this.generatorState.lastYield.getTime();
    
    return timeSinceLastYield >= this.generatorConfig.yieldInterval;
  }
  
  private async yieldControl(): Promise<void> {
    this.generatorState.lastYield = new Date();
    
    // Yield control to event loop
    await new Promise(resolve => setImmediate(resolve));
  }
  
  private isProcessingComplete(message: AgentMessage, conversation: Conversation): boolean {
    // Simple completion check - in real implementation, this would be more sophisticated
    return conversation.messages.length > 0 && 
           conversation.messages[conversation.messages.length - 1].role === 'assistant';
  }
  
  private async simulateAIResponse(message: AgentMessage, conversation: Conversation): Promise<string> {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
    
    // Return a simulated response
    return `This is a simulated response to: "${message.content}". In a real implementation, this would call the Claude API.`;
  }
  
  private extractToolCalls(response: string): ToolCall[] {
    // Simple tool call extraction - in real implementation, this would parse actual tool calls
    if (response.includes('tool:')) {
      return [{
        id: uuidv4(),
        name: 'example_tool',
        parameters: { query: 'example' }
      }];
    }
    return [];
  }
  
  private async simulateToolExecution(toolCall: ToolCall, session: AgentSession): Promise<any> {
    // Simulate tool execution delay
    await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));
    
    return { result: `Executed ${toolCall.name} with parameters: ${JSON.stringify(toolCall.parameters)}` };
  }
  
  private chunkResponse(response: string): StreamChunk[] {
    const chunks: StreamChunk[] = [];
    const chunkSize = this.config.streamingChunkSize;
    
    for (let i = 0; i < response.length; i += chunkSize) {
      chunks.push({
        id: uuidv4(),
        type: StreamChunkType.TEXT,
        content: response.slice(i, i + chunkSize),
        timestamp: new Date()
      });
    }
    
    return chunks;
  }
  
  private estimateTokens(text: string): number {
    // Simple token estimation - in real implementation, use proper tokenizer
    return Math.ceil(text.length / 4);
  }
  
  private estimateConversationTokens(conversation: Conversation): number {
    return conversation.messages.reduce((total, message) => {
      return total + this.estimateTokens(message.content);
    }, 0);
  }
  
  private updateState(): void {
    this.state.lastActivity = new Date();
    this.state.contextSize = Array.from(this.conversations.values())
      .reduce((total, conv) => total + this.estimateConversationTokens(conv), 0);
  }
  
  private updatePerformanceMetrics(processingTime: number): void {
    this.performanceMetrics.push(processingTime);
    
    // Keep only recent metrics
    if (this.performanceMetrics.length > 1000) {
      this.performanceMetrics = this.performanceMetrics.slice(-1000);
    }
    
    // Update average response time
    this.state.averageResponseTime = 
      this.performanceMetrics.reduce((sum, time) => sum + time, 0) / this.performanceMetrics.length;
  }
  
  private updateIterationMetrics(iterationTime: number): void {
    const currentAvg = this.generatorState.performance.averageIterationTime;
    const iterations = this.generatorState.currentIteration;
    
    this.generatorState.performance.averageIterationTime = 
      (currentAvg * iterations + iterationTime) / (iterations + 1);
  }
  
  private async* recoverFromError(error: Error, session: AgentSession): nOAsyncGenerator<StreamChunk> {
    yield {
      id: uuidv4(),
      type: StreamChunkType.TEXT,
      content: 'Attempting error recovery...',
      timestamp: new Date()
    };
    
    // Simple error recovery - in real implementation, this would be more sophisticated
    if (this.generatorState.errors.length < this.generatorConfig.maxErrors) {
      yield {
        id: uuidv4(),
        type: StreamChunkType.TEXT,
        content: 'Error recovered. Continuing processing...',
        timestamp: new Date()
      };
    } else {
      throw new Error('Maximum error count exceeded');
    }
  }
  
  private handleError(error: Error, sessionId: string): void {
    this.emit('error_occurred', {
      agentId: this.getId(),
      sessionId,
      error: error.message,
      timestamp: new Date()
    });
  }
}
