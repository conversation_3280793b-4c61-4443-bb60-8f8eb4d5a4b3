/**
 * Claude Code 3.0 - Agent Layer
 * 
 * AI agent processing layer with nO async generator functions.
 */

// Core exports
export { AgentCore } from './agent-core.js';
export { MultiAgentManager } from './multi-agent-manager.js';

// Type exports
export * from '../../types/agent.js';

// Factory functions
import { AgentCore } from './agent-core.js';
import { AgentConfig, DEFAULT_AGENT_CONFIG } from '../../types/agent.js';

/**
 * Create a standard agent with default configuration
 */
export function createAgent(config?: Partial<AgentConfig>): AgentCore {
  return new AgentCore(config);
}

/**
 * Create a high-performance agent optimized for throughput
 */
export function createHighPerformanceAgent(): AgentCore {
  return new AgentCore({
    ...DEFAULT_AGENT_CONFIG,
    maxConcurrentRequests: 10,
    enableStreaming: true,
    streamingChunkSize: 2048,
    enableCaching: true,
    cacheSize: 200
  });
}

/**
 * Create a memory-efficient agent for resource-constrained environments
 */
export function createMemoryEfficientAgent(): Agent<PERSON>ore {
  return new AgentCore({
    ...DEFAULT_AGENT_CONFIG,
    maxConcurrentRequests: 2,
    contextWindowSize: 50000,
    enableContextCompression: true,
    compressionThreshold: 30000,
    enableCaching: false,
    enableMetrics: false
  });
}

/**
 * Create a development agent with enhanced debugging
 */
export function createDevelopmentAgent(): AgentCore {
  return new AgentCore({
    ...DEFAULT_AGENT_CONFIG,
    enableMetrics: true,
    enableStreaming: true,
    maxRetries: 1, // Fail fast in development
    enableFallback: false
  });
}

/**
 * Create multi-agent manager with default configuration
 */
export function createMultiAgentManager(config = {}) {
  return new MultiAgentManager(config);
}

// Legacy Agent Manager (deprecated - use MultiAgentManager instead)
export class AgentManager {
  private agents = new Map<string, AgentCore>();
  
  async createAgent(id: string, config?: Partial<AgentConfig>): Promise<AgentCore> {
    const agent = new AgentCore(config);
    await agent.start();
    this.agents.set(id, agent);
    return agent;
  }
  
  getAgent(id: string): AgentCore | undefined {
    return this.agents.get(id);
  }
  
  async removeAgent(id: string): Promise<boolean> {
    const agent = this.agents.get(id);
    if (agent) {
      await agent.stop();
      this.agents.delete(id);
      return true;
    }
    return false;
  }
  
  getAllAgents(): AgentCore[] {
    return Array.from(this.agents.values());
  }
  
  async stopAll(): Promise<void> {
    const stopPromises = Array.from(this.agents.values()).map(agent => agent.stop());
    await Promise.all(stopPromises);
    this.agents.clear();
  }
}
