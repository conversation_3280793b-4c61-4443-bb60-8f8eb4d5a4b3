/**
 * Claude Code 3.0 - Steering Layer
 * 
 * h2A dual-buffer async message queue system with real-time steering mechanism.
 * This layer provides the foundation for all inter-component communication.
 */

// ============================================================================
// Core Exports
// ============================================================================

export {
  h2AMessageQueue,
  createMessageQueue,
  createHighPerformanceQueue,
  createMemoryEfficientQueue
} from './message-queue.js';

export { SteeringManager } from './steering-manager.js';

// ============================================================================
// Type Exports
// ============================================================================

export * from '../../types/message-queue.js';

// ============================================================================
// Factory Functions
// ============================================================================

import { SteeringManager } from './steering-manager.js';
import { SteeringConfig, DEFAULT_STEERING_CONFIG } from '../../types/message-queue.js';

/**
 * Create a complete steering environment with default configuration
 */
export function createSteeringEnvironment(config?: Partial<SteeringConfig>): SteeringManager {
  return new SteeringManager(config);
}

/**
 * Create a high-performance steering environment optimized for throughput
 */
export function createHighPerformanceSteeringEnvironment(): SteeringManager {
  return new SteeringManager({
    ...DEFAULT_STEERING_CONFIG,
    steeringInterval: 25, // Faster steering
    queueConfig: {
      ...DEFAULT_STEERING_CONFIG.queueConfig,
      maxBufferSize: 5000,
      batchSize: 50,
      flushInterval: 10
    }
  });
}

/**
 * Create a memory-efficient steering environment for resource-constrained environments
 */
export function createMemoryEfficientSteeringEnvironment(): SteeringManager {
  return new SteeringManager({
    ...DEFAULT_STEERING_CONFIG,
    steeringInterval: 100, // Slower steering
    queueConfig: {
      ...DEFAULT_STEERING_CONFIG.queueConfig,
      maxBufferSize: 200,
      batchSize: 5,
      flushInterval: 200,
      enableMetrics: false
    }
  });
}

/**
 * Create a development steering environment with enhanced debugging
 */
export function createDevelopmentSteeringEnvironment(): SteeringManager {
  return new SteeringManager({
    ...DEFAULT_STEERING_CONFIG,
    enableRealTimeProcessing: true,
    adaptiveThrottling: true,
    enableFlowControl: true,
    queueConfig: {
      ...DEFAULT_STEERING_CONFIG.queueConfig,
      enableMetrics: true,
      enableHealthCheck: true,
      healthCheckInterval: 1000 // More frequent health checks
    }
  });
}
