/**
 * Claude Code 3.0 - h2A Dual-Buffer Async Message Queue
 * 
 * Core implementation of the h2A (High-performance Async) message queue system
 * with dual-buffer architecture for real-time steering mechanism.
 */

import { EventEmitter } from 'eventemitter3';
import { v4 as uuidv4 } from 'uuid';
import {
  Message,
  MessagePriority,
  QueueConfig,
  QueueState,
  QueueStatus,
  QueueMetrics,
  QueueEvent,
  QueueEventType,
  BackpressureStrategy,
  IMessageQueue,
  MessageHandler,
  ErrorHandler,
  EventHandler,
  DEFAULT_QUEUE_CONFIG
} from '../../types/message-queue.js';

/**
 * h2A Dual-Buffer Async Message Queue Implementation
 * 
 * Features:
 * - Dual-buffer architecture for non-blocking operations
 * - Priority-based message handling
 * - Backpressure management
 * - Real-time metrics and monitoring
 * - Event-driven architecture
 * - Automatic error recovery
 */
export class h2AMessageQueue<T = any> extends EventEmitter implements IMessageQueue<T> {
  private config: QueueConfig;
  private state: QueueState;
  private metrics: QueueMetrics;
  
  // Dual Buffer System
  private primaryBuffer: Message<T>[] = [];
  private secondaryBuffer: Message<T>[] = [];
  private activeBuffer: 'primary' | 'secondary' = 'primary';
  
  // Processing Control
  private isProcessing = false;
  private processingInterval?: NodeJS.Timeout;
  private healthCheckInterval?: NodeJS.Timeout;
  
  // Performance Tracking
  private processingTimes: number[] = [];
  private lastProcessingTime = Date.now();
  
  constructor(config: Partial<QueueConfig> = {}) {
    super();
    
    this.config = { ...DEFAULT_QUEUE_CONFIG, ...config };
    this.state = this.initializeState();
    this.metrics = this.initializeMetrics();
    
    this.setupEventHandlers();
  }
  
  // ============================================================================
  // Core Queue Operations
  // ============================================================================
  
  async enqueue(message: Message<T>): Promise<boolean> {
    try {
      // Validate message
      if (!this.validateMessage(message)) {
        this.emit(QueueEventType.QUEUE_ERROR, { error: 'Invalid message format' });
        return false;
      }
      
      // Check backpressure
      if (this.isBackpressureActive()) {
        return this.handleBackpressure(message);
      }
      
      // Add to active buffer
      const buffer = this.getActiveBuffer();
      buffer.push(message);
      
      // Update metrics
      this.metrics.messagesProcessed++;
      this.state.totalMessages++;
      this.state.lastActivity = new Date();
      
      // Emit event
      this.emit(QueueEventType.MESSAGE_ENQUEUED, { message });
      
      // Check if buffer switch is needed
      if (this.shouldSwitchBuffer()) {
        await this.switchBuffer();
      }
      
      // Start processing if not already running
      if (!this.isProcessing) {
        this.startProcessing();
      }
      
      return true;
    } catch (error) {
      this.handleError(error as Error, message);
      return false;
    }
  }
  
  async dequeue(): Promise<Message<T> | null> {
    try {
      const buffer = this.getProcessingBuffer();
      
      if (buffer.length === 0) {
        return null;
      }
      
      // Get highest priority message
      const message = this.getHighestPriorityMessage(buffer);
      
      if (message) {
        // Remove from buffer
        const index = buffer.indexOf(message);
        buffer.splice(index, 1);
        
        // Update state
        this.updateBufferSizes();
        this.state.lastActivity = new Date();
        
        // Emit event
        this.emit(QueueEventType.MESSAGE_DEQUEUED, { message });
      }
      
      return message;
    } catch (error) {
      this.handleError(error as Error);
      return null;
    }
  }
  
  async peek(): Promise<Message<T> | null> {
    const buffer = this.getProcessingBuffer();
    return buffer.length > 0 ? this.getHighestPriorityMessage(buffer) : null;
  }
  
  async clear(): Promise<void> {
    this.primaryBuffer = [];
    this.secondaryBuffer = [];
    this.updateBufferSizes();
    this.state.totalMessages = 0;
    this.emit(QueueEventType.QUEUE_ERROR, { action: 'clear' });
  }
  
  // ============================================================================
  // Batch Operations
  // ============================================================================
  
  async enqueueBatch(messages: Message<T>[]): Promise<number> {
    let successCount = 0;
    
    for (const message of messages) {
      if (await this.enqueue(message)) {
        successCount++;
      }
    }
    
    return successCount;
  }
  
  async dequeueBatch(count: number): Promise<Message<T>[]> {
    const messages: Message<T>[] = [];
    
    for (let i = 0; i < count; i++) {
      const message = await this.dequeue();
      if (message) {
        messages.push(message);
      } else {
        break;
      }
    }
    
    return messages;
  }
  
  // ============================================================================
  // State and Metrics
  // ============================================================================
  
  getState(): QueueState {
    this.updateBufferSizes();
    return { ...this.state };
  }
  
  getMetrics(): QueueMetrics {
    this.updateMetrics();
    return { ...this.metrics };
  }
  
  isHealthy(): boolean {
    const state = this.getState();
    const metrics = this.getMetrics();
    
    return (
      state.status !== QueueStatus.ERROR &&
      metrics.errorCount < 10 &&
      metrics.bufferUtilization < 0.95
    );
  }
  
  // ============================================================================
  // Lifecycle Management
  // ============================================================================
  
  async start(): Promise<void> {
    if (this.state.status === QueueStatus.PROCESSING) {
      return;
    }
    
    this.state.status = QueueStatus.PROCESSING;
    this.startProcessing();
    
    if (this.config.enableHealthCheck) {
      this.startHealthCheck();
    }
    
    this.emit(QueueEventType.MESSAGE_PROCESSED, { action: 'start' });
  }
  
  async stop(): Promise<void> {
    this.state.status = QueueStatus.SHUTDOWN;
    this.stopProcessing();
    this.stopHealthCheck();
    
    this.emit(QueueEventType.QUEUE_SHUTDOWN, { action: 'stop' });
  }
  
  async pause(): Promise<void> {
    this.state.status = QueueStatus.IDLE;
    this.stopProcessing();
  }
  
  async resume(): Promise<void> {
    this.state.status = QueueStatus.PROCESSING;
    this.startProcessing();
  }
  
  // ============================================================================
  // Private Implementation
  // ============================================================================
  
  private initializeState(): QueueState {
    return {
      status: QueueStatus.IDLE,
      activeBuffer: 'primary',
      primaryBufferSize: 0,
      secondaryBufferSize: 0,
      totalMessages: 0,
      processingRate: 0,
      lastActivity: new Date()
    };
  }
  
  private initializeMetrics(): QueueMetrics {
    return {
      messagesProcessed: 0,
      messagesDropped: 0,
      averageProcessingTime: 0,
      throughputPerSecond: 0,
      bufferUtilization: 0,
      bufferSwitches: 0,
      maxBufferSizeReached: 0,
      errorCount: 0,
      retryCount: 0,
      timeoutCount: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      latency: {
        p50: 0,
        p95: 0,
        p99: 0
      }
    };
  }
  
  private setupEventHandlers(): void {
    // Handle uncaught errors
    this.on('error', (error) => {
      this.metrics.errorCount++;
      console.error('Queue error:', error);
    });
  }
  
  private validateMessage(message: Message<T>): boolean {
    return !!(
      message &&
      message.id &&
      message.timestamp &&
      message.type &&
      message.payload !== undefined
    );
  }
  
  private getActiveBuffer(): Message<T>[] {
    return this.activeBuffer === 'primary' ? this.primaryBuffer : this.secondaryBuffer;
  }
  
  private getProcessingBuffer(): Message<T>[] {
    // Process from the non-active buffer to allow concurrent operations
    return this.activeBuffer === 'primary' ? this.secondaryBuffer : this.primaryBuffer;
  }
  
  private getHighestPriorityMessage(buffer: Message<T>[]): Message<T> | null {
    if (buffer.length === 0) return null;
    
    return buffer.reduce((highest, current) => {
      return current.priority > highest.priority ? current : highest;
    });
  }
  
  private isBackpressureActive(): boolean {
    const utilization = this.calculateBufferUtilization();
    return utilization >= this.config.backpressureThreshold;
  }
  
  private handleBackpressure(message: Message<T>): boolean {
    this.state.status = QueueStatus.BACKPRESSURE;
    this.emit(QueueEventType.BACKPRESSURE_ACTIVATED, { message });
    
    switch (this.config.backpressureStrategy) {
      case BackpressureStrategy.DROP_OLDEST:
        return this.dropOldestAndEnqueue(message);
      case BackpressureStrategy.DROP_NEWEST:
        this.metrics.messagesDropped++;
        this.emit(QueueEventType.MESSAGE_DROPPED, { message, reason: 'backpressure' });
        return false;
      case BackpressureStrategy.BLOCK:
        // In async context, we'll return false to indicate failure
        return false;
      case BackpressureStrategy.EXPAND_BUFFER:
        return this.expandBufferAndEnqueue(message);
      default:
        return false;
    }
  }
  
  private dropOldestAndEnqueue(message: Message<T>): boolean {
    const buffer = this.getActiveBuffer();
    if (buffer.length > 0) {
      const dropped = buffer.shift();
      this.metrics.messagesDropped++;
      this.emit(QueueEventType.MESSAGE_DROPPED, { message: dropped, reason: 'backpressure' });
    }
    buffer.push(message);
    return true;
  }
  
  private expandBufferAndEnqueue(message: Message<T>): boolean {
    // Temporarily expand buffer (implementation would need memory checks)
    const buffer = this.getActiveBuffer();
    buffer.push(message);
    return true;
  }
  
  private shouldSwitchBuffer(): boolean {
    const utilization = this.calculateBufferUtilization();
    return utilization >= this.config.bufferSwitchThreshold;
  }
  
  private async switchBuffer(): Promise<void> {
    this.activeBuffer = this.activeBuffer === 'primary' ? 'secondary' : 'primary';
    this.state.activeBuffer = this.activeBuffer;
    this.metrics.bufferSwitches++;
    
    this.emit(QueueEventType.BUFFER_SWITCH, { 
      newActiveBuffer: this.activeBuffer,
      timestamp: new Date()
    });
  }
  
  private calculateBufferUtilization(): number {
    const totalMessages = this.primaryBuffer.length + this.secondaryBuffer.length;
    return totalMessages / (this.config.maxBufferSize * 2);
  }
  
  private updateBufferSizes(): void {
    this.state.primaryBufferSize = this.primaryBuffer.length;
    this.state.secondaryBufferSize = this.secondaryBuffer.length;
  }
  
  private updateMetrics(): void {
    this.metrics.bufferUtilization = this.calculateBufferUtilization();
    
    // Calculate processing rate
    const now = Date.now();
    const timeDiff = now - this.lastProcessingTime;
    if (timeDiff > 0) {
      this.state.processingRate = this.metrics.messagesProcessed / (timeDiff / 1000);
    }
    
    // Update latency metrics
    if (this.processingTimes.length > 0) {
      this.processingTimes.sort((a, b) => a - b);
      const len = this.processingTimes.length;
      this.metrics.latency.p50 = this.processingTimes[Math.floor(len * 0.5)];
      this.metrics.latency.p95 = this.processingTimes[Math.floor(len * 0.95)];
      this.metrics.latency.p99 = this.processingTimes[Math.floor(len * 0.99)];
    }
  }
  
  private startProcessing(): void {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    this.processingInterval = setInterval(() => {
      this.processMessages();
    }, this.config.flushInterval);
  }
  
  private stopProcessing(): void {
    this.isProcessing = false;
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = undefined;
    }
  }
  
  private startHealthCheck(): void {
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, this.config.healthCheckInterval);
  }
  
  private stopHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = undefined;
    }
  }
  
  private async processMessages(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const batch = await this.dequeueBatch(this.config.batchSize);
      
      for (const message of batch) {
        this.emit(QueueEventType.MESSAGE_PROCESSED, { message });
      }
      
      // Track processing time
      const processingTime = Date.now() - startTime;
      this.processingTimes.push(processingTime);
      
      // Keep only recent processing times for metrics
      if (this.processingTimes.length > 1000) {
        this.processingTimes = this.processingTimes.slice(-1000);
      }
      
    } catch (error) {
      this.handleError(error as Error);
    }
  }
  
  private performHealthCheck(): void {
    const isHealthy = this.isHealthy();
    
    if (!isHealthy && this.state.status !== QueueStatus.ERROR) {
      this.state.status = QueueStatus.ERROR;
      this.emit(QueueEventType.QUEUE_ERROR, { 
        reason: 'health_check_failed',
        metrics: this.getMetrics()
      });
    } else if (isHealthy && this.state.status === QueueStatus.ERROR) {
      this.state.status = QueueStatus.PROCESSING;
    }
  }
  
  private handleError(error: Error, message?: Message<T>): void {
    this.metrics.errorCount++;
    this.emit('error', error);

    if (message) {
      this.emit(QueueEventType.QUEUE_ERROR, { error, message });
    }
  }
}

// ============================================================================
// Factory Functions
// ============================================================================

/**
 * Create a standard h2A message queue with default configuration
 */
export function createMessageQueue<T = any>(config?: Partial<QueueConfig>): h2AMessageQueue<T> {
  return new h2AMessageQueue<T>(config);
}

/**
 * Create a high-performance message queue optimized for throughput
 */
export function createHighPerformanceQueue<T = any>(): h2AMessageQueue<T> {
  return new h2AMessageQueue<T>({
    maxBufferSize: 5000,
    batchSize: 50,
    flushInterval: 10,
    backpressureStrategy: BackpressureStrategy.EXPAND_BUFFER,
    enableMetrics: true
  });
}

/**
 * Create a memory-efficient message queue optimized for low resource usage
 */
export function createMemoryEfficientQueue<T = any>(): h2AMessageQueue<T> {
  return new h2AMessageQueue<T>({
    maxBufferSize: 100,
    batchSize: 5,
    flushInterval: 200,
    backpressureStrategy: BackpressureStrategy.DROP_OLDEST,
    enableMetrics: false
  });
}
