/**
 * Claude Code 3.0 - CLI Layer
 *
 * Layer 1: Command Line Interface
 * Provides command-line access to the Claude Code 3.0 system.
 */

export { CLIApplication } from './cli-application.js';

// Default CLI configuration
export const DEFAULT_CLI_CONFIG = {
  enabled: true,
  interactive: true,
  colors: true,
  verbose: false
};

/**
 * Create CLI application instance
 */
export function createCLIApplication(config = {}) {
  return new CLIApplication({
    ...DEFAULT_CLI_CONFIG,
    ...config
  });
}

const program = new Command();

/**
 * CLI Application Class
 */
export class CLIApplication {
  private system?: ClaudeCodeSystem;
  private agent?: AgentCore;
  
  constructor() {
    this.setupCommands();
  }
  
  /**
   * Run the CLI application
   */
  async run(args: string[]): Promise<void> {
    await program.parseAsync(args);
  }
  
  private setupCommands(): void {
    program
      .name('claude-code-3.0')
      .description('Claude Code 3.0 - AI-driven code generation and management platform')
      .version('3.0.0');
    
    // System commands
    program
      .command('start')
      .description('Start the Claude Code 3.0 system')
      .option('-d, --dev', 'Start in development mode')
      .option('-p, --port <port>', 'Port for UI server', '3000')
      .action(async (options) => {
        await this.startSystem(options);
      });
    
    program
      .command('stop')
      .description('Stop the Claude Code 3.0 system')
      .action(async () => {
        await this.stopSystem();
      });
    
    program
      .command('status')
      .description('Show system status')
      .action(async () => {
        await this.showStatus();
      });
    
    // Agent commands
    program
      .command('chat')
      .description('Start an interactive chat session')
      .option('-m, --model <model>', 'AI model to use', 'claude-3-sonnet')
      .action(async (options) => {
        await this.startChat(options);
      });
    
    program
      .command('ask <message>')
      .description('Ask a single question')
      .option('-m, --model <model>', 'AI model to use', 'claude-3-sonnet')
      .option('-s, --stream', 'Stream the response')
      .action(async (message, options) => {
        await this.askQuestion(message, options);
      });
    
    // Testing commands
    program
      .command('test')
      .description('Run system tests')
      .option('-u, --unit', 'Run unit tests only')
      .option('-i, --integration', 'Run integration tests only')
      .option('-b, --benchmark', 'Run performance benchmarks')
      .action(async (options) => {
        await this.runTests(options);
      });
    
    // Development commands
    program
      .command('dev')
      .description('Start development environment')
      .action(async () => {
        await this.startDevelopment();
      });
  }
  
  private async startSystem(options: any): Promise<void> {
    const spinner = ora('Starting Claude Code 3.0 system...').start();
    
    try {
      // Create system with appropriate configuration
      const config = options.dev ? {
        environment: 'development' as const,
        enableDebugMode: true,
        enableMetrics: true,
        layers: {
          ui: { port: parseInt(options.port) }
        }
      } : {};
      
      this.system = new ClaudeCodeSystem(config);
      
      // Set up event listeners
      this.system.on('system_started', () => {
        spinner.succeed(chalk.green('System started successfully!'));
        console.log(chalk.blue(`UI available at: http://localhost:${options.port}`));
      });
      
      this.system.on('system_error', (error) => {
        console.error(chalk.red('System error:'), error.message);
      });
      
      // Start the system
      await this.system.start();
      
    } catch (error) {
      spinner.fail(chalk.red('Failed to start system'));
      console.error(error);
      process.exit(1);
    }
  }
  
  private async stopSystem(): Promise<void> {
    if (!this.system) {
      console.log(chalk.yellow('System is not running'));
      return;
    }
    
    const spinner = ora('Stopping system...').start();
    
    try {
      await this.system.stop();
      spinner.succeed(chalk.green('System stopped successfully'));
    } catch (error) {
      spinner.fail(chalk.red('Failed to stop system'));
      console.error(error);
    }
  }
  
  private async showStatus(): Promise<void> {
    if (!this.system) {
      console.log(chalk.yellow('System is not running'));
      return;
    }
    
    const state = this.system.getState();
    const metrics = this.system.getMetrics();
    
    console.log(chalk.blue('\n=== Claude Code 3.0 Status ==='));
    console.log(`Status: ${this.getStatusColor(state.status)}${state.status}${chalk.reset()}`);
    console.log(`Uptime: ${chalk.cyan(this.formatUptime(state.uptime))}`);
    console.log(`Memory Usage: ${chalk.cyan(this.formatBytes(metrics.memoryUsage))}`);
    console.log(`Total Requests: ${chalk.cyan(metrics.totalRequests)}`);
    console.log(`Success Rate: ${chalk.cyan(this.calculateSuccessRate(metrics))}%`);
    
    console.log(chalk.blue('\n=== Layer Status ==='));
    Object.entries(state.layers).forEach(([name, layer]) => {
      const status = layer.initialized ? 
        (layer.status === 'running' ? chalk.green('✓') : chalk.yellow('○')) : 
        chalk.red('✗');
      console.log(`${status} ${name}: ${layer.status}`);
    });
    
    if (state.errors.length > 0) {
      console.log(chalk.blue('\n=== Recent Errors ==='));
      state.errors.slice(-3).forEach(error => {
        console.log(chalk.red(`[${error.timestamp.toISOString()}] ${error.layer}: ${error.message}`));
      });
    }
  }
  
  private async startChat(options: any): Promise<void> {
    console.log(chalk.blue('Starting interactive chat session...'));
    console.log(chalk.gray('Type "exit" to quit, "help" for commands\n'));
    
    // Initialize agent if not already done
    if (!this.agent) {
      this.agent = new AgentCore({ model: options.model });
      await this.agent.start();
    }
    
    // Simple chat loop (in real implementation, would use proper readline interface)
    console.log(chalk.green('Chat session started! (This is a demo - interactive input not implemented)'));
    console.log(chalk.gray('In a full implementation, this would provide an interactive chat interface.'));
  }
  
  private async askQuestion(message: string, options: any): Promise<void> {
    const spinner = ora('Processing question...').start();
    
    try {
      // Initialize agent if not already done
      if (!this.agent) {
        this.agent = new AgentCore({ model: options.model });
        await this.agent.start();
      }
      
      // Create message
      const agentMessage = {
        id: uuidv4(),
        role: 'user' as const,
        content: message,
        timestamp: new Date()
      };
      
      // Process message
      const response = await this.agent.processMessage(agentMessage);
      
      spinner.stop();
      console.log(chalk.blue('\n=== Question ==='));
      console.log(message);
      console.log(chalk.blue('\n=== Response ==='));
      
      if (options.stream) {
        // Stream the response
        for await (const chunk of response.chunks) {
          if (chunk.type === 'text') {
            process.stdout.write(chunk.content);
          } else if (chunk.type === 'done') {
            console.log(chalk.green('\n\n✓ Response complete'));
            break;
          } else if (chunk.type === 'error') {
            console.log(chalk.red(`\n\n✗ Error: ${chunk.content}`));
            break;
          }
        }
      } else {
        // Collect full response
        let fullResponse = '';
        for await (const chunk of response.chunks) {
          if (chunk.type === 'text') {
            fullResponse += chunk.content;
          } else if (chunk.type === 'done') {
            break;
          }
        }
        console.log(fullResponse);
      }
      
    } catch (error) {
      spinner.fail(chalk.red('Failed to process question'));
      console.error(error);
    }
  }
  
  private async runTests(options: any): Promise<void> {
    console.log(chalk.blue('Running Claude Code 3.0 tests...'));
    
    if (options.unit || (!options.integration && !options.benchmark)) {
      console.log(chalk.yellow('Running unit tests...'));
      // In real implementation, would run Jest tests
      console.log(chalk.green('✓ Unit tests passed (demo)'));
    }
    
    if (options.integration) {
      console.log(chalk.yellow('Running integration tests...'));
      // In real implementation, would run integration tests
      console.log(chalk.green('✓ Integration tests passed (demo)'));
    }
    
    if (options.benchmark) {
      console.log(chalk.yellow('Running performance benchmarks...'));
      // In real implementation, would run benchmarks
      console.log(chalk.green('✓ Benchmarks completed (demo)'));
    }
  }
  
  private async startDevelopment(): Promise<void> {
    console.log(chalk.blue('Starting development environment...'));
    
    // Start system in development mode
    await this.startSystem({ dev: true, port: '3000' });
    
    console.log(chalk.green('\n=== Development Environment Ready ==='));
    console.log(chalk.cyan('- System running in development mode'));
    console.log(chalk.cyan('- Debug mode enabled'));
    console.log(chalk.cyan('- Metrics collection enabled'));
    console.log(chalk.cyan('- Hot reload enabled'));
    console.log(chalk.gray('\nPress Ctrl+C to stop'));
  }
  
  // Utility methods
  private getStatusColor(status: string): string {
    switch (status) {
      case 'running': return chalk.green.bold;
      case 'error': return chalk.red.bold;
      case 'paused': return chalk.yellow.bold;
      default: return chalk.gray;
    }
  }
  
  private formatUptime(uptime: number): string {
    const seconds = Math.floor(uptime / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }
  
  private formatBytes(bytes: number): string {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100} ${sizes[i]}`;
  }
  
  private calculateSuccessRate(metrics: any): number {
    const total = metrics.totalRequests;
    if (total === 0) return 100;
    return Math.round((metrics.successfulRequests / total) * 100);
  }
}

// CLI entry point
if (import.meta.url === `file://${process.argv[1]}`) {
  const cli = new CLIApplication();
  cli.run(process.argv).catch(console.error);
}
