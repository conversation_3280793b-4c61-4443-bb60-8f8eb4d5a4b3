/**
 * Claude Code 3.0 - Event Layer
 * 
 * Layer 4: Event Processing and Routing
 * Advanced event-driven processing and routing system.
 */

import { EventEmitter } from 'eventemitter3';

export interface EventConfig {
  enabled: boolean;
  maxListeners: number;
  enableMetrics: boolean;
  eventHistory: number;
}

export const DEFAULT_EVENT_CONFIG: EventConfig = {
  enabled: true,
  maxListeners: 100,
  enableMetrics: true,
  eventHistory: 1000
};

export interface SystemEvent<T = any> {
  id: string;
  type: string;
  timestamp: Date;
  source: string;
  data: T;
  metadata?: Record<string, any>;
}

/**
 * Event Manager
 * Handles system-wide event processing and routing
 */
export class EventManager extends EventEmitter {
  private config: EventConfig;
  private eventHistory: SystemEvent[] = [];
  private metrics = {
    totalEvents: 0,
    eventsPerSecond: 0,
    lastEventTime: Date.now()
  };
  
  constructor(config: Partial<EventConfig> = {}) {
    super();
    this.config = { ...DEFAULT_EVENT_CONFIG, ...config };
    this.setMaxListeners(this.config.maxListeners);
  }
  
  /**
   * Emit a system event
   */
  emitSystemEvent<T>(type: string, data: T, source: string = 'unknown'): void {
    const event: SystemEvent<T> = {
      id: this.generateEventId(),
      type,
      timestamp: new Date(),
      source,
      data
    };
    
    // Add to history
    this.eventHistory.push(event);
    if (this.eventHistory.length > this.config.eventHistory) {
      this.eventHistory.shift();
    }
    
    // Update metrics
    if (this.config.enableMetrics) {
      this.updateMetrics();
    }
    
    // Emit the event
    this.emit(type, event);
    this.emit('*', event); // Wildcard listener
  }
  
  /**
   * Get event history
   */
  getEventHistory(type?: string): SystemEvent[] {
    if (type) {
      return this.eventHistory.filter(event => event.type === type);
    }
    return [...this.eventHistory];
  }
  
  /**
   * Get event metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Clear event history
   */
  clearHistory(): void {
    this.eventHistory = [];
  }
  
  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  private updateMetrics(): void {
    this.metrics.totalEvents++;
    
    const now = Date.now();
    const timeDiff = now - this.metrics.lastEventTime;
    
    if (timeDiff > 0) {
      this.metrics.eventsPerSecond = 1000 / timeDiff;
    }
    
    this.metrics.lastEventTime = now;
  }
}

/**
 * Create event manager instance
 */
export function createEventManager(config: Partial<EventConfig> = {}): EventManager {
  return new EventManager(config);
}
