/**
 * Claude Code 3.0 - Testing Framework
 * 
 * Comprehensive testing utilities and frameworks.
 */

// Test utilities
export * from './test-reporter.js';
export * from './llm-test-adapter.js';

// Testing interfaces
export interface TestConfig {
  mode: 'unit' | 'integration' | 'performance' | 'e2e';
  timeout: number;
  retries: number;
  enableReporting: boolean;
  reportFormat: 'console' | 'json' | 'html';
}

export const DEFAULT_TEST_CONFIG: TestConfig = {
  mode: 'unit',
  timeout: 30000,
  retries: 0,
  enableReporting: true,
  reportFormat: 'console'
};

export interface TestSuite {
  name: string;
  tests: TestCase[];
  setup?: () => Promise<void>;
  teardown?: () => Promise<void>;
}

export interface TestCase {
  name: string;
  test: () => Promise<void>;
  timeout?: number;
  skip?: boolean;
}

/**
 * Test Runner
 * Executes test suites and generates reports
 */
export class TestRunner {
  private config: TestConfig;
  private suites: TestSuite[] = [];
  
  constructor(config: Partial<TestConfig> = {}) {
    this.config = { ...DEFAULT_TEST_CONFIG, ...config };
  }
  
  /**
   * Add test suite
   */
  addSuite(suite: TestSuite): void {
    this.suites.push(suite);
  }
  
  /**
   * Run all test suites
   */
  async runAll(): Promise<void> {
    console.log(`Running ${this.suites.length} test suites...`);
    
    for (const suite of this.suites) {
      await this.runSuite(suite);
    }
  }
  
  /**
   * Run specific test suite
   */
  async runSuite(suite: TestSuite): Promise<void> {
    console.log(`\nRunning suite: ${suite.name}`);
    
    // Setup
    if (suite.setup) {
      await suite.setup();
    }
    
    try {
      // Run tests
      for (const testCase of suite.tests) {
        if (testCase.skip) {
          console.log(`  ⏭️  ${testCase.name} (skipped)`);
          continue;
        }
        
        try {
          await this.runTest(testCase);
          console.log(`  ✅ ${testCase.name}`);
        } catch (error) {
          console.log(`  ❌ ${testCase.name}: ${(error as Error).message}`);
        }
      }
    } finally {
      // Teardown
      if (suite.teardown) {
        await suite.teardown();
      }
    }
  }
  
  private async runTest(testCase: TestCase): Promise<void> {
    const timeout = testCase.timeout || this.config.timeout;
    
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Test timeout after ${timeout}ms`));
      }, timeout);
      
      testCase.test()
        .then(() => {
          clearTimeout(timer);
          resolve();
        })
        .catch((error) => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }
}

/**
 * Create test runner
 */
export function createTestRunner(config: Partial<TestConfig> = {}): TestRunner {
  return new TestRunner(config);
}
