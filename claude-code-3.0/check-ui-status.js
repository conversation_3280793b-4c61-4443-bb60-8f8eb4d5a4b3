#!/usr/bin/env node

/**
 * Claude Code 3.0 - UI Status Checker
 * 
 * Comprehensive diagnostic script to check all UI components
 */

const http = require('http');
const https = require('https');

console.log('🔍 Claude Code 3.0 UI Diagnostic Tool');
console.log('=====================================\n');

// Test API Server
async function testAPI() {
  console.log('📊 Testing API Server...');
  
  try {
    const response = await fetch('http://localhost:8080/api/status');
    const data = await response.json();
    console.log('✅ API Server: Online');
    console.log(`   Version: ${data.version}`);
    console.log(`   Uptime: ${Math.floor(data.uptime)}s`);
    
    // Test metrics endpoint
    const metricsResponse = await fetch('http://localhost:8080/api/metrics');
    const metricsData = await metricsResponse.json();
    console.log('✅ Metrics Endpoint: Working');
    console.log(`   Active Agents: ${metricsData.activeAgents}/${metricsData.totalAgents}`);
    console.log(`   Messages/sec: ${Math.floor(metricsData.messagesPerSecond).toLocaleString()}`);
    console.log(`   Latency: ${metricsData.averageLatency}ms`);
    
    return true;
  } catch (error) {
    console.log('❌ API Server: Failed');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

// Test UI Server
async function testUI() {
  console.log('\n🎨 Testing UI Server...');
  
  return new Promise((resolve) => {
    const req = http.get('http://localhost:3000', (res) => {
      console.log('✅ UI Server: Accessible');
      console.log(`   Status: ${res.statusCode}`);
      console.log(`   Content-Type: ${res.headers['content-type']}`);
      resolve(true);
    });
    
    req.on('error', (error) => {
      console.log('❌ UI Server: Failed');
      console.log(`   Error: ${error.message}`);
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      console.log('❌ UI Server: Timeout');
      req.destroy();
      resolve(false);
    });
  });
}

// Test WebSocket
async function testWebSocket() {
  console.log('\n📡 Testing WebSocket...');
  
  try {
    const WebSocket = require('ws');
    const ws = new WebSocket('ws://localhost:8080');
    
    return new Promise((resolve) => {
      ws.on('open', () => {
        console.log('✅ WebSocket: Connected');
        ws.close();
        resolve(true);
      });
      
      ws.on('error', (error) => {
        console.log('❌ WebSocket: Failed');
        console.log(`   Error: ${error.message}`);
        resolve(false);
      });
      
      setTimeout(() => {
        console.log('❌ WebSocket: Timeout');
        ws.terminate();
        resolve(false);
      }, 5000);
    });
  } catch (error) {
    console.log('❌ WebSocket: Module not available');
    console.log('   Note: This is expected in some environments');
    return false;
  }
}

// Check processes
function checkProcesses() {
  console.log('\n🔧 Process Information...');
  console.log(`   Node.js Version: ${process.version}`);
  console.log(`   Platform: ${process.platform}`);
  console.log(`   Architecture: ${process.arch}`);
  console.log(`   Memory Usage: ${Math.floor(process.memoryUsage().heapUsed / 1024 / 1024)}MB`);
}

// Main diagnostic function
async function runDiagnostics() {
  const results = {
    api: false,
    ui: false,
    websocket: false
  };
  
  results.api = await testAPI();
  results.ui = await testUI();
  results.websocket = await testWebSocket();
  
  checkProcesses();
  
  console.log('\n📋 Summary');
  console.log('===========');
  console.log(`API Server:    ${results.api ? '✅ Working' : '❌ Failed'}`);
  console.log(`UI Server:     ${results.ui ? '✅ Working' : '❌ Failed'}`);
  console.log(`WebSocket:     ${results.websocket ? '✅ Working' : '❌ Failed'}`);
  
  const allWorking = results.api && results.ui;
  
  if (allWorking) {
    console.log('\n🎉 All core systems are working!');
    console.log('\n🌐 Access your Claude Code 3.0 UI at:');
    console.log('   Main Dashboard: http://localhost:3000');
    console.log('   Test Component: http://localhost:3000/test');
    console.log('   API Status:     http://localhost:8080/api/status');
  } else {
    console.log('\n⚠️  Some systems need attention:');
    if (!results.api) {
      console.log('   • Start API server: npm run dev');
    }
    if (!results.ui) {
      console.log('   • Start UI server: cd ui && npm run dev');
    }
  }
  
  console.log('\n📚 Troubleshooting:');
  console.log('   • Check browser console for JavaScript errors');
  console.log('   • Verify all dependencies: npm install && cd ui && npm install');
  console.log('   • Clear browser cache and reload');
  console.log('   • Check firewall/antivirus blocking localhost ports');
}

// Add fetch polyfill for older Node.js versions
if (!global.fetch) {
  global.fetch = async (url) => {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port,
        path: urlObj.pathname + urlObj.search,
        method: 'GET'
      };
      
      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          resolve({
            ok: res.statusCode >= 200 && res.statusCode < 300,
            status: res.statusCode,
            json: () => Promise.resolve(JSON.parse(data))
          });
        });
      });
      
      req.on('error', reject);
      req.end();
    });
  };
}

// Run the diagnostics
runDiagnostics().catch(console.error);
