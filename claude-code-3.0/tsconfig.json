{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "jsx": "react-jsx", "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/core/*": ["src/core/*"], "@/layers/*": ["src/layers/*"], "@/tools/*": ["src/tools/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"], "@/ui/*": ["src/ui/*"], "@/tests/*": ["tests/*"]}}, "include": ["src/**/*", "tests/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"], "ts-node": {"esm": true}}