#!/usr/bin/env node

/**
 * Claude Code 3.0 - Local LLM Integration Test
 * 
 * Tests integration with local LLMs (Ollama) including your qwen2.5:3b model.
 * Compares performance between local, API, and mock modes.
 */

import { performance } from 'perf_hooks';

// Simple Ollama client for testing
class SimpleOllamaClient {
  constructor(config = {}) {
    this.config = {
      baseURL: 'http://localhost:11434',
      model: 'qwen2.5:3b',
      ...config
    };
  }
  
  async isAvailable() {
    try {
      const response = await fetch(`${this.config.baseURL}/api/tags`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }
  
  async listModels() {
    try {
      const response = await fetch(`${this.config.baseURL}/api/tags`);
      if (!response.ok) return [];
      const data = await response.json();
      return data.models || [];
    } catch (error) {
      return [];
    }
  }
  
  async chat(messages, options = {}) {
    const requestBody = {
      model: this.config.model,
      messages: messages,
      stream: false,
      options: {
        temperature: options.temperature || 0.7,
        num_predict: options.maxTokens || 1000
      }
    };
    
    const response = await fetch(`${this.config.baseURL}/api/chat`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(30000)
    });
    
    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Ollama error: ${response.status} - ${error}`);
    }
    
    return await response.json();
  }
  
  async testConnection() {
    const startTime = performance.now();
    
    try {
      if (!(await this.isAvailable())) {
        return {
          success: false,
          latency: performance.now() - startTime,
          error: 'Ollama server not accessible'
        };
      }
      
      const models = await this.listModels();
      const hasModel = models.some(m => m.name === this.config.model);
      
      if (!hasModel) {
        return {
          success: false,
          latency: performance.now() - startTime,
          error: `Model ${this.config.model} not found. Available: ${models.map(m => m.name).join(', ')}`
        };
      }
      
      const response = await this.chat([
        { role: 'user', content: 'Hello! Please respond with exactly: "Local LLM connection successful"' }
      ]);
      
      const latency = performance.now() - startTime;
      
      return {
        success: true,
        latency,
        model: response.model,
        response: response.message.content,
        performance: {
          totalDuration: (response.total_duration || 0) / 1000000,
          loadDuration: (response.load_duration || 0) / 1000000,
          promptEvalDuration: (response.prompt_eval_duration || 0) / 1000000,
          evalDuration: (response.eval_duration || 0) / 1000000,
          tokensPerSecond: response.eval_count && response.eval_duration 
            ? (response.eval_count / (response.eval_duration / 1000000000))
            : 0
        }
      };
      
    } catch (error) {
      return {
        success: false,
        latency: performance.now() - startTime,
        error: error.message
      };
    }
  }
  
  async benchmarkPerformance(testCases) {
    const results = [];
    
    for (const testCase of testCases) {
      const startTime = performance.now();
      
      try {
        const response = await this.chat([
          { role: 'user', content: testCase.prompt }
        ]);
        
        const latency = performance.now() - startTime;
        
        results.push({
          name: testCase.name,
          success: true,
          latency,
          tokensPerSecond: response.eval_count && response.eval_duration 
            ? (response.eval_count / (response.eval_duration / 1000000000))
            : 0,
          responseLength: response.message.content.length,
          performance: {
            totalDuration: (response.total_duration || 0) / 1000000,
            loadDuration: (response.load_duration || 0) / 1000000,
            evalDuration: (response.eval_duration || 0) / 1000000
          }
        });
        
      } catch (error) {
        results.push({
          name: testCase.name,
          success: false,
          latency: performance.now() - startTime,
          error: error.message
        });
      }
    }
    
    return results;
  }
}

// Mock client for comparison
class MockLLMClient {
  async testConnection() {
    const delay = 200 + Math.random() * 400;
    await new Promise(resolve => setTimeout(resolve, delay));
    
    return {
      success: true,
      latency: delay,
      model: 'mock-qwen2.5:3b',
      response: 'Local LLM connection successful (simulated)',
      performance: {
        totalDuration: delay,
        loadDuration: 50,
        promptEvalDuration: 100,
        evalDuration: delay - 150,
        tokensPerSecond: 25
      }
    };
  }
  
  async benchmarkPerformance(testCases) {
    const results = [];
    
    for (const testCase of testCases) {
      const delay = 300 + Math.random() * 700;
      await new Promise(resolve => setTimeout(resolve, delay));
      
      results.push({
        name: testCase.name,
        success: true,
        latency: delay,
        tokensPerSecond: 20 + Math.random() * 10,
        responseLength: 100 + Math.random() * 200,
        performance: {
          totalDuration: delay,
          loadDuration: 50,
          evalDuration: delay - 50
        }
      });
    }
    
    return results;
  }
}

async function runLocalLLMTest() {
  console.log('🤖 Claude Code 3.0 - Local LLM Integration Test');
  console.log('=' .repeat(70));
  console.log('Testing integration with local LLMs (Ollama) including qwen2.5:3b');
  console.log('=' .repeat(70));
  
  // Environment detection
  console.log('🔍 Environment Detection:');
  console.log(`   Node.js: ${process.version}`);
  console.log(`   Platform: ${process.platform}-${process.arch}`);
  console.log(`   Expected Ollama URL: http://localhost:11434`);
  console.log(`   Target Model: ${process.env.OLLAMA_MODEL || 'qwen2.5:3b'}`);
  
  // Test configurations
  const configs = [
    {
      name: 'Mock Mode',
      client: new MockLLMClient(),
      description: 'Simulated local LLM for development'
    },
    {
      name: 'Ollama Local',
      client: new SimpleOllamaClient({ 
        model: process.env.OLLAMA_MODEL || 'qwen2.5:3b' 
      }),
      description: 'Real local LLM via Ollama'
    }
  ];
  
  const results = new Map();
  
  // Connection tests
  console.log('\n🔌 Connection Tests:');
  console.log('-'.repeat(50));
  
  for (const config of configs) {
    console.log(`\n${config.name} (${config.description}):`);
    
    const result = await config.client.testConnection();
    results.set(config.name, { connection: result });
    
    console.log(`   Status: ${result.success ? '✅ Success' : '❌ Failed'}`);
    console.log(`   Latency: ${result.latency.toFixed(2)}ms`);
    
    if (result.success) {
      console.log(`   Model: ${result.model}`);
      console.log(`   Response: "${result.response}"`);
      
      if (result.performance) {
        console.log(`   Performance:`);
        console.log(`     • Total Duration: ${result.performance.totalDuration.toFixed(2)}ms`);
        console.log(`     • Load Duration: ${result.performance.loadDuration.toFixed(2)}ms`);
        console.log(`     • Eval Duration: ${result.performance.evalDuration.toFixed(2)}ms`);
        console.log(`     • Tokens/sec: ${result.performance.tokensPerSecond.toFixed(1)}`);
      }
    } else {
      console.log(`   Error: ${result.error}`);
    }
  }
  
  // Performance benchmarks
  console.log('\n🏃 Performance Benchmarks:');
  console.log('-'.repeat(50));
  
  const testCases = [
    {
      name: 'Simple Query',
      prompt: 'What is 2+2?'
    },
    {
      name: 'Code Generation',
      prompt: 'Write a simple JavaScript function to reverse a string'
    },
    {
      name: 'Reasoning',
      prompt: 'Explain the benefits of using local LLMs vs cloud APIs'
    },
    {
      name: 'Long Context',
      prompt: 'Analyze this text and provide insights: ' + 'Lorem ipsum '.repeat(100)
    }
  ];
  
  for (const config of configs) {
    if (!results.get(config.name).connection.success) {
      console.log(`\n⏭️  Skipping ${config.name} benchmarks (connection failed)`);
      continue;
    }
    
    console.log(`\n📊 ${config.name} Benchmarks:`);
    
    const benchmarkResults = await config.client.benchmarkPerformance(testCases);
    results.get(config.name).benchmarks = benchmarkResults;
    
    for (const result of benchmarkResults) {
      console.log(`   ${result.name}:`);
      console.log(`     • Status: ${result.success ? '✅' : '❌'}`);
      console.log(`     • Latency: ${result.latency.toFixed(2)}ms`);
      
      if (result.success) {
        console.log(`     • Tokens/sec: ${result.tokensPerSecond.toFixed(1)}`);
        console.log(`     • Response Length: ${result.responseLength} chars`);
      } else {
        console.log(`     • Error: ${result.error}`);
      }
    }
  }
  
  // Comparison analysis
  console.log('\n📈 Performance Comparison:');
  console.log('-'.repeat(50));
  
  const mockResults = results.get('Mock Mode');
  const ollamaResults = results.get('Ollama Local');
  
  if (mockResults?.connection.success && ollamaResults?.connection.success) {
    console.log('Connection Latency:');
    console.log(`   • Mock: ${mockResults.connection.latency.toFixed(2)}ms`);
    console.log(`   • Ollama: ${ollamaResults.connection.latency.toFixed(2)}ms`);
    console.log(`   • Difference: ${(ollamaResults.connection.latency - mockResults.connection.latency).toFixed(2)}ms`);
    
    if (mockResults.benchmarks && ollamaResults.benchmarks) {
      console.log('\nBenchmark Averages:');
      
      const mockAvg = mockResults.benchmarks.reduce((sum, r) => sum + r.latency, 0) / mockResults.benchmarks.length;
      const ollamaAvg = ollamaResults.benchmarks.reduce((sum, r) => sum + r.latency, 0) / ollamaResults.benchmarks.length;
      
      console.log(`   • Mock Average: ${mockAvg.toFixed(2)}ms`);
      console.log(`   • Ollama Average: ${ollamaAvg.toFixed(2)}ms`);
      console.log(`   • Real/Mock Ratio: ${(ollamaAvg / mockAvg).toFixed(2)}x`);
      
      const mockTokens = mockResults.benchmarks.reduce((sum, r) => sum + (r.tokensPerSecond || 0), 0) / mockResults.benchmarks.length;
      const ollamaTokens = ollamaResults.benchmarks.reduce((sum, r) => sum + (r.tokensPerSecond || 0), 0) / ollamaResults.benchmarks.length;
      
      console.log(`   • Mock Tokens/sec: ${mockTokens.toFixed(1)}`);
      console.log(`   • Ollama Tokens/sec: ${ollamaTokens.toFixed(1)}`);
    }
  }
  
  // Framework integration insights
  console.log('\n🎯 Framework Integration Insights:');
  console.log('-'.repeat(50));
  
  if (ollamaResults?.connection.success) {
    console.log('✅ Local LLM Integration: SUCCESSFUL');
    console.log('   • Ollama server is accessible');
    console.log(`   • Model ${process.env.OLLAMA_MODEL || 'qwen2.5:3b'} is available`);
    console.log('   • Performance metrics are being captured');
    console.log('   • Ready for production local testing');
    
    const avgLatency = ollamaResults.connection.latency;
    if (avgLatency < 1000) {
      console.log('   🚀 EXCELLENT: Sub-second response times');
    } else if (avgLatency < 3000) {
      console.log('   ✅ GOOD: Reasonable response times for local processing');
    } else {
      console.log('   ⚠️  SLOW: Consider model optimization or hardware upgrade');
    }
    
  } else {
    console.log('⚠️  Local LLM Integration: NOT AVAILABLE');
    console.log('   • Ollama server not accessible');
    console.log('   • To enable local LLM testing:');
    console.log('     1. Install Ollama: https://ollama.ai/');
    console.log('     2. Start Ollama: ollama serve');
    console.log('     3. Pull model: ollama pull qwen2.5:3b');
    console.log('     4. Re-run this test');
  }
  
  // Architecture benefits
  console.log('\n💡 Architecture Benefits with Local LLMs:');
  console.log('-'.repeat(50));
  console.log('✅ Privacy: All processing stays local');
  console.log('✅ Cost: No API fees or token costs');
  console.log('✅ Latency: No network round-trips');
  console.log('✅ Availability: Works offline');
  console.log('✅ Control: Full model and parameter control');
  console.log('✅ Testing: Unlimited testing without costs');
  
  // Recommendations
  console.log('\n🎯 Recommendations:');
  console.log('-'.repeat(50));
  
  if (ollamaResults?.connection.success) {
    console.log('🚀 You have local LLM capability! Consider:');
    console.log('   • Use local models for development and testing');
    console.log('   • Use cloud APIs for production when needed');
    console.log('   • Implement hybrid mode for best of both worlds');
    console.log('   • Monitor performance and adjust model size as needed');
  } else {
    console.log('🔧 To unlock local LLM capabilities:');
    console.log('   • Set up Ollama with qwen2.5:3b or similar model');
    console.log('   • Configure OLLAMA_MODEL environment variable');
    console.log('   • Test with different model sizes for optimal performance');
  }
  
  console.log('\n' + '='.repeat(70));
  console.log('🎉 Local LLM Integration Test Complete!');
  
  return results;
}

// Run test if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runLocalLLMTest().catch(console.error);
}
