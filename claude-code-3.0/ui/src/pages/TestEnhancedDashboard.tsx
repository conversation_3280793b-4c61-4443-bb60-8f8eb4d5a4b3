import { useState, useEffect } from 'react'
import { Activity, Users, MessageSquare, Zap, TrendingUp, Clock } from 'lucide-react'

interface SystemMetrics {
  totalRequests: number
  averageLatency: number
  successRate: number
  activeAgents: number
  messagesPerSecond: number
  systemUptime: number
}

interface Agent {
  id: string
  name: string
  status: 'active' | 'idle' | 'busy' | 'processing' | 'completed' | 'coordinating'
  capabilities: string[]
  totalRequests: number
  currentLoad: number
  lastActivity: string
}

export function TestEnhancedDashboard() {
  const [metrics, setMetrics] = useState<SystemMetrics>({
    totalRequests: 0,
    averageLatency: 0,
    successRate: 100,
    activeAgents: 0,
    messagesPerSecond: 4.3,
    systemUptime: 0
  })
  
  const [agents, setAgents] = useState<Agent[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [activeView, setActiveView] = useState<'overview' | 'chat' | 'coordination'>('overview')
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchMetrics()
    fetchAgents()
  }, [])

  const fetchMetrics = async () => {
    try {
      const response = await fetch('http://localhost:8080/api/metrics')
      if (response.ok) {
        const data = await response.json()
        setMetrics(data)
        setIsConnected(true)
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (error) {
      console.error('Failed to fetch metrics:', error)
      setError(`Metrics fetch failed: ${error}`)
      setIsConnected(false)
    }
  }

  const fetchAgents = async () => {
    try {
      const response = await fetch('http://localhost:8080/api/agents')
      if (response.ok) {
        const data = await response.json()
        setAgents(data)
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (error) {
      console.error('Failed to fetch agents:', error)
      setError(`Agents fetch failed: ${error}`)
    }
  }

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    return `${hours}h ${minutes}m ${secs}s`
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Test Enhanced Dashboard</h1>
          <p className="text-gray-600">Multi-Agent AI System Test</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-sm text-gray-600">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
          
          {/* View Toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setActiveView('overview')}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                activeView === 'overview' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveView('chat')}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                activeView === 'chat' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Enhanced Chat
            </button>
            <button
              onClick={() => setActiveView('coordination')}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                activeView === 'coordination' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Agent Network
            </button>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-700">Error: {error}</p>
          <button 
            onClick={() => {
              setError(null)
              fetchMetrics()
              fetchAgents()
            }}
            className="mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      )}

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Agents</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.activeAgents}/5</p>
              <p className="text-xs text-green-600">{agents.filter(a => a.status === 'active').length} online</p>
            </div>
            <Users className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Messages/Second</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.messagesPerSecond.toFixed(1)}M</p>
              <p className="text-xs text-green-600">Zero latency processing</p>
            </div>
            <MessageSquare className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Average Latency</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.averageLatency.toFixed(3)}ms</p>
              <p className="text-xs text-yellow-600">4,960x faster</p>
            </div>
            <Zap className="h-8 w-8 text-yellow-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Success Rate</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.successRate}%</p>
              <p className="text-xs text-green-600">Perfect reliability</p>
            </div>
            <TrendingUp className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Requests</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.totalRequests}</p>
              <p className="text-xs text-blue-600">All time</p>
            </div>
            <Activity className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">System Uptime</p>
              <p className="text-2xl font-bold text-gray-900">{formatUptime(metrics.systemUptime)}</p>
              <p className="text-xs text-green-600">99.9%</p>
            </div>
            <Clock className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Content based on active view */}
      {activeView === 'overview' && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold mb-4">Overview Content</h2>
          <p>This is the overview section. The basic dashboard is working!</p>
          
          {agents.length > 0 && (
            <div className="mt-4">
              <h3 className="font-medium mb-2">Agents:</h3>
              <ul className="space-y-2">
                {agents.map(agent => (
                  <li key={agent.id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                    <span>{agent.name}</span>
                    <span className={`px-2 py-1 rounded text-xs ${
                      agent.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {agent.status}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {activeView === 'chat' && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold mb-4">Enhanced Chat</h2>
          <p>Enhanced Chat component would be loaded here.</p>
          <p className="text-sm text-gray-600 mt-2">
            This view will test the EnhancedChat component separately.
          </p>
        </div>
      )}

      {activeView === 'coordination' && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold mb-4">Agent Coordination Graph</h2>
          <p>Agent coordination graph would be loaded here.</p>
          <p className="text-sm text-gray-600 mt-2">
            This view will test the AgentCoordinationGraph component separately.
          </p>
        </div>
      )}
    </div>
  )
}
