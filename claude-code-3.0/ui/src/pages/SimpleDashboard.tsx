// Simplified Dashboard for debugging
export function SimpleDashboard() {
  return (
    <div className="p-6 space-y-6">
      <div className="border-b border-gray-200 pb-4">
        <h1 className="text-2xl font-bold text-gray-900">Claude Code 3.0 Dashboard</h1>
        <p className="text-gray-600">
          Zero-latency AI processing framework
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Active Agents</p>
              <p className="text-2xl font-bold text-gray-900">3/5</p>
            </div>
            <div className="p-3 rounded-lg bg-primary-50 text-primary-600">
              🤖
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Messages/Second</p>
              <p className="text-2xl font-bold text-gray-900">4.3M</p>
            </div>
            <div className="p-3 rounded-lg bg-success-50 text-success-600">
              💬
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Latency</p>
              <p className="text-2xl font-bold text-gray-900">0.001ms</p>
            </div>
            <div className="p-3 rounded-lg bg-warning-50 text-warning-600">
              ⚡
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Success Rate</p>
              <p className="text-2xl font-bold text-gray-900">100%</p>
            </div>
            <div className="p-3 rounded-lg bg-success-50 text-success-600">
              ✅
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">System Status</h3>
        </div>
        <div className="px-6 py-4 space-y-4">
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-success-500 rounded-full"></div>
            <span className="text-sm text-gray-700">Framework Core - Online</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-success-500 rounded-full"></div>
            <span className="text-sm text-gray-700">h2A Message Queue - Online</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-success-500 rounded-full"></div>
            <span className="text-sm text-gray-700">Multi-Agent System - Online</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-success-500 rounded-full"></div>
            <span className="text-sm text-gray-700">Local LLM (qwen2.5:3b) - Online</span>
          </div>
        </div>
      </div>
    </div>
  )
}
