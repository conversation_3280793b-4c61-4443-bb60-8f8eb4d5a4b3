import { useState, useEffect } from 'react'

export function DebugPage() {
  const [apiStatus, setApiStatus] = useState<string>('Checking...')
  const [agents, setAgents] = useState<any[]>([])
  const [metrics, setMetrics] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    checkApiStatus()
  }, [])

  const checkApiStatus = async () => {
    try {
      // Test API connectivity
      const agentsResponse = await fetch('http://localhost:8080/api/agents')
      if (!agentsResponse.ok) {
        throw new Error(`Agents API failed: ${agentsResponse.status}`)
      }
      const agentsData = await agentsResponse.json()
      setAgents(agentsData)

      const metricsResponse = await fetch('http://localhost:8080/api/metrics')
      if (!metricsResponse.ok) {
        throw new Error(`Metrics API failed: ${metricsResponse.status}`)
      }
      const metricsData = await metricsResponse.json()
      setMetrics(metricsData)

      setApiStatus('✅ Connected')
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMsg)
      setApiStatus('❌ Failed')
    }
  }

  const testStreamingEndpoint = async () => {
    try {
      const response = await fetch('http://localhost:8080/api/messages/stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: 'Hello, this is a test message',
          sessionId: 'debug-session',
          capabilities: ['general']
        })
      })

      if (!response.ok) {
        throw new Error(`Streaming endpoint failed: ${response.status}`)
      }

      console.log('Streaming endpoint test successful')
      alert('Streaming endpoint is working!')
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error'
      console.error('Streaming test failed:', errorMsg)
      alert(`Streaming test failed: ${errorMsg}`)
    }
  }

  const testWebSocket = () => {
    try {
      const ws = new WebSocket('ws://localhost:8080')
      
      ws.onopen = () => {
        console.log('WebSocket connected successfully')
        alert('WebSocket connection successful!')
        ws.close()
      }
      
      ws.onerror = (error) => {
        console.error('WebSocket error:', error)
        alert('WebSocket connection failed!')
      }
      
      ws.onclose = () => {
        console.log('WebSocket closed')
      }
    } catch (err) {
      console.error('WebSocket test failed:', err)
      alert('WebSocket test failed!')
    }
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Debug Dashboard</h1>
      
      {/* API Status */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">API Status</h2>
        <div className="space-y-2">
          <p><strong>Status:</strong> {apiStatus}</p>
          {error && (
            <div className="bg-red-50 border border-red-200 rounded p-3">
              <p className="text-red-700"><strong>Error:</strong> {error}</p>
            </div>
          )}
        </div>
      </div>

      {/* Test Buttons */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Connection Tests</h2>
        <div className="space-x-4">
          <button
            onClick={checkApiStatus}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Refresh API Status
          </button>
          <button
            onClick={testStreamingEndpoint}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Test Streaming
          </button>
          <button
            onClick={testWebSocket}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            Test WebSocket
          </button>
        </div>
      </div>

      {/* Agents Data */}
      {agents.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Agents Data</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(agents, null, 2)}
          </pre>
        </div>
      )}

      {/* Metrics Data */}
      {metrics && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Metrics Data</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(metrics, null, 2)}
          </pre>
        </div>
      )}

      {/* Component Tests */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold mb-4">Component Tests</h2>
        <div className="space-y-4">
          <div>
            <h3 className="font-medium mb-2">Marked Library Test</h3>
            <button
              onClick={async () => {
                try {
                  const { marked } = await import('marked')
                  const testMarkdown = '# Hello\n\n```javascript\nconsole.log("test")\n```'
                  const result = marked.parse(testMarkdown)
                  console.log('Marked test result:', result)
                  alert('Marked library is working!')
                } catch (err) {
                  console.error('Marked test failed:', err)
                  alert(`Marked test failed: ${err}`)
                }
              }}
              className="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600"
            >
              Test Marked
            </button>
          </div>

          <div>
            <h3 className="font-medium mb-2">D3 Library Test</h3>
            <button
              onClick={async () => {
                try {
                  const d3 = await import('d3')
                  console.log('D3 version:', d3.version)
                  alert(`D3 library is working! Version: ${d3.version}`)
                } catch (err) {
                  console.error('D3 test failed:', err)
                  alert(`D3 test failed: ${err}`)
                }
              }}
              className="px-3 py-1 bg-orange-500 text-white rounded text-sm hover:bg-orange-600"
            >
              Test D3
            </button>
          </div>

          <div>
            <h3 className="font-medium mb-2">Syntax Highlighter Test</h3>
            <button
              onClick={async () => {
                try {
                  const { Prism } = await import('react-syntax-highlighter')
                  console.log('Syntax highlighter loaded:', !!Prism)
                  alert('React Syntax Highlighter is working!')
                } catch (err) {
                  console.error('Syntax highlighter test failed:', err)
                  alert(`Syntax highlighter test failed: ${err}`)
                }
              }}
              className="px-3 py-1 bg-indigo-500 text-white rounded text-sm hover:bg-indigo-600"
            >
              Test Syntax Highlighter
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
