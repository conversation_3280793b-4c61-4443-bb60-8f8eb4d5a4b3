import { useState, useEffect, useRef } from 'react'
import { Activity, AlertTriangle, CheckCircle } from 'lucide-react'

interface DiagnosticResult {
  test: string
  status: 'pending' | 'success' | 'error'
  message: string
  timestamp: Date
}

export function DiagnosticDashboard() {
  const [results, setResults] = useState<DiagnosticResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const mountedRef = useRef(true)
  const renderCountRef = useRef(0)

  // Track render count
  renderCountRef.current += 1

  useEffect(() => {
    return () => {
      mountedRef.current = false
    }
  }, [])

  const addResult = (test: string, status: 'success' | 'error', message: string) => {
    if (!mountedRef.current) return
    
    setResults(prev => [...prev, {
      test,
      status,
      message,
      timestamp: new Date()
    }])
  }

  const runDiagnostics = async () => {
    if (isRunning) return
    
    setIsRunning(true)
    setResults([])

    // Test 1: Basic React functionality
    addResult('React State', 'success', 'useState and useEffect working correctly')

    // Test 2: Dynamic imports
    try {
      await import('marked')
      addResult('Marked Import', 'success', 'Marked library loaded successfully')
    } catch (error) {
      addResult('Marked Import', 'error', `Failed to load marked: ${error}`)
    }

    try {
      await import('react-syntax-highlighter')
      addResult('Syntax Highlighter Import', 'success', 'React Syntax Highlighter loaded successfully')
    } catch (error) {
      addResult('Syntax Highlighter Import', 'error', `Failed to load syntax highlighter: ${error}`)
    }

    try {
      await import('d3')
      addResult('D3 Import', 'success', 'D3 library loaded successfully')
    } catch (error) {
      addResult('D3 Import', 'error', `Failed to load D3: ${error}`)
    }

    // Test 3: API connectivity
    try {
      const response = await fetch('http://localhost:8080/api/metrics', {
        signal: AbortSignal.timeout(5000)
      })
      if (response.ok) {
        addResult('API Connectivity', 'success', 'Backend API is accessible')
      } else {
        addResult('API Connectivity', 'error', `API returned status: ${response.status}`)
      }
    } catch (error) {
      addResult('API Connectivity', 'error', `API connection failed: ${error}`)
    }

    // Test 4: WebSocket connectivity
    try {
      const ws = new WebSocket('ws://localhost:8080')
      
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          ws.close()
          reject(new Error('WebSocket connection timeout'))
        }, 5000)

        ws.onopen = () => {
          clearTimeout(timeout)
          ws.close()
          resolve(true)
        }

        ws.onerror = (error) => {
          clearTimeout(timeout)
          reject(error)
        }
      })

      addResult('WebSocket Connectivity', 'success', 'WebSocket connection established')
    } catch (error) {
      addResult('WebSocket Connectivity', 'error', `WebSocket failed: ${error}`)
    }

    // Test 5: Memory usage
    if ('memory' in performance) {
      const memInfo = (performance as any).memory
      const usedMB = Math.round(memInfo.usedJSHeapSize / 1024 / 1024)
      addResult('Memory Usage', 'success', `JS Heap: ${usedMB}MB`)
    } else {
      addResult('Memory Usage', 'success', 'Memory API not available (normal in production)')
    }

    // Test 6: Render count check
    if (renderCountRef.current > 10) {
      addResult('Render Count', 'error', `High render count detected: ${renderCountRef.current}`)
    } else {
      addResult('Render Count', 'success', `Normal render count: ${renderCountRef.current}`)
    }

    setIsRunning(false)
  }

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'pending':
        return <Activity className="h-4 w-4 text-blue-500 animate-spin" />
    }
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard Diagnostics</h1>
        <p className="text-gray-600">Comprehensive testing of all dashboard components and dependencies</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">System Status</h2>
          <button
            onClick={runDiagnostics}
            disabled={isRunning}
            className={`px-4 py-2 rounded-md font-medium ${
              isRunning 
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {isRunning ? 'Running Diagnostics...' : 'Run Diagnostics'}
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">Component Status</h3>
            <p className="text-sm text-gray-600">Render Count: {renderCountRef.current}</p>
            <p className="text-sm text-gray-600">Mounted: {mountedRef.current ? 'Yes' : 'No'}</p>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">Test Results</h3>
            <p className="text-sm text-gray-600">
              Total: {results.length} | 
              Success: {results.filter(r => r.status === 'success').length} | 
              Errors: {results.filter(r => r.status === 'error').length}
            </p>
          </div>
        </div>

        <div className="space-y-3">
          {results.length === 0 ? (
            <p className="text-gray-500 text-center py-8">No diagnostics run yet. Click "Run Diagnostics" to start.</p>
          ) : (
            results.map((result, index) => (
              <div
                key={index}
                className={`flex items-center space-x-3 p-3 rounded-lg border ${
                  result.status === 'success' ? 'bg-green-50 border-green-200' :
                  result.status === 'error' ? 'bg-red-50 border-red-200' :
                  'bg-blue-50 border-blue-200'
                }`}
              >
                {getStatusIcon(result.status)}
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{result.test}</h4>
                  <p className="text-sm text-gray-600">{result.message}</p>
                </div>
                <span className="text-xs text-gray-500">
                  {result.timestamp.toLocaleTimeString()}
                </span>
              </div>
            ))
          )}
        </div>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 className="font-medium text-yellow-800 mb-2">Troubleshooting Tips</h3>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• If API tests fail, ensure the backend server is running on port 8080</li>
          <li>• High render counts may indicate infinite re-render loops</li>
          <li>• Import failures suggest missing dependencies or build issues</li>
          <li>• WebSocket failures are normal if the backend doesn't support WebSockets</li>
        </ul>
      </div>
    </div>
  )
}
