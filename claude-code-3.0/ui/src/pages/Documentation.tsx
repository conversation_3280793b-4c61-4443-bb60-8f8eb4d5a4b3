
import { BookOpen, ExternalLink, Download } from 'lucide-react'

export function Documentation() {
  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 pb-4">
        <h1 className="text-2xl font-bold text-gray-900">Documentation</h1>
        <p className="text-gray-600">
          Comprehensive guides and API reference for Claude Code 3.0
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card p-6 text-center">
          <BookOpen className="w-12 h-12 text-primary-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Quick Start</h3>
          <p className="text-gray-600 mb-4">Get started in 5 minutes</p>
          <button className="btn-primary">
            Read Guide
          </button>
        </div>
        
        <div className="card p-6 text-center">
          <ExternalLink className="w-12 h-12 text-success-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">API Reference</h3>
          <p className="text-gray-600 mb-4">Complete API documentation</p>
          <button className="btn-secondary">
            View API Docs
          </button>
        </div>
        
        <div className="card p-6 text-center">
          <Download className="w-12 h-12 text-warning-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Examples</h3>
          <p className="text-gray-600 mb-4">Code examples and tutorials</p>
          <button className="btn-secondary">
            Browse Examples
          </button>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">System Information</h3>
        </div>
        <div className="card-content">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Framework Version</h4>
              <p className="text-gray-600">Claude Code 3.0.0</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Architecture</h4>
              <p className="text-gray-600">8-layer event-driven system</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Performance</h4>
              <p className="text-gray-600">0.001ms latency, 4.3M msg/sec</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Local LLM</h4>
              <p className="text-gray-600">Ollama with qwen2.5:3b support</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
