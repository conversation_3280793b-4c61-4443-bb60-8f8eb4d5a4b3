import React from 'react'
import { Save, RefreshCw } from 'lucide-react'

export function Settings() {
  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 pb-4">
        <h1 className="text-2xl font-bold text-gray-900">System Settings</h1>
        <p className="text-gray-600">
          Configure your Claude Code 3.0 system
        </p>
      </div>

      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">Multi-Agent Configuration</h3>
        </div>
        <div className="card-content space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Maximum Agents
              </label>
              <input
                type="number"
                defaultValue={10}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Load Balancing Strategy
              </label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="least-loaded">Least Loaded</option>
                <option value="round-robin">Round Robin</option>
                <option value="random">Random</option>
                <option value="capability-based">Capability Based</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">Local LLM Configuration</h3>
        </div>
        <div className="card-content space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ollama Model
              </label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="qwen2.5:3b">qwen2.5:3b (Recommended)</option>
                <option value="qwen2.5:7b">qwen2.5:7b</option>
                <option value="llama3.2:3b">llama3.2:3b</option>
                <option value="codellama:7b">codellama:7b</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Temperature
              </label>
              <input
                type="number"
                step="0.1"
                min="0"
                max="2"
                defaultValue={0.7}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="flex items-center space-x-4">
        <button className="btn-primary">
          <Save className="w-4 h-4 mr-2" />
          Save Settings
        </button>
        <button className="btn-secondary">
          <RefreshCw className="w-4 h-4 mr-2" />
          Reset to Defaults
        </button>
      </div>
    </div>
  )
}
