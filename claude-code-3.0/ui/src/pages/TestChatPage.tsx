import { useState } from 'react'

export function TestChatPage() {
  const [testResults, setTestResults] = useState<string[]>([])

  const addResult = (result: string) => {
    setTestResults(prev => [...prev, result])
  }

  const testMarkedLibrary = async () => {
    try {
      const { marked } = await import('marked')
      const testMarkdown = '# Hello World\n\nThis is a **test** with `code`.\n\n```javascript\nconsole.log("Hello!");\n```'
      const result = marked.parse(testMarkdown)
      addResult(`✅ Marked library working: ${result.length} chars generated`)
      console.log('Marked result:', result)
    } catch (error) {
      addResult(`❌ Marked library failed: ${error}`)
      console.error('Marked error:', error)
    }
  }

  const testSyntaxHighlighter = async () => {
    try {
      const { Prism } = await import('react-syntax-highlighter')
      addResult(`✅ Syntax highlighter imported successfully`)
      console.log('Syntax highlighter:', Prism)
    } catch (error) {
      addResult(`❌ Syntax highlighter failed: ${error}`)
      console.error('Syntax highlighter error:', error)
    }
  }

  const testStreamingAPI = async () => {
    try {
      const response = await fetch('http://localhost:8080/api/messages/stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: 'Test streaming message',
          sessionId: 'test-session',
          capabilities: ['general']
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      addResult(`✅ Streaming API endpoint accessible`)
      
      // Test reading the stream
      const reader = response.body?.getReader()
      const decoder = new TextDecoder()
      let chunks = 0

      if (reader) {
        while (chunks < 5) { // Read first 5 chunks
          const { done, value } = await reader.read()
          if (done) break
          
          const chunk = decoder.decode(value)
          console.log('Stream chunk:', chunk)
          chunks++
        }
        reader.cancel()
        addResult(`✅ Streaming data received: ${chunks} chunks`)
      }
    } catch (error) {
      addResult(`❌ Streaming API failed: ${error}`)
      console.error('Streaming error:', error)
    }
  }

  const testEnhancedChatComponent = async () => {
    try {
      const { EnhancedChat } = await import('../components/EnhancedChat')
      addResult(`✅ EnhancedChat component imported successfully`)
      console.log('EnhancedChat:', EnhancedChat)
    } catch (error) {
      addResult(`❌ EnhancedChat component failed: ${error}`)
      console.error('EnhancedChat error:', error)
    }
  }

  const runAllTests = async () => {
    setTestResults([])
    addResult('🚀 Starting component tests...')
    
    await testMarkedLibrary()
    await testSyntaxHighlighter()
    await testStreamingAPI()
    await testEnhancedChatComponent()
    
    addResult('✅ All tests completed!')
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Enhanced Chat Component Tests</h1>
      
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
        <div className="space-x-4">
          <button
            onClick={runAllTests}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Run All Tests
          </button>
          <button
            onClick={testMarkedLibrary}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Test Marked
          </button>
          <button
            onClick={testSyntaxHighlighter}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            Test Syntax Highlighter
          </button>
          <button
            onClick={testStreamingAPI}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
          >
            Test Streaming API
          </button>
          <button
            onClick={testEnhancedChatComponent}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Test Enhanced Chat
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold mb-4">Test Results</h2>
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {testResults.length === 0 ? (
            <p className="text-gray-500">No tests run yet. Click "Run All Tests" to start.</p>
          ) : (
            testResults.map((result, index) => (
              <div
                key={index}
                className={`p-2 rounded text-sm font-mono ${
                  result.startsWith('✅') ? 'bg-green-50 text-green-800' :
                  result.startsWith('❌') ? 'bg-red-50 text-red-800' :
                  result.startsWith('🚀') ? 'bg-blue-50 text-blue-800' :
                  'bg-gray-50 text-gray-800'
                }`}
              >
                {result}
              </div>
            ))
          )}
        </div>
      </div>

      {/* Try to render EnhancedChat if tests pass */}
      <div className="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold mb-4">Enhanced Chat Component Test</h2>
        <div id="enhanced-chat-container">
          <p className="text-gray-500">Enhanced chat component will be tested here after running tests.</p>
        </div>
      </div>
    </div>
  )
}
