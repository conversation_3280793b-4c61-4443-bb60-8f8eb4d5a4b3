import React from 'react'
import { MessageSquare, Zap, TrendingUp, Activity } from 'lucide-react'

export function MessageQueue() {
  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 pb-4">
        <h1 className="text-2xl font-bold text-gray-900">h2A Message Queue</h1>
        <p className="text-gray-600">
          Zero-latency dual-buffer message queue system
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Throughput</p>
              <p className="metric-value">4.3M/sec</p>
            </div>
            <TrendingUp className="w-8 h-8 text-success-600" />
          </div>
        </div>
        
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Latency</p>
              <p className="metric-value">0.001ms</p>
            </div>
            <Zap className="w-8 h-8 text-warning-600" />
          </div>
        </div>
        
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Queue Size</p>
              <p className="metric-value">1,247</p>
            </div>
            <MessageSquare className="w-8 h-8 text-primary-600" />
          </div>
        </div>
        
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Buffer Switch</p>
              <p className="metric-value">Active</p>
            </div>
            <Activity className="w-8 h-8 text-success-600" />
          </div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">Dual Buffer System</h3>
        </div>
        <div className="card-content">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="border-2 border-primary-200 rounded-lg p-4 bg-primary-50">
              <h4 className="font-semibold text-primary-900 mb-2">Primary Buffer (Active)</h4>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Messages:</span>
                  <span className="font-medium">847</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Processing Rate:</span>
                  <span className="font-medium">2.1M/sec</span>
                </div>
                <div className="w-full bg-primary-200 rounded-full h-2">
                  <div className="bg-primary-600 h-2 rounded-full" style={{ width: '65%' }}></div>
                </div>
              </div>
            </div>
            
            <div className="border-2 border-gray-200 rounded-lg p-4 bg-gray-50">
              <h4 className="font-semibold text-gray-900 mb-2">Secondary Buffer (Standby)</h4>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Messages:</span>
                  <span className="font-medium">400</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Processing Rate:</span>
                  <span className="font-medium">2.2M/sec</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-gray-600 h-2 rounded-full" style={{ width: '35%' }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
