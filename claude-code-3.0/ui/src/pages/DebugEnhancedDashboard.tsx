import { useState, useEffect } from 'react'
import { Activity, Users, MessageSquare, Zap, TrendingUp, Clock, AlertTriangle } from 'lucide-react'
import { ErrorBoundary } from '../components/ErrorBoundary'

interface SystemMetrics {
  totalRequests: number
  averageLatency: number
  successRate: number
  activeAgents: number
  messagesPerSecond: number
  systemUptime: number
}

interface Agent {
  id: string
  name: string
  status: 'active' | 'idle' | 'busy' | 'processing' | 'completed' | 'coordinating'
  capabilities: string[]
  totalRequests: number
  currentLoad: number
  lastActivity: string
}

interface DebugStep {
  name: string
  status: 'pending' | 'running' | 'success' | 'error'
  error?: string
  duration?: number
}

export function DebugEnhancedDashboard() {
  const [metrics, setMetrics] = useState<SystemMetrics>({
    totalRequests: 0,
    averageLatency: 0,
    successRate: 100,
    activeAgents: 0,
    messagesPerSecond: 4.3,
    systemUptime: 0
  })

  const [agents, setAgents] = useState<Agent[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [activeView, setActiveView] = useState<'overview' | 'chat' | 'coordination'>('overview')
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [debugSteps, setDebugSteps] = useState<DebugStep[]>([])

  const addDebugStep = (name: string) => {
    setDebugSteps(prev => [...prev, { name, status: 'running' }])
  }

  const updateDebugStep = (name: string, status: DebugStep['status'], error?: string, duration?: number) => {
    setDebugSteps(prev => prev.map(step => 
      step.name === name 
        ? { ...step, status, error, duration }
        : step
    ))
  }

  useEffect(() => {
    let mounted = true

    const initializeData = async () => {
      try {
        setIsLoading(true)
        setError(null)
        setDebugSteps([])

        // Step 1: Basic initialization
        addDebugStep('Basic initialization')
        const step1Start = Date.now()
        await new Promise(resolve => setTimeout(resolve, 100))
        if (!mounted) return
        updateDebugStep('Basic initialization', 'success', undefined, Date.now() - step1Start)

        // Step 2: Test fetch capabilities
        addDebugStep('Testing fetch capabilities')
        const step2Start = Date.now()
        try {
          const testResponse = await fetch('http://localhost:8080/api/metrics', {
            method: 'HEAD', // Just test connectivity
            signal: AbortSignal.timeout(2000)
          })
          updateDebugStep('Testing fetch capabilities', 'success', undefined, Date.now() - step2Start)
        } catch (fetchError) {
          updateDebugStep('Testing fetch capabilities', 'error', `${fetchError}`, Date.now() - step2Start)
        }

        if (!mounted) return

        // Step 3: Fetch metrics with detailed error handling
        addDebugStep('Fetching system metrics')
        const step3Start = Date.now()
        try {
          await fetchMetrics()
          updateDebugStep('Fetching system metrics', 'success', undefined, Date.now() - step3Start)
        } catch (metricsError) {
          updateDebugStep('Fetching system metrics', 'error', `${metricsError}`, Date.now() - step3Start)
        }

        if (!mounted) return

        // Step 4: Fetch agents with detailed error handling
        addDebugStep('Loading agent data')
        const step4Start = Date.now()
        try {
          await fetchAgents()
          updateDebugStep('Loading agent data', 'success', undefined, Date.now() - step4Start)
        } catch (agentsError) {
          updateDebugStep('Loading agent data', 'error', `${agentsError}`, Date.now() - step4Start)
        }

        if (!mounted) return

        // Step 5: Test WebSocket connection (but don't keep it open)
        addDebugStep('Testing WebSocket connection')
        const step5Start = Date.now()
        try {
          const ws = new WebSocket('ws://localhost:8080')
          
          await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
              ws.close()
              reject(new Error('WebSocket connection timeout'))
            }, 3000)

            ws.onopen = () => {
              clearTimeout(timeout)
              ws.close()
              resolve(true)
            }

            ws.onerror = (error) => {
              clearTimeout(timeout)
              reject(error)
            }
          })
          
          updateDebugStep('Testing WebSocket connection', 'success', undefined, Date.now() - step5Start)
        } catch (wsError) {
          updateDebugStep('Testing WebSocket connection', 'error', `${wsError}`, Date.now() - step5Start)
        }

        if (!mounted) return

        // Step 6: Test dynamic imports
        addDebugStep('Testing dynamic imports')
        const step6Start = Date.now()
        try {
          const [markedModule, syntaxModule, d3Module] = await Promise.allSettled([
            import('marked'),
            import('react-syntax-highlighter'),
            import('d3')
          ])

          const errors = []
          if (markedModule.status === 'rejected') errors.push(`marked: ${markedModule.reason}`)
          if (syntaxModule.status === 'rejected') errors.push(`syntax-highlighter: ${syntaxModule.reason}`)
          if (d3Module.status === 'rejected') errors.push(`d3: ${d3Module.reason}`)

          if (errors.length > 0) {
            updateDebugStep('Testing dynamic imports', 'error', errors.join(', '), Date.now() - step6Start)
          } else {
            updateDebugStep('Testing dynamic imports', 'success', undefined, Date.now() - step6Start)
          }
        } catch (importError) {
          updateDebugStep('Testing dynamic imports', 'error', `${importError}`, Date.now() - step6Start)
        }

        if (!mounted) return

        // Step 7: Generate mock data
        addDebugStep('Generating mock data')
        const step7Start = Date.now()
        try {
          generateMockAgents()
          updateDebugStep('Generating mock data', 'success', undefined, Date.now() - step7Start)
        } catch (mockError) {
          updateDebugStep('Generating mock data', 'error', `${mockError}`, Date.now() - step7Start)
        }

        if (!mounted) return

        setIsLoading(false)

      } catch (initError) {
        console.error('Failed to initialize dashboard:', initError)
        if (mounted) {
          setError(`Initialization failed: ${initError}`)
          setIsLoading(false)
        }
      }
    }

    initializeData()

    // Cleanup
    return () => {
      mounted = false
    }
  }, [])

  const fetchMetrics = async () => {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 3000)

    try {
      const response = await fetch('http://localhost:8080/api/metrics', {
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        }
      })

      clearTimeout(timeoutId)

      if (response.ok) {
        const data = await response.json()
        setMetrics(data)
        setIsConnected(true)
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      clearTimeout(timeoutId)
      setMetrics(prev => ({ ...prev, activeAgents: 3 }))
      setIsConnected(false)
      throw error
    }
  }

  const fetchAgents = async () => {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 3000)

    try {
      const response = await fetch('http://localhost:8080/api/agents', {
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        }
      })

      clearTimeout(timeoutId)

      if (response.ok) {
        const data = await response.json()
        setAgents(data)
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      clearTimeout(timeoutId)
      generateMockAgents()
      throw error
    }
  }

  const generateMockAgents = () => {
    const mockAgents: Agent[] = [
      {
        id: 'agent-1',
        name: 'General Agent',
        status: 'active',
        capabilities: ['general', 'analysis'],
        totalRequests: 150,
        currentLoad: 2.3,
        lastActivity: new Date().toISOString()
      },
      {
        id: 'agent-2',
        name: 'Code Agent',
        status: 'idle',
        capabilities: ['coding', 'debugging'],
        totalRequests: 89,
        currentLoad: 0.8,
        lastActivity: new Date().toISOString()
      },
      {
        id: 'agent-3',
        name: 'Research Agent',
        status: 'processing',
        capabilities: ['research', 'analysis'],
        totalRequests: 67,
        currentLoad: 4.1,
        lastActivity: new Date().toISOString()
      }
    ]
    setAgents(mockAgents)
  }

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    return `${hours}h ${minutes}m ${secs}s`
  }

  const getStepIcon = (status: DebugStep['status']) => {
    switch (status) {
      case 'success':
        return <div className="w-4 h-4 bg-green-500 rounded-full"></div>
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-500" />
      case 'running':
        return <div className="w-4 h-4 bg-blue-500 rounded-full animate-pulse"></div>
      default:
        return <div className="w-4 h-4 bg-gray-300 rounded-full"></div>
    }
  }

  // Show loading state with debug steps
  if (isLoading) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Debug Enhanced Dashboard</h1>
          <p className="text-gray-600">Debugging dashboard initialization step by step</p>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold mb-4">Initialization Steps</h2>
          <div className="space-y-3">
            {debugSteps.map((step, index) => (
              <div key={index} className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200">
                {getStepIcon(step.status)}
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{step.name}</h3>
                  {step.error && (
                    <p className="text-sm text-red-600 mt-1">{step.error}</p>
                  )}
                </div>
                {step.duration && (
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    {step.duration}ms
                  </span>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-red-800 mb-2">Dashboard Error</h2>
          <p className="text-red-700 mb-4">{error}</p>
          
          <div className="mb-4">
            <h3 className="font-medium text-red-800 mb-2">Debug Steps:</h3>
            <div className="space-y-2">
              {debugSteps.map((step, index) => (
                <div key={index} className="flex items-center space-x-2 text-sm">
                  {getStepIcon(step.status)}
                  <span className={step.status === 'error' ? 'text-red-700' : 'text-gray-700'}>
                    {step.name}
                  </span>
                  {step.error && <span className="text-red-600">- {step.error}</span>}
                </div>
              ))}
            </div>
          </div>
          
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Reload Dashboard
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Debug Enhanced Dashboard</h1>
          <p className="text-gray-600">Successfully loaded with detailed debugging</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-sm text-gray-600">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </div>
      </div>

      {/* Debug Steps Summary */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Initialization Summary</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {debugSteps.map((step, index) => (
            <div key={index} className={`p-3 rounded-lg border ${
              step.status === 'success' ? 'bg-green-50 border-green-200' :
              step.status === 'error' ? 'bg-red-50 border-red-200' :
              'bg-blue-50 border-blue-200'
            }`}>
              <div className="flex items-center space-x-2 mb-1">
                {getStepIcon(step.status)}
                <span className="text-sm font-medium">{step.name}</span>
              </div>
              {step.duration && (
                <span className="text-xs text-gray-500">{step.duration}ms</span>
              )}
              {step.error && (
                <p className="text-xs text-red-600 mt-1">{step.error}</p>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Basic metrics display */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Agents</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.activeAgents}/5</p>
              <p className="text-xs text-green-600">{agents.filter(a => a.status === 'active').length} online</p>
            </div>
            <Users className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Success Rate</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.successRate}%</p>
              <p className="text-xs text-green-600">All steps completed</p>
            </div>
            <TrendingUp className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Debug Status</p>
              <p className="text-2xl font-bold text-gray-900">✓</p>
              <p className="text-xs text-green-600">All systems operational</p>
            </div>
            <Activity className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <h3 className="font-medium text-green-800 mb-2">Debug Dashboard Loaded Successfully</h3>
        <p className="text-green-700 text-sm">
          All initialization steps completed. The issue with the original EnhancedDashboard is likely in the WebSocket connection management or component rendering after successful initialization.
        </p>
      </div>
    </div>
  )
}
