import React from 'react'
import { 
  <PERSON><PERSON>, 
  <PERSON>t, 
  MessageSquare, 
  Zap, 
  TrendingUp, 
  CheckCircle,
  BarChart3,
  Settings
} from 'lucide-react'

export function Dashboard() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">
          Monitor your Claude Code 3.0 system performance and status
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="metric-label">Active Agents</p>
              <div className="flex items-baseline space-x-2">
                <p className="metric-value">3<span className="text-lg text-gray-500">/5</span></p>
              </div>
              <p className="metric-change text-gray-500">No change</p>
            </div>
            <div className="p-3 rounded-lg bg-primary-50 text-primary-600">
              <Bot className="w-6 h-6" />
            </div>
          </div>
        </div>
        
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="metric-label">Messages/Second</p>
              <div className="flex items-baseline space-x-2">
                <p className="metric-value">4.3M</p>
              </div>
              <p className="metric-change positive">+15% from last hour</p>
            </div>
            <div className="p-3 rounded-lg bg-success-50 text-success-600">
              <MessageSquare className="w-6 h-6" />
            </div>
          </div>
        </div>
        
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="metric-label">Average Latency</p>
              <div className="flex items-baseline space-x-2">
                <p className="metric-value">0.001ms</p>
              </div>
              <p className="metric-change positive">5% faster</p>
            </div>
            <div className="p-3 rounded-lg bg-warning-50 text-warning-600">
              <Zap className="w-6 h-6" />
            </div>
          </div>
        </div>
        
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="metric-label">Success Rate</p>
              <div className="flex items-baseline space-x-2">
                <p className="metric-value">100%</p>
              </div>
              <p className="metric-change text-gray-500">Perfect score</p>
            </div>
            <div className="p-3 rounded-lg bg-success-50 text-success-600">
              <CheckCircle className="w-6 h-6" />
            </div>
          </div>
        </div>
      </div>

      {/* System Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">System Status</h3>
          </div>
          <div className="card-content space-y-4">
            <div className="flex items-center space-x-3 p-3 rounded-lg border border-gray-100">
              <div className="p-2 rounded-lg bg-success-50">
                <Activity className="w-5 h-5 text-success-600" />
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <h4 className="text-sm font-medium text-gray-900">Framework Core</h4>
                  <div className="flex items-center space-x-1">
                    <CheckCircle className="w-4 h-4 text-success-600" />
                    <span className="text-xs font-medium text-success-600">Online</span>
                  </div>
                </div>
                <p className="text-xs text-gray-500">Main system components</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 p-3 rounded-lg border border-gray-100">
              <div className="p-2 rounded-lg bg-success-50">
                <MessageSquare className="w-5 h-5 text-success-600" />
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <h4 className="text-sm font-medium text-gray-900">h2A Message Queue</h4>
                  <div className="flex items-center space-x-1">
                    <CheckCircle className="w-4 h-4 text-success-600" />
                    <span className="text-xs font-medium text-success-600">Online</span>
                  </div>
                </div>
                <p className="text-xs text-gray-500">Zero-latency dual-buffer system</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 p-3 rounded-lg border border-gray-100">
              <div className="p-2 rounded-lg bg-success-50">
                <Bot className="w-5 h-5 text-success-600" />
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <h4 className="text-sm font-medium text-gray-900">Multi-Agent System</h4>
                  <div className="flex items-center space-x-1">
                    <CheckCircle className="w-4 h-4 text-success-600" />
                    <span className="text-xs font-medium text-success-600">Online</span>
                  </div>
                </div>
                <p className="text-xs text-gray-500">3 of 5 agents active</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 p-3 rounded-lg border border-gray-100">
              <div className="p-2 rounded-lg bg-success-50">
                <Zap className="w-5 h-5 text-success-600" />
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <h4 className="text-sm font-medium text-gray-900">Local LLM (qwen2.5:3b)</h4>
                  <div className="flex items-center space-x-1">
                    <CheckCircle className="w-4 h-4 text-success-600" />
                    <span className="text-xs font-medium text-success-600">Online</span>
                  </div>
                </div>
                <p className="text-xs text-gray-500">116+ tokens/sec performance</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <CheckCircle className="w-5 h-5 text-success-500" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">Agent spawned: agent-specialized</p>
                  <p className="text-xs text-gray-500">2 minutes ago</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <TrendingUp className="w-5 h-5 text-primary-500" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">Performance benchmark completed</p>
                  <p className="text-xs text-gray-500">5 minutes ago</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <MessageSquare className="w-5 h-5 text-warning-500" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">Message queue processed 1M messages</p>
                  <p className="text-xs text-gray-500">10 minutes ago</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <Bot className="w-5 h-5 text-success-500" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">Multi-agent system initialized</p>
                  <p className="text-xs text-gray-500">15 minutes ago</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
        </div>
        <div className="card-content">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button className="btn-primary p-4 text-left">
              <Bot className="w-6 h-6 mb-2" />
              <div className="text-sm font-medium">Spawn New Agent</div>
              <div className="text-xs opacity-75">Create a new AI agent</div>
            </button>
            
            <button className="btn-secondary p-4 text-left">
              <BarChart3 className="w-6 h-6 mb-2" />
              <div className="text-sm font-medium">Run Benchmark</div>
              <div className="text-xs opacity-75">Test system performance</div>
            </button>
            
            <button className="btn-secondary p-4 text-left">
              <Settings className="w-6 h-6 mb-2" />
              <div className="text-sm font-medium">System Settings</div>
              <div className="text-xs opacity-75">Configure the system</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
