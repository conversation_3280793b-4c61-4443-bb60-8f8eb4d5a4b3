
import { useState, useEffect, useRef } from 'react'
import {
  <PERSON><PERSON>,
  Bot,
  MessageSquare,
  Zap,
  TrendingUp,
  CheckCircle,
  BarChart3,
  <PERSON><PERSON>s,
  Send,
  User,
  C<PERSON>,
  Clock,
  Users
} from 'lucide-react'

interface Message {
  id: string
  content: string
  sender: 'user' | 'agent'
  agentId?: string
  agentName?: string
  timestamp: Date
  processingTime?: number
}

interface Agent {
  id: string
  name: string
  status: 'active' | 'idle' | 'busy'
  capabilities: string[]
  currentLoad: number
  totalRequests: number
  lastActivity: string
  model: string
}

export function Dashboard() {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [agents, setAgents] = useState<Agent[]>([])
  const [metrics, setMetrics] = useState({
    totalAgents: 5,
    activeAgents: 3,
    messagesPerSecond: 4322773,
    averageLatency: 0.001,
    successRate: 100,
    uptime: '99.9%'
  })
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Fetch initial data
  useEffect(() => {
    fetchAgents()
    fetchMetrics()

    // Set up WebSocket for real-time updates
    const ws = new WebSocket('ws://localhost:8080')

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      if (data.type === 'metrics_update') {
        setMetrics(data.data)
      } else if (data.type === 'agents_update') {
        setAgents(data.data)
      }
    }

    return () => ws.close()
  }, [])

  // Auto-scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const fetchAgents = async () => {
    try {
      const response = await fetch('http://localhost:8080/api/agents')
      const data = await response.json()
      setAgents(data)
    } catch (error) {
      console.error('Failed to fetch agents:', error)
    }
  }

  const fetchMetrics = async () => {
    try {
      const response = await fetch('http://localhost:8080/api/metrics')
      const data = await response.json()
      setMetrics(data)
    } catch (error) {
      console.error('Failed to fetch metrics:', error)
    }
  }

  const sendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      content: inputMessage,
      sender: 'user',
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsLoading(true)

    try {
      const response = await fetch('http://localhost:8080/api/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: inputMessage,
          sessionId: 'dashboard-session',
          capabilities: ['general']
        })
      })

      const data = await response.json()

      const agentMessage: Message = {
        id: data.id,
        content: data.content,
        sender: 'agent',
        agentId: data.agentId,
        agentName: agents.find(a => a.id === data.agentId)?.name || 'AI Agent',
        timestamp: new Date(data.timestamp),
        processingTime: data.processingTime
      }

      setMessages(prev => [...prev, agentMessage])
    } catch (error) {
      console.error('Failed to send message:', error)
      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        content: 'Sorry, I encountered an error processing your message.',
        sender: 'agent',
        agentName: 'System',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h1 className="text-2xl font-bold text-gray-900">Claude Code 3.0 - Multi-Agent Chat</h1>
        <p className="text-gray-600">
          Zero-latency AI processing with intelligent agent orchestration
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="metric-label">Active Agents</p>
              <div className="flex items-baseline space-x-2">
                <p className="metric-value">{metrics.activeAgents}<span className="text-lg text-gray-500">/{metrics.totalAgents}</span></p>
              </div>
              <p className="metric-change text-gray-500">{agents.filter(a => a.status === 'active').length} online</p>
            </div>
            <div className="p-3 rounded-lg bg-primary-50 text-primary-600">
              <Bot className="w-6 h-6" />
            </div>
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="metric-label">Messages/Second</p>
              <div className="flex items-baseline space-x-2">
                <p className="metric-value">{(metrics.messagesPerSecond / 1000000).toFixed(1)}M</p>
              </div>
              <p className="metric-change positive">Zero latency processing</p>
            </div>
            <div className="p-3 rounded-lg bg-success-50 text-success-600">
              <MessageSquare className="w-6 h-6" />
            </div>
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="metric-label">Average Latency</p>
              <div className="flex items-baseline space-x-2">
                <p className="metric-value">{metrics.averageLatency.toFixed(3)}ms</p>
              </div>
              <p className="metric-change positive">4,960x faster</p>
            </div>
            <div className="p-3 rounded-lg bg-warning-50 text-warning-600">
              <Zap className="w-6 h-6" />
            </div>
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="metric-label">Success Rate</p>
              <div className="flex items-baseline space-x-2">
                <p className="metric-value">{metrics.successRate}%</p>
              </div>
              <p className="metric-change text-gray-500">Perfect reliability</p>
            </div>
            <div className="p-3 rounded-lg bg-success-50 text-success-600">
              <CheckCircle className="w-6 h-6" />
            </div>
          </div>
        </div>
      </div>

      {/* Multi-Agent Chat Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Chat Messages */}
        <div className="lg:col-span-2 card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">Multi-Agent Chat</h3>
            <p className="text-sm text-gray-500">Interact with AI agents in real-time</p>
          </div>
          <div className="card-content p-0">
            {/* Messages Container */}
            <div className="h-96 overflow-y-auto p-4 space-y-4">
              {messages.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  <Bot className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>Start a conversation with the AI agents</p>
                  <p className="text-sm">Ask questions, request code generation, or get analysis</p>
                </div>
              ) : (
                messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                        message.sender === 'user'
                          ? 'bg-primary-600 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      {message.sender === 'agent' && (
                        <div className="flex items-center space-x-2 mb-1">
                          <Bot className="w-4 h-4" />
                          <span className="text-xs font-medium">{message.agentName}</span>
                          {message.processingTime && (
                            <span className="text-xs opacity-75">
                              ({message.processingTime}ms)
                            </span>
                          )}
                        </div>
                      )}
                      <p className="text-sm">{message.content}</p>
                      <p className={`text-xs mt-1 ${
                        message.sender === 'user' ? 'text-primary-100' : 'text-gray-500'
                      }`}>
                        {message.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                ))
              )}
              {isLoading && (
                <div className="flex justify-start">
                  <div className="bg-gray-100 text-gray-900 max-w-xs lg:max-w-md px-4 py-2 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Bot className="w-4 h-4" />
                      <span className="text-xs font-medium">AI Agent</span>
                    </div>
                    <div className="flex space-x-1 mt-2">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <div className="border-t border-gray-200 p-4">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask the AI agents anything..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  disabled={isLoading}
                />
                <button
                  onClick={sendMessage}
                  disabled={!inputMessage.trim() || isLoading}
                  className="btn-primary px-4 py-2"
                >
                  <Send className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Active Agents Panel */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">Active Agents</h3>
            <p className="text-sm text-gray-500">{agents.filter(a => a.status === 'active').length} of {agents.length} online</p>
          </div>
          <div className="card-content space-y-4">
            {agents.map((agent) => (
              <div key={agent.id} className="flex items-center space-x-3 p-3 rounded-lg border border-gray-100">
                <div className={`p-2 rounded-lg ${
                  agent.status === 'active' ? 'bg-success-50' :
                  agent.status === 'busy' ? 'bg-warning-50' : 'bg-gray-50'
                }`}>
                  <Bot className={`w-5 h-5 ${
                    agent.status === 'active' ? 'text-success-600' :
                    agent.status === 'busy' ? 'text-warning-600' : 'text-gray-400'
                  }`} />
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <h4 className="text-sm font-medium text-gray-900">{agent.name}</h4>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      agent.status === 'active' ? 'bg-success-100 text-success-800' :
                      agent.status === 'busy' ? 'bg-warning-100 text-warning-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {agent.status}
                    </span>
                  </div>
                  <div className="flex items-center space-x-4 mt-1">
                    <div className="flex items-center space-x-1">
                      <Cpu className="w-3 h-3 text-gray-400" />
                      <span className="text-xs text-gray-500">{agent.model}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="w-3 h-3 text-gray-400" />
                      <span className="text-xs text-gray-500">{agent.totalRequests} requests</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 mt-2">
                    <div className="flex-1 bg-gray-200 rounded-full h-1">
                      <div
                        className={`h-1 rounded-full ${
                          agent.currentLoad > 3 ? 'bg-error-500' :
                          agent.currentLoad > 1 ? 'bg-warning-500' : 'bg-success-500'
                        }`}
                        style={{ width: `${Math.min((agent.currentLoad / 5) * 100, 100)}%` }}
                      ></div>
                    </div>
                    <span className="text-xs text-gray-500">Load: {agent.currentLoad}/5</span>
                  </div>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {agent.capabilities.map((cap) => (
                      <span key={cap} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                        {cap}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}

            {agents.length === 0 && (
              <div className="text-center text-gray-500 py-4">
                <Bot className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                <p className="text-sm">No agents available</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* System Performance & Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Overview */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">Performance Overview</h3>
          </div>
          <div className="card-content space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">h2A Message Queue</span>
              <span className="text-sm font-medium text-success-600">0.001ms latency</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Throughput</span>
              <span className="text-sm font-medium text-primary-600">{(metrics.messagesPerSecond / 1000000).toFixed(1)}M msg/sec</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Success Rate</span>
              <span className="text-sm font-medium text-success-600">{metrics.successRate}%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">System Uptime</span>
              <span className="text-sm font-medium text-gray-900">{metrics.uptime}</span>
            </div>
            <div className="pt-2 border-t border-gray-200">
              <div className="text-xs text-gray-500 mb-2">vs Traditional Architecture</div>
              <div className="text-lg font-bold text-success-600">4,960x Faster</div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
          </div>
          <div className="card-content">
            <div className="grid grid-cols-1 gap-3">
              <button
                onClick={() => setInputMessage("Generate a TypeScript function that calculates fibonacci numbers")}
                className="btn-primary p-4 text-left"
              >
                <Bot className="w-6 h-6 mb-2" />
                <div className="text-sm font-medium">Code Generation</div>
                <div className="text-xs opacity-75">Ask agents to generate code</div>
              </button>

              <button
                onClick={() => setInputMessage("Explain the h2A zero-latency architecture")}
                className="btn-secondary p-4 text-left"
              >
                <MessageSquare className="w-6 h-6 mb-2" />
                <div className="text-sm font-medium">System Analysis</div>
                <div className="text-xs opacity-75">Get technical explanations</div>
              </button>

              <button
                onClick={() => setInputMessage("What are the current performance metrics?")}
                className="btn-secondary p-4 text-left"
              >
                <BarChart3 className="w-6 h-6 mb-2" />
                <div className="text-sm font-medium">Performance Query</div>
                <div className="text-xs opacity-75">Ask about system performance</div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
