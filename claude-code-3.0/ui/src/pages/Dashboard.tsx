
import { useState, useEffect, useRef } from 'react'
import {
  <PERSON><PERSON>,
  MessageSquare,
  Zap,
  CheckCircle,
  BarChart3,
  Send,
  Cpu,
  Users,
  TestTube,
  X
} from 'lucide-react'

interface Message {
  id: string
  content: string
  sender: 'user' | 'agent'
  agentId?: string
  agentName?: string
  timestamp: Date
  processingTime?: number
}

interface Agent {
  id: string
  name: string
  status: 'active' | 'idle' | 'busy'
  capabilities: string[]
  currentLoad: number
  totalRequests: number
  lastActivity: string
  model: string
}

export function Dashboard() {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [agents, setAgents] = useState<Agent[]>([])
  const [showStressTest, setShowStressTest] = useState(false)
  const [metrics, setMetrics] = useState({
    totalAgents: 5,
    activeAgents: 3,
    messagesPerSecond: 4322773,
    averageLatency: 0.001,
    successRate: 100,
    uptime: '99.9%'
  })
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Fetch initial data
  useEffect(() => {
    fetchAgents()
    fetchMetrics()

    // Set up WebSocket for real-time updates
    const ws = new WebSocket('ws://localhost:8080')

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      if (data.type === 'metrics_update') {
        setMetrics(data.data)
      } else if (data.type === 'agents_update') {
        setAgents(data.data)
      }
    }

    return () => ws.close()
  }, [])

  // Auto-scroll to bottom of messages
  useEffect(() => {
    console.log('Dashboard: Messages updated:', messages)
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Debug: Log initial state
  useEffect(() => {
    console.log('Dashboard: Component mounted, initial messages:', messages)
  }, [])

  const fetchAgents = async () => {
    try {
      const response = await fetch('http://localhost:8080/api/agents')
      const data = await response.json()
      setAgents(data)
    } catch (error) {
      console.error('Failed to fetch agents:', error)
    }
  }

  const fetchMetrics = async () => {
    try {
      const response = await fetch('http://localhost:8080/api/metrics')
      const data = await response.json()
      setMetrics(data)
    } catch (error) {
      console.error('Failed to fetch metrics:', error)
    }
  }

  const sendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return

    console.log('Sending message:', inputMessage)

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      content: inputMessage,
      sender: 'user',
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    const messageContent = inputMessage
    setInputMessage('')
    setIsLoading(true)

    try {
      console.log('Making API request to:', 'http://localhost:8080/api/messages')
      const response = await fetch('http://localhost:8080/api/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: messageContent,
          sessionId: 'dashboard-session',
          capabilities: ['general']
        })
      })

      console.log('Response status:', response.status)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      console.log('Response data:', data)

      const agentMessage: Message = {
        id: data.id,
        content: data.content,
        sender: 'agent',
        agentId: data.agentId,
        agentName: agents.find(a => a.id === data.agentId)?.name || 'AI Agent',
        timestamp: new Date(data.timestamp),
        processingTime: data.processingTime
      }

      setMessages(prev => [...prev, agentMessage])
      console.log('Agent message added:', agentMessage)
    } catch (error) {
      console.error('Failed to send message:', error)
      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        content: `Sorry, I encountered an error processing your message. Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        sender: 'agent',
        agentName: 'System',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      console.log('Enter key pressed, sending message')
      sendMessage()
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('Input changed:', e.target.value)
    setInputMessage(e.target.value)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Claude Code 3.0 - Multi-Agent Chat</h1>
            <p className="text-gray-600">
              Zero-latency AI processing with intelligent agent orchestration
            </p>
          </div>
          <button
            onClick={() => setShowStressTest(true)}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <TestTube className="w-4 h-4" />
            Stress Test
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="metric-label">Active Agents</p>
              <div className="flex items-baseline space-x-2">
                <p className="metric-value">{metrics.activeAgents}<span className="text-lg text-gray-500">/{metrics.totalAgents}</span></p>
              </div>
              <p className="metric-change text-gray-500">{agents.filter(a => a.status === 'active').length} online</p>
            </div>
            <div className="p-3 rounded-lg bg-primary-50 text-primary-600">
              <Bot className="w-6 h-6" />
            </div>
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="metric-label">Messages/Second</p>
              <div className="flex items-baseline space-x-2">
                <p className="metric-value">{(metrics.messagesPerSecond / 1000000).toFixed(1)}M</p>
              </div>
              <p className="metric-change positive">Zero latency processing</p>
            </div>
            <div className="p-3 rounded-lg bg-success-50 text-success-600">
              <MessageSquare className="w-6 h-6" />
            </div>
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="metric-label">Average Latency</p>
              <div className="flex items-baseline space-x-2">
                <p className="metric-value">{metrics.averageLatency.toFixed(3)}ms</p>
              </div>
              <p className="metric-change positive">4,960x faster</p>
            </div>
            <div className="p-3 rounded-lg bg-warning-50 text-warning-600">
              <Zap className="w-6 h-6" />
            </div>
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="metric-label">Success Rate</p>
              <div className="flex items-baseline space-x-2">
                <p className="metric-value">{metrics.successRate}%</p>
              </div>
              <p className="metric-change text-gray-500">Perfect reliability</p>
            </div>
            <div className="p-3 rounded-lg bg-success-50 text-success-600">
              <CheckCircle className="w-6 h-6" />
            </div>
          </div>
        </div>
      </div>

      {/* Multi-Agent Chat Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Chat Messages */}
        <div className="lg:col-span-2 card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">Multi-Agent Chat</h3>
            <p className="text-sm text-gray-500">Interact with AI agents in real-time</p>
          </div>
          <div className="card-content p-0">
            {/* Messages Container */}
            <div className="h-96 overflow-y-auto p-4 space-y-4">
              {messages.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  <Bot className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>Start a conversation with the AI agents</p>
                  <p className="text-sm">Ask questions, request code generation, or get analysis</p>
                </div>
              ) : (
                messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                        message.sender === 'user'
                          ? 'bg-primary-600 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      {message.sender === 'agent' && (
                        <div className="flex items-center space-x-2 mb-1">
                          <Bot className="w-4 h-4" />
                          <span className="text-xs font-medium">{message.agentName}</span>
                          {message.processingTime && (
                            <span className="text-xs opacity-75">
                              ({message.processingTime}ms)
                            </span>
                          )}
                        </div>
                      )}
                      <p className="text-sm">{message.content}</p>
                      <p className={`text-xs mt-1 ${
                        message.sender === 'user' ? 'text-primary-100' : 'text-gray-500'
                      }`}>
                        {message.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                ))
              )}
              {isLoading && (
                <div className="flex justify-start">
                  <div className="bg-gray-100 text-gray-900 max-w-xs lg:max-w-md px-4 py-2 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Bot className="w-4 h-4" />
                      <span className="text-xs font-medium">AI Agent</span>
                    </div>
                    <div className="flex space-x-1 mt-2">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <div className="border-t border-gray-200 p-4">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={inputMessage}
                  onChange={handleInputChange}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask the AI agents anything..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  disabled={isLoading}
                />
                <button
                  onClick={() => {
                    console.log('Send button clicked')
                    sendMessage()
                  }}
                  disabled={!inputMessage.trim() || isLoading}
                  className="btn-primary px-4 py-2"
                >
                  <Send className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Active Agents Panel */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">Active Agents</h3>
            <p className="text-sm text-gray-500">{agents.filter(a => a.status === 'active').length} of {agents.length} online</p>
          </div>
          <div className="card-content space-y-4">
            {agents.map((agent) => (
              <div key={agent.id} className="flex items-center space-x-3 p-3 rounded-lg border border-gray-100">
                <div className={`p-2 rounded-lg ${
                  agent.status === 'active' ? 'bg-success-50' :
                  agent.status === 'busy' ? 'bg-warning-50' : 'bg-gray-50'
                }`}>
                  <Bot className={`w-5 h-5 ${
                    agent.status === 'active' ? 'text-success-600' :
                    agent.status === 'busy' ? 'text-warning-600' : 'text-gray-400'
                  }`} />
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <h4 className="text-sm font-medium text-gray-900">{agent.name}</h4>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      agent.status === 'active' ? 'bg-success-100 text-success-800' :
                      agent.status === 'busy' ? 'bg-warning-100 text-warning-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {agent.status}
                    </span>
                  </div>
                  <div className="flex items-center space-x-4 mt-1">
                    <div className="flex items-center space-x-1">
                      <Cpu className="w-3 h-3 text-gray-400" />
                      <span className="text-xs text-gray-500">{agent.model}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="w-3 h-3 text-gray-400" />
                      <span className="text-xs text-gray-500">{agent.totalRequests} requests</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 mt-2">
                    <div className="flex-1 bg-gray-200 rounded-full h-1">
                      <div
                        className={`h-1 rounded-full ${
                          agent.currentLoad > 3 ? 'bg-error-500' :
                          agent.currentLoad > 1 ? 'bg-warning-500' : 'bg-success-500'
                        }`}
                        style={{ width: `${Math.min((agent.currentLoad / 5) * 100, 100)}%` }}
                      ></div>
                    </div>
                    <span className="text-xs text-gray-500">Load: {agent.currentLoad}/5</span>
                  </div>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {agent.capabilities.map((cap) => (
                      <span key={cap} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                        {cap}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}

            {agents.length === 0 && (
              <div className="text-center text-gray-500 py-4">
                <Bot className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                <p className="text-sm">No agents available</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* System Performance & Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Overview */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">Performance Overview</h3>
          </div>
          <div className="card-content space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">h2A Message Queue</span>
              <span className="text-sm font-medium text-success-600">0.001ms latency</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Throughput</span>
              <span className="text-sm font-medium text-primary-600">{(metrics.messagesPerSecond / 1000000).toFixed(1)}M msg/sec</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Success Rate</span>
              <span className="text-sm font-medium text-success-600">{metrics.successRate}%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">System Uptime</span>
              <span className="text-sm font-medium text-gray-900">{metrics.uptime}</span>
            </div>
            <div className="pt-2 border-t border-gray-200">
              <div className="text-xs text-gray-500 mb-2">vs Traditional Architecture</div>
              <div className="text-lg font-bold text-success-600">4,960x Faster</div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
          </div>
          <div className="card-content">
            <div className="grid grid-cols-1 gap-3">
              <button
                onClick={() => setInputMessage("Generate a TypeScript function that calculates fibonacci numbers")}
                className="btn-primary p-4 text-left"
              >
                <Bot className="w-6 h-6 mb-2" />
                <div className="text-sm font-medium">Code Generation</div>
                <div className="text-xs opacity-75">Ask agents to generate code</div>
              </button>

              <button
                onClick={() => setInputMessage("Explain the h2A zero-latency architecture")}
                className="btn-secondary p-4 text-left"
              >
                <MessageSquare className="w-6 h-6 mb-2" />
                <div className="text-sm font-medium">System Analysis</div>
                <div className="text-xs opacity-75">Get technical explanations</div>
              </button>

              <button
                onClick={() => setInputMessage("What are the current performance metrics?")}
                className="btn-secondary p-4 text-left"
              >
                <BarChart3 className="w-6 h-6 mb-2" />
                <div className="text-sm font-medium">Performance Query</div>
                <div className="text-xs opacity-75">Ask about system performance</div>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stress Test Modal */}
      {showStressTest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b">
              <h2 className="text-xl font-bold">🧪 Multi-Agent Stress Test</h2>
              <button
                onClick={() => setShowStressTest(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="p-6">
              <StressTestComponent agents={agents} />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Inline Stress Test Component
function StressTestComponent({ agents }: { agents: Agent[] }) {
  const [isRunning, setIsRunning] = useState(false)
  const [activeRequests, setActiveRequests] = useState(0)
  const [completedRequests, setCompletedRequests] = useState(0)
  const [failedRequests, setFailedRequests] = useState(0)
  const [testProgress, setTestProgress] = useState(0)
  const [testResults, setTestResults] = useState<any[]>([])

  const stressTestScenarios = [
    {
      title: 'Quantum ML System',
      content: 'Design and implement a quantum-inspired machine learning optimization system that combines quantum computing principles with neural network training. Include mathematical derivations, complete TypeScript implementation, quantum state management, and integration with existing ML frameworks.',
      complexity: 'extreme'
    },
    {
      title: 'Byzantine Consensus',
      content: 'Explain the theoretical foundations of Byzantine fault tolerance in distributed systems. Cover mathematical proofs, game-theoretic analysis, information-theoretic bounds, and implications for blockchain systems.',
      complexity: 'extreme'
    },
    {
      title: 'Protein Folding AI',
      content: 'Develop a comprehensive protein folding prediction system combining AI and molecular dynamics. Implement AlphaFold-style mechanisms, molecular simulation, energy minimization, and 3D visualization in TypeScript.',
      complexity: 'extreme'
    },
    {
      title: 'Quantum Cryptography',
      content: 'Analyze the security implications of quantum computing on modern cryptography. Cover Shor\'s algorithm impact, post-quantum alternatives, quantum key distribution, and migration strategies.',
      complexity: 'extreme'
    },
    {
      title: 'Advanced ML Algorithms',
      content: 'Create advanced machine learning optimization algorithms with mathematical foundations. Derive novel optimization methods, implement adaptive schedules, design distributed optimization, and analyze convergence properties.',
      complexity: 'extreme'
    },
    {
      title: 'Complexity Theory & ML',
      content: 'Explore the relationship between computational complexity theory and machine learning. Cover P vs NP implications, sample complexity bounds, approximation algorithms, and quantum complexity classes.',
      complexity: 'extreme'
    }
  ]

  const startStressTest = async () => {
    setIsRunning(true)
    setActiveRequests(0)
    setCompletedRequests(0)
    setFailedRequests(0)
    setTestProgress(0)
    setTestResults([])

    const concurrentRequests = 8 // More than current 3 agents to trigger scaling
    const totalRequests = 24
    const batchSize = concurrentRequests

    console.log('🚀 Starting UI Stress Test...')
    console.log(`📊 Config: ${concurrentRequests} concurrent, ${totalRequests} total`)

    try {
      for (let batch = 0; batch < Math.ceil(totalRequests / batchSize); batch++) {
        const currentBatchSize = Math.min(batchSize, totalRequests - batch * batchSize)
        console.log(`🌊 Batch ${batch + 1}: Launching ${currentBatchSize} concurrent requests`)

        setActiveRequests(currentBatchSize)

        // Launch concurrent requests
        const promises = []
        for (let i = 0; i < currentBatchSize; i++) {
          const scenario = stressTestScenarios[i % stressTestScenarios.length]
          promises.push(sendStressTestRequest(scenario, `${batch}-${i}`))
        }

        // Wait for batch completion
        const results = await Promise.allSettled(promises)

        const successful = results.filter(r => r.status === 'fulfilled').length
        const failed = results.filter(r => r.status === 'rejected').length

        setCompletedRequests(prev => prev + successful)
        setFailedRequests(prev => prev + failed)
        setActiveRequests(0)
        setTestProgress(((batch + 1) * batchSize / totalRequests) * 100)

        // Small delay between batches
        if (batch < Math.ceil(totalRequests / batchSize) - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }

      console.log('✅ Stress test completed!')
    } catch (error) {
      console.error('❌ Stress test failed:', error)
    } finally {
      setIsRunning(false)
      setActiveRequests(0)
    }
  }

  const sendStressTestRequest = async (scenario: any, requestId: string) => {
    const startTime = Date.now()

    try {
      console.log(`🎯 ${requestId}: ${scenario.title}`)

      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content: scenario.content })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const result = await response.json()
      const duration = Date.now() - startTime

      setTestResults(prev => [...prev, {
        id: requestId,
        scenario: scenario.title,
        duration,
        agentId: result.agentId,
        success: true
      }])

      console.log(`✅ ${requestId}: Completed in ${duration}ms by ${result.agentId}`)
      return result

    } catch (error) {
      const duration = Date.now() - startTime

      setTestResults(prev => [...prev, {
        id: requestId,
        scenario: scenario.title,
        duration,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }])

      console.error(`❌ ${requestId}: Failed after ${duration}ms`)
      throw error
    }
  }

  const getAgentStatusColor = (status: string, load: number) => {
    if (status !== 'active') return 'bg-gray-500'
    if (load > 4.0) return 'bg-red-500'
    if (load > 2.0) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  const avgResponseTime = testResults.filter(r => r.success).length > 0
    ? testResults.filter(r => r.success).reduce((sum, r) => sum + r.duration, 0) / testResults.filter(r => r.success).length
    : 0

  return (
    <div className="space-y-6">
      {/* Test Controls */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold">Concurrent Load Test</h3>
            <p className="text-sm text-gray-600">Send 8 concurrent extreme-complexity requests to trigger agent scaling</p>
          </div>
          <button
            onClick={startStressTest}
            disabled={isRunning}
            className={`px-6 py-2 rounded-lg font-medium ${
              isRunning
                ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {isRunning ? '🔄 Running...' : '🚀 Start Stress Test'}
          </button>
        </div>

        {isRunning && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{Math.round(testProgress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${testProgress}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Real-time Agent Status */}
      <div className="bg-white border rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-4">🤖 Real-time Agent Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {agents.map(agent => (
            <div key={agent.id} className="p-3 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <div className={`w-3 h-3 rounded-full ${getAgentStatusColor(agent.status, agent.currentLoad)}`} />
                <span className="font-medium text-sm">{agent.name}</span>
              </div>
              <div className="text-xs space-y-1">
                <div>Load: <span className="font-mono">{agent.currentLoad.toFixed(2)}</span></div>
                <div>Requests: <span className="font-mono">{agent.totalRequests}</span></div>
                <div>Status: <span className="px-2 py-1 bg-gray-100 rounded text-xs">{agent.status}</span></div>
              </div>
            </div>
          ))}
        </div>

        {agents.length > 3 && (
          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2">
              <span className="text-green-600">🆕</span>
              <span className="font-medium text-green-800 text-sm">
                Agent Scaling Detected: {agents.length} agents active (scaled from 3)
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Test Results */}
      <div className="bg-white border rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-4">📊 Test Results</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{activeRequests}</div>
            <div className="text-sm text-gray-600">Active</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{completedRequests}</div>
            <div className="text-sm text-gray-600">Completed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{failedRequests}</div>
            <div className="text-sm text-gray-600">Failed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{Math.round(avgResponseTime)}</div>
            <div className="text-sm text-gray-600">Avg Time (ms)</div>
          </div>
        </div>

        {testResults.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium">Recent Results</h4>
            <div className="max-h-40 overflow-y-auto space-y-1">
              {testResults.slice(-10).reverse().map(result => (
                <div key={result.id} className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm">
                  <span>{result.scenario}</span>
                  <div className="flex items-center gap-2">
                    {result.agentId && <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">{result.agentId}</span>}
                    <span className="font-mono">{result.duration}ms</span>
                    <span className={result.success ? 'text-green-600' : 'text-red-600'}>
                      {result.success ? '✅' : '❌'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-blue-800 mb-2">📋 How to Use</h3>
        <div className="text-sm text-blue-700 space-y-2">
          <p><strong>1. Click "Start Stress Test"</strong> to launch 8 concurrent extreme-complexity requests</p>
          <p><strong>2. Watch Agent Status</strong> - Load will increase and new agents will spawn when threshold exceeded</p>
          <p><strong>3. Monitor Results</strong> - See real-time completion rates and response times</p>
          <p><strong>4. Check Console</strong> - Open browser DevTools to see detailed logging</p>
          <p className="font-medium">💡 The system will automatically spawn additional agents when load exceeds 80% threshold!</p>
        </div>
      </div>
    </div>
  )
}
