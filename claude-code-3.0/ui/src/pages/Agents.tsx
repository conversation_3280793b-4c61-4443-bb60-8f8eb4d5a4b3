import React from 'react'
import { Bot, Plus } from 'lucide-react'

export function Agents() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between border-b border-gray-200 pb-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Multi-Agent System</h1>
          <p className="text-gray-600">
            Manage and monitor your AI agents with load balancing
          </p>
        </div>
        <button className="btn-primary">
          <Plus className="w-4 h-4 mr-2" />
          Spawn Agent
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Total Agents</p>
              <p className="metric-value">5</p>
            </div>
            <Bot className="w-8 h-8 text-primary-600" />
          </div>
        </div>
        
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Active Agents</p>
              <p className="metric-value">3</p>
            </div>
            <div className="w-8 h-8 bg-success-100 rounded-lg flex items-center justify-center">
              <div className="w-3 h-3 bg-success-500 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>
        
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Total Requests</p>
              <p className="metric-value">2,595</p>
            </div>
            <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
              <span className="text-primary-600 font-bold text-sm">R</span>
            </div>
          </div>
        </div>
        
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Avg Load</p>
              <p className="metric-value">1.0</p>
            </div>
            <div className="w-8 h-8 bg-warning-100 rounded-lg flex items-center justify-center">
              <span className="text-warning-600 font-bold text-sm">L</span>
            </div>
          </div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">Agent Management</h3>
        </div>
        <div className="card-content">
          <p className="text-gray-600">
            Multi-agent management interface will be available here. 
            You can spawn, monitor, and manage AI agents with different capabilities.
          </p>
        </div>
      </div>
    </div>
  )
}
