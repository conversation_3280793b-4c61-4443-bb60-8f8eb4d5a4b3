
import { useState, useEffect } from 'react'
import { Bo<PERSON>, Plus, Play, Square, Trash2, <PERSON><PERSON>, Users, Clock, MessageSquare } from 'lucide-react'

interface Agent {
  id: string
  name: string
  status: 'active' | 'idle' | 'busy'
  capabilities: string[]
  currentLoad: number
  totalRequests: number
  lastActivity: string
  model: string
}

export function Agents() {
  const [agents, setAgents] = useState<Agent[]>([])
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    fetchAgents()

    // Set up WebSocket for real-time updates
    const ws = new WebSocket('ws://localhost:8080')

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      if (data.type === 'agents_update') {
        setAgents(data.data)
      }
    }

    return () => ws.close()
  }, [])

  const fetchAgents = async () => {
    try {
      const response = await fetch('http://localhost:8080/api/agents')
      const data = await response.json()
      setAgents(data)
    } catch (error) {
      console.error('Failed to fetch agents:', error)
    }
  }

  const spawnAgent = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('http://localhost:8080/api/agents', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: `Agent-${Date.now()}`,
          capabilities: ['general', 'analysis'],
          model: 'qwen2.5:3b'
        })
      })

      if (response.ok) {
        fetchAgents()
      }
    } catch (error) {
      console.error('Failed to spawn agent:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const deleteAgent = async (agentId: string) => {
    try {
      const response = await fetch(`http://localhost:8080/api/agents/${agentId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        fetchAgents()
      }
    } catch (error) {
      console.error('Failed to delete agent:', error)
    }
  }
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between border-b border-gray-200 pb-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Multi-Agent System</h1>
          <p className="text-gray-600">
            Intelligent agent orchestration with load balancing and auto-scaling
          </p>
        </div>
        <button
          onClick={spawnAgent}
          disabled={isLoading}
          className="btn-primary"
        >
          <Plus className="w-4 h-4 mr-2" />
          {isLoading ? 'Spawning...' : 'Spawn Agent'}
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Total Agents</p>
              <p className="metric-value">{agents.length}</p>
              <p className="text-xs text-gray-500">Spawned instances</p>
            </div>
            <Bot className="w-8 h-8 text-primary-600" />
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Active Agents</p>
              <p className="metric-value">{agents.filter(a => a.status === 'active').length}</p>
              <p className="text-xs text-gray-500">Currently processing</p>
            </div>
            <div className="w-8 h-8 bg-success-100 rounded-lg flex items-center justify-center">
              <div className="w-3 h-3 bg-success-500 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Total Requests</p>
              <p className="metric-value">{agents.reduce((sum, agent) => sum + agent.totalRequests, 0).toLocaleString()}</p>
              <p className="text-xs text-gray-500">All time processed</p>
            </div>
            <MessageSquare className="w-8 h-8 text-primary-600" />
          </div>
        </div>

        <div className="metric-card">
          <div className="flex items-center justify-between">
            <div>
              <p className="metric-label">Avg Load</p>
              <p className="metric-value">{agents.length > 0 ? (agents.reduce((sum, agent) => sum + agent.currentLoad, 0) / agents.length).toFixed(1) : '0.0'}</p>
              <p className="text-xs text-gray-500">System utilization</p>
            </div>
            <Cpu className="w-8 h-8 text-warning-600" />
          </div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">Agent Management</h3>
          <p className="text-sm text-gray-500">Monitor and control individual AI agents</p>
        </div>
        <div className="card-content p-0">
          {agents.length === 0 ? (
            <div className="text-center py-12">
              <Bot className="w-16 h-16 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Agents Available</h3>
              <p className="text-gray-500 mb-4">Spawn your first AI agent to get started</p>
              <button
                onClick={spawnAgent}
                disabled={isLoading}
                className="btn-primary"
              >
                <Plus className="w-4 h-4 mr-2" />
                {isLoading ? 'Spawning...' : 'Spawn First Agent'}
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Agent
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Capabilities
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Load
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Requests
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Model
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Last Activity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {agents.map((agent) => (
                    <tr key={agent.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <Bot className="w-8 h-8 text-primary-600" />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {agent.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {agent.id}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          agent.status === 'active' ? 'bg-success-100 text-success-800' :
                          agent.status === 'busy' ? 'bg-warning-100 text-warning-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {agent.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-wrap gap-1">
                          {agent.capabilities.map((cap) => (
                            <span key={cap} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                              {cap}
                            </span>
                          ))}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="text-sm text-gray-900">{agent.currentLoad}/5</div>
                          <div className="ml-2 w-16 bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${
                                agent.currentLoad > 3 ? 'bg-error-500' :
                                agent.currentLoad > 1 ? 'bg-warning-500' : 'bg-success-500'
                              }`}
                              style={{ width: `${Math.min((agent.currentLoad / 5) * 100, 100)}%` }}
                            ></div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {agent.totalRequests.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Cpu className="w-4 h-4 text-gray-400 mr-1" />
                          <span className="text-sm text-gray-900">{agent.model}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 text-gray-400 mr-1" />
                          <span className="text-sm text-gray-500">
                            {new Date(agent.lastActivity).toLocaleTimeString()}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          {agent.status === 'active' ? (
                            <button
                              className="text-warning-600 hover:text-warning-900"
                              title="Pause Agent"
                            >
                              <Square className="w-4 h-4" />
                            </button>
                          ) : (
                            <button
                              className="text-success-600 hover:text-success-900"
                              title="Start Agent"
                            >
                              <Play className="w-4 h-4" />
                            </button>
                          )}
                          <button
                            onClick={() => deleteAgent(agent.id)}
                            className="text-error-600 hover:text-error-900"
                            title="Delete Agent"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
