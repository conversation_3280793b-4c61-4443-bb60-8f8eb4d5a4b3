
import { Routes, Route } from 'react-router-dom'
import { Layout } from './components/Layout'
import { Dashboard } from './pages/Dashboard'
import { SimpleDashboard } from './pages/SimpleDashboard'
import { Agents } from './pages/Agents'
import { MessageQueue } from './pages/MessageQueue'
import { Performance } from './pages/Performance'
import { Settings } from './pages/Settings'
import { Documentation } from './pages/Documentation'
import { TestComponent } from './components/TestComponent'

function App() {
  try {
    return (
      <Layout>
        <Routes>
          <Route path="/" element={<SimpleDashboard />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/agents" element={<Agents />} />
          <Route path="/queue" element={<MessageQueue />} />
          <Route path="/performance" element={<Performance />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="/docs" element={<Documentation />} />
          <Route path="/test" element={<TestComponent />} />
        </Routes>
      </Layout>
    )
  } catch (error) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold text-red-600">Error Loading App</h1>
        <p className="text-red-500">Please check the browser console for details.</p>
        <TestComponent />
      </div>
    )
  }
}

export default App
