
import { Routes, Route } from 'react-router-dom'
import { Layout } from './components/Layout'
import { Dashboard } from './pages/Dashboard'
import { EnhancedDashboard } from './pages/EnhancedDashboard'
import { TestEnhancedDashboard } from './pages/TestEnhancedDashboard'
import { TestChatPage } from './pages/TestChatPage'
import { DebugPage } from './pages/DebugPage'
import { Agents } from './pages/Agents'
import { MessageQueue } from './pages/MessageQueue'
import { Performance } from './pages/Performance'
import { Settings } from './pages/Settings'
import { Documentation } from './pages/Documentation'
import { ChatTestPage } from './pages/ChatTestPage'

function App() {
  return (
    <Layout>
      <Routes>
        <Route path="/" element={<EnhancedDashboard />} />
        <Route path="/enhanced" element={<EnhancedDashboard />} />
        <Route path="/test-chat" element={<TestChatPage />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/debug" element={<DebugPage />} />
        <Route path="/agents" element={<Agents />} />
        <Route path="/queue" element={<MessageQueue />} />
        <Route path="/performance" element={<Performance />} />
        <Route path="/settings" element={<Settings />} />
        <Route path="/docs" element={<Documentation />} />
        <Route path="/chat-test" element={<ChatTestPage />} />
      </Routes>
    </Layout>
  )
}

export default App
