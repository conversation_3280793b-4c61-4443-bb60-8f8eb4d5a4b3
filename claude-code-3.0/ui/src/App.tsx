
import { Routes, Route } from 'react-router-dom'
import { Layout } from './components/Layout'
import { ErrorBoundary } from './components/ErrorBoundary'
import { Dashboard } from './pages/Dashboard'
import { EnhancedDashboard } from './pages/EnhancedDashboard'
import { SafeEnhancedDashboard } from './pages/SafeEnhancedDashboard'
import { DebugEnhancedDashboard } from './pages/DebugEnhancedDashboard'
import { TestEnhancedDashboard } from './pages/TestEnhancedDashboard'
import { DiagnosticDashboard } from './pages/DiagnosticDashboard'
import { StabilityTest } from './pages/StabilityTest'
import { TestChatPage } from './pages/TestChatPage'
import { DebugPage } from './pages/DebugPage'
import { Agents } from './pages/Agents'
import { MessageQueue } from './pages/MessageQueue'
import { Performance } from './pages/Performance'
import { Settings } from './pages/Settings'
import { Documentation } from './pages/Documentation'
import { ChatTestPage } from './pages/ChatTestPage'

function App() {
  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('App Error Boundary caught error:', error)
        console.error('Error Info:', errorInfo)
      }}
    >
      <Layout>
        <ErrorBoundary fallback={
          <div className="p-6 text-center">
            <h2 className="text-xl font-bold text-red-600 mb-2">Page Error</h2>
            <p className="text-gray-600">There was an error loading this page.</p>
          </div>
        }>
          <Routes>
            <Route path="/" element={<EnhancedDashboard />} />
            <Route path="/enhanced" element={<EnhancedDashboard />} />
            <Route path="/safe" element={<SafeEnhancedDashboard />} />
            <Route path="/debug-enhanced" element={<DebugEnhancedDashboard />} />
            <Route path="/test" element={<TestEnhancedDashboard />} />
            <Route path="/test-chat" element={<TestChatPage />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/debug" element={<DebugPage />} />
            <Route path="/diagnostic" element={<DiagnosticDashboard />} />
            <Route path="/stability" element={<StabilityTest />} />
            <Route path="/agents" element={<Agents />} />
            <Route path="/queue" element={<MessageQueue />} />
            <Route path="/performance" element={<Performance />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="/docs" element={<Documentation />} />
            <Route path="/chat-test" element={<ChatTestPage />} />
          </Routes>
        </ErrorBoundary>
      </Layout>
    </ErrorBoundary>
  )
}

export default App
