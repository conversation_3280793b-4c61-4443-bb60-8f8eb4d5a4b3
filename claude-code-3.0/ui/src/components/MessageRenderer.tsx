import { useState } from 'react'
import { Copy, Check, Code, FileText } from 'lucide-react'

interface MessageRendererProps {
  content: string
  className?: string
}

export function MessageRenderer({ content, className = '' }: MessageRendererProps) {
  const [copiedCode, setCopiedCode] = useState<string | null>(null)

  const copyToClipboard = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedCode(id)
      setTimeout(() => setCopiedCode(null), 2000)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  // Parse content for different types of blocks
  const parseContent = (text: string) => {
    const blocks: Array<{
      type: 'text' | 'code' | 'math' | 'table' | 'list'
      content: string
      language?: string
      id: string
    }> = []

    const lines = text.split('\n')
    let i = 0

    while (i < lines.length) {
      const line = lines[i]

      // Code blocks (```language)
      if (line.trim().startsWith('```')) {
        const language = line.trim().slice(3).trim() || 'text'
        const codeLines: string[] = []
        i++

        while (i < lines.length && !lines[i].trim().startsWith('```')) {
          codeLines.push(lines[i])
          i++
        }

        if (codeLines.length > 0) {
          blocks.push({
            type: 'code',
            content: codeLines.join('\n'),
            language,
            id: `code-${blocks.length}`
          })
        }
        i++ // Skip closing ```
        continue
      }

      // LaTeX math blocks ($$...$$)
      if (line.trim().startsWith('$$')) {
        const mathLines: string[] = []
        i++

        while (i < lines.length && !lines[i].trim().startsWith('$$')) {
          mathLines.push(lines[i])
          i++
        }

        if (mathLines.length > 0) {
          blocks.push({
            type: 'math',
            content: mathLines.join('\n'),
            id: `math-${blocks.length}`
          })
        }
        i++ // Skip closing $$
        continue
      }

      // Tables (|...| format)
      if (line.trim().includes('|') && line.trim().split('|').length > 2) {
        const tableLines: string[] = [line]
        i++

        while (i < lines.length && lines[i].trim().includes('|') && lines[i].trim().split('|').length > 2) {
          tableLines.push(lines[i])
          i++
        }

        blocks.push({
          type: 'table',
          content: tableLines.join('\n'),
          id: `table-${blocks.length}`
        })
        continue
      }

      // Lists (- or * or numbered)
      if (line.trim().match(/^[-*]\s+/) || line.trim().match(/^\d+\.\s+/)) {
        const listLines: string[] = [line]
        i++

        while (i < lines.length && (
          lines[i].trim().match(/^[-*]\s+/) || 
          lines[i].trim().match(/^\d+\.\s+/) ||
          lines[i].trim().match(/^\s+/)
        )) {
          listLines.push(lines[i])
          i++
        }

        blocks.push({
          type: 'list',
          content: listLines.join('\n'),
          id: `list-${blocks.length}`
        })
        continue
      }

      // Regular text
      const textLines: string[] = [line]
      i++

      while (i < lines.length && 
        !lines[i].trim().startsWith('```') &&
        !lines[i].trim().startsWith('$$') &&
        !lines[i].trim().includes('|') &&
        !lines[i].trim().match(/^[-*]\s+/) &&
        !lines[i].trim().match(/^\d+\.\s+/)
      ) {
        textLines.push(lines[i])
        i++
      }

      if (textLines.some(l => l.trim())) {
        blocks.push({
          type: 'text',
          content: textLines.join('\n'),
          id: `text-${blocks.length}`
        })
      }
    }

    return blocks
  }

  const renderCodeBlock = (content: string, language: string, id: string) => (
    <div className="relative group">
      <div className="flex items-center justify-between bg-gray-800 text-gray-200 px-4 py-2 rounded-t-lg">
        <div className="flex items-center space-x-2">
          <Code className="w-4 h-4" />
          <span className="text-sm font-medium">{language}</span>
        </div>
        <button
          onClick={() => copyToClipboard(content, id)}
          className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-gray-700 rounded"
        >
          {copiedCode === id ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
        </button>
      </div>
      <pre className="bg-gray-900 text-gray-100 p-4 rounded-b-lg overflow-x-auto">
        <code className={`language-${language}`}>{content}</code>
      </pre>
    </div>
  )

  const renderMathBlock = (content: string, id: string) => (
    <div className="relative group bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <FileText className="w-4 h-4 text-blue-600" />
          <span className="text-sm font-medium text-blue-800">LaTeX Math</span>
        </div>
        <button
          onClick={() => copyToClipboard(content, id)}
          className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-blue-200 rounded text-blue-600"
        >
          {copiedCode === id ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
        </button>
      </div>
      <div className="font-mono text-sm bg-white p-3 rounded border">
        {content}
      </div>
      <div className="text-xs text-blue-600 mt-2">
        Note: LaTeX rendering requires a math library like KaTeX or MathJax
      </div>
    </div>
  )

  const renderTable = (content: string) => {
    const lines = content.split('\n').filter(line => line.trim())
    if (lines.length < 2) return <div className="text-gray-600">Invalid table format</div>

    const headers = lines[0].split('|').map(h => h.trim()).filter(h => h)
    const rows = lines.slice(1).map(line => 
      line.split('|').map(cell => cell.trim()).filter(cell => cell)
    ).filter(row => row.length > 0 && !row.every(cell => cell.match(/^[-:]+$/)))

    return (
      <div className="overflow-x-auto">
        <table className="min-w-full border border-gray-300 rounded-lg">
          <thead className="bg-gray-50">
            <tr>
              {headers.map((header, i) => (
                <th key={i} className="px-4 py-2 text-left text-sm font-medium text-gray-900 border-b border-gray-300">
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {rows.map((row, i) => (
              <tr key={i} className={i % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                {row.map((cell, j) => (
                  <td key={j} className="px-4 py-2 text-sm text-gray-900 border-b border-gray-200">
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    )
  }

  const renderList = (content: string) => {
    const lines = content.split('\n').filter(line => line.trim())
    const isOrdered = lines[0].trim().match(/^\d+\./)

    return isOrdered ? (
      <ol className="list-decimal list-inside space-y-1 pl-4">
        {lines.map((line, i) => (
          <li key={i} className="text-gray-900">
            {line.replace(/^\d+\.\s*/, '')}
          </li>
        ))}
      </ol>
    ) : (
      <ul className="list-disc list-inside space-y-1 pl-4">
        {lines.map((line, i) => (
          <li key={i} className="text-gray-900">
            {line.replace(/^[-*]\s*/, '')}
          </li>
        ))}
      </ul>
    )
  }

  const renderText = (content: string) => {
    // Handle inline formatting
    let formattedContent = content
      // Bold text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // Italic text
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // Inline code
      .replace(/`(.*?)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">$1</code>')
      // Links
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 hover:text-blue-800 underline" target="_blank" rel="noopener noreferrer">$1</a>')

    return (
      <div 
        className="prose prose-sm max-w-none text-gray-900 leading-relaxed"
        dangerouslySetInnerHTML={{ __html: formattedContent }}
      />
    )
  }

  const blocks = parseContent(content)

  return (
    <div className={`space-y-4 ${className}`}>
      {blocks.map((block) => {
        switch (block.type) {
          case 'code':
            return (
              <div key={block.id}>
                {renderCodeBlock(block.content, block.language || 'text', block.id)}
              </div>
            )
          case 'math':
            return (
              <div key={block.id}>
                {renderMathBlock(block.content, block.id)}
              </div>
            )
          case 'table':
            return (
              <div key={block.id}>
                {renderTable(block.content)}
              </div>
            )
          case 'list':
            return (
              <div key={block.id}>
                {renderList(block.content)}
              </div>
            )
          case 'text':
          default:
            return (
              <div key={block.id}>
                {renderText(block.content)}
              </div>
            )
        }
      })}
    </div>
  )
}
