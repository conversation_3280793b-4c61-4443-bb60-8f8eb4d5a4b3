import { useState, useEffect } from 'react'
import { X, Activity, Cpu, MessageSquare, Clock, Users, Zap } from 'lucide-react'

interface Agent {
  id: string
  name: string
  status: 'idle' | 'processing' | 'completed' | 'coordinating'
  capabilities: string[]
  currentLoad: number
  totalRequests?: number
  lastActivity?: string
  x?: number
  y?: number
}

interface Connection {
  source: string
  target: string
  type: 'coordination' | 'data_flow' | 'handoff'
  strength: number
}

interface Message {
  id: string
  content: string
  timestamp: Date
  type: 'sent' | 'received'
}

interface AgentInfoPanelProps {
  agent: Agent | null
  connections: Connection[]
  allAgents: Agent[]
  onClose: () => void
  className?: string
}

export function AgentInfoPanel({ agent, connections, allAgents, onClose, className = '' }: AgentInfoPanelProps) {
  const [recentMessages, setRecentMessages] = useState<Message[]>([])
  const [performanceMetrics, setPerformanceMetrics] = useState({
    averageResponseTime: 0,
    successRate: 100,
    throughput: 0
  })

  useEffect(() => {
    if (agent) {
      // Generate mock recent messages
      const mockMessages: Message[] = [
        {
          id: '1',
          content: 'Processed code analysis request',
          timestamp: new Date(Date.now() - 5 * 60 * 1000),
          type: 'sent'
        },
        {
          id: '2',
          content: 'Received coordination signal from General Agent',
          timestamp: new Date(Date.now() - 10 * 60 * 1000),
          type: 'received'
        },
        {
          id: '3',
          content: 'Completed debugging task',
          timestamp: new Date(Date.now() - 15 * 60 * 1000),
          type: 'sent'
        }
      ]
      setRecentMessages(mockMessages)

      // Generate mock performance metrics
      setPerformanceMetrics({
        averageResponseTime: Math.random() * 2000 + 500,
        successRate: 95 + Math.random() * 5,
        throughput: Math.random() * 10 + 5
      })
    }
  }, [agent])

  if (!agent) return null

  const connectedAgents = connections
    .filter(conn => conn.source === agent.id || conn.target === agent.id)
    .map(conn => {
      const connectedId = conn.source === agent.id ? conn.target : conn.source
      return {
        agent: allAgents.find(a => a.id === connectedId),
        connection: conn
      }
    })
    .filter(item => item.agent)

  const getStatusColor = (status: Agent['status']) => {
    switch (status) {
      case 'processing': return 'text-blue-600 bg-blue-100'
      case 'completed': return 'text-green-600 bg-green-100'
      case 'coordinating': return 'text-yellow-600 bg-yellow-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getConnectionTypeIcon = (type: Connection['type']) => {
    switch (type) {
      case 'coordination': return <Users className="h-4 w-4" />
      case 'data_flow': return <Activity className="h-4 w-4" />
      case 'handoff': return <Zap className="h-4 w-4" />
      default: return <MessageSquare className="h-4 w-4" />
    }
  }

  return (
    <div className={`fixed inset-y-0 right-0 w-96 bg-white shadow-xl border-l border-gray-200 z-50 transform transition-transform duration-300 ease-in-out ${className}`}>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">{agent.name}</h2>
            <p className="text-sm text-gray-600">Agent ID: {agent.id}</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* Status and Load */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Status</span>
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(agent.status)}`}>
                {agent.status.charAt(0).toUpperCase() + agent.status.slice(1)}
              </span>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Current Load</span>
                <span className="text-sm text-gray-900">{agent.currentLoad.toFixed(1)}/5.0</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    agent.currentLoad < 2 ? 'bg-green-500' :
                    agent.currentLoad < 4 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${(agent.currentLoad / 5) * 100}%` }}
                />
              </div>
            </div>
          </div>

          {/* Capabilities */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-gray-700">Capabilities</h3>
            <div className="flex flex-wrap gap-2">
              {agent.capabilities.map((capability, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full"
                >
                  {capability}
                </span>
              ))}
            </div>
          </div>

          {/* Performance Metrics */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-gray-700">Performance Metrics</h3>
            <div className="grid grid-cols-1 gap-3">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-700">Avg Response Time</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {performanceMetrics.averageResponseTime.toFixed(0)}ms
                </span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Activity className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-700">Success Rate</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {performanceMetrics.successRate.toFixed(1)}%
                </span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Cpu className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-700">Throughput</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {performanceMetrics.throughput.toFixed(1)} req/min
                </span>
              </div>
            </div>
          </div>

          {/* Connected Agents */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-gray-700">Connected Agents ({connectedAgents.length})</h3>
            <div className="space-y-2">
              {connectedAgents.map(({ agent: connectedAgent, connection }, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getConnectionTypeIcon(connection.type)}
                    <div>
                      <p className="text-sm font-medium text-gray-900">{connectedAgent?.name}</p>
                      <p className="text-xs text-gray-500 capitalize">{connection.type.replace('_', ' ')}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-gray-500">Strength</p>
                    <p className="text-sm font-medium text-gray-900">{(connection.strength * 100).toFixed(0)}%</p>
                  </div>
                </div>
              ))}
              {connectedAgents.length === 0 && (
                <p className="text-sm text-gray-500 text-center py-4">No active connections</p>
              )}
            </div>
          </div>

          {/* Recent Activity */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-gray-700">Recent Activity</h3>
            <div className="space-y-2">
              {recentMessages.map((message) => (
                <div key={message.id} className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-1">
                    <span className={`text-xs font-medium ${
                      message.type === 'sent' ? 'text-blue-600' : 'text-green-600'
                    }`}>
                      {message.type === 'sent' ? 'Sent' : 'Received'}
                    </span>
                    <span className="text-xs text-gray-500">
                      {message.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                  <p className="text-sm text-gray-700">{message.content}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
