import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from './ui/card';
import { But<PERSON> } from './ui/button';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';

interface StressTestScenario {
  id: string;
  title: string;
  content: string;
  complexity: 'high' | 'extreme';
  expectedAgent: string;
}

interface ActiveRequest {
  id: string;
  scenario: StressTestScenario;
  startTime: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  agentId?: string;
  duration?: number;
}

const STRESS_TEST_SCENARIOS: StressTestScenario[] = [
  {
    id: 'quantum-ml-system',
    title: 'Quantum ML Optimization',
    content: 'Design and implement a quantum-inspired machine learning optimization system that combines quantum computing principles with neural network training. Include mathematical derivations, complete TypeScript implementation, quantum state management, and integration with existing ML frameworks.',
    complexity: 'extreme',
    expectedAgent: 'code-generation'
  },
  {
    id: 'distributed-consensus',
    title: 'Byzantine Fault Tolerance',
    content: 'Explain the theoretical foundations of Byzantine fault tolerance in distributed systems. Cover mathematical proofs, game-theoretic analysis, information-theoretic bounds, and implications for blockchain systems.',
    complexity: 'extreme',
    expectedAgent: 'general'
  },
  {
    id: 'protein-folding-ai',
    title: 'AI Protein Folding Prediction',
    content: 'Develop a comprehensive protein folding prediction system combining AI and molecular dynamics. Implement AlphaFold-style mechanisms, molecular simulation, energy minimization, and 3D visualization in TypeScript.',
    complexity: 'extreme',
    expectedAgent: 'code-generation'
  },
  {
    id: 'quantum-cryptography',
    title: 'Quantum Cryptography Analysis',
    content: 'Analyze the security implications of quantum computing on modern cryptography. Cover Shor\'s algorithm impact, post-quantum alternatives, quantum key distribution, and migration strategies.',
    complexity: 'extreme',
    expectedAgent: 'specialized'
  },
  {
    id: 'advanced-algorithms',
    title: 'Advanced ML Algorithms',
    content: 'Create advanced machine learning optimization algorithms with mathematical foundations. Derive novel optimization methods, implement adaptive schedules, design distributed optimization, and analyze convergence properties.',
    complexity: 'extreme',
    expectedAgent: 'code-generation'
  },
  {
    id: 'complexity-theory',
    title: 'Complexity Theory & ML',
    content: 'Explore the relationship between computational complexity theory and machine learning. Cover P vs NP implications, sample complexity bounds, approximation algorithms, and quantum complexity classes.',
    complexity: 'extreme',
    expectedAgent: 'specialized'
  },
  {
    id: 'concurrent-data-structures',
    title: 'Advanced Data Structures',
    content: 'Design and implement advanced data structures for high-performance computing. Create lock-free concurrent structures, cache-oblivious algorithms, persistent data structures, and probabilistic structures in TypeScript.',
    complexity: 'extreme',
    expectedAgent: 'code-generation'
  },
  {
    id: 'emergence-systems',
    title: 'Systems Theory & Emergence',
    content: 'Analyze emergence and complexity in multi-agent systems. Cover mathematical models of emergent behavior, phase transitions, information theory, network effects, and applications to AI coordination.',
    complexity: 'extreme',
    expectedAgent: 'general'
  }
];

export const StressTestPanel: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [activeRequests, setActiveRequests] = useState<Map<string, ActiveRequest>>(new Map());
  const [completedRequests, setCompletedRequests] = useState<ActiveRequest[]>([]);
  const [testProgress, setTestProgress] = useState(0);
  const [agentStatus, setAgentStatus] = useState<any[]>([]);
  const [testConfig, setTestConfig] = useState({
    concurrentRequests: 6,
    totalRequests: 20,
    requestDelay: 500
  });

  const testStartTime = useRef<number>(0);
  const monitoringInterval = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Monitor agent status every 2 seconds
    const interval = setInterval(fetchAgentStatus, 2000);
    return () => clearInterval(interval);
  }, []);

  const fetchAgentStatus = async () => {
    try {
      const response = await fetch('/api/agents');
      const agents = await response.json();
      setAgentStatus(agents);
    } catch (error) {
      console.error('Failed to fetch agent status:', error);
    }
  };

  const startStressTest = async () => {
    setIsRunning(true);
    setActiveRequests(new Map());
    setCompletedRequests([]);
    setTestProgress(0);
    testStartTime.current = Date.now();

    console.log('🚀 Starting UI-based stress test...');
    console.log(`📊 Config: ${testConfig.concurrentRequests} concurrent, ${testConfig.totalRequests} total`);

    // Start monitoring
    monitoringInterval.current = setInterval(() => {
      const elapsed = Date.now() - testStartTime.current;
      console.log(`⏱️  Elapsed: ${Math.round(elapsed / 1000)}s | Active: ${activeRequests.size} | Completed: ${completedRequests.length}`);
    }, 3000);

    try {
      await runConcurrentRequests();
    } catch (error) {
      console.error('Stress test failed:', error);
    } finally {
      setIsRunning(false);
      if (monitoringInterval.current) {
        clearInterval(monitoringInterval.current);
      }
    }
  };

  const runConcurrentRequests = async () => {
    const totalBatches = Math.ceil(testConfig.totalRequests / testConfig.concurrentRequests);
    let requestCounter = 0;

    for (let batch = 0; batch < totalBatches; batch++) {
      const batchSize = Math.min(testConfig.concurrentRequests, testConfig.totalRequests - requestCounter);
      console.log(`🌊 Batch ${batch + 1}/${totalBatches}: Launching ${batchSize} concurrent requests`);

      // Launch concurrent requests
      const promises = [];
      for (let i = 0; i < batchSize; i++) {
        const scenario = STRESS_TEST_SCENARIOS[requestCounter % STRESS_TEST_SCENARIOS.length];
        const requestId = `req-${requestCounter++}`;
        promises.push(sendRequest(scenario, requestId));
      }

      // Wait for batch completion
      await Promise.allSettled(promises);
      
      // Update progress
      setTestProgress((requestCounter / testConfig.totalRequests) * 100);

      // Delay between batches
      if (batch < totalBatches - 1) {
        await new Promise(resolve => setTimeout(resolve, testConfig.requestDelay));
      }
    }

    console.log('✅ All stress test requests completed!');
  };

  const sendRequest = async (scenario: StressTestScenario, requestId: string) => {
    const startTime = Date.now();
    
    const newRequest: ActiveRequest = {
      id: requestId,
      scenario,
      startTime,
      status: 'pending'
    };

    // Add to active requests
    setActiveRequests(prev => new Map(prev.set(requestId, newRequest)));

    try {
      console.log(`🎯 ${requestId}: ${scenario.title}`);
      
      // Update status to processing
      setActiveRequests(prev => {
        const updated = new Map(prev);
        const req = updated.get(requestId);
        if (req) {
          req.status = 'processing';
          updated.set(requestId, req);
        }
        return updated;
      });

      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content: scenario.content })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      const duration = Date.now() - startTime;

      const completedRequest: ActiveRequest = {
        ...newRequest,
        status: 'completed',
        agentId: result.agentId,
        duration
      };

      // Move to completed
      setActiveRequests(prev => {
        const updated = new Map(prev);
        updated.delete(requestId);
        return updated;
      });

      setCompletedRequests(prev => [...prev, completedRequest]);

      console.log(`✅ ${requestId}: Completed in ${duration}ms by ${result.agentId}`);

    } catch (error) {
      const duration = Date.now() - startTime;
      
      const failedRequest: ActiveRequest = {
        ...newRequest,
        status: 'failed',
        duration
      };

      // Move to completed (as failed)
      setActiveRequests(prev => {
        const updated = new Map(prev);
        updated.delete(requestId);
        return updated;
      });

      setCompletedRequests(prev => [...prev, failedRequest]);

      console.error(`❌ ${requestId}: Failed after ${duration}ms - ${error}`);
    }
  };

  const stopStressTest = () => {
    setIsRunning(false);
    if (monitoringInterval.current) {
      clearInterval(monitoringInterval.current);
    }
  };

  const getAgentStatusColor = (status: string, load: number) => {
    if (status !== 'active') return 'bg-gray-500';
    if (load > 4.0) return 'bg-red-500';
    if (load > 2.0) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const successfulRequests = completedRequests.filter(r => r.status === 'completed');
  const failedRequests = completedRequests.filter(r => r.status === 'failed');
  const avgResponseTime = successfulRequests.length > 0 
    ? successfulRequests.reduce((sum, r) => sum + (r.duration || 0), 0) / successfulRequests.length 
    : 0;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🧪 Multi-Agent Stress Test Panel
            {isRunning && <Badge variant="secondary" className="animate-pulse">Running</Badge>}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Test Configuration */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium">Concurrent Requests</label>
              <input
                type="number"
                value={testConfig.concurrentRequests}
                onChange={(e) => setTestConfig(prev => ({ ...prev, concurrentRequests: parseInt(e.target.value) }))}
                className="w-full p-2 border rounded"
                min="1"
                max="10"
                disabled={isRunning}
              />
            </div>
            <div>
              <label className="text-sm font-medium">Total Requests</label>
              <input
                type="number"
                value={testConfig.totalRequests}
                onChange={(e) => setTestConfig(prev => ({ ...prev, totalRequests: parseInt(e.target.value) }))}
                className="w-full p-2 border rounded"
                min="5"
                max="50"
                disabled={isRunning}
              />
            </div>
            <div>
              <label className="text-sm font-medium">Batch Delay (ms)</label>
              <input
                type="number"
                value={testConfig.requestDelay}
                onChange={(e) => setTestConfig(prev => ({ ...prev, requestDelay: parseInt(e.target.value) }))}
                className="w-full p-2 border rounded"
                min="100"
                max="2000"
                disabled={isRunning}
              />
            </div>
          </div>

          {/* Control Buttons */}
          <div className="flex gap-2">
            <Button 
              onClick={startStressTest} 
              disabled={isRunning}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isRunning ? '🔄 Running...' : '🚀 Start Stress Test'}
            </Button>
            <Button 
              onClick={stopStressTest} 
              disabled={!isRunning}
              variant="destructive"
            >
              ⏹️ Stop Test
            </Button>
          </div>

          {/* Progress */}
          {isRunning && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span>{Math.round(testProgress)}%</span>
              </div>
              <Progress value={testProgress} className="w-full" />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Real-time Agent Status */}
      <Card>
        <CardHeader>
          <CardTitle>🤖 Real-time Agent Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {agentStatus.map(agent => (
              <div key={agent.id} className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <div className={`w-3 h-3 rounded-full ${getAgentStatusColor(agent.status, agent.currentLoad)}`} />
                  <span className="font-medium">{agent.name}</span>
                </div>
                <div className="text-sm space-y-1">
                  <div>Load: <span className="font-mono">{agent.currentLoad.toFixed(2)}</span></div>
                  <div>Requests: <span className="font-mono">{agent.totalRequests}</span></div>
                  <div>Status: <Badge variant="outline">{agent.status}</Badge></div>
                </div>
              </div>
            ))}
          </div>
          
          {agentStatus.length > 3 && (
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2">
                <span className="text-green-600">🆕</span>
                <span className="font-medium text-green-800">
                  Agent Scaling Detected: {agentStatus.length} agents active (scaled from 3)
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>📊 Test Results</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{activeRequests.size}</div>
              <div className="text-sm text-gray-600">Active</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{successfulRequests.length}</div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{failedRequests.length}</div>
              <div className="text-sm text-gray-600">Failed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{Math.round(avgResponseTime)}</div>
              <div className="text-sm text-gray-600">Avg Time (ms)</div>
            </div>
          </div>

          {/* Recent Requests */}
          {completedRequests.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium">Recent Requests</h4>
              <div className="max-h-40 overflow-y-auto space-y-1">
                {completedRequests.slice(-10).reverse().map(request => (
                  <div key={request.id} className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm">
                    <span>{request.scenario.title}</span>
                    <div className="flex items-center gap-2">
                      {request.agentId && <Badge variant="outline">{request.agentId}</Badge>}
                      <span className="font-mono">{request.duration}ms</span>
                      <span className={request.status === 'completed' ? 'text-green-600' : 'text-red-600'}>
                        {request.status === 'completed' ? '✅' : '❌'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
