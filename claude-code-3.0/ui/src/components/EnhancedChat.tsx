import { useState, useEffect, useRef } from 'react'

interface Message {
  id: string
  content: string
  sender: 'user' | 'agent'
  timestamp: Date
  agentId?: string
  agentName?: string
  processingTime?: number
  isStreaming?: boolean
}

interface AgentActivity {
  agentId: string
  agentName: string
  status: 'idle' | 'processing' | 'completed'
  content?: string
  processingTime?: number
}

export function EnhancedChat() {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [agentActivities, setAgentActivities] = useState<AgentActivity[]>([])
  const [currentStreamingMessage, setCurrentStreamingMessage] = useState<Message | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const eventSourceRef = useRef<EventSource | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)
  const mountedRef = useRef(true)

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false
      if (eventSourceRef.current) {
        eventSourceRef.current.close()
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages, currentStreamingMessage])

  const [markedLoaded, setMarkedLoaded] = useState(false)
  const [syntaxHighlighterLoaded, setSyntaxHighlighterLoaded] = useState(false)
  const [marked, setMarked] = useState<any>(null)
  const [SyntaxHighlighter, setSyntaxHighlighter] = useState<any>(null)
  const [vscDarkPlus, setVscDarkPlus] = useState<any>(null)

  // Load dependencies dynamically with better error handling
  useEffect(() => {
    if (!mountedRef.current) return

    const loadDependencies = async () => {
      // Load marked
      try {
        const markedModule = await import('marked')
        if (mountedRef.current) {
          setMarked(markedModule.marked)
          setMarkedLoaded(true)

          markedModule.marked.setOptions({
            breaks: true,
            gfm: true
          })
        }
      } catch (error) {
        console.error('Error loading marked:', error)
        if (mountedRef.current) {
          setMarkedLoaded(false)
        }
      }

      // Load syntax highlighter
      try {
        const [syntaxModule, styleModule] = await Promise.all([
          import('react-syntax-highlighter'),
          import('react-syntax-highlighter/dist/esm/styles/prism')
        ])

        if (mountedRef.current) {
          setSyntaxHighlighter(syntaxModule.Prism)
          setVscDarkPlus(styleModule.vscDarkPlus)
          setSyntaxHighlighterLoaded(true)
        }
      } catch (error) {
        console.error('Error loading syntax highlighter:', error)
        if (mountedRef.current) {
          setSyntaxHighlighterLoaded(false)
        }
      }
    }

    // Add a small delay to ensure component is fully mounted
    const timeoutId = setTimeout(() => {
      if (mountedRef.current) {
        loadDependencies()
      }
    }, 100)

    return () => {
      clearTimeout(timeoutId)
    }
  }, [])

  const renderCodeBlock = (code: string, language: string) => {
    if (syntaxHighlighterLoaded && SyntaxHighlighter && vscDarkPlus) {
      return (
        <div className="my-4">
          <div className="bg-gray-800 text-gray-200 px-3 py-2 text-sm font-mono rounded-t-md flex justify-between items-center">
            <span>{language || 'code'}</span>
            <button
              onClick={() => navigator.clipboard.writeText(code)}
              className="text-xs bg-gray-700 hover:bg-gray-600 px-2 py-1 rounded"
            >
              Copy
            </button>
          </div>
          <SyntaxHighlighter
            language={language || 'text'}
            style={vscDarkPlus}
            customStyle={{
              margin: 0,
              borderRadius: '0 0 6px 6px',
              fontSize: '0.875rem'
            }}
          >
            {code}
          </SyntaxHighlighter>
        </div>
      )
    } else {
      // Fallback to simple code block
      return (
        <div className="my-4">
          <div className="bg-gray-800 text-gray-200 px-3 py-2 text-sm font-mono rounded-t-md flex justify-between items-center">
            <span>{language || 'code'}</span>
            <button
              onClick={() => navigator.clipboard.writeText(code)}
              className="text-xs bg-gray-700 hover:bg-gray-600 px-2 py-1 rounded"
            >
              Copy
            </button>
          </div>
          <pre className="bg-gray-900 text-gray-100 p-4 rounded-b-md overflow-x-auto">
            <code>{code}</code>
          </pre>
        </div>
      )
    }
  }

  const renderMessageContent = (content: string) => {
    try {
      if (markedLoaded && marked) {
        // Parse markdown and render with syntax highlighting
        const tokens = marked.lexer(content)

        return tokens.map((token: any, index: number) => {
          if (token.type === 'code') {
            return renderCodeBlock(token.text, token.lang)
          } else if (token.type === 'paragraph') {
            return (
              <p key={index} className="mb-3 leading-relaxed">
                {token.text}
              </p>
            )
          } else if (token.type === 'heading') {
            const HeadingTag = `h${token.depth}` as keyof JSX.IntrinsicElements
            return (
              <HeadingTag key={index} className={`font-bold mb-2 mt-4 ${
                token.depth === 1 ? 'text-xl' :
                token.depth === 2 ? 'text-lg' : 'text-base'
              }`}>
                {token.text}
              </HeadingTag>
            )
          } else if (token.type === 'list') {
            const ListTag = token.ordered ? 'ol' : 'ul'
            return (
              <ListTag key={index} className={`mb-3 ${token.ordered ? 'list-decimal' : 'list-disc'} list-inside space-y-1`}>
                {token.items.map((item: any, itemIndex: number) => (
                  <li key={itemIndex} className="ml-4">{item.text}</li>
                ))}
              </ListTag>
            )
          } else {
            // Fallback for other token types
            try {
              const html = marked.parser([token])
              return <div key={index} dangerouslySetInnerHTML={{ __html: html }} />
            } catch (error) {
              console.error('Error parsing token:', error)
              return <div key={index} className="text-gray-500">Error rendering content</div>
            }
          }
        })
      } else {
        // Simple rendering with code block detection
        const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g
        const parts = []
        let lastIndex = 0
        let match

        while ((match = codeBlockRegex.exec(content)) !== null) {
          // Add text before code block
          if (match.index > lastIndex) {
            const textContent = content.slice(lastIndex, match.index)
            if (textContent.trim()) {
              parts.push(
                <div key={`text-${lastIndex}`} className="mb-3">
                  {textContent.split('\n').map((line, i) => (
                    <p key={i} className="mb-2 leading-relaxed">{line}</p>
                  ))}
                </div>
              )
            }
          }

          // Add code block
          const language = match[1] || 'text'
          const code = match[2]
          parts.push(renderCodeBlock(code, language))

          lastIndex = match.index + match[0].length
        }

        // Add remaining text
        if (lastIndex < content.length) {
          const remainingContent = content.slice(lastIndex)
          if (remainingContent.trim()) {
            parts.push(
              <div key={`text-${lastIndex}`} className="mb-3">
                {remainingContent.split('\n').map((line, i) => (
                  <p key={i} className="mb-2 leading-relaxed">{line}</p>
                ))}
              </div>
            )
          }
        }

        return parts.length > 0 ? parts : (
          <div className="mb-3">
            {content.split('\n').map((line, i) => (
              <p key={i} className="mb-2 leading-relaxed">{line}</p>
            ))}
          </div>
        )
      }
    } catch (error) {
      console.error('Error rendering message content:', error)
      // Ultimate fallback to simple text rendering
      return (
        <div className="prose prose-sm max-w-none">
          {content.split('\n').map((line, i) => (
            <p key={i} className="mb-2 leading-relaxed">{line}</p>
          ))}
        </div>
      )
    }
  }

  const sendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      content: inputMessage,
      sender: 'user',
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    const messageContent = inputMessage
    setInputMessage('')
    setIsLoading(true)

    // Create streaming message placeholder
    const streamingMessage: Message = {
      id: `agent-${Date.now()}`,
      content: '',
      sender: 'agent',
      timestamp: new Date(),
      isStreaming: true
    }
    setCurrentStreamingMessage(streamingMessage)

    try {
      // Close any existing connections
      if (eventSourceRef.current) {
        eventSourceRef.current.close()
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create new abort controller
      abortControllerRef.current = new AbortController()

      // Start streaming request
      const response = await fetch('http://localhost:8080/api/messages/stream', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: messageContent,
          sessionId: 'enhanced-chat-session',
          capabilities: ['general']
        }),
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split('\n').filter(line => line.trim())

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6))

                // Check if component is still mounted before updating state
                if (!mountedRef.current) break

                if (data.type === 'agent_selected') {
                  setAgentActivities(prev => [
                    ...prev.filter(a => a.agentId !== data.agentId),
                    {
                      agentId: data.agentId,
                      agentName: data.agentName,
                      status: 'processing'
                    }
                  ])
                  setCurrentStreamingMessage(prev => prev ? {
                    ...prev,
                    agentId: data.agentId,
                    agentName: data.agentName
                  } : null)
                } else if (data.type === 'content_chunk') {
                  setCurrentStreamingMessage(prev => prev ? {
                    ...prev,
                    content: prev.content + data.chunk
                  } : null)
                } else if (data.type === 'message_complete') {
                  const finalMessage: Message = {
                    id: data.messageId,
                    content: data.content,
                    sender: 'agent',
                    timestamp: new Date(data.timestamp),
                    agentId: data.agentId,
                    processingTime: data.processingTime,
                    isStreaming: false
                  }
                  
                  if (mountedRef.current) {
                    setMessages(prev => [...prev, finalMessage])
                    setCurrentStreamingMessage(null)

                    setAgentActivities(prev =>
                      prev.map(a => a.agentId === data.agentId ?
                        { ...a, status: 'completed', processingTime: data.processingTime } : a
                      )
                    )
                  }
                }
              } catch (e) {
                console.error('Error parsing SSE data:', e)
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Streaming failed:', error)

      if (mountedRef.current) {
        // Only show error if it's not an abort error
        if (error instanceof Error && error.name !== 'AbortError') {
          const errorMessage: Message = {
            id: `error-${Date.now()}`,
            content: `Error: ${error.message}`,
            sender: 'agent',
            timestamp: new Date()
          }
          setMessages(prev => [...prev, errorMessage])
        }
        setCurrentStreamingMessage(null)
      }
    } finally {
      if (mountedRef.current) {
        setIsLoading(false)
      }
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  return (
    <div className="max-w-6xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Agent Activity Panel */}
        <div className="lg:col-span-1">
          <h3 className="text-lg font-semibold mb-4">Agent Activity</h3>
          <div className="space-y-3">
            {agentActivities.map((activity) => (
              <div
                key={activity.agentId}
                className={`p-3 rounded-lg border ${
                  activity.status === 'processing' ? 'border-blue-500 bg-blue-50' :
                  activity.status === 'completed' ? 'border-green-500 bg-green-50' :
                  'border-gray-300 bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="font-medium text-sm">{activity.agentName}</span>
                  <div className={`w-2 h-2 rounded-full ${
                    activity.status === 'processing' ? 'bg-blue-500 animate-pulse' :
                    activity.status === 'completed' ? 'bg-green-500' :
                    'bg-gray-400'
                  }`} />
                </div>
                <div className="text-xs text-gray-600 mt-1">
                  {activity.status === 'processing' && 'Processing...'}
                  {activity.status === 'completed' && activity.processingTime && 
                    `Completed in ${activity.processingTime}s`}
                  {activity.status === 'idle' && 'Idle'}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Chat Panel */}
        <div className="lg:col-span-3">
          <h2 className="text-xl font-bold mb-4">Enhanced Multi-Agent Chat</h2>
          
          {/* Messages Display */}
          <div className="h-96 overflow-y-auto border border-gray-300 rounded-lg p-4 mb-4 bg-gray-50">
            {messages.length === 0 && !currentStreamingMessage ? (
              <p className="text-gray-500 text-center">Start a conversation with the AI agents!</p>
            ) : (
              <>
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`mb-4 p-4 rounded-lg ${
                      message.sender === 'user' 
                        ? 'bg-blue-100 ml-8' 
                        : 'bg-white mr-8 border border-gray-200'
                    }`}
                  >
                    <div className="flex justify-between items-center mb-2">
                      <div className="text-sm font-medium flex items-center">
                        {message.sender === 'user' ? (
                          <span className="text-blue-700">You</span>
                        ) : (
                          <span className="text-gray-700">
                            {message.agentName || 'AI Agent'}
                            {message.agentId && (
                              <span className="text-xs text-gray-500 ml-1">({message.agentId})</span>
                            )}
                          </span>
                        )}
                      </div>
                      <div className="text-xs text-gray-500">
                        {message.timestamp.toLocaleTimeString()}
                        {message.processingTime && (
                          <span className="ml-2 text-green-600">
                            {message.processingTime}s
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="prose prose-sm max-w-none">
                      {message.sender === 'user' ? (
                        <p className="whitespace-pre-wrap">{message.content}</p>
                      ) : (
                        renderMessageContent(message.content)
                      )}
                    </div>
                  </div>
                ))}
                
                {/* Streaming Message */}
                {currentStreamingMessage && (
                  <div className="mb-4 p-4 rounded-lg bg-white mr-8 border border-gray-200">
                    <div className="flex justify-between items-center mb-2">
                      <div className="text-sm font-medium flex items-center">
                        <span className="text-gray-700">
                          {currentStreamingMessage.agentName || 'AI Agent'}
                          {currentStreamingMessage.agentId && (
                            <span className="text-xs text-gray-500 ml-1">
                              ({currentStreamingMessage.agentId})
                            </span>
                          )}
                        </span>
                        <div className="ml-2 w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                      </div>
                      <div className="text-xs text-gray-500">
                        {currentStreamingMessage.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                    <div className="prose prose-sm max-w-none">
                      {renderMessageContent(currentStreamingMessage.content)}
                      <span className="inline-block w-2 h-4 bg-gray-400 animate-pulse ml-1" />
                    </div>
                  </div>
                )}
              </>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input Form */}
          <div className="flex space-x-2">
            <input
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask the AI agents anything..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isLoading}
            />
            <button
              onClick={sendMessage}
              disabled={!inputMessage.trim() || isLoading}
              className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? 'Processing...' : 'Send'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
