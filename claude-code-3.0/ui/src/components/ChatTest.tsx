import { useState } from 'react'

interface Message {
  id: string
  content: string
  sender: 'user' | 'agent'
  timestamp: Date
}

export function ChatTest() {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const sendMessage = async () => {
    console.log('ChatTest: sendMessage called with:', inputMessage)
    
    if (!inputMessage.trim() || isLoading) {
      console.log('ChatTest: Message empty or loading, returning')
      return
    }

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      content: inputMessage,
      sender: 'user',
      timestamp: new Date()
    }

    console.log('ChatTest: Adding user message:', userMessage)
    setMessages(prev => {
      const newMessages = [...prev, userMessage]
      console.log('ChatTest: New messages array:', newMessages)
      return newMessages
    })
    
    const messageContent = inputMessage
    setInputMessage('')
    setIsLoading(true)

    try {
      console.log('ChatTest: Making API request...')
      const response = await fetch('http://localhost:8080/api/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: messageContent,
          sessionId: 'chat-test-session',
          capabilities: ['general']
        })
      })

      console.log('ChatTest: Response status:', response.status)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      console.log('ChatTest: Response data:', data)

      const agentMessage: Message = {
        id: data.id,
        content: data.content,
        sender: 'agent',
        timestamp: new Date(data.timestamp)
      }

      console.log('ChatTest: Adding agent message:', agentMessage)
      setMessages(prev => {
        const newMessages = [...prev, agentMessage]
        console.log('ChatTest: Updated messages array:', newMessages)
        return newMessages
      })
    } catch (error) {
      console.error('ChatTest: Failed to send message:', error)
      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        sender: 'agent',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      console.log('ChatTest: Enter key pressed')
      sendMessage()
    }
  }

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-xl font-bold mb-4">Chat Test Component</h2>
      
      {/* Messages Display */}
      <div className="h-64 overflow-y-auto border border-gray-300 rounded-lg p-4 mb-4 bg-gray-50">
        {messages.length === 0 ? (
          <p className="text-gray-500 text-center">No messages yet. Send a test message!</p>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`mb-2 p-2 rounded ${
                message.sender === 'user' 
                  ? 'bg-blue-100 ml-8 text-right' 
                  : 'bg-gray-100 mr-8'
              }`}
            >
              <div className="text-sm font-medium">
                {message.sender === 'user' ? 'You' : 'Agent'}
              </div>
              <div className="text-sm">{message.content}</div>
              <div className="text-xs text-gray-500">
                {message.timestamp.toLocaleTimeString()}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Input Form */}
      <div className="flex space-x-2">
        <input
          type="text"
          value={inputMessage}
          onChange={(e) => {
            console.log('ChatTest: Input changed to:', e.target.value)
            setInputMessage(e.target.value)
          }}
          onKeyPress={handleKeyPress}
          placeholder="Type a test message..."
          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          disabled={isLoading}
        />
        <button
          onClick={() => {
            console.log('ChatTest: Send button clicked')
            sendMessage()
          }}
          disabled={!inputMessage.trim() || isLoading}
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Sending...' : 'Send'}
        </button>
      </div>

      {/* Debug Info */}
      <div className="mt-4 p-2 bg-gray-100 rounded text-xs">
        <strong>Debug Info:</strong><br/>
        Messages count: {messages.length}<br/>
        Input value: "{inputMessage}"<br/>
        Is loading: {isLoading.toString()}<br/>
        Button disabled: {(!inputMessage.trim() || isLoading).toString()}
      </div>
    </div>
  )
}
