import { useEffect, useRef, useState } from 'react'

interface Agent {
  id: string
  name: string
  status: 'idle' | 'processing' | 'completed' | 'coordinating'
  capabilities: string[]
  currentLoad: number
  x?: number
  y?: number
}

interface Connection {
  source: string
  target: string
  type: 'coordination' | 'data_flow' | 'handoff'
  strength: number
}

interface AgentCoordinationGraphProps {
  agents: Agent[]
  connections: Connection[]
  className?: string
}

export function AgentCoordinationGraph({ agents, connections, className = '' }: AgentCoordinationGraphProps) {
  const svgRef = useRef<SVGSVGElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [dimensions, setDimensions] = useState({ width: 600, height: 320 })
  const [d3Loaded, setD3Loaded] = useState(false)
  const [d3, setD3] = useState<any>(null)

  // Container padding to keep all elements within bounds
  const PADDING = 20
  const NODE_RADIUS = 25

  // Load D3 dynamically
  useEffect(() => {
    const loadD3 = async () => {
      try {
        const d3Module = await import('d3')
        setD3(d3Module)
        setD3Loaded(true)
      } catch (error) {
        console.error('Error loading D3:', error)
      }
    }
    loadD3()
  }, [])

  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect()
        // Account for padding and ensure minimum dimensions
        const width = Math.max(300, rect.width - (PADDING * 2))
        const height = Math.max(200, rect.height - (PADDING * 2))
        setDimensions({ width, height })
      }
    }

    // Use a timeout to ensure the container is properly rendered
    const timeoutId = setTimeout(handleResize, 100)
    window.addEventListener('resize', handleResize)

    return () => {
      clearTimeout(timeoutId)
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  useEffect(() => {
    if (!svgRef.current || agents.length === 0 || !d3Loaded || !d3) return

    const svg = d3.select(svgRef.current)
    svg.selectAll('*').remove()

    const { width, height } = dimensions

    // Calculate effective area within padding
    const effectiveWidth = width - (PADDING * 2)
    const effectiveHeight = height - (PADDING * 2)

    // Set up SVG viewBox to contain everything
    svg.attr('viewBox', `0 0 ${width} ${height}`)
       .attr('preserveAspectRatio', 'xMidYMid meet')

    // Create force simulation with boundary constraints
    const simulation = d3.forceSimulation(agents)
      .force('link', d3.forceLink(connections)
        .id((d: any) => d.id)
        .distance(Math.min(80, effectiveWidth / 4))
        .strength(0.5)
      )
      .force('charge', d3.forceManyBody().strength(-200))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius(NODE_RADIUS + 5))
      // Add boundary forces to keep nodes within container
      .force('boundary', () => {
        agents.forEach((node: any) => {
          // Keep nodes within the padded area
          node.x = Math.max(PADDING + NODE_RADIUS, Math.min(width - PADDING - NODE_RADIUS, node.x || width / 2))
          node.y = Math.max(PADDING + NODE_RADIUS, Math.min(height - PADDING - NODE_RADIUS, node.y || height / 2))
        })
      })

    // Create container groups
    const container = svg.append('g')
    const linksGroup = container.append('g').attr('class', 'links')
    const nodesGroup = container.append('g').attr('class', 'nodes')

    // Create links
    const links = linksGroup.selectAll('line')
      .data(connections)
      .enter()
      .append('line')
      .attr('stroke', (d) => {
        switch (d.type) {
          case 'coordination': return '#3b82f6'
          case 'data_flow': return '#10b981'
          case 'handoff': return '#f59e0b'
          default: return '#6b7280'
        }
      })
      .attr('stroke-width', (d) => Math.max(1, d.strength * 3))
      .attr('stroke-opacity', 0.6)
      .attr('stroke-dasharray', (d) => d.type === 'coordination' ? '5,5' : 'none')

    // Create nodes
    const nodes = nodesGroup.selectAll('g')
      .data(agents)
      .enter()
      .append('g')
      .attr('class', 'node')
      .call(d3.drag<SVGGElement, Agent>()
        .on('start', (event, d) => {
          if (!event.active) simulation.alphaTarget(0.3).restart()
          d.fx = d.x
          d.fy = d.y
        })
        .on('drag', (event, d) => {
          d.fx = event.x
          d.fy = event.y
        })
        .on('end', (event, d) => {
          if (!event.active) simulation.alphaTarget(0)
          d.fx = null
          d.fy = null
        })
      )

    // Add circles for nodes (constrained size)
    nodes.append('circle')
      .attr('r', NODE_RADIUS)
      .attr('fill', (d) => {
        switch (d.status) {
          case 'processing': return '#3b82f6'
          case 'completed': return '#10b981'
          case 'coordinating': return '#f59e0b'
          default: return '#6b7280'
        }
      })
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)
      .attr('opacity', 0.8)

    // Add status indicator rings (constrained size)
    nodes.append('circle')
      .attr('r', NODE_RADIUS + 3)
      .attr('fill', 'none')
      .attr('stroke', (d) => {
        switch (d.status) {
          case 'processing': return '#3b82f6'
          case 'completed': return '#10b981'
          case 'coordinating': return '#f59e0b'
          default: return 'transparent'
        }
      })
      .attr('stroke-width', 2)
      .attr('stroke-dasharray', '3,3')
      .attr('opacity', (d) => d.status !== 'idle' ? 1 : 0)
      .style('animation', (d) => d.status === 'processing' ? 'spin 2s linear infinite' : 'none')

    // Add labels (constrained to fit within node)
    nodes.append('text')
      .text((d) => {
        const name = d.name.split(' ')[0]
        return name.length > 6 ? name.substring(0, 6) + '...' : name
      })
      .attr('text-anchor', 'middle')
      .attr('dy', '.35em')
      .attr('font-size', '9px')
      .attr('font-weight', 'bold')
      .attr('fill', '#fff')
      .attr('pointer-events', 'none')

    // Add capability indicators (positioned to stay within bounds)
    nodes.append('text')
      .text((d) => {
        const caps = d.capabilities.slice(0, 1).join('')
        return caps.length > 8 ? caps.substring(0, 8) + '...' : caps
      })
      .attr('text-anchor', 'middle')
      .attr('dy', NODE_RADIUS + 15)
      .attr('font-size', '7px')
      .attr('fill', '#374151')
      .attr('pointer-events', 'none')

    // Add load indicators
    nodes.append('rect')
      .attr('x', -15)
      .attr('y', -35)
      .attr('width', 30)
      .attr('height', 4)
      .attr('fill', '#e5e7eb')
      .attr('rx', 2)

    nodes.append('rect')
      .attr('x', -15)
      .attr('y', -35)
      .attr('width', (d) => (d.currentLoad / 5) * 30)
      .attr('height', 4)
      .attr('fill', (d) => {
        if (d.currentLoad < 2) return '#10b981'
        if (d.currentLoad < 4) return '#f59e0b'
        return '#ef4444'
      })
      .attr('rx', 2)

    // Add tooltips
    nodes.append('title')
      .text((d) => `${d.name}\nStatus: ${d.status}\nLoad: ${d.currentLoad.toFixed(1)}/5\nCapabilities: ${d.capabilities.join(', ')}`)

    // Update positions on simulation tick
    simulation.on('tick', () => {
      links
        .attr('x1', (d: any) => d.source.x)
        .attr('y1', (d: any) => d.source.y)
        .attr('x2', (d: any) => d.target.x)
        .attr('y2', (d: any) => d.target.y)

      nodes.attr('transform', (d) => `translate(${d.x},${d.y})`)
    })

    // Add legend (positioned within container bounds)
    const legend = svg.append('g')
      .attr('class', 'legend')
      .attr('transform', `translate(${PADDING}, ${PADDING})`)

    const legendData = [
      { color: '#6b7280', label: 'Idle', type: 'status' },
      { color: '#3b82f6', label: 'Processing', type: 'status' },
      { color: '#10b981', label: 'Completed', type: 'status' },
      { color: '#f59e0b', label: 'Coordinating', type: 'status' }
    ]

    const legendItems = legend.selectAll('.legend-item')
      .data(legendData)
      .enter()
      .append('g')
      .attr('class', 'legend-item')
      .attr('transform', (d, i) => `translate(0, ${i * 16})`)

    legendItems.append('circle')
      .attr('r', 4)
      .attr('fill', (d) => d.color)

    legendItems.append('text')
      .attr('x', 10)
      .attr('y', 0)
      .attr('dy', '.35em')
      .attr('font-size', '10px')
      .attr('fill', '#374151')
      .text((d) => d.label)

    return () => {
      simulation.stop()
    }
  }, [agents, connections, dimensions, d3Loaded, d3])

  return (
    <div className={`relative bg-white rounded-lg border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">Agent Coordination Network</h3>
        <p className="text-sm text-gray-600">Real-time visualization of multi-agent interactions</p>
      </div>
      <div
        ref={containerRef}
        className="relative overflow-hidden"
        style={{ height: '320px' }}
      >
        {!d3Loaded ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-sm text-gray-600">Loading visualization...</p>
            </div>
          </div>
        ) : agents.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <p className="text-sm text-gray-600">No agents available for visualization</p>
          </div>
        ) : (
          <svg
            ref={svgRef}
            width="100%"
            height="100%"
            className="border border-gray-100"
            style={{
              maxWidth: '100%',
              maxHeight: '100%'
            }}
          >
            <defs>
              <style>
                {`
                  @keyframes spin {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                  }
                  .node {
                    cursor: pointer;
                  }
                  .node:hover circle {
                    stroke-width: 3;
                  }
                `}
              </style>
            </defs>
          </svg>
        )}
      </div>
    </div>
  )
}
