import { useEffect, useRef, useState } from 'react'

interface Agent {
  id: string
  name: string
  status: 'idle' | 'processing' | 'completed' | 'coordinating'
  capabilities: string[]
  totalRequests: number
  currentLoad: number
  lastActivity: string
  x?: number
  y?: number
}

interface Connection {
  source: string
  target: string
  type: 'coordination' | 'data_flow' | 'handoff'
  strength: number
}

interface AgentCoordinationGraphProps {
  agents: Agent[]
  connections: Connection[]
  className?: string
  onAgentClick?: (agent: Agent) => void
  onConnectionUpdate?: (connections: Connection[]) => void
}

export function AgentCoordinationGraph({
  agents,
  connections,
  className = '',
  onAgentClick,
  onConnectionUpdate
}: AgentCoordinationGraphProps) {
  const svgRef = useRef<SVGSVGElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [dimensions, setDimensions] = useState({ width: 600, height: 320 })
  const [d3Loaded, setD3Loaded] = useState(false)
  const [d3, setD3] = useState<any>(null)
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null)

  // Container padding to keep all elements within bounds
  const PADDING = 20
  const NODE_RADIUS = 25

  // Load D3 dynamically
  useEffect(() => {
    const loadD3 = async () => {
      try {
        const d3Module = await import('d3')
        setD3(d3Module)
        setD3Loaded(true)
      } catch (error) {
        console.error('Error loading D3:', error)
      }
    }
    loadD3()
  }, [])

  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect()
        // Account for padding and ensure minimum dimensions
        const width = Math.max(300, rect.width - (PADDING * 2))
        const height = Math.max(200, rect.height - (PADDING * 2))
        setDimensions({ width, height })
      }
    }

    // Use a timeout to ensure the container is properly rendered
    const timeoutId = setTimeout(handleResize, 100)
    window.addEventListener('resize', handleResize)

    return () => {
      clearTimeout(timeoutId)
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  // Dynamic agent network management
  const generateIntelligentConnections = (newAgents: Agent[], existingConnections: Connection[]): Connection[] => {
    const connections: Connection[] = [...existingConnections]

    newAgents.forEach(newAgent => {
      // Find agents with complementary capabilities
      const compatibleAgents = agents.filter(agent =>
        agent.id !== newAgent.id &&
        agent.capabilities.some(cap =>
          newAgent.capabilities.includes(cap) ||
          getComplementaryCapabilities(cap).some(compCap => newAgent.capabilities.includes(compCap))
        )
      )

      // Connect to the least loaded compatible agents
      const sortedByLoad = compatibleAgents.sort((a, b) => a.currentLoad - b.currentLoad)
      const connectionsToMake = Math.min(3, sortedByLoad.length) // Max 3 connections per new agent

      sortedByLoad.slice(0, connectionsToMake).forEach(targetAgent => {
        const connectionType = determineConnectionType(newAgent, targetAgent)
        const strength = calculateConnectionStrength(newAgent, targetAgent)

        connections.push({
          source: newAgent.id,
          target: targetAgent.id,
          type: connectionType,
          strength
        })
      })
    })

    return connections
  }

  const getComplementaryCapabilities = (capability: string): string[] => {
    const complementaryMap: Record<string, string[]> = {
      'general': ['code', 'research'],
      'code': ['general', 'research'],
      'research': ['general', 'code'],
      'analysis': ['code', 'research'],
      'debugging': ['code', 'general'],
      'optimization': ['code', 'analysis']
    }
    return complementaryMap[capability] || []
  }

  const determineConnectionType = (agent1: Agent, agent2: Agent): Connection['type'] => {
    const hasSharedCapabilities = agent1.capabilities.some(cap => agent2.capabilities.includes(cap))

    if (hasSharedCapabilities) return 'coordination'
    if (agent1.capabilities.includes('general') || agent2.capabilities.includes('general')) return 'handoff'
    return 'data_flow'
  }

  const calculateConnectionStrength = (agent1: Agent, agent2: Agent): number => {
    const sharedCapabilities = agent1.capabilities.filter(cap => agent2.capabilities.includes(cap)).length
    const totalCapabilities = new Set([...agent1.capabilities, ...agent2.capabilities]).size
    const loadBalance = 1 - Math.abs(agent1.currentLoad - agent2.currentLoad) / 5

    return Math.min(1, (sharedCapabilities / totalCapabilities) * 0.7 + loadBalance * 0.3)
  }

  // Update connections when agents change
  useEffect(() => {
    if (agents.length > 0 && onConnectionUpdate) {
      const newConnections = generateIntelligentConnections(agents, connections)
      if (newConnections.length !== connections.length) {
        onConnectionUpdate(newConnections)
      }
    }
  }, [agents.length])

  useEffect(() => {
    if (!svgRef.current || agents.length === 0 || !d3Loaded || !d3) return

    const svg = d3.select(svgRef.current)
    svg.selectAll('*').remove()

    const { width, height } = dimensions

    // Calculate effective area within padding
    const effectiveWidth = width - (PADDING * 2)
    const effectiveHeight = height - (PADDING * 2)

    // Set up SVG viewBox to contain everything
    svg.attr('viewBox', `0 0 ${width} ${height}`)
       .attr('preserveAspectRatio', 'xMidYMid meet')

    // Initialize node positions if not set
    const nodesWithPositions = agents.map((agent, i) => ({
      ...agent,
      x: agent.x || (width / 2 + Math.cos(i * 2 * Math.PI / agents.length) * 60),
      y: agent.y || (height / 2 + Math.sin(i * 2 * Math.PI / agents.length) * 60),
      vx: 0,
      vy: 0
    }))

    // Create proper links data with source/target references
    const linksData = connections.map(conn => ({
      ...conn,
      source: nodesWithPositions.find(n => n.id === conn.source) || conn.source,
      target: nodesWithPositions.find(n => n.id === conn.target) || conn.target
    }))

    // Create force simulation with improved stability and proper link handling
    const simulation = d3.forceSimulation(nodesWithPositions)
      .force('link', d3.forceLink(linksData)
        .id((d: any) => d.id)
        .distance(Math.min(120, Math.max(80, effectiveWidth / 4)))
        .strength(0.6)
        .iterations(3)
      )
      .force('charge', d3.forceManyBody()
        .strength(-400)
        .distanceMin(NODE_RADIUS * 2.5)
        .distanceMax(Math.min(effectiveWidth, effectiveHeight) / 2)
      )
      .force('center', d3.forceCenter(width / 2, height / 2).strength(0.15))
      .force('collision', d3.forceCollide()
        .radius(NODE_RADIUS + 15)
        .strength(1.0)
        .iterations(3)
      )
      // Improved boundary force
      .force('boundary', () => {
        nodesWithPositions.forEach((node: any) => {
          const minX = PADDING + NODE_RADIUS + 5
          const maxX = width - PADDING - NODE_RADIUS - 5
          const minY = PADDING + NODE_RADIUS + 5
          const maxY = height - PADDING - NODE_RADIUS - 5

          if (node.x < minX) {
            node.x = minX
            node.vx = Math.abs(node.vx) * 0.1
          }
          if (node.x > maxX) {
            node.x = maxX
            node.vx = -Math.abs(node.vx) * 0.1
          }
          if (node.y < minY) {
            node.y = minY
            node.vy = Math.abs(node.vy) * 0.1
          }
          if (node.y > maxY) {
            node.y = maxY
            node.vy = -Math.abs(node.vy) * 0.1
          }
        })
      })
      .alphaDecay(0.02)
      .velocityDecay(0.3)

    // Create container groups
    const container = svg.append('g')
    const linksGroup = container.append('g').attr('class', 'links')
    const nodesGroup = container.append('g').attr('class', 'nodes')

    // Create links with improved rendering and proper data binding
    const links = linksGroup.selectAll('line')
      .data(linksData)
      .enter()
      .append('line')
      .attr('class', 'link')
      .attr('stroke', (d: any) => {
        switch (d.type) {
          case 'coordination': return '#3b82f6'
          case 'data_flow': return '#10b981'
          case 'handoff': return '#f59e0b'
          default: return '#6b7280'
        }
      })
      .attr('stroke-width', (d: any) => Math.max(2, d.strength * 4))
      .attr('stroke-opacity', (d: any) => Math.max(0.4, d.strength * 0.8))
      .attr('stroke-dasharray', (d: any) => d.type === 'coordination' ? '8,4' : 'none')
      .attr('stroke-linecap', 'round')
      .attr('marker-end', 'url(#arrowhead)')

    // Create nodes with improved interaction
    const nodes = nodesGroup.selectAll('g')
      .data(nodesWithPositions)
      .enter()
      .append('g')
      .attr('class', 'node')
      .style('cursor', 'pointer')
      .call(d3.drag()
        .on('start', (event: any, d: any) => {
          if (!event.active) simulation.alphaTarget(0.1).restart()
          d.fx = d.x
          d.fy = d.y
          // Highlight connected nodes
          svg.selectAll('.link')
            .style('opacity', (l: any) =>
              l.source.id === d.id || l.target.id === d.id ? 1 : 0.2
            )
        })
        .on('drag', (event: any, d: any) => {
          d.fx = Math.max(PADDING + NODE_RADIUS, Math.min(width - PADDING - NODE_RADIUS, event.x))
          d.fy = Math.max(PADDING + NODE_RADIUS, Math.min(height - PADDING - NODE_RADIUS, event.y))
        })
        .on('end', (event: any, d: any) => {
          if (!event.active) simulation.alphaTarget(0)
          d.fx = null
          d.fy = null
          // Reset link opacity
          svg.selectAll('.link').style('opacity', null)
        })
      )

    // Add main node circles
    nodes.append('circle')
      .attr('class', 'node-main')
      .attr('r', NODE_RADIUS)
      .attr('fill', (d: any) => {
        switch (d.status) {
          case 'processing': return '#3b82f6'
          case 'completed': return '#10b981'
          case 'coordinating': return '#f59e0b'
          default: return '#6b7280'
        }
      })
      .attr('stroke', '#fff')
      .attr('stroke-width', 3)
      .attr('opacity', 0.9)
      .style('filter', 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))')

    // Add circular progress indicators around nodes
    nodes.each(function(this: SVGGElement, d: any) {
      const nodeGroup = d3.select(this)
      const progressRadius = NODE_RADIUS + 6
      const progress = d.currentLoad / 5 // Normalize to 0-1

      // Background circle for progress
      nodeGroup.append('circle')
        .attr('class', 'progress-bg')
        .attr('r', progressRadius)
        .attr('fill', 'none')
        .attr('stroke', '#e5e7eb')
        .attr('stroke-width', 3)
        .attr('opacity', 0.3)

      // Progress arc
      if (progress > 0) {
        const arc = d3.arc()
          .innerRadius(progressRadius - 1.5)
          .outerRadius(progressRadius + 1.5)
          .startAngle(0)
          .endAngle(progress * 2 * Math.PI)

        nodeGroup.append('path')
          .attr('class', 'progress-arc')
          .attr('d', arc as any)
          .attr('fill', () => {
            if (progress < 0.4) return '#10b981' // Green
            if (progress < 0.8) return '#f59e0b' // Yellow
            return '#ef4444' // Red
          })
          .attr('opacity', 0.8)
      }
    })

    // Add status indicator rings with animation
    nodes.append('circle')
      .attr('class', 'status-ring')
      .attr('r', NODE_RADIUS + 12)
      .attr('fill', 'none')
      .attr('stroke', (d: any) => {
        switch (d.status) {
          case 'processing': return '#3b82f6'
          case 'completed': return '#10b981'
          case 'coordinating': return '#f59e0b'
          default: return 'transparent'
        }
      })
      .attr('stroke-width', 2)
      .attr('stroke-dasharray', '6,6')
      .attr('opacity', (d: any) => d.status !== 'idle' ? 0.6 : 0)
      .style('animation', (d: any) => {
        if (d.status === 'processing') return 'spin 3s linear infinite'
        if (d.status === 'coordinating') return 'pulse 2s ease-in-out infinite'
        return 'none'
      })

    // Add node labels
    nodes.append('text')
      .attr('class', 'node-label')
      .text((d: any) => {
        const name = d.name.split(' ')[0]
        return name.length > 8 ? name.substring(0, 8) + '...' : name
      })
      .attr('text-anchor', 'middle')
      .attr('dy', '.35em')
      .attr('font-size', '10px')
      .attr('font-weight', 'bold')
      .attr('fill', '#fff')
      .attr('pointer-events', 'none')
      .style('text-shadow', '0 1px 2px rgba(0,0,0,0.5)')

    // Add capability badges
    nodes.append('text')
      .attr('class', 'capability-label')
      .text((d: any) => {
        const caps = d.capabilities.slice(0, 1).join('')
        return caps.length > 10 ? caps.substring(0, 10) + '...' : caps
      })
      .attr('text-anchor', 'middle')
      .attr('dy', NODE_RADIUS + 20)
      .attr('font-size', '8px')
      .attr('font-weight', '500')
      .attr('fill', '#374151')
      .attr('pointer-events', 'none')
      .style('background', 'rgba(255,255,255,0.8)')

    // Add load percentage text
    nodes.append('text')
      .attr('class', 'load-text')
      .text((d: any) => `${Math.round((d.currentLoad / 5) * 100)}%`)
      .attr('text-anchor', 'middle')
      .attr('dy', -NODE_RADIUS - 8)
      .attr('font-size', '8px')
      .attr('font-weight', 'bold')
      .attr('fill', (d: any) => {
        const progress = d.currentLoad / 5
        if (progress < 0.4) return '#10b981'
        if (progress < 0.8) return '#f59e0b'
        return '#ef4444'
      })
      .attr('pointer-events', 'none')
      .style('text-shadow', '0 1px 2px rgba(255,255,255,0.8)')

    // Add tooltips
    nodes.append('title')
      .text((d: any) => `${d.name}\nStatus: ${d.status}\nLoad: ${d.currentLoad.toFixed(1)}/5\nCapabilities: ${d.capabilities.join(', ')}`)

    // Add arrow markers for directed links
    svg.append('defs').append('marker')
      .attr('id', 'arrowhead')
      .attr('viewBox', '0 -5 10 10')
      .attr('refX', NODE_RADIUS + 8)
      .attr('refY', 0)
      .attr('markerWidth', 6)
      .attr('markerHeight', 6)
      .attr('orient', 'auto')
      .append('path')
      .attr('d', 'M0,-5L10,0L0,5')
      .attr('fill', '#6b7280')
      .attr('opacity', 0.6)

    // Update positions on simulation tick with improved stability
    simulation.on('tick', () => {
      // Update links with proper source/target positioning
      links
        .attr('x1', (d: any) => d.source.x || 0)
        .attr('y1', (d: any) => d.source.y || 0)
        .attr('x2', (d: any) => d.target.x || 0)
        .attr('y2', (d: any) => d.target.y || 0)

      // Update node positions with boundary constraints
      nodes.attr('transform', (d: any) => {
        // Ensure nodes stay within bounds
        d.x = Math.max(PADDING + NODE_RADIUS, Math.min(width - PADDING - NODE_RADIUS, d.x))
        d.y = Math.max(PADDING + NODE_RADIUS, Math.min(height - PADDING - NODE_RADIUS, d.y))
        return `translate(${d.x},${d.y})`
      })
    })

    // Add interaction effects
    nodes
      .on('click', function(this: SVGGElement, event: any, d: any) {
        event.stopPropagation()
        setSelectedAgent(d.id === selectedAgent ? null : d.id)
        if (onAgentClick) {
          onAgentClick(d)
        }
      })
      .on('mouseenter', function(this: SVGGElement, _event: any, d: any) {
        d3.select(this).select('.node-main')
          .transition()
          .duration(200)
          .attr('r', NODE_RADIUS + 3)
          .style('filter', 'drop-shadow(0 4px 8px rgba(0,0,0,0.2))')

        // Highlight connected links
        links
          .transition()
          .duration(200)
          .attr('stroke-opacity', (l: any) =>
            (l.source.id === d.id || l.target.id === d.id) ? 1 : 0.2
          )
      })
      .on('mouseleave', function(this: SVGGElement, _event: any, _d: any) {
        d3.select(this).select('.node-main')
          .transition()
          .duration(200)
          .attr('r', NODE_RADIUS)
          .style('filter', 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))')

        // Reset link opacity
        links
          .transition()
          .duration(200)
          .attr('stroke-opacity', (l: any) => Math.max(0.4, l.strength * 0.8))
      })

    // Update selected node styling
    nodes.select('.node-main')
      .attr('stroke-width', (d: any) => d.id === selectedAgent ? 5 : 3)
      .attr('stroke', (d: any) => d.id === selectedAgent ? '#fbbf24' : '#fff')

    // Add legend (positioned within container bounds)
    const legend = svg.append('g')
      .attr('class', 'legend')
      .attr('transform', `translate(${PADDING}, ${PADDING})`)

    const legendData = [
      { color: '#6b7280', label: 'Idle', type: 'status' },
      { color: '#3b82f6', label: 'Processing', type: 'status' },
      { color: '#10b981', label: 'Completed', type: 'status' },
      { color: '#f59e0b', label: 'Coordinating', type: 'status' }
    ]

    const legendItems = legend.selectAll('.legend-item')
      .data(legendData)
      .enter()
      .append('g')
      .attr('class', 'legend-item')
      .attr('transform', (_d: any, i: any) => `translate(0, ${i * 16})`)

    legendItems.append('circle')
      .attr('r', 4)
      .attr('fill', (d: any) => d.color)

    legendItems.append('text')
      .attr('x', 10)
      .attr('y', 0)
      .attr('dy', '.35em')
      .attr('font-size', '10px')
      .attr('fill', '#374151')
      .text((d: any) => d.label)

    return () => {
      simulation.stop()
    }
  }, [agents, connections, dimensions, d3Loaded, d3])

  return (
    <div className={`relative bg-white rounded-lg border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">Agent Coordination Network</h3>
        <p className="text-sm text-gray-600">Real-time visualization of multi-agent interactions</p>
      </div>
      <div
        ref={containerRef}
        className="relative overflow-hidden"
        style={{ height: '320px' }}
      >
        {!d3Loaded ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-sm text-gray-600">Loading visualization...</p>
            </div>
          </div>
        ) : agents.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <p className="text-sm text-gray-600">No agents available for visualization</p>
          </div>
        ) : (
          <svg
            ref={svgRef}
            width="100%"
            height="100%"
            className="border border-gray-100"
            style={{
              maxWidth: '100%',
              maxHeight: '100%'
            }}
          >
            <defs>
              <style>
                {`
                  @keyframes spin {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                  }
                  @keyframes pulse {
                    0%, 100% { opacity: 0.6; }
                    50% { opacity: 1; }
                  }
                  .node {
                    cursor: pointer;
                    transition: all 0.2s ease;
                  }
                  .node:hover .node-main {
                    stroke-width: 4;
                  }
                  .link {
                    transition: all 0.2s ease;
                  }
                  .progress-arc {
                    transition: all 0.3s ease;
                  }
                  .status-ring {
                    transform-origin: center;
                  }
                `}
              </style>
            </defs>
          </svg>
        )}
      </div>
    </div>
  )
}
