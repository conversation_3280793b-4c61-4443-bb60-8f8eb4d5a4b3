import { useEffect, useRef, useState } from 'react'

interface Agent {
  id: string
  name: string
  status: 'idle' | 'processing' | 'completed' | 'coordinating'
  capabilities: string[]
  currentLoad: number
  x?: number
  y?: number
}

interface Connection {
  source: string
  target: string
  type: 'coordination' | 'data_flow' | 'handoff'
  strength: number
}

interface AgentCoordinationGraphProps {
  agents: Agent[]
  connections: Connection[]
  className?: string
}

export function AgentCoordinationGraph({ agents, connections, className = '' }: AgentCoordinationGraphProps) {
  const svgRef = useRef<SVGSVGElement>(null)
  const [dimensions, setDimensions] = useState({ width: 600, height: 400 })
  const [d3Loaded, setD3Loaded] = useState(false)
  const [d3, setD3] = useState<any>(null)

  // Load D3 dynamically
  useEffect(() => {
    const loadD3 = async () => {
      try {
        const d3Module = await import('d3')
        setD3(d3Module)
        setD3Loaded(true)
      } catch (error) {
        console.error('Error loading D3:', error)
      }
    }
    loadD3()
  }, [])

  useEffect(() => {
    const handleResize = () => {
      if (svgRef.current) {
        const rect = svgRef.current.getBoundingClientRect()
        setDimensions({ width: rect.width, height: rect.height })
      }
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  useEffect(() => {
    if (!svgRef.current || agents.length === 0 || !d3Loaded || !d3) return

    const svg = d3.select(svgRef.current)
    svg.selectAll('*').remove()

    const { width, height } = dimensions
    const margin = 40

    // Create force simulation
    const simulation = d3.forceSimulation(agents)
      .force('link', d3.forceLink(connections)
        .id((d: any) => d.id)
        .distance(100)
        .strength(0.5)
      )
      .force('charge', d3.forceManyBody().strength(-300))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius(50))

    // Create container groups
    const container = svg.append('g')
    const linksGroup = container.append('g').attr('class', 'links')
    const nodesGroup = container.append('g').attr('class', 'nodes')

    // Create links
    const links = linksGroup.selectAll('line')
      .data(connections)
      .enter()
      .append('line')
      .attr('stroke', (d) => {
        switch (d.type) {
          case 'coordination': return '#3b82f6'
          case 'data_flow': return '#10b981'
          case 'handoff': return '#f59e0b'
          default: return '#6b7280'
        }
      })
      .attr('stroke-width', (d) => Math.max(1, d.strength * 3))
      .attr('stroke-opacity', 0.6)
      .attr('stroke-dasharray', (d) => d.type === 'coordination' ? '5,5' : 'none')

    // Create nodes
    const nodes = nodesGroup.selectAll('g')
      .data(agents)
      .enter()
      .append('g')
      .attr('class', 'node')
      .call(d3.drag<SVGGElement, Agent>()
        .on('start', (event, d) => {
          if (!event.active) simulation.alphaTarget(0.3).restart()
          d.fx = d.x
          d.fy = d.y
        })
        .on('drag', (event, d) => {
          d.fx = event.x
          d.fy = event.y
        })
        .on('end', (event, d) => {
          if (!event.active) simulation.alphaTarget(0)
          d.fx = null
          d.fy = null
        })
      )

    // Add circles for nodes
    nodes.append('circle')
      .attr('r', (d) => 20 + d.currentLoad * 5)
      .attr('fill', (d) => {
        switch (d.status) {
          case 'processing': return '#3b82f6'
          case 'completed': return '#10b981'
          case 'coordinating': return '#f59e0b'
          default: return '#6b7280'
        }
      })
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)
      .attr('opacity', 0.8)

    // Add status indicator rings
    nodes.append('circle')
      .attr('r', (d) => 25 + d.currentLoad * 5)
      .attr('fill', 'none')
      .attr('stroke', (d) => {
        switch (d.status) {
          case 'processing': return '#3b82f6'
          case 'completed': return '#10b981'
          case 'coordinating': return '#f59e0b'
          default: return 'transparent'
        }
      })
      .attr('stroke-width', 2)
      .attr('stroke-dasharray', '3,3')
      .attr('opacity', (d) => d.status !== 'idle' ? 1 : 0)
      .style('animation', (d) => d.status === 'processing' ? 'spin 2s linear infinite' : 'none')

    // Add labels
    nodes.append('text')
      .text((d) => d.name.split(' ')[0]) // First word only
      .attr('text-anchor', 'middle')
      .attr('dy', '.35em')
      .attr('font-size', '10px')
      .attr('font-weight', 'bold')
      .attr('fill', '#fff')
      .attr('pointer-events', 'none')

    // Add capability indicators
    nodes.append('text')
      .text((d) => d.capabilities.slice(0, 2).join(', '))
      .attr('text-anchor', 'middle')
      .attr('dy', '25px')
      .attr('font-size', '8px')
      .attr('fill', '#374151')
      .attr('pointer-events', 'none')

    // Add load indicators
    nodes.append('rect')
      .attr('x', -15)
      .attr('y', -35)
      .attr('width', 30)
      .attr('height', 4)
      .attr('fill', '#e5e7eb')
      .attr('rx', 2)

    nodes.append('rect')
      .attr('x', -15)
      .attr('y', -35)
      .attr('width', (d) => (d.currentLoad / 5) * 30)
      .attr('height', 4)
      .attr('fill', (d) => {
        if (d.currentLoad < 2) return '#10b981'
        if (d.currentLoad < 4) return '#f59e0b'
        return '#ef4444'
      })
      .attr('rx', 2)

    // Add tooltips
    nodes.append('title')
      .text((d) => `${d.name}\nStatus: ${d.status}\nLoad: ${d.currentLoad.toFixed(1)}/5\nCapabilities: ${d.capabilities.join(', ')}`)

    // Update positions on simulation tick
    simulation.on('tick', () => {
      links
        .attr('x1', (d: any) => d.source.x)
        .attr('y1', (d: any) => d.source.y)
        .attr('x2', (d: any) => d.target.x)
        .attr('y2', (d: any) => d.target.y)

      nodes.attr('transform', (d) => `translate(${d.x},${d.y})`)
    })

    // Add legend
    const legend = svg.append('g')
      .attr('class', 'legend')
      .attr('transform', `translate(${width - 150}, 20)`)

    const legendData = [
      { color: '#6b7280', label: 'Idle', type: 'status' },
      { color: '#3b82f6', label: 'Processing', type: 'status' },
      { color: '#10b981', label: 'Completed', type: 'status' },
      { color: '#f59e0b', label: 'Coordinating', type: 'status' }
    ]

    const legendItems = legend.selectAll('.legend-item')
      .data(legendData)
      .enter()
      .append('g')
      .attr('class', 'legend-item')
      .attr('transform', (d, i) => `translate(0, ${i * 20})`)

    legendItems.append('circle')
      .attr('r', 6)
      .attr('fill', (d) => d.color)

    legendItems.append('text')
      .attr('x', 12)
      .attr('y', 0)
      .attr('dy', '.35em')
      .attr('font-size', '12px')
      .attr('fill', '#374151')
      .text((d) => d.label)

    return () => {
      simulation.stop()
    }
  }, [agents, connections, dimensions, d3Loaded, d3])

  return (
    <div className={`relative bg-white rounded-lg border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">Agent Coordination Network</h3>
        <p className="text-sm text-gray-600">Real-time visualization of multi-agent interactions</p>
      </div>
      <div className="relative" style={{ height: '400px' }}>
        {!d3Loaded ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <p className="text-sm text-gray-600">Loading visualization...</p>
            </div>
          </div>
        ) : agents.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <p className="text-sm text-gray-600">No agents available for visualization</p>
          </div>
        ) : (
          <svg
            ref={svgRef}
            width="100%"
            height="100%"
            className="overflow-visible"
          >
            <defs>
              <style>
                {`
                  @keyframes spin {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                  }
                  .node {
                    cursor: pointer;
                  }
                  .node:hover circle {
                    stroke-width: 3;
                  }
                `}
              </style>
            </defs>
          </svg>
        )}
        
        {/* Connection type legend */}
        <div className="absolute bottom-4 left-4 bg-white bg-opacity-90 p-3 rounded-lg border border-gray-200 text-xs">
          <div className="font-semibold mb-2">Connection Types:</div>
          <div className="space-y-1">
            <div className="flex items-center">
              <div className="w-4 h-0.5 bg-blue-500 mr-2" style={{ strokeDasharray: '2,2' }}></div>
              <span>Coordination</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-0.5 bg-green-500 mr-2"></div>
              <span>Data Flow</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-0.5 bg-yellow-500 mr-2"></div>
              <span>Handoff</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
