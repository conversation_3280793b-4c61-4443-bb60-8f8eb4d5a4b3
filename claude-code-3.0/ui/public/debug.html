<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat API Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        input { padding: 8px; margin: 5px; width: 300px; }
    </style>
</head>
<body>
    <h1>Claude Code 3.0 - Chat API Debug</h1>
    
    <div class="test-section">
        <h3>1. API Status Test</h3>
        <button onclick="testAPIStatus()">Test API Status</button>
        <div id="status-result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Message API Test</h3>
        <input type="text" id="test-message" placeholder="Enter test message" value="Hello from debug page">
        <button onclick="testMessageAPI()">Send Test Message</button>
        <div id="message-result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Agents API Test</h3>
        <button onclick="testAgentsAPI()">Get Agents</button>
        <div id="agents-result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. WebSocket Test</h3>
        <button onclick="testWebSocket()">Test WebSocket</button>
        <button onclick="closeWebSocket()">Close WebSocket</button>
        <div id="websocket-result"></div>
    </div>

    <script>
        let ws = null;
        
        async function testAPIStatus() {
            const resultDiv = document.getElementById('status-result');
            try {
                console.log('Testing API status...');
                const response = await fetch('http://localhost:8080/api/status');
                const data = await response.json();
                
                resultDiv.className = 'success';
                resultDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                console.log('API Status Success:', data);
            } catch (error) {
                resultDiv.className = 'error';
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
                console.error('API Status Error:', error);
            }
        }
        
        async function testMessageAPI() {
            const resultDiv = document.getElementById('message-result');
            const message = document.getElementById('test-message').value;
            
            try {
                console.log('Testing message API with:', message);
                const response = await fetch('http://localhost:8080/api/messages', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        content: message,
                        sessionId: 'debug-session',
                        capabilities: ['general']
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                resultDiv.className = 'success';
                resultDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                console.log('Message API Success:', data);
            } catch (error) {
                resultDiv.className = 'error';
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
                console.error('Message API Error:', error);
            }
        }
        
        async function testAgentsAPI() {
            const resultDiv = document.getElementById('agents-result');
            try {
                console.log('Testing agents API...');
                const response = await fetch('http://localhost:8080/api/agents');
                const data = await response.json();
                
                resultDiv.className = 'success';
                resultDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                console.log('Agents API Success:', data);
            } catch (error) {
                resultDiv.className = 'error';
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
                console.error('Agents API Error:', error);
            }
        }
        
        function testWebSocket() {
            const resultDiv = document.getElementById('websocket-result');
            
            try {
                console.log('Testing WebSocket connection...');
                ws = new WebSocket('ws://localhost:8080');
                
                ws.onopen = function() {
                    resultDiv.className = 'success';
                    resultDiv.innerHTML = '<strong>WebSocket connected successfully!</strong>';
                    console.log('WebSocket connected');
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    resultDiv.innerHTML += `<br><strong>Received:</strong> <pre>${JSON.stringify(data, null, 2)}</pre>`;
                    console.log('WebSocket message:', data);
                };
                
                ws.onerror = function(error) {
                    resultDiv.className = 'error';
                    resultDiv.innerHTML = `<strong>WebSocket Error:</strong> ${error.message || 'Connection failed'}`;
                    console.error('WebSocket error:', error);
                };
                
                ws.onclose = function() {
                    resultDiv.innerHTML += '<br><strong>WebSocket connection closed</strong>';
                    console.log('WebSocket closed');
                };
                
            } catch (error) {
                resultDiv.className = 'error';
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
                console.error('WebSocket Error:', error);
            }
        }
        
        function closeWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }
        
        // Auto-run status test on page load
        window.onload = function() {
            testAPIStatus();
        };
    </script>
</body>
</html>
