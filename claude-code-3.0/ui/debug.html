<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claude Code 3.0 Debug</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Claude Code 3.0 Debug Page</h1>
    
    <div id="api-status" class="status info">Testing API connection...</div>
    <div id="ui-status" class="status info">Testing UI connection...</div>
    <div id="websocket-status" class="status info">Testing WebSocket connection...</div>
    
    <h2>API Test Results</h2>
    <pre id="api-results"></pre>
    
    <h2>System Information</h2>
    <pre id="system-info"></pre>
    
    <script>
        // Test API connection
        async function testAPI() {
            try {
                const response = await fetch('http://localhost:8080/api/status');
                const data = await response.json();
                document.getElementById('api-status').className = 'status success';
                document.getElementById('api-status').textContent = '✅ API Server: Connected';
                document.getElementById('api-results').textContent = JSON.stringify(data, null, 2);
                
                // Test metrics endpoint
                const metricsResponse = await fetch('http://localhost:8080/api/metrics');
                const metricsData = await metricsResponse.json();
                document.getElementById('api-results').textContent += '\n\nMetrics:\n' + JSON.stringify(metricsData, null, 2);
                
            } catch (error) {
                document.getElementById('api-status').className = 'status error';
                document.getElementById('api-status').textContent = '❌ API Server: Failed - ' + error.message;
                document.getElementById('api-results').textContent = 'Error: ' + error.message;
            }
        }
        
        // Test WebSocket connection
        function testWebSocket() {
            try {
                const ws = new WebSocket('ws://localhost:8080');
                
                ws.onopen = function() {
                    document.getElementById('websocket-status').className = 'status success';
                    document.getElementById('websocket-status').textContent = '✅ WebSocket: Connected';
                };
                
                ws.onerror = function(error) {
                    document.getElementById('websocket-status').className = 'status error';
                    document.getElementById('websocket-status').textContent = '❌ WebSocket: Failed';
                };
                
                ws.onmessage = function(event) {
                    console.log('WebSocket message:', event.data);
                };
                
            } catch (error) {
                document.getElementById('websocket-status').className = 'status error';
                document.getElementById('websocket-status').textContent = '❌ WebSocket: Error - ' + error.message;
            }
        }
        
        // Test UI
        function testUI() {
            try {
                // Check if we can access the main UI
                fetch('http://localhost:3000')
                    .then(response => {
                        if (response.ok) {
                            document.getElementById('ui-status').className = 'status success';
                            document.getElementById('ui-status').textContent = '✅ UI Server: Accessible';
                        } else {
                            document.getElementById('ui-status').className = 'status error';
                            document.getElementById('ui-status').textContent = '❌ UI Server: HTTP ' + response.status;
                        }
                    })
                    .catch(error => {
                        document.getElementById('ui-status').className = 'status error';
                        document.getElementById('ui-status').textContent = '❌ UI Server: Failed - ' + error.message;
                    });
            } catch (error) {
                document.getElementById('ui-status').className = 'status error';
                document.getElementById('ui-status').textContent = '❌ UI Server: Error - ' + error.message;
            }
        }
        
        // Display system info
        function showSystemInfo() {
            const info = {
                userAgent: navigator.userAgent,
                url: window.location.href,
                timestamp: new Date().toISOString(),
                screen: {
                    width: screen.width,
                    height: screen.height
                }
            };
            document.getElementById('system-info').textContent = JSON.stringify(info, null, 2);
        }
        
        // Run all tests
        testAPI();
        testWebSocket();
        testUI();
        showSystemInfo();
        
        // Add links to test different pages
        setTimeout(() => {
            const linksDiv = document.createElement('div');
            linksDiv.innerHTML = `
                <h2>Quick Links</h2>
                <p><a href="http://localhost:3000" target="_blank">Main UI</a></p>
                <p><a href="http://localhost:3000/test" target="_blank">Test Component</a></p>
                <p><a href="http://localhost:3000/agents" target="_blank">Agents Page</a></p>
                <p><a href="http://localhost:8080/api/status" target="_blank">API Status</a></p>
                <p><a href="http://localhost:8080/api/metrics" target="_blank">API Metrics</a></p>
            `;
            document.body.appendChild(linksDiv);
        }, 1000);
    </script>
</body>
</html>
