{"version": 3, "file": "remapping.mjs", "sources": ["../src/source-map-tree.ts", "../src/build-source-map-tree.ts", "../src/source-map.ts", "../src/remapping.ts"], "sourcesContent": ["import { GenMapping, maybeAddSegment, setIgnore, setSourceContent } from '@jridgewell/gen-mapping';\nimport { traceSegment, decodedMappings } from '@jridgewell/trace-mapping';\n\nimport type { TraceMap } from '@jridgewell/trace-mapping';\n\nexport type SourceMapSegmentObject = {\n  column: number;\n  line: number;\n  name: string;\n  source: string;\n  content: string | null;\n  ignore: boolean;\n};\n\nexport type OriginalSource = {\n  map: null;\n  sources: Sources[];\n  source: string;\n  content: string | null;\n  ignore: boolean;\n};\n\nexport type MapSource = {\n  map: TraceMap;\n  sources: Sources[];\n  source: string;\n  content: null;\n  ignore: false;\n};\n\nexport type Sources = OriginalSource | MapSource;\n\nconst SOURCELESS_MAPPING = /* #__PURE__ */ SegmentObject('', -1, -1, '', null, false);\nconst EMPTY_SOURCES: Sources[] = [];\n\nfunction SegmentObject(\n  source: string,\n  line: number,\n  column: number,\n  name: string,\n  content: string | null,\n  ignore: boolean\n): SourceMapSegmentObject {\n  return { source, line, column, name, content, ignore };\n}\n\nfunction Source(\n  map: TraceMap,\n  sources: Sources[],\n  source: '',\n  content: null,\n  ignore: false\n): MapSource;\nfunction Source(\n  map: null,\n  sources: Sources[],\n  source: string,\n  content: string | null,\n  ignore: boolean\n): OriginalSource;\nfunction Source(\n  map: TraceMap | null,\n  sources: Sources[],\n  source: string | '',\n  content: string | null,\n  ignore: boolean\n): Sources {\n  return {\n    map,\n    sources,\n    source,\n    content,\n    ignore,\n  } as any;\n}\n\n/**\n * MapSource represents a single sourcemap, with the ability to trace mappings into its child nodes\n * (which may themselves be SourceMapTrees).\n */\nexport function MapSource(map: TraceMap, sources: Sources[]): MapSource {\n  return Source(map, sources, '', null, false);\n}\n\n/**\n * A \"leaf\" node in the sourcemap tree, representing an original, unmodified source file. Recursive\n * segment tracing ends at the `OriginalSource`.\n */\nexport function OriginalSource(\n  source: string,\n  content: string | null,\n  ignore: boolean\n): OriginalSource {\n  return Source(null, EMPTY_SOURCES, source, content, ignore);\n}\n\n/**\n * traceMappings is only called on the root level SourceMapTree, and begins the process of\n * resolving each mapping in terms of the original source files.\n */\nexport function traceMappings(tree: MapSource): GenMapping {\n  // TODO: Eventually support sourceRoot, which has to be removed because the sources are already\n  // fully resolved. We'll need to make sources relative to the sourceRoot before adding them.\n  const gen = new GenMapping({ file: tree.map.file });\n  const { sources: rootSources, map } = tree;\n  const rootNames = map.names;\n  const rootMappings = decodedMappings(map);\n\n  for (let i = 0; i < rootMappings.length; i++) {\n    const segments = rootMappings[i];\n\n    for (let j = 0; j < segments.length; j++) {\n      const segment = segments[j];\n      const genCol = segment[0];\n      let traced: SourceMapSegmentObject | null = SOURCELESS_MAPPING;\n\n      // 1-length segments only move the current generated column, there's no source information\n      // to gather from it.\n      if (segment.length !== 1) {\n        const source = rootSources[segment[1]];\n        traced = originalPositionFor(\n          source,\n          segment[2],\n          segment[3],\n          segment.length === 5 ? rootNames[segment[4]] : ''\n        );\n\n        // If the trace is invalid, then the trace ran into a sourcemap that doesn't contain a\n        // respective segment into an original source.\n        if (traced == null) continue;\n      }\n\n      const { column, line, name, content, source, ignore } = traced;\n\n      maybeAddSegment(gen, i, genCol, source, line, column, name);\n      if (source && content != null) setSourceContent(gen, source, content);\n      if (ignore) setIgnore(gen, source, true);\n    }\n  }\n\n  return gen;\n}\n\n/**\n * originalPositionFor is only called on children SourceMapTrees. It recurses down into its own\n * child SourceMapTrees, until we find the original source map.\n */\nexport function originalPositionFor(\n  source: Sources,\n  line: number,\n  column: number,\n  name: string\n): SourceMapSegmentObject | null {\n  if (!source.map) {\n    return SegmentObject(source.source, line, column, name, source.content, source.ignore);\n  }\n\n  const segment = traceSegment(source.map, line, column);\n\n  // If we couldn't find a segment, then this doesn't exist in the sourcemap.\n  if (segment == null) return null;\n  // 1-length segments only move the current generated column, there's no source information\n  // to gather from it.\n  if (segment.length === 1) return SOURCELESS_MAPPING;\n\n  return originalPositionFor(\n    source.sources[segment[1]],\n    segment[2],\n    segment[3],\n    segment.length === 5 ? source.map.names[segment[4]] : name\n  );\n}\n", "import { TraceMap } from '@jridgewell/trace-mapping';\n\nimport { OriginalSource, MapSource } from './source-map-tree';\n\nimport type { Sources, MapSource as MapSourceType } from './source-map-tree';\nimport type { SourceMapInput, SourceMapLoader, LoaderContext } from './types';\n\nfunction asArray<T>(value: T | T[]): T[] {\n  if (Array.isArray(value)) return value;\n  return [value];\n}\n\n/**\n * Recursively builds a tree structure out of sourcemap files, with each node\n * being either an `OriginalSource` \"leaf\" or a `SourceMapTree` composed of\n * `OriginalSource`s and `SourceMapTree`s.\n *\n * Every sourcemap is composed of a collection of source files and mappings\n * into locations of those source files. When we generate a `SourceMapTree` for\n * the sourcemap, we attempt to load each source file's own sourcemap. If it\n * does not have an associated sourcemap, it is considered an original,\n * unmodified source file.\n */\nexport default function buildSourceMapTree(\n  input: SourceMapInput | SourceMapInput[],\n  loader: SourceMapLoader\n): MapSourceType {\n  const maps = asArray(input).map((m) => new TraceMap(m, ''));\n  const map = maps.pop()!;\n\n  for (let i = 0; i < maps.length; i++) {\n    if (maps[i].sources.length > 1) {\n      throw new Error(\n        `Transformation map ${i} must have exactly one source file.\\n` +\n          'Did you specify these with the most recent transformation maps first?'\n      );\n    }\n  }\n\n  let tree = build(map, loader, '', 0);\n  for (let i = maps.length - 1; i >= 0; i--) {\n    tree = MapSource(maps[i], [tree]);\n  }\n  return tree;\n}\n\nfunction build(\n  map: TraceMap,\n  loader: SourceMapLoader,\n  importer: string,\n  importerDepth: number\n): MapSourceType {\n  const { resolvedSources, sourcesContent, ignoreList } = map;\n\n  const depth = importerDepth + 1;\n  const children = resolvedSources.map((sourceFile: string | null, i: number): Sources => {\n    // The loading context gives the loader more information about why this file is being loaded\n    // (eg, from which importer). It also allows the loader to override the location of the loaded\n    // sourcemap/original source, or to override the content in the sourcesContent field if it's\n    // an unmodified source file.\n    const ctx: LoaderContext = {\n      importer,\n      depth,\n      source: sourceFile || '',\n      content: undefined,\n      ignore: undefined,\n    };\n\n    // Use the provided loader callback to retrieve the file's sourcemap.\n    // TODO: We should eventually support async loading of sourcemap files.\n    const sourceMap = loader(ctx.source, ctx);\n\n    const { source, content, ignore } = ctx;\n\n    // If there is a sourcemap, then we need to recurse into it to load its source files.\n    if (sourceMap) return build(new TraceMap(sourceMap, source), loader, source, depth);\n\n    // Else, it's an unmodified source file.\n    // The contents of this unmodified source file can be overridden via the loader context,\n    // allowing it to be explicitly null or a string. If it remains undefined, we fall back to\n    // the importing sourcemap's `sourcesContent` field.\n    const sourceContent =\n      content !== undefined ? content : sourcesContent ? sourcesContent[i] : null;\n    const ignored = ignore !== undefined ? ignore : ignoreList ? ignoreList.includes(i) : false;\n    return OriginalSource(source, sourceContent, ignored);\n  });\n\n  return MapSource(map, children);\n}\n", "import { toDecodedMap, toEncodedMap } from '@jridgewell/gen-mapping';\n\nimport type { GenMapping } from '@jridgewell/gen-mapping';\nimport type { DecodedSourceMap, EncodedSourceMap, Options } from './types';\n\n/**\n * A SourceMap v3 compatible sourcemap, which only includes fields that were\n * provided to it.\n */\nexport default class SourceMap {\n  declare file?: string | null;\n  declare mappings: EncodedSourceMap['mappings'] | DecodedSourceMap['mappings'];\n  declare sourceRoot?: string;\n  declare names: string[];\n  declare sources: (string | null)[];\n  declare sourcesContent?: (string | null)[];\n  declare version: 3;\n  declare ignoreList: number[] | undefined;\n\n  constructor(map: GenMapping, options: Options) {\n    const out = options.decodedMappings ? toDecodedMap(map) : toEncodedMap(map);\n    this.version = out.version; // SourceMap spec says this should be first.\n    this.file = out.file;\n    this.mappings = out.mappings as SourceMap['mappings'];\n    this.names = out.names as SourceMap['names'];\n    this.ignoreList = out.ignoreList as SourceMap['ignoreList'];\n    this.sourceRoot = out.sourceRoot;\n\n    this.sources = out.sources as SourceMap['sources'];\n    if (!options.excludeContent) {\n      this.sourcesContent = out.sourcesContent as SourceMap['sourcesContent'];\n    }\n  }\n\n  toString(): string {\n    return JSON.stringify(this);\n  }\n}\n", "import buildSourceMapTree from './build-source-map-tree';\nimport { traceMappings } from './source-map-tree';\nimport SourceMap from './source-map';\n\nimport type { SourceMapInput, SourceMapLoader, Options } from './types';\nexport type {\n  SourceMapSegment,\n  EncodedSourceMap,\n  EncodedSourceMap as RawSourceMap,\n  DecodedSourceMap,\n  SourceMapInput,\n  SourceMapLoader,\n  LoaderContext,\n  Options,\n} from './types';\nexport type { SourceMap };\n\n/**\n * Traces through all the mappings in the root sourcemap, through the sources\n * (and their sourcemaps), all the way back to the original source location.\n *\n * `loader` will be called every time we encounter a source file. If it returns\n * a sourcemap, we will recurse into that sourcemap to continue the trace. If\n * it returns a falsey value, that source file is treated as an original,\n * unmodified source file.\n *\n * Pass `excludeContent` to exclude any self-containing source file content\n * from the output sourcemap.\n *\n * Pass `decodedMappings` to receive a SourceMap with decoded (instead of\n * VLQ encoded) mappings.\n */\nexport default function remapping(\n  input: SourceMapInput | SourceMapInput[],\n  loader: SourceMapLoader,\n  options?: boolean | Options\n): SourceMap {\n  const opts =\n    typeof options === 'object' ? options : { excludeContent: !!options, decodedMappings: false };\n  const tree = buildSourceMapTree(input, loader);\n  return new SourceMap(traceMappings(tree), opts);\n}\n"], "names": [], "mappings": ";;;AAgCA,MAAM,kBAAkB,mBAAmB,aAAa,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACtF,MAAM,aAAa,GAAc,EAAE,CAAC;AAEpC,SAAS,aAAa,CACpB,MAAc,EACd,IAAY,EACZ,MAAc,EACd,IAAY,EACZ,OAAsB,EACtB,MAAe,EAAA;AAEf,IAAA,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;AACzD,CAAC;AAgBD,SAAS,MAAM,CACb,GAAoB,EACpB,OAAkB,EAClB,MAAmB,EACnB,OAAsB,EACtB,MAAe,EAAA;IAEf,OAAO;QACL,GAAG;QACH,OAAO;QACP,MAAM;QACN,OAAO;QACP,MAAM;KACA,CAAC;AACX,CAAC;AAED;;;AAGG;AACa,SAAA,SAAS,CAAC,GAAa,EAAE,OAAkB,EAAA;AACzD,IAAA,OAAO,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAC/C,CAAC;AAED;;;AAGG;SACa,cAAc,CAC5B,MAAc,EACd,OAAsB,EACtB,MAAe,EAAA;AAEf,IAAA,OAAO,MAAM,CAAC,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AAC9D,CAAC;AAED;;;AAGG;AACG,SAAU,aAAa,CAAC,IAAe,EAAA;;;AAG3C,IAAA,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;IACpD,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAC3C,IAAA,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC;AAC5B,IAAA,MAAM,YAAY,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;AAE1C,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,QAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AAEjC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,YAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC5B,YAAA,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,MAAM,GAAkC,kBAAkB,CAAC;;;AAI/D,YAAA,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;gBACxB,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,gBAAA,MAAM,GAAG,mBAAmB,CAC1B,MAAM,EACN,OAAO,CAAC,CAAC,CAAC,EACV,OAAO,CAAC,CAAC,CAAC,EACV,OAAO,CAAC,MAAM,KAAK,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAClD,CAAC;;;gBAIF,IAAI,MAAM,IAAI,IAAI;oBAAE,SAAS;AAC9B,aAAA;AAED,YAAA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;AAE/D,YAAA,eAAe,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AAC5D,YAAA,IAAI,MAAM,IAAI,OAAO,IAAI,IAAI;AAAE,gBAAA,gBAAgB,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AACtE,YAAA,IAAI,MAAM;AAAE,gBAAA,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AAC1C,SAAA;AACF,KAAA;AAED,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;AAGG;AACG,SAAU,mBAAmB,CACjC,MAAe,EACf,IAAY,EACZ,MAAc,EACd,IAAY,EAAA;AAEZ,IAAA,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;QACf,OAAO,aAAa,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;AACxF,KAAA;AAED,IAAA,MAAM,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;;IAGvD,IAAI,OAAO,IAAI,IAAI;AAAE,QAAA,OAAO,IAAI,CAAC;;;AAGjC,IAAA,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;AAAE,QAAA,OAAO,kBAAkB,CAAC;IAEpD,OAAO,mBAAmB,CACxB,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAC1B,OAAO,CAAC,CAAC,CAAC,EACV,OAAO,CAAC,CAAC,CAAC,EACV,OAAO,CAAC,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAC3D,CAAC;AACJ;;ACpKA,SAAS,OAAO,CAAI,KAAc,EAAA;AAChC,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;AAAE,QAAA,OAAO,KAAK,CAAC;IACvC,OAAO,CAAC,KAAK,CAAC,CAAC;AACjB,CAAC;AAED;;;;;;;;;;AAUG;AACW,SAAU,kBAAkB,CACxC,KAAwC,EACxC,MAAuB,EAAA;IAEvB,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAC5D,IAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAG,CAAC;AAExB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AAC9B,YAAA,MAAM,IAAI,KAAK,CACb,CAAA,mBAAA,EAAsB,CAAC,CAAuC,qCAAA,CAAA;AAC5D,gBAAA,uEAAuE,CAC1E,CAAC;AACH,SAAA;AACF,KAAA;AAED,IAAA,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AACrC,IAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACzC,QAAA,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;AACnC,KAAA;AACD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,KAAK,CACZ,GAAa,EACb,MAAuB,EACvB,QAAgB,EAChB,aAAqB,EAAA;IAErB,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC;AAE5D,IAAA,MAAM,KAAK,GAAG,aAAa,GAAG,CAAC,CAAC;IAChC,MAAM,QAAQ,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,UAAyB,EAAE,CAAS,KAAa;;;;;AAKrF,QAAA,MAAM,GAAG,GAAkB;YACzB,QAAQ;YACR,KAAK;YACL,MAAM,EAAE,UAAU,IAAI,EAAE;AACxB,YAAA,OAAO,EAAE,SAAS;AAClB,YAAA,MAAM,EAAE,SAAS;SAClB,CAAC;;;QAIF,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAE1C,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC;;AAGxC,QAAA,IAAI,SAAS;AAAE,YAAA,OAAO,KAAK,CAAC,IAAI,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;;;;;QAMpF,MAAM,aAAa,GACjB,OAAO,KAAK,SAAS,GAAG,OAAO,GAAG,cAAc,GAAG,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;QAC9E,MAAM,OAAO,GAAG,MAAM,KAAK,SAAS,GAAG,MAAM,GAAG,UAAU,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;QAC5F,OAAO,cAAc,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;AACxD,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,SAAS,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AAClC;;ACnFA;;;AAGG;AACW,MAAO,SAAS,CAAA;IAU5B,WAAY,CAAA,GAAe,EAAE,OAAgB,EAAA;AAC3C,QAAA,MAAM,GAAG,GAAG,OAAO,CAAC,eAAe,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;QAC5E,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;AAC3B,QAAA,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;AACrB,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAiC,CAAC;AACtD,QAAA,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAA2B,CAAC;AAC7C,QAAA,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC,UAAqC,CAAC;AAC5D,QAAA,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC;AAEjC,QAAA,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,OAA+B,CAAC;AACnD,QAAA,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;AAC3B,YAAA,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC,cAA6C,CAAC;AACzE,SAAA;KACF;IAED,QAAQ,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;KAC7B;AACF;;ACpBD;;;;;;;;;;;;;;AAcG;AACqB,SAAA,SAAS,CAC/B,KAAwC,EACxC,MAAuB,EACvB,OAA2B,EAAA;IAE3B,MAAM,IAAI,GACR,OAAO,OAAO,KAAK,QAAQ,GAAG,OAAO,GAAG,EAAE,cAAc,EAAE,CAAC,CAAC,OAAO,EAAE,eAAe,EAAE,KAAK,EAAE,CAAC;IAChG,MAAM,IAAI,GAAG,kBAAkB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAC/C,OAAO,IAAI,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AAClD;;;;"}