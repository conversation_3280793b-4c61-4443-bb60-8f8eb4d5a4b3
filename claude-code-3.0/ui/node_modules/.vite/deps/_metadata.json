{"hash": "5ae6637a", "browserHash": "c0782d41", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "e2ef7a2d", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "f57569e6", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "93afad1a", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "f8e45122", "needsInterop": true}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "711cb15e", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "3950edfa", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "879c90cb", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "55958aa4", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "cd70cdb9", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "3259f0a4", "needsInterop": false}}, "chunks": {"chunk-2PMNUQTO": {"file": "chunk-2PMNUQTO.js"}, "chunk-MFTD2LE2": {"file": "chunk-MFTD2LE2.js"}, "chunk-KGUGL44G": {"file": "chunk-KGUGL44G.js"}, "chunk-WS6WXAIR": {"file": "chunk-WS6WXAIR.js"}}}