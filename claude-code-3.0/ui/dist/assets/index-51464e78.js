var Ue=(s,e,r)=>{if(!e.has(s))throw TypeError("Cannot "+r)};var i=(s,e,r)=>(Ue(s,e,"read from private field"),r?r.call(s):e.get(s)),v=(s,e,r)=>{if(e.has(s))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(s):e.set(s,r)},x=(s,e,r,a)=>(Ue(s,e,"write to private field"),a?a.call(s,r):e.set(s,r),r);var Le=(s,e,r,a)=>({set _(n){x(s,e,n,r)},get _(){return i(s,e,a)}}),R=(s,e,r)=>(Ue(s,e,"access private method"),r);import{r as We,a as Dt,b as zt}from"./vendor-b69f2a9f.js";import{u as Gt,L as Ut,R as _t,a as ue,B as Bt}from"./router-a94a9c26.js";import{c as Kt}from"./charts-75920557.js";import{Z as Me,A as Ze,L as Ht,B as me,M as Ae,a as Je,S as wt,b as Ct,C as he,T as Ye,P as Vt,c as $t,d as Wt,R as Zt,E as Jt,D as Yt}from"./icons-c88b2895.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))a(n);new MutationObserver(n=>{for(const c of n)if(c.type==="childList")for(const o of c.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&a(o)}).observe(document,{childList:!0,subtree:!0});function r(n){const c={};return n.integrity&&(c.integrity=n.integrity),n.referrerPolicy&&(c.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?c.credentials="include":n.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function a(n){if(n.ep)return;n.ep=!0;const c=r(n);fetch(n.href,c)}})();var St={exports:{}},Qe={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xt=We,es=Symbol.for("react.element"),ts=Symbol.for("react.fragment"),ss=Object.prototype.hasOwnProperty,rs=Xt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,as={key:!0,ref:!0,__self:!0,__source:!0};function Pt(s,e,r){var a,n={},c=null,o=null;r!==void 0&&(c=""+r),e.key!==void 0&&(c=""+e.key),e.ref!==void 0&&(o=e.ref);for(a in e)ss.call(e,a)&&!as.hasOwnProperty(a)&&(n[a]=e[a]);if(s&&s.defaultProps)for(a in e=s.defaultProps,e)n[a]===void 0&&(n[a]=e[a]);return{$$typeof:es,type:s,key:c,ref:o,props:n,_owner:rs.current}}Qe.Fragment=ts;Qe.jsx=Pt;Qe.jsxs=Pt;St.exports=Qe;var t=St.exports,Ke={},it=Dt;Ke.createRoot=it.createRoot,Ke.hydrateRoot=it.hydrateRoot;var De=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(s){return this.listeners.add(s),this.onSubscribe(),()=>{this.listeners.delete(s),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},ze=typeof window>"u"||"Deno"in globalThis;function Q(){}function ns(s,e){return typeof s=="function"?s(e):s}function is(s){return typeof s=="number"&&s>=0&&s!==1/0}function cs(s,e){return Math.max(s+(e||0)-Date.now(),0)}function He(s,e){return typeof s=="function"?s(e):s}function ls(s,e){return typeof s=="function"?s(e):s}function ct(s,e){const{type:r="all",exact:a,fetchStatus:n,predicate:c,queryKey:o,stale:l}=s;if(o){if(a){if(e.queryHash!==Xe(o,e.options))return!1}else if(!Re(e.queryKey,o))return!1}if(r!=="all"){const h=e.isActive();if(r==="active"&&!h||r==="inactive"&&h)return!1}return!(typeof l=="boolean"&&e.isStale()!==l||n&&n!==e.state.fetchStatus||c&&!c(e))}function lt(s,e){const{exact:r,status:a,predicate:n,mutationKey:c}=s;if(c){if(!e.options.mutationKey)return!1;if(r){if(Oe(e.options.mutationKey)!==Oe(c))return!1}else if(!Re(e.options.mutationKey,c))return!1}return!(a&&e.state.status!==a||n&&!n(e))}function Xe(s,e){return((e==null?void 0:e.queryKeyHashFn)||Oe)(s)}function Oe(s){return JSON.stringify(s,(e,r)=>Ve(r)?Object.keys(r).sort().reduce((a,n)=>(a[n]=r[n],a),{}):r)}function Re(s,e){return s===e?!0:typeof s!=typeof e?!1:s&&e&&typeof s=="object"&&typeof e=="object"?Object.keys(e).every(r=>Re(s[r],e[r])):!1}function At(s,e){if(s===e)return s;const r=ot(s)&&ot(e);if(r||Ve(s)&&Ve(e)){const a=r?s:Object.keys(s),n=a.length,c=r?e:Object.keys(e),o=c.length,l=r?[]:{},h=new Set(a);let y=0;for(let b=0;b<o;b++){const p=r?b:c[b];(!r&&h.has(p)||r)&&s[p]===void 0&&e[p]===void 0?(l[p]=void 0,y++):(l[p]=At(s[p],e[p]),l[p]===s[p]&&s[p]!==void 0&&y++)}return n===o&&y===n?s:l}return e}function ot(s){return Array.isArray(s)&&s.length===Object.keys(s).length}function Ve(s){if(!dt(s))return!1;const e=s.constructor;if(e===void 0)return!0;const r=e.prototype;return!(!dt(r)||!r.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(s)!==Object.prototype)}function dt(s){return Object.prototype.toString.call(s)==="[object Object]"}function os(s){return new Promise(e=>{setTimeout(e,s)})}function ds(s,e,r){return typeof r.structuralSharing=="function"?r.structuralSharing(s,e):r.structuralSharing!==!1?At(s,e):e}function us(s,e,r=0){const a=[...s,e];return r&&a.length>r?a.slice(1):a}function hs(s,e,r=0){const a=[e,...s];return r&&a.length>r?a.slice(0,-1):a}var et=Symbol();function Mt(s,e){return!s.queryFn&&(e!=null&&e.initialPromise)?()=>e.initialPromise:!s.queryFn||s.queryFn===et?()=>Promise.reject(new Error(`Missing queryFn: '${s.queryHash}'`)):s.queryFn}var ae,J,fe,ft,ms=(ft=class extends De{constructor(){super();v(this,ae,void 0);v(this,J,void 0);v(this,fe,void 0);x(this,fe,e=>{if(!ze&&window.addEventListener){const r=()=>e();return window.addEventListener("visibilitychange",r,!1),()=>{window.removeEventListener("visibilitychange",r)}}})}onSubscribe(){i(this,J)||this.setEventListener(i(this,fe))}onUnsubscribe(){var e;this.hasListeners()||((e=i(this,J))==null||e.call(this),x(this,J,void 0))}setEventListener(e){var r;x(this,fe,e),(r=i(this,J))==null||r.call(this),x(this,J,e(a=>{typeof a=="boolean"?this.setFocused(a):this.onFocus()}))}setFocused(e){i(this,ae)!==e&&(x(this,ae,e),this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach(r=>{r(e)})}isFocused(){var e;return typeof i(this,ae)=="boolean"?i(this,ae):((e=globalThis.document)==null?void 0:e.visibilityState)!=="hidden"}},ae=new WeakMap,J=new WeakMap,fe=new WeakMap,ft),Ot=new ms,pe,Y,ge,pt,xs=(pt=class extends De{constructor(){super();v(this,pe,!0);v(this,Y,void 0);v(this,ge,void 0);x(this,ge,e=>{if(!ze&&window.addEventListener){const r=()=>e(!0),a=()=>e(!1);return window.addEventListener("online",r,!1),window.addEventListener("offline",a,!1),()=>{window.removeEventListener("online",r),window.removeEventListener("offline",a)}}})}onSubscribe(){i(this,Y)||this.setEventListener(i(this,ge))}onUnsubscribe(){var e;this.hasListeners()||((e=i(this,Y))==null||e.call(this),x(this,Y,void 0))}setEventListener(e){var r;x(this,ge,e),(r=i(this,Y))==null||r.call(this),x(this,Y,e(this.setOnline.bind(this)))}setOnline(e){i(this,pe)!==e&&(x(this,pe,e),this.listeners.forEach(a=>{a(e)}))}isOnline(){return i(this,pe)}},pe=new WeakMap,Y=new WeakMap,ge=new WeakMap,pt),Ie=new xs;function fs(){let s,e;const r=new Promise((n,c)=>{s=n,e=c});r.status="pending",r.catch(()=>{});function a(n){Object.assign(r,n),delete r.resolve,delete r.reject}return r.resolve=n=>{a({status:"fulfilled",value:n}),s(n)},r.reject=n=>{a({status:"rejected",reason:n}),e(n)},r}function ps(s){return Math.min(1e3*2**s,3e4)}function Rt(s){return(s??"online")==="online"?Ie.isOnline():!0}var kt=class extends Error{constructor(s){super("CancelledError"),this.revert=s==null?void 0:s.revert,this.silent=s==null?void 0:s.silent}};function _e(s){return s instanceof kt}function Ft(s){let e=!1,r=0,a=!1,n;const c=fs(),o=d=>{var u;a||(m(new kt(d)),(u=s.abort)==null||u.call(s))},l=()=>{e=!0},h=()=>{e=!1},y=()=>Ot.isFocused()&&(s.networkMode==="always"||Ie.isOnline())&&s.canRun(),b=()=>Rt(s.networkMode)&&s.canRun(),p=d=>{var u;a||(a=!0,(u=s.onSuccess)==null||u.call(s,d),n==null||n(),c.resolve(d))},m=d=>{var u;a||(a=!0,(u=s.onError)==null||u.call(s,d),n==null||n(),c.reject(d))},j=()=>new Promise(d=>{var u;n=g=>{(a||y())&&d(g)},(u=s.onPause)==null||u.call(s)}).then(()=>{var d;n=void 0,a||(d=s.onContinue)==null||d.call(s)}),N=()=>{if(a)return;let d;const u=r===0?s.initialPromise:void 0;try{d=u??s.fn()}catch(g){d=Promise.reject(g)}Promise.resolve(d).then(p).catch(g=>{var T;if(a)return;const C=s.retry??(ze?0:3),S=s.retryDelay??ps,O=typeof S=="function"?S(r,g):S,F=C===!0||typeof C=="number"&&r<C||typeof C=="function"&&C(r,g);if(e||!F){m(g);return}r++,(T=s.onFail)==null||T.call(s,r,g),os(O).then(()=>y()?void 0:j()).then(()=>{e?m(g):N()})})};return{promise:c,cancel:o,continue:()=>(n==null||n(),c),cancelRetry:l,continueRetry:h,canStart:b,start:()=>(b()?N():j().then(N),c)}}var gs=s=>setTimeout(s,0);function ys(){let s=[],e=0,r=l=>{l()},a=l=>{l()},n=gs;const c=l=>{e?s.push(l):n(()=>{r(l)})},o=()=>{const l=s;s=[],l.length&&n(()=>{a(()=>{l.forEach(h=>{r(h)})})})};return{batch:l=>{let h;e++;try{h=l()}finally{e--,e||o()}return h},batchCalls:l=>(...h)=>{c(()=>{l(...h)})},schedule:c,setNotifyFunction:l=>{r=l},setBatchNotifyFunction:l=>{a=l},setScheduler:l=>{n=l}}}var q=ys(),ne,gt,Et=(gt=class{constructor(){v(this,ne,void 0)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),is(this.gcTime)&&x(this,ne,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(s){this.gcTime=Math.max(this.gcTime||0,s??(ze?1/0:5*60*1e3))}clearGcTimeout(){i(this,ne)&&(clearTimeout(i(this,ne)),x(this,ne,void 0))}},ne=new WeakMap,gt),ye,ie,L,ce,k,ke,le,D,H,yt,bs=(yt=class extends Et{constructor(e){super();v(this,D);v(this,ye,void 0);v(this,ie,void 0);v(this,L,void 0);v(this,ce,void 0);v(this,k,void 0);v(this,ke,void 0);v(this,le,void 0);x(this,le,!1),x(this,ke,e.defaultOptions),this.setOptions(e.options),this.observers=[],x(this,ce,e.client),x(this,L,i(this,ce).getQueryCache()),this.queryKey=e.queryKey,this.queryHash=e.queryHash,x(this,ye,js(this.options)),this.state=e.state??i(this,ye),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var e;return(e=i(this,k))==null?void 0:e.promise}setOptions(e){this.options={...i(this,ke),...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&i(this,L).remove(this)}setData(e,r){const a=ds(this.state.data,e,this.options);return R(this,D,H).call(this,{data:a,type:"success",dataUpdatedAt:r==null?void 0:r.updatedAt,manual:r==null?void 0:r.manual}),a}setState(e,r){R(this,D,H).call(this,{type:"setState",state:e,setStateOptions:r})}cancel(e){var a,n;const r=(a=i(this,k))==null?void 0:a.promise;return(n=i(this,k))==null||n.cancel(e),r?r.then(Q).catch(Q):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(i(this,ye))}isActive(){return this.observers.some(e=>ls(e.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===et||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(e=>He(e.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(e=0){return this.state.data===void 0?!0:e==="static"?!1:this.state.isInvalidated?!0:!cs(this.state.dataUpdatedAt,e)}onFocus(){var r;const e=this.observers.find(a=>a.shouldFetchOnWindowFocus());e==null||e.refetch({cancelRefetch:!1}),(r=i(this,k))==null||r.continue()}onOnline(){var r;const e=this.observers.find(a=>a.shouldFetchOnReconnect());e==null||e.refetch({cancelRefetch:!1}),(r=i(this,k))==null||r.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),i(this,L).notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(r=>r!==e),this.observers.length||(i(this,k)&&(i(this,le)?i(this,k).cancel({revert:!0}):i(this,k).cancelRetry()),this.scheduleGc()),i(this,L).notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||R(this,D,H).call(this,{type:"invalidate"})}fetch(e,r){var y,b,p;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(r!=null&&r.cancelRefetch))this.cancel({silent:!0});else if(i(this,k))return i(this,k).continueRetry(),i(this,k).promise}if(e&&this.setOptions(e),!this.options.queryFn){const m=this.observers.find(j=>j.options.queryFn);m&&this.setOptions(m.options)}const a=new AbortController,n=m=>{Object.defineProperty(m,"signal",{enumerable:!0,get:()=>(x(this,le,!0),a.signal)})},c=()=>{const m=Mt(this.options,r),N=(()=>{const d={client:i(this,ce),queryKey:this.queryKey,meta:this.meta};return n(d),d})();return x(this,le,!1),this.options.persister?this.options.persister(m,N,this):m(N)},l=(()=>{const m={fetchOptions:r,options:this.options,queryKey:this.queryKey,client:i(this,ce),state:this.state,fetchFn:c};return n(m),m})();(y=this.options.behavior)==null||y.onFetch(l,this),x(this,ie,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((b=l.fetchOptions)==null?void 0:b.meta))&&R(this,D,H).call(this,{type:"fetch",meta:(p=l.fetchOptions)==null?void 0:p.meta});const h=m=>{var j,N,d,u;_e(m)&&m.silent||R(this,D,H).call(this,{type:"error",error:m}),_e(m)||((N=(j=i(this,L).config).onError)==null||N.call(j,m,this),(u=(d=i(this,L).config).onSettled)==null||u.call(d,this.state.data,m,this)),this.scheduleGc()};return x(this,k,Ft({initialPromise:r==null?void 0:r.initialPromise,fn:l.fetchFn,abort:a.abort.bind(a),onSuccess:m=>{var j,N,d,u;if(m===void 0){h(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(m)}catch(g){h(g);return}(N=(j=i(this,L).config).onSuccess)==null||N.call(j,m,this),(u=(d=i(this,L).config).onSettled)==null||u.call(d,m,this.state.error,this),this.scheduleGc()},onError:h,onFail:(m,j)=>{R(this,D,H).call(this,{type:"failed",failureCount:m,error:j})},onPause:()=>{R(this,D,H).call(this,{type:"pause"})},onContinue:()=>{R(this,D,H).call(this,{type:"continue"})},retry:l.options.retry,retryDelay:l.options.retryDelay,networkMode:l.options.networkMode,canRun:()=>!0})),i(this,k).start()}},ye=new WeakMap,ie=new WeakMap,L=new WeakMap,ce=new WeakMap,k=new WeakMap,ke=new WeakMap,le=new WeakMap,D=new WeakSet,H=function(e){const r=a=>{switch(e.type){case"failed":return{...a,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...a,fetchStatus:"paused"};case"continue":return{...a,fetchStatus:"fetching"};case"fetch":return{...a,...vs(a.data,this.options),fetchMeta:e.meta??null};case"success":return x(this,ie,void 0),{...a,data:e.data,dataUpdateCount:a.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const n=e.error;return _e(n)&&n.revert&&i(this,ie)?{...i(this,ie),fetchStatus:"idle"}:{...a,error:n,errorUpdateCount:a.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:a.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...a,isInvalidated:!0};case"setState":return{...a,...e.state}}};this.state=r(this.state),q.batch(()=>{this.observers.forEach(a=>{a.onQueryUpdate()}),i(this,L).notify({query:this,type:"updated",action:e})})},yt);function vs(s,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Rt(e.networkMode)?"fetching":"paused",...s===void 0&&{error:null,status:"pending"}}}function js(s){const e=typeof s.initialData=="function"?s.initialData():s.initialData,r=e!==void 0,a=r?typeof s.initialDataUpdatedAt=="function"?s.initialDataUpdatedAt():s.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:r?a??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}var G,bt,Ns=(bt=class extends De{constructor(e={}){super();v(this,G,void 0);this.config=e,x(this,G,new Map)}build(e,r,a){const n=r.queryKey,c=r.queryHash??Xe(n,r);let o=this.get(c);return o||(o=new bs({client:e,queryKey:n,queryHash:c,options:e.defaultQueryOptions(r),state:a,defaultOptions:e.getQueryDefaults(n)}),this.add(o)),o}add(e){i(this,G).has(e.queryHash)||(i(this,G).set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const r=i(this,G).get(e.queryHash);r&&(e.destroy(),r===e&&i(this,G).delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){q.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return i(this,G).get(e)}getAll(){return[...i(this,G).values()]}find(e){const r={exact:!0,...e};return this.getAll().find(a=>ct(r,a))}findAll(e={}){const r=this.getAll();return Object.keys(e).length>0?r.filter(a=>ct(e,a)):r}notify(e){q.batch(()=>{this.listeners.forEach(r=>{r(e)})})}onFocus(){q.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){q.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},G=new WeakMap,bt),U,E,oe,_,Z,vt,ws=(vt=class extends Et{constructor(e){super();v(this,_);v(this,U,void 0);v(this,E,void 0);v(this,oe,void 0);this.mutationId=e.mutationId,x(this,E,e.mutationCache),x(this,U,[]),this.state=e.state||Cs(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){i(this,U).includes(e)||(i(this,U).push(e),this.clearGcTimeout(),i(this,E).notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){x(this,U,i(this,U).filter(r=>r!==e)),this.scheduleGc(),i(this,E).notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){i(this,U).length||(this.state.status==="pending"?this.scheduleGc():i(this,E).remove(this))}continue(){var e;return((e=i(this,oe))==null?void 0:e.continue())??this.execute(this.state.variables)}async execute(e){var c,o,l,h,y,b,p,m,j,N,d,u,g,C,S,O,F,T,se,re;const r=()=>{R(this,_,Z).call(this,{type:"continue"})};x(this,oe,Ft({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(A,I)=>{R(this,_,Z).call(this,{type:"failed",failureCount:A,error:I})},onPause:()=>{R(this,_,Z).call(this,{type:"pause"})},onContinue:r,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>i(this,E).canRun(this)}));const a=this.state.status==="pending",n=!i(this,oe).canStart();try{if(a)r();else{R(this,_,Z).call(this,{type:"pending",variables:e,isPaused:n}),await((o=(c=i(this,E).config).onMutate)==null?void 0:o.call(c,e,this));const I=await((h=(l=this.options).onMutate)==null?void 0:h.call(l,e));I!==this.state.context&&R(this,_,Z).call(this,{type:"pending",context:I,variables:e,isPaused:n})}const A=await i(this,oe).start();return await((b=(y=i(this,E).config).onSuccess)==null?void 0:b.call(y,A,e,this.state.context,this)),await((m=(p=this.options).onSuccess)==null?void 0:m.call(p,A,e,this.state.context)),await((N=(j=i(this,E).config).onSettled)==null?void 0:N.call(j,A,null,this.state.variables,this.state.context,this)),await((u=(d=this.options).onSettled)==null?void 0:u.call(d,A,null,e,this.state.context)),R(this,_,Z).call(this,{type:"success",data:A}),A}catch(A){try{throw await((C=(g=i(this,E).config).onError)==null?void 0:C.call(g,A,e,this.state.context,this)),await((O=(S=this.options).onError)==null?void 0:O.call(S,A,e,this.state.context)),await((T=(F=i(this,E).config).onSettled)==null?void 0:T.call(F,void 0,A,this.state.variables,this.state.context,this)),await((re=(se=this.options).onSettled)==null?void 0:re.call(se,void 0,A,e,this.state.context)),A}finally{R(this,_,Z).call(this,{type:"error",error:A})}}finally{i(this,E).runNext(this)}}},U=new WeakMap,E=new WeakMap,oe=new WeakMap,_=new WeakSet,Z=function(e){const r=a=>{switch(e.type){case"failed":return{...a,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...a,isPaused:!0};case"continue":return{...a,isPaused:!1};case"pending":return{...a,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...a,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...a,data:void 0,error:e.error,failureCount:a.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}};this.state=r(this.state),q.batch(()=>{i(this,U).forEach(a=>{a.onMutationUpdate(e)}),i(this,E).notify({mutation:this,type:"updated",action:e})})},vt);function Cs(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var V,z,Fe,jt,Ss=(jt=class extends De{constructor(e={}){super();v(this,V,void 0);v(this,z,void 0);v(this,Fe,void 0);this.config=e,x(this,V,new Set),x(this,z,new Map),x(this,Fe,0)}build(e,r,a){const n=new ws({mutationCache:this,mutationId:++Le(this,Fe)._,options:e.defaultMutationOptions(r),state:a});return this.add(n),n}add(e){i(this,V).add(e);const r=Te(e);if(typeof r=="string"){const a=i(this,z).get(r);a?a.push(e):i(this,z).set(r,[e])}this.notify({type:"added",mutation:e})}remove(e){if(i(this,V).delete(e)){const r=Te(e);if(typeof r=="string"){const a=i(this,z).get(r);if(a)if(a.length>1){const n=a.indexOf(e);n!==-1&&a.splice(n,1)}else a[0]===e&&i(this,z).delete(r)}}this.notify({type:"removed",mutation:e})}canRun(e){const r=Te(e);if(typeof r=="string"){const a=i(this,z).get(r),n=a==null?void 0:a.find(c=>c.state.status==="pending");return!n||n===e}else return!0}runNext(e){var a;const r=Te(e);if(typeof r=="string"){const n=(a=i(this,z).get(r))==null?void 0:a.find(c=>c!==e&&c.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}else return Promise.resolve()}clear(){q.batch(()=>{i(this,V).forEach(e=>{this.notify({type:"removed",mutation:e})}),i(this,V).clear(),i(this,z).clear()})}getAll(){return Array.from(i(this,V))}find(e){const r={exact:!0,...e};return this.getAll().find(a=>lt(r,a))}findAll(e={}){return this.getAll().filter(r=>lt(e,r))}notify(e){q.batch(()=>{this.listeners.forEach(r=>{r(e)})})}resumePausedMutations(){const e=this.getAll().filter(r=>r.state.isPaused);return q.batch(()=>Promise.all(e.map(r=>r.continue().catch(Q))))}},V=new WeakMap,z=new WeakMap,Fe=new WeakMap,jt);function Te(s){var e;return(e=s.options.scope)==null?void 0:e.id}function ut(s){return{onFetch:(e,r)=>{var b,p,m,j,N;const a=e.options,n=(m=(p=(b=e.fetchOptions)==null?void 0:b.meta)==null?void 0:p.fetchMore)==null?void 0:m.direction,c=((j=e.state.data)==null?void 0:j.pages)||[],o=((N=e.state.data)==null?void 0:N.pageParams)||[];let l={pages:[],pageParams:[]},h=0;const y=async()=>{let d=!1;const u=S=>{Object.defineProperty(S,"signal",{enumerable:!0,get:()=>(e.signal.aborted?d=!0:e.signal.addEventListener("abort",()=>{d=!0}),e.signal)})},g=Mt(e.options,e.fetchOptions),C=async(S,O,F)=>{if(d)return Promise.reject();if(O==null&&S.pages.length)return Promise.resolve(S);const se=(()=>{const de={client:e.client,queryKey:e.queryKey,pageParam:O,direction:F?"backward":"forward",meta:e.options.meta};return u(de),de})(),re=await g(se),{maxPages:A}=e.options,I=F?hs:us;return{pages:I(S.pages,re,A),pageParams:I(S.pageParams,O,A)}};if(n&&c.length){const S=n==="backward",O=S?Ps:ht,F={pages:c,pageParams:o},T=O(a,F);l=await C(F,T,S)}else{const S=s??c.length;do{const O=h===0?o[0]??a.initialPageParam:ht(a,l);if(h>0&&O==null)break;l=await C(l,O),h++}while(h<S)}return l};e.options.persister?e.fetchFn=()=>{var d,u;return(u=(d=e.options).persister)==null?void 0:u.call(d,y,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},r)}:e.fetchFn=y}}}function ht(s,{pages:e,pageParams:r}){const a=e.length-1;return e.length>0?s.getNextPageParam(e[a],e,r[a],r):void 0}function Ps(s,{pages:e,pageParams:r}){var a;return e.length>0?(a=s.getPreviousPageParam)==null?void 0:a.call(s,e[0],e,r[0],r):void 0}var M,X,ee,be,ve,te,je,Ne,Nt,As=(Nt=class{constructor(s={}){v(this,M,void 0);v(this,X,void 0);v(this,ee,void 0);v(this,be,void 0);v(this,ve,void 0);v(this,te,void 0);v(this,je,void 0);v(this,Ne,void 0);x(this,M,s.queryCache||new Ns),x(this,X,s.mutationCache||new Ss),x(this,ee,s.defaultOptions||{}),x(this,be,new Map),x(this,ve,new Map),x(this,te,0)}mount(){Le(this,te)._++,i(this,te)===1&&(x(this,je,Ot.subscribe(async s=>{s&&(await this.resumePausedMutations(),i(this,M).onFocus())})),x(this,Ne,Ie.subscribe(async s=>{s&&(await this.resumePausedMutations(),i(this,M).onOnline())})))}unmount(){var s,e;Le(this,te)._--,i(this,te)===0&&((s=i(this,je))==null||s.call(this),x(this,je,void 0),(e=i(this,Ne))==null||e.call(this),x(this,Ne,void 0))}isFetching(s){return i(this,M).findAll({...s,fetchStatus:"fetching"}).length}isMutating(s){return i(this,X).findAll({...s,status:"pending"}).length}getQueryData(s){var r;const e=this.defaultQueryOptions({queryKey:s});return(r=i(this,M).get(e.queryHash))==null?void 0:r.state.data}ensureQueryData(s){const e=this.defaultQueryOptions(s),r=i(this,M).build(this,e),a=r.state.data;return a===void 0?this.fetchQuery(s):(s.revalidateIfStale&&r.isStaleByTime(He(e.staleTime,r))&&this.prefetchQuery(e),Promise.resolve(a))}getQueriesData(s){return i(this,M).findAll(s).map(({queryKey:e,state:r})=>{const a=r.data;return[e,a]})}setQueryData(s,e,r){const a=this.defaultQueryOptions({queryKey:s}),n=i(this,M).get(a.queryHash),c=n==null?void 0:n.state.data,o=ns(e,c);if(o!==void 0)return i(this,M).build(this,a).setData(o,{...r,manual:!0})}setQueriesData(s,e,r){return q.batch(()=>i(this,M).findAll(s).map(({queryKey:a})=>[a,this.setQueryData(a,e,r)]))}getQueryState(s){var r;const e=this.defaultQueryOptions({queryKey:s});return(r=i(this,M).get(e.queryHash))==null?void 0:r.state}removeQueries(s){const e=i(this,M);q.batch(()=>{e.findAll(s).forEach(r=>{e.remove(r)})})}resetQueries(s,e){const r=i(this,M);return q.batch(()=>(r.findAll(s).forEach(a=>{a.reset()}),this.refetchQueries({type:"active",...s},e)))}cancelQueries(s,e={}){const r={revert:!0,...e},a=q.batch(()=>i(this,M).findAll(s).map(n=>n.cancel(r)));return Promise.all(a).then(Q).catch(Q)}invalidateQueries(s,e={}){return q.batch(()=>(i(this,M).findAll(s).forEach(r=>{r.invalidate()}),(s==null?void 0:s.refetchType)==="none"?Promise.resolve():this.refetchQueries({...s,type:(s==null?void 0:s.refetchType)??(s==null?void 0:s.type)??"active"},e)))}refetchQueries(s,e={}){const r={...e,cancelRefetch:e.cancelRefetch??!0},a=q.batch(()=>i(this,M).findAll(s).filter(n=>!n.isDisabled()&&!n.isStatic()).map(n=>{let c=n.fetch(void 0,r);return r.throwOnError||(c=c.catch(Q)),n.state.fetchStatus==="paused"?Promise.resolve():c}));return Promise.all(a).then(Q)}fetchQuery(s){const e=this.defaultQueryOptions(s);e.retry===void 0&&(e.retry=!1);const r=i(this,M).build(this,e);return r.isStaleByTime(He(e.staleTime,r))?r.fetch(e):Promise.resolve(r.state.data)}prefetchQuery(s){return this.fetchQuery(s).then(Q).catch(Q)}fetchInfiniteQuery(s){return s.behavior=ut(s.pages),this.fetchQuery(s)}prefetchInfiniteQuery(s){return this.fetchInfiniteQuery(s).then(Q).catch(Q)}ensureInfiniteQueryData(s){return s.behavior=ut(s.pages),this.ensureQueryData(s)}resumePausedMutations(){return Ie.isOnline()?i(this,X).resumePausedMutations():Promise.resolve()}getQueryCache(){return i(this,M)}getMutationCache(){return i(this,X)}getDefaultOptions(){return i(this,ee)}setDefaultOptions(s){x(this,ee,s)}setQueryDefaults(s,e){i(this,be).set(Oe(s),{queryKey:s,defaultOptions:e})}getQueryDefaults(s){const e=[...i(this,be).values()],r={};return e.forEach(a=>{Re(s,a.queryKey)&&Object.assign(r,a.defaultOptions)}),r}setMutationDefaults(s,e){i(this,ve).set(Oe(s),{mutationKey:s,defaultOptions:e})}getMutationDefaults(s){const e=[...i(this,ve).values()],r={};return e.forEach(a=>{Re(s,a.mutationKey)&&Object.assign(r,a.defaultOptions)}),r}defaultQueryOptions(s){if(s._defaulted)return s;const e={...i(this,ee).queries,...this.getQueryDefaults(s.queryKey),...s,_defaulted:!0};return e.queryHash||(e.queryHash=Xe(e.queryKey,e)),e.refetchOnReconnect===void 0&&(e.refetchOnReconnect=e.networkMode!=="always"),e.throwOnError===void 0&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===et&&(e.enabled=!1),e}defaultMutationOptions(s){return s!=null&&s._defaulted?s:{...i(this,ee).mutations,...(s==null?void 0:s.mutationKey)&&this.getMutationDefaults(s.mutationKey),...s,_defaulted:!0}}clear(){i(this,M).clear(),i(this,X).clear()}},M=new WeakMap,X=new WeakMap,ee=new WeakMap,be=new WeakMap,ve=new WeakMap,te=new WeakMap,je=new WeakMap,Ne=new WeakMap,Nt),Ms=We.createContext(void 0),Os=({client:s,children:e})=>(We.useEffect(()=>(s.mount(),()=>{s.unmount()}),[s]),t.jsx(Ms.Provider,{value:s,children:e}));const tt="-",Rs=s=>{const e=Fs(s),{conflictingClassGroups:r,conflictingClassGroupModifiers:a}=s;return{getClassGroupId:o=>{const l=o.split(tt);return l[0]===""&&l.length!==1&&l.shift(),qt(l,e)||ks(o)},getConflictingClassGroupIds:(o,l)=>{const h=r[o]||[];return l&&a[o]?[...h,...a[o]]:h}}},qt=(s,e)=>{var o;if(s.length===0)return e.classGroupId;const r=s[0],a=e.nextPart.get(r),n=a?qt(s.slice(1),a):void 0;if(n)return n;if(e.validators.length===0)return;const c=s.join(tt);return(o=e.validators.find(({validator:l})=>l(c)))==null?void 0:o.classGroupId},mt=/^\[(.+)\]$/,ks=s=>{if(mt.test(s)){const e=mt.exec(s)[1],r=e==null?void 0:e.substring(0,e.indexOf(":"));if(r)return"arbitrary.."+r}},Fs=s=>{const{theme:e,prefix:r}=s,a={nextPart:new Map,validators:[]};return qs(Object.entries(s.classGroups),r).forEach(([c,o])=>{$e(o,a,c,e)}),a},$e=(s,e,r,a)=>{s.forEach(n=>{if(typeof n=="string"){const c=n===""?e:xt(e,n);c.classGroupId=r;return}if(typeof n=="function"){if(Es(n)){$e(n(a),e,r,a);return}e.validators.push({validator:n,classGroupId:r});return}Object.entries(n).forEach(([c,o])=>{$e(o,xt(e,c),r,a)})})},xt=(s,e)=>{let r=s;return e.split(tt).forEach(a=>{r.nextPart.has(a)||r.nextPart.set(a,{nextPart:new Map,validators:[]}),r=r.nextPart.get(a)}),r},Es=s=>s.isThemeGetter,qs=(s,e)=>e?s.map(([r,a])=>{const n=a.map(c=>typeof c=="string"?e+c:typeof c=="object"?Object.fromEntries(Object.entries(c).map(([o,l])=>[e+o,l])):c);return[r,n]}):s,Ls=s=>{if(s<1)return{get:()=>{},set:()=>{}};let e=0,r=new Map,a=new Map;const n=(c,o)=>{r.set(c,o),e++,e>s&&(e=0,a=r,r=new Map)};return{get(c){let o=r.get(c);if(o!==void 0)return o;if((o=a.get(c))!==void 0)return n(c,o),o},set(c,o){r.has(c)?r.set(c,o):n(c,o)}}},Lt="!",Ts=s=>{const{separator:e,experimentalParseClassName:r}=s,a=e.length===1,n=e[0],c=e.length,o=l=>{const h=[];let y=0,b=0,p;for(let u=0;u<l.length;u++){let g=l[u];if(y===0){if(g===n&&(a||l.slice(u,u+c)===e)){h.push(l.slice(b,u)),b=u+c;continue}if(g==="/"){p=u;continue}}g==="["?y++:g==="]"&&y--}const m=h.length===0?l:l.substring(b),j=m.startsWith(Lt),N=j?m.substring(1):m,d=p&&p>b?p-b:void 0;return{modifiers:h,hasImportantModifier:j,baseClassName:N,maybePostfixModifierPosition:d}};return r?l=>r({className:l,parseClassName:o}):o},Is=s=>{if(s.length<=1)return s;const e=[];let r=[];return s.forEach(a=>{a[0]==="["?(e.push(...r.sort(),a),r=[]):r.push(a)}),e.push(...r.sort()),e},Qs=s=>({cache:Ls(s.cacheSize),parseClassName:Ts(s),...Rs(s)}),Ds=/\s+/,zs=(s,e)=>{const{parseClassName:r,getClassGroupId:a,getConflictingClassGroupIds:n}=e,c=[],o=s.trim().split(Ds);let l="";for(let h=o.length-1;h>=0;h-=1){const y=o[h],{modifiers:b,hasImportantModifier:p,baseClassName:m,maybePostfixModifierPosition:j}=r(y);let N=!!j,d=a(N?m.substring(0,j):m);if(!d){if(!N){l=y+(l.length>0?" "+l:l);continue}if(d=a(m),!d){l=y+(l.length>0?" "+l:l);continue}N=!1}const u=Is(b).join(":"),g=p?u+Lt:u,C=g+d;if(c.includes(C))continue;c.push(C);const S=n(d,N);for(let O=0;O<S.length;++O){const F=S[O];c.push(g+F)}l=y+(l.length>0?" "+l:l)}return l};function Gs(){let s=0,e,r,a="";for(;s<arguments.length;)(e=arguments[s++])&&(r=Tt(e))&&(a&&(a+=" "),a+=r);return a}const Tt=s=>{if(typeof s=="string")return s;let e,r="";for(let a=0;a<s.length;a++)s[a]&&(e=Tt(s[a]))&&(r&&(r+=" "),r+=e);return r};function Us(s,...e){let r,a,n,c=o;function o(h){const y=e.reduce((b,p)=>p(b),s());return r=Qs(y),a=r.cache.get,n=r.cache.set,c=l,l(h)}function l(h){const y=a(h);if(y)return y;const b=zs(h,r);return n(h,b),b}return function(){return c(Gs.apply(null,arguments))}}const w=s=>{const e=r=>r[s]||[];return e.isThemeGetter=!0,e},It=/^\[(?:([a-z-]+):)?(.+)\]$/i,_s=/^\d+\/\d+$/,Bs=new Set(["px","full","screen"]),Ks=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Hs=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Vs=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,$s=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Ws=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,K=s=>xe(s)||Bs.has(s)||_s.test(s),$=s=>we(s,"length",rr),xe=s=>!!s&&!Number.isNaN(Number(s)),Be=s=>we(s,"number",xe),Se=s=>!!s&&Number.isInteger(Number(s)),Zs=s=>s.endsWith("%")&&xe(s.slice(0,-1)),f=s=>It.test(s),W=s=>Ks.test(s),Js=new Set(["length","size","percentage"]),Ys=s=>we(s,Js,Qt),Xs=s=>we(s,"position",Qt),er=new Set(["image","url"]),tr=s=>we(s,er,nr),sr=s=>we(s,"",ar),Pe=()=>!0,we=(s,e,r)=>{const a=It.exec(s);return a?a[1]?typeof e=="string"?a[1]===e:e.has(a[1]):r(a[2]):!1},rr=s=>Hs.test(s)&&!Vs.test(s),Qt=()=>!1,ar=s=>$s.test(s),nr=s=>Ws.test(s),ir=()=>{const s=w("colors"),e=w("spacing"),r=w("blur"),a=w("brightness"),n=w("borderColor"),c=w("borderRadius"),o=w("borderSpacing"),l=w("borderWidth"),h=w("contrast"),y=w("grayscale"),b=w("hueRotate"),p=w("invert"),m=w("gap"),j=w("gradientColorStops"),N=w("gradientColorStopPositions"),d=w("inset"),u=w("margin"),g=w("opacity"),C=w("padding"),S=w("saturate"),O=w("scale"),F=w("sepia"),T=w("skew"),se=w("space"),re=w("translate"),A=()=>["auto","contain","none"],I=()=>["auto","hidden","clip","visible","scroll"],de=()=>["auto",f,e],P=()=>[f,e],st=()=>["",K,$],Ee=()=>["auto",xe,f],rt=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],qe=()=>["solid","dashed","dotted","double","none"],at=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],Ge=()=>["start","end","center","between","around","evenly","stretch"],Ce=()=>["","0",f],nt=()=>["auto","avoid","all","avoid-page","page","left","right","column"],B=()=>[xe,f];return{cacheSize:500,separator:":",theme:{colors:[Pe],spacing:[K,$],blur:["none","",W,f],brightness:B(),borderColor:[s],borderRadius:["none","","full",W,f],borderSpacing:P(),borderWidth:st(),contrast:B(),grayscale:Ce(),hueRotate:B(),invert:Ce(),gap:P(),gradientColorStops:[s],gradientColorStopPositions:[Zs,$],inset:de(),margin:de(),opacity:B(),padding:P(),saturate:B(),scale:B(),sepia:Ce(),skew:B(),space:P(),translate:P()},classGroups:{aspect:[{aspect:["auto","square","video",f]}],container:["container"],columns:[{columns:[W]}],"break-after":[{"break-after":nt()}],"break-before":[{"break-before":nt()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...rt(),f]}],overflow:[{overflow:I()}],"overflow-x":[{"overflow-x":I()}],"overflow-y":[{"overflow-y":I()}],overscroll:[{overscroll:A()}],"overscroll-x":[{"overscroll-x":A()}],"overscroll-y":[{"overscroll-y":A()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[d]}],"inset-x":[{"inset-x":[d]}],"inset-y":[{"inset-y":[d]}],start:[{start:[d]}],end:[{end:[d]}],top:[{top:[d]}],right:[{right:[d]}],bottom:[{bottom:[d]}],left:[{left:[d]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Se,f]}],basis:[{basis:de()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",f]}],grow:[{grow:Ce()}],shrink:[{shrink:Ce()}],order:[{order:["first","last","none",Se,f]}],"grid-cols":[{"grid-cols":[Pe]}],"col-start-end":[{col:["auto",{span:["full",Se,f]},f]}],"col-start":[{"col-start":Ee()}],"col-end":[{"col-end":Ee()}],"grid-rows":[{"grid-rows":[Pe]}],"row-start-end":[{row:["auto",{span:[Se,f]},f]}],"row-start":[{"row-start":Ee()}],"row-end":[{"row-end":Ee()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",f]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",f]}],gap:[{gap:[m]}],"gap-x":[{"gap-x":[m]}],"gap-y":[{"gap-y":[m]}],"justify-content":[{justify:["normal",...Ge()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...Ge(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...Ge(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[C]}],px:[{px:[C]}],py:[{py:[C]}],ps:[{ps:[C]}],pe:[{pe:[C]}],pt:[{pt:[C]}],pr:[{pr:[C]}],pb:[{pb:[C]}],pl:[{pl:[C]}],m:[{m:[u]}],mx:[{mx:[u]}],my:[{my:[u]}],ms:[{ms:[u]}],me:[{me:[u]}],mt:[{mt:[u]}],mr:[{mr:[u]}],mb:[{mb:[u]}],ml:[{ml:[u]}],"space-x":[{"space-x":[se]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[se]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",f,e]}],"min-w":[{"min-w":[f,e,"min","max","fit"]}],"max-w":[{"max-w":[f,e,"none","full","min","max","fit","prose",{screen:[W]},W]}],h:[{h:[f,e,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[f,e,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[f,e,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[f,e,"auto","min","max","fit"]}],"font-size":[{text:["base",W,$]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Be]}],"font-family":[{font:[Pe]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",f]}],"line-clamp":[{"line-clamp":["none",xe,Be]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",K,f]}],"list-image":[{"list-image":["none",f]}],"list-style-type":[{list:["none","disc","decimal",f]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[s]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[s]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...qe(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",K,$]}],"underline-offset":[{"underline-offset":["auto",K,f]}],"text-decoration-color":[{decoration:[s]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:P()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",f]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",f]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...rt(),Xs]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Ys]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},tr]}],"bg-color":[{bg:[s]}],"gradient-from-pos":[{from:[N]}],"gradient-via-pos":[{via:[N]}],"gradient-to-pos":[{to:[N]}],"gradient-from":[{from:[j]}],"gradient-via":[{via:[j]}],"gradient-to":[{to:[j]}],rounded:[{rounded:[c]}],"rounded-s":[{"rounded-s":[c]}],"rounded-e":[{"rounded-e":[c]}],"rounded-t":[{"rounded-t":[c]}],"rounded-r":[{"rounded-r":[c]}],"rounded-b":[{"rounded-b":[c]}],"rounded-l":[{"rounded-l":[c]}],"rounded-ss":[{"rounded-ss":[c]}],"rounded-se":[{"rounded-se":[c]}],"rounded-ee":[{"rounded-ee":[c]}],"rounded-es":[{"rounded-es":[c]}],"rounded-tl":[{"rounded-tl":[c]}],"rounded-tr":[{"rounded-tr":[c]}],"rounded-br":[{"rounded-br":[c]}],"rounded-bl":[{"rounded-bl":[c]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...qe(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:qe()}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-s":[{"border-s":[n]}],"border-color-e":[{"border-e":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["",...qe()]}],"outline-offset":[{"outline-offset":[K,f]}],"outline-w":[{outline:[K,$]}],"outline-color":[{outline:[s]}],"ring-w":[{ring:st()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[s]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[K,$]}],"ring-offset-color":[{"ring-offset":[s]}],shadow:[{shadow:["","inner","none",W,sr]}],"shadow-color":[{shadow:[Pe]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...at(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":at()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[a]}],contrast:[{contrast:[h]}],"drop-shadow":[{"drop-shadow":["","none",W,f]}],grayscale:[{grayscale:[y]}],"hue-rotate":[{"hue-rotate":[b]}],invert:[{invert:[p]}],saturate:[{saturate:[S]}],sepia:[{sepia:[F]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[a]}],"backdrop-contrast":[{"backdrop-contrast":[h]}],"backdrop-grayscale":[{"backdrop-grayscale":[y]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[b]}],"backdrop-invert":[{"backdrop-invert":[p]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[S]}],"backdrop-sepia":[{"backdrop-sepia":[F]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[o]}],"border-spacing-x":[{"border-spacing-x":[o]}],"border-spacing-y":[{"border-spacing-y":[o]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",f]}],duration:[{duration:B()}],ease:[{ease:["linear","in","out","in-out",f]}],delay:[{delay:B()}],animate:[{animate:["none","spin","ping","pulse","bounce",f]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[O]}],"scale-x":[{"scale-x":[O]}],"scale-y":[{"scale-y":[O]}],rotate:[{rotate:[Se,f]}],"translate-x":[{"translate-x":[re]}],"translate-y":[{"translate-y":[re]}],"skew-x":[{"skew-x":[T]}],"skew-y":[{"skew-y":[T]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",f]}],accent:[{accent:["auto",s]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",f]}],"caret-color":[{caret:[s]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":P()}],"scroll-mx":[{"scroll-mx":P()}],"scroll-my":[{"scroll-my":P()}],"scroll-ms":[{"scroll-ms":P()}],"scroll-me":[{"scroll-me":P()}],"scroll-mt":[{"scroll-mt":P()}],"scroll-mr":[{"scroll-mr":P()}],"scroll-mb":[{"scroll-mb":P()}],"scroll-ml":[{"scroll-ml":P()}],"scroll-p":[{"scroll-p":P()}],"scroll-px":[{"scroll-px":P()}],"scroll-py":[{"scroll-py":P()}],"scroll-ps":[{"scroll-ps":P()}],"scroll-pe":[{"scroll-pe":P()}],"scroll-pt":[{"scroll-pt":P()}],"scroll-pr":[{"scroll-pr":P()}],"scroll-pb":[{"scroll-pb":P()}],"scroll-pl":[{"scroll-pl":P()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",f]}],fill:[{fill:[s,"none"]}],"stroke-w":[{stroke:[K,$,Be]}],stroke:[{stroke:[s,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},cr=Us(ir);function lr(...s){return cr(Kt(s))}const or=[{name:"Dashboard",href:"/",icon:Ht},{name:"Agents",href:"/agents",icon:me},{name:"Message Queue",href:"/queue",icon:Ae},{name:"Performance",href:"/performance",icon:Je},{name:"Settings",href:"/settings",icon:wt},{name:"Documentation",href:"/docs",icon:Ct}];function dr({children:s}){const e=Gt();return t.jsxs("div",{className:"min-h-screen bg-gray-50",children:[t.jsxs("div",{className:"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg",children:[t.jsx("div",{className:"flex h-16 items-center px-6 border-b border-gray-200",children:t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"flex items-center justify-center w-8 h-8 bg-primary-600 rounded-lg",children:t.jsx(Me,{className:"w-5 h-5 text-white"})}),t.jsxs("div",{children:[t.jsx("h1",{className:"text-lg font-bold text-gray-900",children:"Claude Code 3.0"}),t.jsx("p",{className:"text-xs text-gray-500",children:"Zero Latency AI"})]})]})}),t.jsx("nav",{className:"mt-6 px-3",children:t.jsx("ul",{className:"space-y-1",children:or.map(r=>{const a=e.pathname===r.href;return t.jsx("li",{children:t.jsxs(Ut,{to:r.href,className:lr("flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",a?"bg-primary-50 text-primary-700 border-r-2 border-primary-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[t.jsx(r.icon,{className:"w-5 h-5 mr-3"}),r.name]})},r.name)})})}),t.jsx("div",{className:"absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200",children:t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ze,{className:"w-4 h-4 text-success-500"}),t.jsx("span",{className:"text-xs text-gray-600",children:"System Online"})]}),t.jsx("div",{className:"flex-1"}),t.jsx("div",{className:"w-2 h-2 bg-success-500 rounded-full animate-pulse"})]})})]}),t.jsx("div",{className:"pl-64",children:t.jsx("main",{className:"py-6",children:t.jsx("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:s})})})]})}function ur(){return t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{className:"border-b border-gray-200 pb-4",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),t.jsx("p",{className:"text-gray-600",children:"Monitor your Claude Code 3.0 system performance and status"})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[t.jsx("div",{className:"metric-card",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex-1",children:[t.jsx("p",{className:"metric-label",children:"Active Agents"}),t.jsx("div",{className:"flex items-baseline space-x-2",children:t.jsxs("p",{className:"metric-value",children:["3",t.jsx("span",{className:"text-lg text-gray-500",children:"/5"})]})}),t.jsx("p",{className:"metric-change text-gray-500",children:"No change"})]}),t.jsx("div",{className:"p-3 rounded-lg bg-primary-50 text-primary-600",children:t.jsx(me,{className:"w-6 h-6"})})]})}),t.jsx("div",{className:"metric-card",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex-1",children:[t.jsx("p",{className:"metric-label",children:"Messages/Second"}),t.jsx("div",{className:"flex items-baseline space-x-2",children:t.jsx("p",{className:"metric-value",children:"4.3M"})}),t.jsx("p",{className:"metric-change positive",children:"+15% from last hour"})]}),t.jsx("div",{className:"p-3 rounded-lg bg-success-50 text-success-600",children:t.jsx(Ae,{className:"w-6 h-6"})})]})}),t.jsx("div",{className:"metric-card",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex-1",children:[t.jsx("p",{className:"metric-label",children:"Average Latency"}),t.jsx("div",{className:"flex items-baseline space-x-2",children:t.jsx("p",{className:"metric-value",children:"0.001ms"})}),t.jsx("p",{className:"metric-change positive",children:"5% faster"})]}),t.jsx("div",{className:"p-3 rounded-lg bg-warning-50 text-warning-600",children:t.jsx(Me,{className:"w-6 h-6"})})]})}),t.jsx("div",{className:"metric-card",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex-1",children:[t.jsx("p",{className:"metric-label",children:"Success Rate"}),t.jsx("div",{className:"flex items-baseline space-x-2",children:t.jsx("p",{className:"metric-value",children:"100%"})}),t.jsx("p",{className:"metric-change text-gray-500",children:"Perfect score"})]}),t.jsx("div",{className:"p-3 rounded-lg bg-success-50 text-success-600",children:t.jsx(he,{className:"w-6 h-6"})})]})})]}),t.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[t.jsxs("div",{className:"card",children:[t.jsx("div",{className:"card-header",children:t.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"System Status"})}),t.jsxs("div",{className:"card-content space-y-4",children:[t.jsxs("div",{className:"flex items-center space-x-3 p-3 rounded-lg border border-gray-100",children:[t.jsx("div",{className:"p-2 rounded-lg bg-success-50",children:t.jsx(Ze,{className:"w-5 h-5 text-success-600"})}),t.jsxs("div",{className:"flex-1",children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Framework Core"}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(he,{className:"w-4 h-4 text-success-600"}),t.jsx("span",{className:"text-xs font-medium text-success-600",children:"Online"})]})]}),t.jsx("p",{className:"text-xs text-gray-500",children:"Main system components"})]})]}),t.jsxs("div",{className:"flex items-center space-x-3 p-3 rounded-lg border border-gray-100",children:[t.jsx("div",{className:"p-2 rounded-lg bg-success-50",children:t.jsx(Ae,{className:"w-5 h-5 text-success-600"})}),t.jsxs("div",{className:"flex-1",children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"h2A Message Queue"}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(he,{className:"w-4 h-4 text-success-600"}),t.jsx("span",{className:"text-xs font-medium text-success-600",children:"Online"})]})]}),t.jsx("p",{className:"text-xs text-gray-500",children:"Zero-latency dual-buffer system"})]})]}),t.jsxs("div",{className:"flex items-center space-x-3 p-3 rounded-lg border border-gray-100",children:[t.jsx("div",{className:"p-2 rounded-lg bg-success-50",children:t.jsx(me,{className:"w-5 h-5 text-success-600"})}),t.jsxs("div",{className:"flex-1",children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Multi-Agent System"}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(he,{className:"w-4 h-4 text-success-600"}),t.jsx("span",{className:"text-xs font-medium text-success-600",children:"Online"})]})]}),t.jsx("p",{className:"text-xs text-gray-500",children:"3 of 5 agents active"})]})]}),t.jsxs("div",{className:"flex items-center space-x-3 p-3 rounded-lg border border-gray-100",children:[t.jsx("div",{className:"p-2 rounded-lg bg-success-50",children:t.jsx(Me,{className:"w-5 h-5 text-success-600"})}),t.jsxs("div",{className:"flex-1",children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Local LLM (qwen2.5:3b)"}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(he,{className:"w-4 h-4 text-success-600"}),t.jsx("span",{className:"text-xs font-medium text-success-600",children:"Online"})]})]}),t.jsx("p",{className:"text-xs text-gray-500",children:"116+ tokens/sec performance"})]})]})]})]}),t.jsxs("div",{className:"card",children:[t.jsx("div",{className:"card-header",children:t.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Activity"})}),t.jsx("div",{className:"card-content",children:t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx(he,{className:"w-5 h-5 text-success-500"})}),t.jsxs("div",{className:"flex-1",children:[t.jsx("p",{className:"text-sm text-gray-900",children:"Agent spawned: agent-specialized"}),t.jsx("p",{className:"text-xs text-gray-500",children:"2 minutes ago"})]})]}),t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx(Ye,{className:"w-5 h-5 text-primary-500"})}),t.jsxs("div",{className:"flex-1",children:[t.jsx("p",{className:"text-sm text-gray-900",children:"Performance benchmark completed"}),t.jsx("p",{className:"text-xs text-gray-500",children:"5 minutes ago"})]})]}),t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx(Ae,{className:"w-5 h-5 text-warning-500"})}),t.jsxs("div",{className:"flex-1",children:[t.jsx("p",{className:"text-sm text-gray-900",children:"Message queue processed 1M messages"}),t.jsx("p",{className:"text-xs text-gray-500",children:"10 minutes ago"})]})]}),t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx(me,{className:"w-5 h-5 text-success-500"})}),t.jsxs("div",{className:"flex-1",children:[t.jsx("p",{className:"text-sm text-gray-900",children:"Multi-agent system initialized"}),t.jsx("p",{className:"text-xs text-gray-500",children:"15 minutes ago"})]})]})]})})]})]}),t.jsxs("div",{className:"card",children:[t.jsx("div",{className:"card-header",children:t.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Quick Actions"})}),t.jsx("div",{className:"card-content",children:t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[t.jsxs("button",{className:"btn-primary p-4 text-left",children:[t.jsx(me,{className:"w-6 h-6 mb-2"}),t.jsx("div",{className:"text-sm font-medium",children:"Spawn New Agent"}),t.jsx("div",{className:"text-xs opacity-75",children:"Create a new AI agent"})]}),t.jsxs("button",{className:"btn-secondary p-4 text-left",children:[t.jsx(Je,{className:"w-6 h-6 mb-2"}),t.jsx("div",{className:"text-sm font-medium",children:"Run Benchmark"}),t.jsx("div",{className:"text-xs opacity-75",children:"Test system performance"})]}),t.jsxs("button",{className:"btn-secondary p-4 text-left",children:[t.jsx(wt,{className:"w-6 h-6 mb-2"}),t.jsx("div",{className:"text-sm font-medium",children:"System Settings"}),t.jsx("div",{className:"text-xs opacity-75",children:"Configure the system"})]})]})})]})]})}function hr(){return t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{className:"flex items-center justify-between border-b border-gray-200 pb-4",children:[t.jsxs("div",{children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Multi-Agent System"}),t.jsx("p",{className:"text-gray-600",children:"Manage and monitor your AI agents with load balancing"})]}),t.jsxs("button",{className:"btn-primary",children:[t.jsx(Vt,{className:"w-4 h-4 mr-2"}),"Spawn Agent"]})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[t.jsx("div",{className:"metric-card",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("p",{className:"metric-label",children:"Total Agents"}),t.jsx("p",{className:"metric-value",children:"5"})]}),t.jsx(me,{className:"w-8 h-8 text-primary-600"})]})}),t.jsx("div",{className:"metric-card",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("p",{className:"metric-label",children:"Active Agents"}),t.jsx("p",{className:"metric-value",children:"3"})]}),t.jsx("div",{className:"w-8 h-8 bg-success-100 rounded-lg flex items-center justify-center",children:t.jsx("div",{className:"w-3 h-3 bg-success-500 rounded-full animate-pulse"})})]})}),t.jsx("div",{className:"metric-card",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("p",{className:"metric-label",children:"Total Requests"}),t.jsx("p",{className:"metric-value",children:"2,595"})]}),t.jsx("div",{className:"w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center",children:t.jsx("span",{className:"text-primary-600 font-bold text-sm",children:"R"})})]})}),t.jsx("div",{className:"metric-card",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("p",{className:"metric-label",children:"Avg Load"}),t.jsx("p",{className:"metric-value",children:"1.0"})]}),t.jsx("div",{className:"w-8 h-8 bg-warning-100 rounded-lg flex items-center justify-center",children:t.jsx("span",{className:"text-warning-600 font-bold text-sm",children:"L"})})]})})]}),t.jsxs("div",{className:"card",children:[t.jsx("div",{className:"card-header",children:t.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Agent Management"})}),t.jsx("div",{className:"card-content",children:t.jsx("p",{className:"text-gray-600",children:"Multi-agent management interface will be available here. You can spawn, monitor, and manage AI agents with different capabilities."})})]})]})}function mr(){return t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{className:"border-b border-gray-200 pb-4",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"h2A Message Queue"}),t.jsx("p",{className:"text-gray-600",children:"Zero-latency dual-buffer message queue system"})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[t.jsx("div",{className:"metric-card",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("p",{className:"metric-label",children:"Throughput"}),t.jsx("p",{className:"metric-value",children:"4.3M/sec"})]}),t.jsx(Ye,{className:"w-8 h-8 text-success-600"})]})}),t.jsx("div",{className:"metric-card",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("p",{className:"metric-label",children:"Latency"}),t.jsx("p",{className:"metric-value",children:"0.001ms"})]}),t.jsx(Me,{className:"w-8 h-8 text-warning-600"})]})}),t.jsx("div",{className:"metric-card",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("p",{className:"metric-label",children:"Queue Size"}),t.jsx("p",{className:"metric-value",children:"1,247"})]}),t.jsx(Ae,{className:"w-8 h-8 text-primary-600"})]})}),t.jsx("div",{className:"metric-card",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("p",{className:"metric-label",children:"Buffer Switch"}),t.jsx("p",{className:"metric-value",children:"Active"})]}),t.jsx(Ze,{className:"w-8 h-8 text-success-600"})]})})]}),t.jsxs("div",{className:"card",children:[t.jsx("div",{className:"card-header",children:t.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Dual Buffer System"})}),t.jsx("div",{className:"card-content",children:t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[t.jsxs("div",{className:"border-2 border-primary-200 rounded-lg p-4 bg-primary-50",children:[t.jsx("h4",{className:"font-semibold text-primary-900 mb-2",children:"Primary Buffer (Active)"}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{children:"Messages:"}),t.jsx("span",{className:"font-medium",children:"847"})]}),t.jsxs("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{children:"Processing Rate:"}),t.jsx("span",{className:"font-medium",children:"2.1M/sec"})]}),t.jsx("div",{className:"w-full bg-primary-200 rounded-full h-2",children:t.jsx("div",{className:"bg-primary-600 h-2 rounded-full",style:{width:"65%"}})})]})]}),t.jsxs("div",{className:"border-2 border-gray-200 rounded-lg p-4 bg-gray-50",children:[t.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"Secondary Buffer (Standby)"}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{children:"Messages:"}),t.jsx("span",{className:"font-medium",children:"400"})]}),t.jsxs("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{children:"Processing Rate:"}),t.jsx("span",{className:"font-medium",children:"2.2M/sec"})]}),t.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:t.jsx("div",{className:"bg-gray-600 h-2 rounded-full",style:{width:"35%"}})})]})]})]})})]})]})}function xr(){return t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{className:"border-b border-gray-200 pb-4",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Performance Metrics"}),t.jsx("p",{className:"text-gray-600",children:"Real-time system performance monitoring and benchmarks"})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[t.jsx("div",{className:"metric-card",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("p",{className:"metric-label",children:"Throughput"}),t.jsx("p",{className:"metric-value",children:"4.3M/sec"}),t.jsx("p",{className:"metric-change positive",children:"+15% vs traditional"})]}),t.jsx(Ye,{className:"w-8 h-8 text-success-600"})]})}),t.jsx("div",{className:"metric-card",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("p",{className:"metric-label",children:"Latency"}),t.jsx("p",{className:"metric-value",children:"0.001ms"}),t.jsx("p",{className:"metric-change positive",children:"4,960x faster"})]}),t.jsx(Me,{className:"w-8 h-8 text-warning-600"})]})}),t.jsx("div",{className:"metric-card",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("p",{className:"metric-label",children:"Success Rate"}),t.jsx("p",{className:"metric-value",children:"100%"}),t.jsx("p",{className:"metric-change",children:"Perfect score"})]}),t.jsx(Je,{className:"w-8 h-8 text-primary-600"})]})}),t.jsx("div",{className:"metric-card",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("p",{className:"metric-label",children:"Uptime"}),t.jsx("p",{className:"metric-value",children:"99.9%"}),t.jsx("p",{className:"metric-change",children:"24h average"})]}),t.jsx($t,{className:"w-8 h-8 text-success-600"})]})})]}),t.jsxs("div",{className:"card",children:[t.jsx("div",{className:"card-header",children:t.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Architecture Benchmarks"})}),t.jsx("div",{className:"card-content",children:t.jsx("div",{className:"overflow-x-auto",children:t.jsxs("table",{className:"w-full",children:[t.jsx("thead",{className:"bg-gray-50",children:t.jsxs("tr",{children:[t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Architecture"}),t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Avg Latency"}),t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Throughput"}),t.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Performance vs h2A"})]})}),t.jsxs("tbody",{className:"divide-y divide-gray-200",children:[t.jsxs("tr",{children:[t.jsx("td",{className:"px-6 py-4 whitespace-nowrap font-medium text-gray-900",children:"h2A (Our Architecture)"}),t.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-success-600 font-medium",children:"0.001ms"}),t.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-success-600 font-medium",children:"4,322,773 msg/sec"}),t.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:t.jsx("span",{className:"status-success",children:"🏆 BASELINE"})})]}),t.jsxs("tr",{children:[t.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-gray-900",children:"Traditional Sync"}),t.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-error-600",children:"5.634ms"}),t.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-error-600",children:"178 msg/sec"}),t.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:t.jsx("span",{className:"status-error",children:"4,960x slower"})})]}),t.jsxs("tr",{children:[t.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-gray-900",children:"Traditional Async"}),t.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-error-600",children:"5.324ms"}),t.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-error-600",children:"621 msg/sec"}),t.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:t.jsx("span",{className:"status-error",children:"4,687x slower"})})]})]})]})})})]})]})}function fr(){return t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{className:"border-b border-gray-200 pb-4",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"System Settings"}),t.jsx("p",{className:"text-gray-600",children:"Configure your Claude Code 3.0 system"})]}),t.jsxs("div",{className:"card",children:[t.jsx("div",{className:"card-header",children:t.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Multi-Agent Configuration"})}),t.jsx("div",{className:"card-content space-y-4",children:t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Maximum Agents"}),t.jsx("input",{type:"number",defaultValue:10,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Load Balancing Strategy"}),t.jsxs("select",{className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",children:[t.jsx("option",{value:"least-loaded",children:"Least Loaded"}),t.jsx("option",{value:"round-robin",children:"Round Robin"}),t.jsx("option",{value:"random",children:"Random"}),t.jsx("option",{value:"capability-based",children:"Capability Based"})]})]})]})})]}),t.jsxs("div",{className:"card",children:[t.jsx("div",{className:"card-header",children:t.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Local LLM Configuration"})}),t.jsx("div",{className:"card-content space-y-4",children:t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Ollama Model"}),t.jsxs("select",{className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500",children:[t.jsx("option",{value:"qwen2.5:3b",children:"qwen2.5:3b (Recommended)"}),t.jsx("option",{value:"qwen2.5:7b",children:"qwen2.5:7b"}),t.jsx("option",{value:"llama3.2:3b",children:"llama3.2:3b"}),t.jsx("option",{value:"codellama:7b",children:"codellama:7b"})]})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Temperature"}),t.jsx("input",{type:"number",step:"0.1",min:"0",max:"2",defaultValue:.7,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"})]})]})})]}),t.jsxs("div",{className:"flex items-center space-x-4",children:[t.jsxs("button",{className:"btn-primary",children:[t.jsx(Wt,{className:"w-4 h-4 mr-2"}),"Save Settings"]}),t.jsxs("button",{className:"btn-secondary",children:[t.jsx(Zt,{className:"w-4 h-4 mr-2"}),"Reset to Defaults"]})]})]})}function pr(){return t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{className:"border-b border-gray-200 pb-4",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Documentation"}),t.jsx("p",{className:"text-gray-600",children:"Comprehensive guides and API reference for Claude Code 3.0"})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[t.jsxs("div",{className:"card p-6 text-center",children:[t.jsx(Ct,{className:"w-12 h-12 text-primary-600 mx-auto mb-4"}),t.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Quick Start"}),t.jsx("p",{className:"text-gray-600 mb-4",children:"Get started in 5 minutes"}),t.jsx("button",{className:"btn-primary",children:"Read Guide"})]}),t.jsxs("div",{className:"card p-6 text-center",children:[t.jsx(Jt,{className:"w-12 h-12 text-success-600 mx-auto mb-4"}),t.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"API Reference"}),t.jsx("p",{className:"text-gray-600 mb-4",children:"Complete API documentation"}),t.jsx("button",{className:"btn-secondary",children:"View API Docs"})]}),t.jsxs("div",{className:"card p-6 text-center",children:[t.jsx(Yt,{className:"w-12 h-12 text-warning-600 mx-auto mb-4"}),t.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Examples"}),t.jsx("p",{className:"text-gray-600 mb-4",children:"Code examples and tutorials"}),t.jsx("button",{className:"btn-secondary",children:"Browse Examples"})]})]}),t.jsxs("div",{className:"card",children:[t.jsx("div",{className:"card-header",children:t.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"System Information"})}),t.jsx("div",{className:"card-content",children:t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[t.jsxs("div",{children:[t.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Framework Version"}),t.jsx("p",{className:"text-gray-600",children:"Claude Code 3.0.0"})]}),t.jsxs("div",{children:[t.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Architecture"}),t.jsx("p",{className:"text-gray-600",children:"8-layer event-driven system"})]}),t.jsxs("div",{children:[t.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Performance"}),t.jsx("p",{className:"text-gray-600",children:"0.001ms latency, 4.3M msg/sec"})]}),t.jsxs("div",{children:[t.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Local LLM"}),t.jsx("p",{className:"text-gray-600",children:"Ollama with qwen2.5:3b support"})]})]})})]})]})}function gr(){return t.jsx(dr,{children:t.jsxs(_t,{children:[t.jsx(ue,{path:"/",element:t.jsx(ur,{})}),t.jsx(ue,{path:"/agents",element:t.jsx(hr,{})}),t.jsx(ue,{path:"/queue",element:t.jsx(mr,{})}),t.jsx(ue,{path:"/performance",element:t.jsx(xr,{})}),t.jsx(ue,{path:"/settings",element:t.jsx(fr,{})}),t.jsx(ue,{path:"/docs",element:t.jsx(pr,{})})]})})}const yr=new As({defaultOptions:{queries:{staleTime:1e3*60*5,refetchOnWindowFocus:!1}}});Ke.createRoot(document.getElementById("root")).render(t.jsx(zt.StrictMode,{children:t.jsx(Os,{client:yr,children:t.jsx(Bt,{children:t.jsx(gr,{})})})}));
//# sourceMappingURL=index-51464e78.js.map
