import{i as gc,a as ba,b as ma,d as pc,e as bc,f as mc,g as vc,p as yc,t as wc,h as _c,j as Do,n as xc,k as Mc,m as va,A as st,l as Lo,r as _n,o as Sc,q as ki,s as on,u as Ac,v as Ec}from"./charts-150f5ced.js";import{aM as vb,aA as yb,aB as wb,b6 as _b,bA as xb,bB as Mb,bE as Sb,B as Ab,V as Eb,w as $b,z as Rb,y as Pb,x as Ib,C as Nb,D as Tb,E as zb,F as kb,G as Cb,H as Bb,c1 as Ob,b$ as Db,c0 as Lb,c2 as Fb,c3 as qb,c4 as Yb,c7 as Gb,c5 as Vb,c6 as Xb,ca as Hb,c8 as Ub,c9 as Wb,cc as Kb,cb as Zb,cd as Qb,ce as Jb,cf as jb,cg as tm,ch as nm,ci as em,I as rm,J as im,au as am,av as om,an as fm,L as cm,ap as um,N as sm,O as lm,aI as hm,aH as dm,aK as gm,aJ as pm,aL as bm,K as mm,aG as vm,ac as ym,ad as wm,M as _m,U as xm,P as Mm,aE as Sm,V as Am,aC as Em,Q as $m,R as Rm,aQ as Pm,aR as Im,aS as Nm,b3 as Tm,aT as zm,aU as km,b1 as Cm,b2 as Bm,a_ as Om,a$ as Dm,aV as Lm,b0 as Fm,aW as qm,aX as Ym,aZ as Gm,aY as Vm,aw as Xm,dx as Hm,dy as Um,aD as Wm,aF as Km,aa as Zm,ab as Qm,bC as Jm,bF as jm,bH as t6,bI as n6,bK as e6,bJ as r6,aq as i6,Y as a6,Z as o6,_ as f6,$ as c6,a0 as u6,a1 as s6,a2 as l6,a3 as h6,b7 as d6,a4 as g6,bD as p6,b4 as b6,bG as m6,aN as v6,aO as y6,aP as w6,a5 as _6,a6 as x6,a7 as M6,b5 as S6,a8 as A6,bE as E6,bF as $6,a9 as R6,ar as P6,as as I6,S as N6,T as T6,b8 as z6,bu as k6,bv as C6,bw as B6,bx as O6,by as D6,ba as L6,bf as F6,bb as q6,bc as Y6,be as G6,b9 as V6,bg as X6,bj as H6,bk as U6,bi as W6,bo as K6,bp as Z6,bq as Q6,bt as J6,br as j6,bs as tv,bh as nv,bd as ev,bl as rv,bm as iv,bn as av,ae as ov,af as fv,ag as cv,ao as uv,at as sv,cj as lv,cl as hv,ck as dv,cm as gv,cn as pv,co as bv,cp as mv,cq as vv,cr as yv,cs as wv,ct as _v,cu as xv,ax as Mv,ah as Sv,ay as Av,bL as Ev,bO as $v,bP as Rv,bQ as Pv,bR as Iv,bS as Nv,bT as Tv,bU as zv,bV as kv,bW as Cv,b_ as Bv,bX as Ov,bY as Dv,bZ as Lv,b_ as Fv,bN as qv,bN as Yv,bM as Gv,W as Vv,X as Xv,bz as Hv,ai as Uv,aj as Wv,cI as Kv,cJ as Zv,ds as Qv,dr as Jv,dw as jv,cY as t8,cZ as n8,cE as e8,cF as r8,cv as i8,cw as a8,cx as o8,cA as f8,cB as c8,cQ as u8,cR as s8,de as l8,df as h8,dt as d8,c_ as g8,c$ as p8,cy as b8,cz as m8,cO as v8,cP as y8,cW as w8,cX as _8,dq as x8,dp as M8,cS as S8,cT as A8,cU as E8,cV as $8,cO as R8,cP as P8,di as I8,dj as N8,ak as T8,az as z8,cM as k8,cN as C8,cK as B8,cL as O8,du as D8,da as L8,db as F8,cG as q8,cH as Y8,cw as G8,cx as V8,cC as X8,cD as H8,d2 as U8,d3 as W8,dg as K8,dh as Z8,dv as Q8,dc as J8,dd as j8,cy as ty,cz as ny,d0 as ey,d1 as ry,d8 as iy,d9 as ay,dn as oy,dm as fy,d4 as cy,d5 as uy,d6 as sy,d7 as ly,d0 as hy,d1 as dy,dk as gy,dl as py,al as by,am as my}from"./charts-150f5ced.js";import"./vendor-b69f2a9f.js";function $c(t){return t}var Ne=1,Te=2,Zr=3,Gn=4,ya=1e-6;function Rc(t){return"translate("+t+",0)"}function Pc(t){return"translate(0,"+t+")"}function Ic(t){return n=>+t(n)}function Nc(t,n){return n=Math.max(0,t.bandwidth()-n*2)/2,t.round()&&(n=Math.round(n)),e=>+t(e)+n}function Tc(){return!this.__axis}function mr(t,n){var e=[],r=null,i=null,a=6,o=6,f=3,c=typeof window<"u"&&window.devicePixelRatio>1?0:.5,u=t===Ne||t===Gn?-1:1,s=t===Gn||t===Te?"x":"y",h=t===Ne||t===Zr?Rc:Pc;function l(d){var v=r??(n.ticks?n.ticks.apply(n,e):n.domain()),m=i??(n.tickFormat?n.tickFormat.apply(n,e):$c),g=Math.max(a,0)+f,p=n.range(),y=+p[0]+c,w=+p[p.length-1]+c,b=(n.bandwidth?Nc:Ic)(n.copy(),c),_=d.selection?d.selection():d,M=_.selectAll(".domain").data([null]),I=_.selectAll(".tick").data(v,n).order(),P=I.exit(),R=I.enter().append("g").attr("class","tick"),E=I.select("line"),$=I.select("text");M=M.merge(M.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),I=I.merge(R),E=E.merge(R.append("line").attr("stroke","currentColor").attr(s+"2",u*a)),$=$.merge(R.append("text").attr("fill","currentColor").attr(s,u*g).attr("dy",t===Ne?"0em":t===Zr?"0.71em":"0.32em")),d!==_&&(M=M.transition(d),I=I.transition(d),E=E.transition(d),$=$.transition(d),P=P.transition(d).attr("opacity",ya).attr("transform",function(z){return isFinite(z=b(z))?h(z+c):this.getAttribute("transform")}),R.attr("opacity",ya).attr("transform",function(z){var T=this.parentNode.__axis;return h((T&&isFinite(T=T(z))?T:b(z))+c)})),P.remove(),M.attr("d",t===Gn||t===Te?o?"M"+u*o+","+y+"H"+c+"V"+w+"H"+u*o:"M"+c+","+y+"V"+w:o?"M"+y+","+u*o+"V"+c+"H"+w+"V"+u*o:"M"+y+","+c+"H"+w),I.attr("opacity",1).attr("transform",function(z){return h(b(z)+c)}),E.attr(s+"2",u*a),$.attr(s,u*g).text(m),_.filter(Tc).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",t===Te?"start":t===Gn?"end":"middle"),_.each(function(){this.__axis=b})}return l.scale=function(d){return arguments.length?(n=d,l):n},l.ticks=function(){return e=Array.from(arguments),l},l.tickArguments=function(d){return arguments.length?(e=d==null?[]:Array.from(d),l):e.slice()},l.tickValues=function(d){return arguments.length?(r=d==null?null:Array.from(d),l):r&&r.slice()},l.tickFormat=function(d){return arguments.length?(i=d,l):i},l.tickSize=function(d){return arguments.length?(a=o=+d,l):a},l.tickSizeInner=function(d){return arguments.length?(a=+d,l):a},l.tickSizeOuter=function(d){return arguments.length?(o=+d,l):o},l.tickPadding=function(d){return arguments.length?(f=+d,l):f},l.offset=function(d){return arguments.length?(c=+d,l):c},l}function j1(t){return mr(Ne,t)}function t2(t){return mr(Te,t)}function n2(t){return mr(Zr,t)}function e2(t){return mr(Gn,t)}var zc={value:()=>{}};function kn(){for(var t=0,n=arguments.length,e={},r;t<n;++t){if(!(r=arguments[t]+"")||r in e||/[\s.]/.test(r))throw new Error("illegal type: "+r);e[r]=[]}return new ze(e)}function ze(t){this._=t}function kc(t,n){return t.trim().split(/^|\s+/).map(function(e){var r="",i=e.indexOf(".");if(i>=0&&(r=e.slice(i+1),e=e.slice(0,i)),e&&!n.hasOwnProperty(e))throw new Error("unknown type: "+e);return{type:e,name:r}})}ze.prototype=kn.prototype={constructor:ze,on:function(t,n){var e=this._,r=kc(t+"",e),i,a=-1,o=r.length;if(arguments.length<2){for(;++a<o;)if((i=(t=r[a]).type)&&(i=Cc(e[i],t.name)))return i;return}if(n!=null&&typeof n!="function")throw new Error("invalid callback: "+n);for(;++a<o;)if(i=(t=r[a]).type)e[i]=wa(e[i],t.name,n);else if(n==null)for(i in e)e[i]=wa(e[i],t.name,null);return this},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new ze(t)},call:function(t,n){if((i=arguments.length-2)>0)for(var e=new Array(i),r=0,i,a;r<i;++r)e[r]=arguments[r+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(a=this._[t],r=0,i=a.length;r<i;++r)a[r].value.apply(n,e)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],i=0,a=r.length;i<a;++i)r[i].value.apply(n,e)}};function Cc(t,n){for(var e=0,r=t.length,i;e<r;++e)if((i=t[e]).name===n)return i.value}function wa(t,n,e){for(var r=0,i=t.length;r<i;++r)if(t[r].name===n){t[r]=zc,t=t.slice(0,r).concat(t.slice(r+1));break}return e!=null&&t.push({name:n,value:e}),t}var Qr="http://www.w3.org/1999/xhtml";const _a={svg:"http://www.w3.org/2000/svg",xhtml:Qr,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function vr(t){var n=t+="",e=n.indexOf(":");return e>=0&&(n=t.slice(0,e))!=="xmlns"&&(t=t.slice(e+1)),_a.hasOwnProperty(n)?{space:_a[n],local:t}:t}function Bc(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===Qr&&n.documentElement.namespaceURI===Qr?n.createElement(t):n.createElementNS(e,t)}}function Oc(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}function Ci(t){var n=vr(t);return(n.local?Oc:Bc)(n)}function Dc(){}function Bi(t){return t==null?Dc:function(){return this.querySelector(t)}}function Lc(t){typeof t!="function"&&(t=Bi(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var a=n[i],o=a.length,f=r[i]=new Array(o),c,u,s=0;s<o;++s)(c=a[s])&&(u=t.call(c,c.__data__,s,a))&&("__data__"in c&&(u.__data__=c.__data__),f[s]=u);return new vt(r,this._parents)}function Fo(t){return t==null?[]:Array.isArray(t)?t:Array.from(t)}function Fc(){return[]}function qo(t){return t==null?Fc:function(){return this.querySelectorAll(t)}}function qc(t){return function(){return Fo(t.apply(this,arguments))}}function Yc(t){typeof t=="function"?t=qc(t):t=qo(t);for(var n=this._groups,e=n.length,r=[],i=[],a=0;a<e;++a)for(var o=n[a],f=o.length,c,u=0;u<f;++u)(c=o[u])&&(r.push(t.call(c,c.__data__,u,o)),i.push(c));return new vt(r,i)}function Yo(t){return function(){return this.matches(t)}}function Go(t){return function(n){return n.matches(t)}}var Gc=Array.prototype.find;function Vc(t){return function(){return Gc.call(this.children,t)}}function Xc(){return this.firstElementChild}function Hc(t){return this.select(t==null?Xc:Vc(typeof t=="function"?t:Go(t)))}var Uc=Array.prototype.filter;function Wc(){return Array.from(this.children)}function Kc(t){return function(){return Uc.call(this.children,t)}}function Zc(t){return this.selectAll(t==null?Wc:Kc(typeof t=="function"?t:Go(t)))}function Qc(t){typeof t!="function"&&(t=Yo(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var a=n[i],o=a.length,f=r[i]=[],c,u=0;u<o;++u)(c=a[u])&&t.call(c,c.__data__,u,a)&&f.push(c);return new vt(r,this._parents)}function Vo(t){return new Array(t.length)}function Jc(){return new vt(this._enter||this._groups.map(Vo),this._parents)}function Ge(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}Ge.prototype={constructor:Ge,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};function jc(t){return function(){return t}}function tu(t,n,e,r,i,a){for(var o=0,f,c=n.length,u=a.length;o<u;++o)(f=n[o])?(f.__data__=a[o],r[o]=f):e[o]=new Ge(t,a[o]);for(;o<c;++o)(f=n[o])&&(i[o]=f)}function nu(t,n,e,r,i,a,o){var f,c,u=new Map,s=n.length,h=a.length,l=new Array(s),d;for(f=0;f<s;++f)(c=n[f])&&(l[f]=d=o.call(c,c.__data__,f,n)+"",u.has(d)?i[f]=c:u.set(d,c));for(f=0;f<h;++f)d=o.call(t,a[f],f,a)+"",(c=u.get(d))?(r[f]=c,c.__data__=a[f],u.delete(d)):e[f]=new Ge(t,a[f]);for(f=0;f<s;++f)(c=n[f])&&u.get(l[f])===c&&(i[f]=c)}function eu(t){return t.__data__}function ru(t,n){if(!arguments.length)return Array.from(this,eu);var e=n?nu:tu,r=this._parents,i=this._groups;typeof t!="function"&&(t=jc(t));for(var a=i.length,o=new Array(a),f=new Array(a),c=new Array(a),u=0;u<a;++u){var s=r[u],h=i[u],l=h.length,d=iu(t.call(s,s&&s.__data__,u,r)),v=d.length,m=f[u]=new Array(v),g=o[u]=new Array(v),p=c[u]=new Array(l);e(s,h,m,g,p,d,n);for(var y=0,w=0,b,_;y<v;++y)if(b=m[y]){for(y>=w&&(w=y+1);!(_=g[w])&&++w<v;);b._next=_||null}}return o=new vt(o,r),o._enter=f,o._exit=c,o}function iu(t){return typeof t=="object"&&"length"in t?t:Array.from(t)}function au(){return new vt(this._exit||this._groups.map(Vo),this._parents)}function ou(t,n,e){var r=this.enter(),i=this,a=this.exit();return typeof t=="function"?(r=t(r),r&&(r=r.selection())):r=r.append(t+""),n!=null&&(i=n(i),i&&(i=i.selection())),e==null?a.remove():e(a),r&&i?r.merge(i).order():i}function fu(t){for(var n=t.selection?t.selection():t,e=this._groups,r=n._groups,i=e.length,a=r.length,o=Math.min(i,a),f=new Array(i),c=0;c<o;++c)for(var u=e[c],s=r[c],h=u.length,l=f[c]=new Array(h),d,v=0;v<h;++v)(d=u[v]||s[v])&&(l[v]=d);for(;c<i;++c)f[c]=e[c];return new vt(f,this._parents)}function cu(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r=t[n],i=r.length-1,a=r[i],o;--i>=0;)(o=r[i])&&(a&&o.compareDocumentPosition(a)^4&&a.parentNode.insertBefore(o,a),a=o);return this}function uu(t){t||(t=su);function n(h,l){return h&&l?t(h.__data__,l.__data__):!h-!l}for(var e=this._groups,r=e.length,i=new Array(r),a=0;a<r;++a){for(var o=e[a],f=o.length,c=i[a]=new Array(f),u,s=0;s<f;++s)(u=o[s])&&(c[s]=u);c.sort(n)}return new vt(i,this._parents).order()}function su(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}function lu(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this}function hu(){return Array.from(this)}function du(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,a=r.length;i<a;++i){var o=r[i];if(o)return o}return null}function gu(){let t=0;for(const n of this)++t;return t}function pu(){return!this.node()}function bu(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var i=n[e],a=0,o=i.length,f;a<o;++a)(f=i[a])&&t.call(f,f.__data__,a,i);return this}function mu(t){return function(){this.removeAttribute(t)}}function vu(t){return function(){this.removeAttributeNS(t.space,t.local)}}function yu(t,n){return function(){this.setAttribute(t,n)}}function wu(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}function _u(t,n){return function(){var e=n.apply(this,arguments);e==null?this.removeAttribute(t):this.setAttribute(t,e)}}function xu(t,n){return function(){var e=n.apply(this,arguments);e==null?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}function Mu(t,n){var e=vr(t);if(arguments.length<2){var r=this.node();return e.local?r.getAttributeNS(e.space,e.local):r.getAttribute(e)}return this.each((n==null?e.local?vu:mu:typeof n=="function"?e.local?xu:_u:e.local?wu:yu)(e,n))}function Xo(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function Su(t){return function(){this.style.removeProperty(t)}}function Au(t,n,e){return function(){this.style.setProperty(t,n,e)}}function Eu(t,n,e){return function(){var r=n.apply(this,arguments);r==null?this.style.removeProperty(t):this.style.setProperty(t,r,e)}}function $u(t,n,e){return arguments.length>1?this.each((n==null?Su:typeof n=="function"?Eu:Au)(t,n,e??"")):$n(this.node(),t)}function $n(t,n){return t.style.getPropertyValue(n)||Xo(t).getComputedStyle(t,null).getPropertyValue(n)}function Ru(t){return function(){delete this[t]}}function Pu(t,n){return function(){this[t]=n}}function Iu(t,n){return function(){var e=n.apply(this,arguments);e==null?delete this[t]:this[t]=e}}function Nu(t,n){return arguments.length>1?this.each((n==null?Ru:typeof n=="function"?Iu:Pu)(t,n)):this.node()[t]}function Ho(t){return t.trim().split(/^|\s+/)}function Oi(t){return t.classList||new Uo(t)}function Uo(t){this._node=t,this._names=Ho(t.getAttribute("class")||"")}Uo.prototype={add:function(t){var n=this._names.indexOf(t);n<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};function Wo(t,n){for(var e=Oi(t),r=-1,i=n.length;++r<i;)e.add(n[r])}function Ko(t,n){for(var e=Oi(t),r=-1,i=n.length;++r<i;)e.remove(n[r])}function Tu(t){return function(){Wo(this,t)}}function zu(t){return function(){Ko(this,t)}}function ku(t,n){return function(){(n.apply(this,arguments)?Wo:Ko)(this,t)}}function Cu(t,n){var e=Ho(t+"");if(arguments.length<2){for(var r=Oi(this.node()),i=-1,a=e.length;++i<a;)if(!r.contains(e[i]))return!1;return!0}return this.each((typeof n=="function"?ku:n?Tu:zu)(e,n))}function Bu(){this.textContent=""}function Ou(t){return function(){this.textContent=t}}function Du(t){return function(){var n=t.apply(this,arguments);this.textContent=n??""}}function Lu(t){return arguments.length?this.each(t==null?Bu:(typeof t=="function"?Du:Ou)(t)):this.node().textContent}function Fu(){this.innerHTML=""}function qu(t){return function(){this.innerHTML=t}}function Yu(t){return function(){var n=t.apply(this,arguments);this.innerHTML=n??""}}function Gu(t){return arguments.length?this.each(t==null?Fu:(typeof t=="function"?Yu:qu)(t)):this.node().innerHTML}function Vu(){this.nextSibling&&this.parentNode.appendChild(this)}function Xu(){return this.each(Vu)}function Hu(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Uu(){return this.each(Hu)}function Wu(t){var n=typeof t=="function"?t:Ci(t);return this.select(function(){return this.appendChild(n.apply(this,arguments))})}function Ku(){return null}function Zu(t,n){var e=typeof t=="function"?t:Ci(t),r=n==null?Ku:typeof n=="function"?n:Bi(n);return this.select(function(){return this.insertBefore(e.apply(this,arguments),r.apply(this,arguments)||null)})}function Qu(){var t=this.parentNode;t&&t.removeChild(this)}function Ju(){return this.each(Qu)}function ju(){var t=this.cloneNode(!1),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function ts(){var t=this.cloneNode(!0),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function ns(t){return this.select(t?ts:ju)}function es(t){return arguments.length?this.property("__data__",t):this.node().__data__}function rs(t){return function(n){t.call(this,n,this.__data__)}}function is(t){return t.trim().split(/^|\s+/).map(function(n){var e="",r=n.indexOf(".");return r>=0&&(e=n.slice(r+1),n=n.slice(0,r)),{type:n,name:e}})}function as(t){return function(){var n=this.__on;if(n){for(var e=0,r=-1,i=n.length,a;e<i;++e)a=n[e],(!t.type||a.type===t.type)&&a.name===t.name?this.removeEventListener(a.type,a.listener,a.options):n[++r]=a;++r?n.length=r:delete this.__on}}}function os(t,n,e){return function(){var r=this.__on,i,a=rs(n);if(r){for(var o=0,f=r.length;o<f;++o)if((i=r[o]).type===t.type&&i.name===t.name){this.removeEventListener(i.type,i.listener,i.options),this.addEventListener(i.type,i.listener=a,i.options=e),i.value=n;return}}this.addEventListener(t.type,a,e),i={type:t.type,name:t.name,value:n,listener:a,options:e},r?r.push(i):this.__on=[i]}}function fs(t,n,e){var r=is(t+""),i,a=r.length,o;if(arguments.length<2){var f=this.node().__on;if(f){for(var c=0,u=f.length,s;c<u;++c)for(i=0,s=f[c];i<a;++i)if((o=r[i]).type===s.type&&o.name===s.name)return s.value}return}for(f=n?os:as,i=0;i<a;++i)this.each(f(r[i],n,e));return this}function Zo(t,n,e){var r=Xo(t),i=r.CustomEvent;typeof i=="function"?i=new i(n,e):(i=r.document.createEvent("Event"),e?(i.initEvent(n,e.bubbles,e.cancelable),i.detail=e.detail):i.initEvent(n,!1,!1)),t.dispatchEvent(i)}function cs(t,n){return function(){return Zo(this,t,n)}}function us(t,n){return function(){return Zo(this,t,n.apply(this,arguments))}}function ss(t,n){return this.each((typeof n=="function"?us:cs)(t,n))}function*ls(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,a=r.length,o;i<a;++i)(o=r[i])&&(yield o)}var Di=[null];function vt(t,n){this._groups=t,this._parents=n}function Cn(){return new vt([[document.documentElement]],Di)}function hs(){return this}vt.prototype=Cn.prototype={constructor:vt,select:Lc,selectAll:Yc,selectChild:Hc,selectChildren:Zc,filter:Qc,data:ru,enter:Jc,exit:au,join:ou,merge:fu,selection:hs,order:cu,sort:uu,call:lu,nodes:hu,node:du,size:gu,empty:pu,each:bu,attr:Mu,style:$u,property:Nu,classed:Cu,text:Lu,html:Gu,raise:Xu,lower:Uu,append:Wu,insert:Zu,remove:Ju,clone:ns,datum:es,on:fs,dispatch:ss,[Symbol.iterator]:ls};function bt(t){return typeof t=="string"?new vt([[document.querySelector(t)]],[document.documentElement]):new vt([[t]],Di)}function r2(t){return bt(Ci(t).call(document.documentElement))}var ds=0;function gs(){return new Jr}function Jr(){this._="@"+(++ds).toString(36)}Jr.prototype=gs.prototype={constructor:Jr,get:function(t){for(var n=this._;!(n in t);)if(!(t=t.parentNode))return;return t[n]},set:function(t,n){return t[this._]=n},remove:function(t){return this._ in t&&delete t[this._]},toString:function(){return this._}};function Qo(t){let n;for(;n=t.sourceEvent;)t=n;return t}function Rt(t,n){if(t=Qo(t),n===void 0&&(n=t.currentTarget),n){var e=n.ownerSVGElement||n;if(e.createSVGPoint){var r=e.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,r=r.matrixTransform(n.getScreenCTM().inverse()),[r.x,r.y]}if(n.getBoundingClientRect){var i=n.getBoundingClientRect();return[t.clientX-i.left-n.clientLeft,t.clientY-i.top-n.clientTop]}}return[t.pageX,t.pageY]}function i2(t,n){return t.target&&(t=Qo(t),n===void 0&&(n=t.currentTarget),t=t.touches||[t]),Array.from(t,e=>Rt(e,n))}function a2(t){return typeof t=="string"?new vt([document.querySelectorAll(t)],[document.documentElement]):new vt([Fo(t)],Di)}const ps={passive:!1},ie={capture:!0,passive:!1};function Er(t){t.stopImmediatePropagation()}function Sn(t){t.preventDefault(),t.stopImmediatePropagation()}function Li(t){var n=t.document.documentElement,e=bt(t).on("dragstart.drag",Sn,ie);"onselectstart"in n?e.on("selectstart.drag",Sn,ie):(n.__noselect=n.style.MozUserSelect,n.style.MozUserSelect="none")}function Fi(t,n){var e=t.document.documentElement,r=bt(t).on("dragstart.drag",null);n&&(r.on("click.drag",Sn,ie),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in e?r.on("selectstart.drag",null):(e.style.MozUserSelect=e.__noselect,delete e.__noselect)}const pe=t=>()=>t;function jr(t,{sourceEvent:n,subject:e,target:r,identifier:i,active:a,x:o,y:f,dx:c,dy:u,dispatch:s}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},subject:{value:e,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:i,enumerable:!0,configurable:!0},active:{value:a,enumerable:!0,configurable:!0},x:{value:o,enumerable:!0,configurable:!0},y:{value:f,enumerable:!0,configurable:!0},dx:{value:c,enumerable:!0,configurable:!0},dy:{value:u,enumerable:!0,configurable:!0},_:{value:s}})}jr.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t};function bs(t){return!t.ctrlKey&&!t.button}function ms(){return this.parentNode}function vs(t,n){return n??{x:t.x,y:t.y}}function ys(){return navigator.maxTouchPoints||"ontouchstart"in this}function o2(){var t=bs,n=ms,e=vs,r=ys,i={},a=kn("start","drag","end"),o=0,f,c,u,s,h=0;function l(b){b.on("mousedown.drag",d).filter(r).on("touchstart.drag",g).on("touchmove.drag",p,ps).on("touchend.drag touchcancel.drag",y).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function d(b,_){if(!(s||!t.call(this,b,_))){var M=w(this,n.call(this,b,_),b,_,"mouse");M&&(bt(b.view).on("mousemove.drag",v,ie).on("mouseup.drag",m,ie),Li(b.view),Er(b),u=!1,f=b.clientX,c=b.clientY,M("start",b))}}function v(b){if(Sn(b),!u){var _=b.clientX-f,M=b.clientY-c;u=_*_+M*M>h}i.mouse("drag",b)}function m(b){bt(b.view).on("mousemove.drag mouseup.drag",null),Fi(b.view,u),Sn(b),i.mouse("end",b)}function g(b,_){if(t.call(this,b,_)){var M=b.changedTouches,I=n.call(this,b,_),P=M.length,R,E;for(R=0;R<P;++R)(E=w(this,I,b,_,M[R].identifier,M[R]))&&(Er(b),E("start",b,M[R]))}}function p(b){var _=b.changedTouches,M=_.length,I,P;for(I=0;I<M;++I)(P=i[_[I].identifier])&&(Sn(b),P("drag",b,_[I]))}function y(b){var _=b.changedTouches,M=_.length,I,P;for(s&&clearTimeout(s),s=setTimeout(function(){s=null},500),I=0;I<M;++I)(P=i[_[I].identifier])&&(Er(b),P("end",b,_[I]))}function w(b,_,M,I,P,R){var E=a.copy(),$=Rt(R||M,_),z,T,x;if((x=e.call(b,new jr("beforestart",{sourceEvent:M,target:l,identifier:P,active:o,x:$[0],y:$[1],dx:0,dy:0,dispatch:E}),I))!=null)return z=x.x-$[0]||0,T=x.y-$[1]||0,function A(S,N,C){var k=$,B;switch(S){case"start":i[P]=A,B=o++;break;case"end":delete i[P],--o;case"drag":$=Rt(C||N,_),B=o;break}E.call(S,b,new jr(S,{sourceEvent:N,subject:x,target:l,identifier:P,active:B,x:$[0]+z,y:$[1]+T,dx:$[0]-k[0],dy:$[1]-k[1],dispatch:E}),I)}}return l.filter=function(b){return arguments.length?(t=typeof b=="function"?b:pe(!!b),l):t},l.container=function(b){return arguments.length?(n=typeof b=="function"?b:pe(b),l):n},l.subject=function(b){return arguments.length?(e=typeof b=="function"?b:pe(b),l):e},l.touchable=function(b){return arguments.length?(r=typeof b=="function"?b:pe(!!b),l):r},l.on=function(){var b=a.on.apply(a,arguments);return b===a?l:b},l.clickDistance=function(b){return arguments.length?(h=(b=+b)*b,l):Math.sqrt(h)},l}var Rn=0,Vn=0,Ln=0,Jo=1e3,Ve,Xn,Xe=0,fn=0,yr=0,ae=typeof performance=="object"&&performance.now?performance:Date,jo=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function wr(){return fn||(jo(ws),fn=ae.now()+yr)}function ws(){fn=0}function oe(){this._call=this._time=this._next=null}oe.prototype=qi.prototype={constructor:oe,restart:function(t,n,e){if(typeof t!="function")throw new TypeError("callback is not a function");e=(e==null?wr():+e)+(n==null?0:+n),!this._next&&Xn!==this&&(Xn?Xn._next=this:Ve=this,Xn=this),this._call=t,this._time=e,ti()},stop:function(){this._call&&(this._call=null,this._time=1/0,ti())}};function qi(t,n,e){var r=new oe;return r.restart(t,n,e),r}function _s(){wr(),++Rn;for(var t=Ve,n;t;)(n=fn-t._time)>=0&&t._call.call(void 0,n),t=t._next;--Rn}function xa(){fn=(Xe=ae.now())+yr,Rn=Vn=0;try{_s()}finally{Rn=0,Ms(),fn=0}}function xs(){var t=ae.now(),n=t-Xe;n>Jo&&(yr-=n,Xe=t)}function Ms(){for(var t,n=Ve,e,r=1/0;n;)n._call?(r>n._time&&(r=n._time),t=n,n=n._next):(e=n._next,n._next=null,n=t?t._next=e:Ve=e);Xn=t,ti(r)}function ti(t){if(!Rn){Vn&&(Vn=clearTimeout(Vn));var n=t-fn;n>24?(t<1/0&&(Vn=setTimeout(xa,t-ae.now()-yr)),Ln&&(Ln=clearInterval(Ln))):(Ln||(Xe=ae.now(),Ln=setInterval(xs,Jo)),Rn=1,jo(xa))}}function Ma(t,n,e){var r=new oe;return n=n==null?0:+n,r.restart(i=>{r.stop(),t(i+n)},n,e),r}function f2(t,n,e){var r=new oe,i=n;return n==null?(r.restart(t,n,e),r):(r._restart=r.restart,r.restart=function(a,o,f){o=+o,f=f==null?wr():+f,r._restart(function c(u){u+=i,r._restart(c,i+=o,f),a(u)},o,f)},r.restart(t,n,e),r)}var Ss=kn("start","end","cancel","interrupt"),As=[],tf=0,ni=1,ei=2,ke=3,Sa=4,ri=5,Ce=6;function _r(t,n,e,r,i,a){var o=t.__transition;if(!o)t.__transition={};else if(e in o)return;Es(t,e,{name:n,index:r,group:i,on:Ss,tween:As,time:a.time,delay:a.delay,duration:a.duration,ease:a.ease,timer:null,state:tf})}function Yi(t,n){var e=Tt(t,n);if(e.state>tf)throw new Error("too late; already scheduled");return e}function Dt(t,n){var e=Tt(t,n);if(e.state>ke)throw new Error("too late; already running");return e}function Tt(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw new Error("transition not found");return e}function Es(t,n,e){var r=t.__transition,i;r[n]=e,e.timer=qi(a,0,e.time);function a(u){e.state=ni,e.timer.restart(o,e.delay,e.time),e.delay<=u&&o(u-e.delay)}function o(u){var s,h,l,d;if(e.state!==ni)return c();for(s in r)if(d=r[s],d.name===e.name){if(d.state===ke)return Ma(o);d.state===Sa?(d.state=Ce,d.timer.stop(),d.on.call("interrupt",t,t.__data__,d.index,d.group),delete r[s]):+s<n&&(d.state=Ce,d.timer.stop(),d.on.call("cancel",t,t.__data__,d.index,d.group),delete r[s])}if(Ma(function(){e.state===ke&&(e.state=Sa,e.timer.restart(f,e.delay,e.time),f(u))}),e.state=ei,e.on.call("start",t,t.__data__,e.index,e.group),e.state===ei){for(e.state=ke,i=new Array(l=e.tween.length),s=0,h=-1;s<l;++s)(d=e.tween[s].value.call(t,t.__data__,e.index,e.group))&&(i[++h]=d);i.length=h+1}}function f(u){for(var s=u<e.duration?e.ease.call(null,u/e.duration):(e.timer.restart(c),e.state=ri,1),h=-1,l=i.length;++h<l;)i[h].call(t,s);e.state===ri&&(e.on.call("end",t,t.__data__,e.index,e.group),c())}function c(){e.state=Ce,e.timer.stop(),delete r[n];for(var u in r)return;delete t.__transition}}function An(t,n){var e=t.__transition,r,i,a=!0,o;if(e){n=n==null?null:n+"";for(o in e){if((r=e[o]).name!==n){a=!1;continue}i=r.state>ei&&r.state<ri,r.state=Ce,r.timer.stop(),r.on.call(i?"interrupt":"cancel",t,t.__data__,r.index,r.group),delete e[o]}a&&delete t.__transition}}function $s(t){return this.each(function(){An(this,t)})}function Rs(t,n){var e,r;return function(){var i=Dt(this,t),a=i.tween;if(a!==e){r=e=a;for(var o=0,f=r.length;o<f;++o)if(r[o].name===n){r=r.slice(),r.splice(o,1);break}}i.tween=r}}function Ps(t,n,e){var r,i;if(typeof e!="function")throw new Error;return function(){var a=Dt(this,t),o=a.tween;if(o!==r){i=(r=o).slice();for(var f={name:n,value:e},c=0,u=i.length;c<u;++c)if(i[c].name===n){i[c]=f;break}c===u&&i.push(f)}a.tween=i}}function Is(t,n){var e=this._id;if(t+="",arguments.length<2){for(var r=Tt(this.node(),e).tween,i=0,a=r.length,o;i<a;++i)if((o=r[i]).name===t)return o.value;return null}return this.each((n==null?Rs:Ps)(e,t,n))}function Gi(t,n,e){var r=t._id;return t.each(function(){var i=Dt(this,r);(i.value||(i.value={}))[n]=e.apply(this,arguments)}),function(i){return Tt(i,r).value[n]}}function nf(t,n){var e;return(typeof n=="number"?gc:n instanceof ba?ma:(e=ba(n))?(n=e,ma):pc)(t,n)}function Ns(t){return function(){this.removeAttribute(t)}}function Ts(t){return function(){this.removeAttributeNS(t.space,t.local)}}function zs(t,n,e){var r,i=e+"",a;return function(){var o=this.getAttribute(t);return o===i?null:o===r?a:a=n(r=o,e)}}function ks(t,n,e){var r,i=e+"",a;return function(){var o=this.getAttributeNS(t.space,t.local);return o===i?null:o===r?a:a=n(r=o,e)}}function Cs(t,n,e){var r,i,a;return function(){var o,f=e(this),c;return f==null?void this.removeAttribute(t):(o=this.getAttribute(t),c=f+"",o===c?null:o===r&&c===i?a:(i=c,a=n(r=o,f)))}}function Bs(t,n,e){var r,i,a;return function(){var o,f=e(this),c;return f==null?void this.removeAttributeNS(t.space,t.local):(o=this.getAttributeNS(t.space,t.local),c=f+"",o===c?null:o===r&&c===i?a:(i=c,a=n(r=o,f)))}}function Os(t,n){var e=vr(t),r=e==="transform"?bc:nf;return this.attrTween(t,typeof n=="function"?(e.local?Bs:Cs)(e,r,Gi(this,"attr."+t,n)):n==null?(e.local?Ts:Ns)(e):(e.local?ks:zs)(e,r,n))}function Ds(t,n){return function(e){this.setAttribute(t,n.call(this,e))}}function Ls(t,n){return function(e){this.setAttributeNS(t.space,t.local,n.call(this,e))}}function Fs(t,n){var e,r;function i(){var a=n.apply(this,arguments);return a!==r&&(e=(r=a)&&Ls(t,a)),e}return i._value=n,i}function qs(t,n){var e,r;function i(){var a=n.apply(this,arguments);return a!==r&&(e=(r=a)&&Ds(t,a)),e}return i._value=n,i}function Ys(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(n==null)return this.tween(e,null);if(typeof n!="function")throw new Error;var r=vr(t);return this.tween(e,(r.local?Fs:qs)(r,n))}function Gs(t,n){return function(){Yi(this,t).delay=+n.apply(this,arguments)}}function Vs(t,n){return n=+n,function(){Yi(this,t).delay=n}}function Xs(t){var n=this._id;return arguments.length?this.each((typeof t=="function"?Gs:Vs)(n,t)):Tt(this.node(),n).delay}function Hs(t,n){return function(){Dt(this,t).duration=+n.apply(this,arguments)}}function Us(t,n){return n=+n,function(){Dt(this,t).duration=n}}function Ws(t){var n=this._id;return arguments.length?this.each((typeof t=="function"?Hs:Us)(n,t)):Tt(this.node(),n).duration}function Ks(t,n){if(typeof n!="function")throw new Error;return function(){Dt(this,t).ease=n}}function Zs(t){var n=this._id;return arguments.length?this.each(Ks(n,t)):Tt(this.node(),n).ease}function Qs(t,n){return function(){var e=n.apply(this,arguments);if(typeof e!="function")throw new Error;Dt(this,t).ease=e}}function Js(t){if(typeof t!="function")throw new Error;return this.each(Qs(this._id,t))}function js(t){typeof t!="function"&&(t=Yo(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var a=n[i],o=a.length,f=r[i]=[],c,u=0;u<o;++u)(c=a[u])&&t.call(c,c.__data__,u,a)&&f.push(c);return new Bt(r,this._parents,this._name,this._id)}function tl(t){if(t._id!==this._id)throw new Error;for(var n=this._groups,e=t._groups,r=n.length,i=e.length,a=Math.min(r,i),o=new Array(r),f=0;f<a;++f)for(var c=n[f],u=e[f],s=c.length,h=o[f]=new Array(s),l,d=0;d<s;++d)(l=c[d]||u[d])&&(h[d]=l);for(;f<r;++f)o[f]=n[f];return new Bt(o,this._parents,this._name,this._id)}function nl(t){return(t+"").trim().split(/^|\s+/).every(function(n){var e=n.indexOf(".");return e>=0&&(n=n.slice(0,e)),!n||n==="start"})}function el(t,n,e){var r,i,a=nl(n)?Yi:Dt;return function(){var o=a(this,t),f=o.on;f!==r&&(i=(r=f).copy()).on(n,e),o.on=i}}function rl(t,n){var e=this._id;return arguments.length<2?Tt(this.node(),e).on.on(t):this.each(el(e,t,n))}function il(t){return function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;n&&n.removeChild(this)}}function al(){return this.on("end.remove",il(this._id))}function ol(t){var n=this._name,e=this._id;typeof t!="function"&&(t=Bi(t));for(var r=this._groups,i=r.length,a=new Array(i),o=0;o<i;++o)for(var f=r[o],c=f.length,u=a[o]=new Array(c),s,h,l=0;l<c;++l)(s=f[l])&&(h=t.call(s,s.__data__,l,f))&&("__data__"in s&&(h.__data__=s.__data__),u[l]=h,_r(u[l],n,e,l,u,Tt(s,e)));return new Bt(a,this._parents,n,e)}function fl(t){var n=this._name,e=this._id;typeof t!="function"&&(t=qo(t));for(var r=this._groups,i=r.length,a=[],o=[],f=0;f<i;++f)for(var c=r[f],u=c.length,s,h=0;h<u;++h)if(s=c[h]){for(var l=t.call(s,s.__data__,h,c),d,v=Tt(s,e),m=0,g=l.length;m<g;++m)(d=l[m])&&_r(d,n,e,m,l,v);a.push(l),o.push(s)}return new Bt(a,o,n,e)}var cl=Cn.prototype.constructor;function ul(){return new cl(this._groups,this._parents)}function sl(t,n){var e,r,i;return function(){var a=$n(this,t),o=(this.style.removeProperty(t),$n(this,t));return a===o?null:a===e&&o===r?i:i=n(e=a,r=o)}}function ef(t){return function(){this.style.removeProperty(t)}}function ll(t,n,e){var r,i=e+"",a;return function(){var o=$n(this,t);return o===i?null:o===r?a:a=n(r=o,e)}}function hl(t,n,e){var r,i,a;return function(){var o=$n(this,t),f=e(this),c=f+"";return f==null&&(c=f=(this.style.removeProperty(t),$n(this,t))),o===c?null:o===r&&c===i?a:(i=c,a=n(r=o,f))}}function dl(t,n){var e,r,i,a="style."+n,o="end."+a,f;return function(){var c=Dt(this,t),u=c.on,s=c.value[a]==null?f||(f=ef(n)):void 0;(u!==e||i!==s)&&(r=(e=u).copy()).on(o,i=s),c.on=r}}function gl(t,n,e){var r=(t+="")=="transform"?mc:nf;return n==null?this.styleTween(t,sl(t,r)).on("end.style."+t,ef(t)):typeof n=="function"?this.styleTween(t,hl(t,r,Gi(this,"style."+t,n))).each(dl(this._id,t)):this.styleTween(t,ll(t,r,n),e).on("end.style."+t,null)}function pl(t,n,e){return function(r){this.style.setProperty(t,n.call(this,r),e)}}function bl(t,n,e){var r,i;function a(){var o=n.apply(this,arguments);return o!==i&&(r=(i=o)&&pl(t,o,e)),r}return a._value=n,a}function ml(t,n,e){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(n==null)return this.tween(r,null);if(typeof n!="function")throw new Error;return this.tween(r,bl(t,n,e??""))}function vl(t){return function(){this.textContent=t}}function yl(t){return function(){var n=t(this);this.textContent=n??""}}function wl(t){return this.tween("text",typeof t=="function"?yl(Gi(this,"text",t)):vl(t==null?"":t+""))}function _l(t){return function(n){this.textContent=t.call(this,n)}}function xl(t){var n,e;function r(){var i=t.apply(this,arguments);return i!==e&&(n=(e=i)&&_l(i)),n}return r._value=t,r}function Ml(t){var n="text";if(arguments.length<1)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;return this.tween(n,xl(t))}function Sl(){for(var t=this._name,n=this._id,e=rf(),r=this._groups,i=r.length,a=0;a<i;++a)for(var o=r[a],f=o.length,c,u=0;u<f;++u)if(c=o[u]){var s=Tt(c,n);_r(c,t,e,u,o,{time:s.time+s.delay+s.duration,delay:0,duration:s.duration,ease:s.ease})}return new Bt(r,this._parents,t,e)}function Al(){var t,n,e=this,r=e._id,i=e.size();return new Promise(function(a,o){var f={value:o},c={value:function(){--i===0&&a()}};e.each(function(){var u=Dt(this,r),s=u.on;s!==t&&(n=(t=s).copy(),n._.cancel.push(f),n._.interrupt.push(f),n._.end.push(c)),u.on=n}),i===0&&a()})}var El=0;function Bt(t,n,e,r){this._groups=t,this._parents=n,this._name=e,this._id=r}function $l(t){return Cn().transition(t)}function rf(){return++El}var Lt=Cn.prototype;Bt.prototype=$l.prototype={constructor:Bt,select:ol,selectAll:fl,selectChild:Lt.selectChild,selectChildren:Lt.selectChildren,filter:js,merge:tl,selection:ul,transition:Sl,call:Lt.call,nodes:Lt.nodes,node:Lt.node,size:Lt.size,empty:Lt.empty,each:Lt.each,on:rl,attr:Os,attrTween:Ys,style:gl,styleTween:ml,text:wl,textTween:Ml,remove:al,tween:Is,delay:Xs,duration:Ws,ease:Zs,easeVarying:Js,end:Al,[Symbol.iterator]:Lt[Symbol.iterator]};const c2=t=>+t;function u2(t){return t*t}function s2(t){return t*(2-t)}function l2(t){return((t*=2)<=1?t*t:--t*(2-t)+1)/2}function h2(t){return t*t*t}function d2(t){return--t*t*t+1}function Rl(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}var Vi=3,g2=function t(n){n=+n;function e(r){return Math.pow(r,n)}return e.exponent=t,e}(Vi),p2=function t(n){n=+n;function e(r){return 1-Math.pow(1-r,n)}return e.exponent=t,e}(Vi),b2=function t(n){n=+n;function e(r){return((r*=2)<=1?Math.pow(r,n):2-Math.pow(2-r,n))/2}return e.exponent=t,e}(Vi),af=Math.PI,of=af/2;function m2(t){return+t==1?1:1-Math.cos(t*of)}function v2(t){return Math.sin(t*of)}function y2(t){return(1-Math.cos(af*t))/2}function Jt(t){return(Math.pow(2,-10*t)-.0009765625)*1.0009775171065494}function w2(t){return Jt(1-+t)}function _2(t){return 1-Jt(t)}function x2(t){return((t*=2)<=1?Jt(1-t):2-Jt(t-1))/2}function M2(t){return 1-Math.sqrt(1-t*t)}function S2(t){return Math.sqrt(1- --t*t)}function A2(t){return((t*=2)<=1?1-Math.sqrt(1-t*t):Math.sqrt(1-(t-=2)*t)+1)/2}var ii=4/11,Pl=6/11,Il=8/11,Nl=3/4,Tl=9/11,zl=10/11,kl=15/16,Cl=21/22,Bl=63/64,be=1/ii/ii;function E2(t){return 1-ai(1-t)}function ai(t){return(t=+t)<ii?be*t*t:t<Il?be*(t-=Pl)*t+Nl:t<zl?be*(t-=Tl)*t+kl:be*(t-=Cl)*t+Bl}function $2(t){return((t*=2)<=1?1-ai(1-t):ai(t-1)+1)/2}var Xi=1.70158,R2=function t(n){n=+n;function e(r){return(r=+r)*r*(n*(r-1)+r)}return e.overshoot=t,e}(Xi),P2=function t(n){n=+n;function e(r){return--r*r*((r+1)*n+r)+1}return e.overshoot=t,e}(Xi),I2=function t(n){n=+n;function e(r){return((r*=2)<1?r*r*((n+1)*r-n):(r-=2)*r*((n+1)*r+n)+2)/2}return e.overshoot=t,e}(Xi),Pn=2*Math.PI,Hi=1,Ui=.3,N2=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=Pn);function i(a){return n*Jt(- --a)*Math.sin((r-a)/e)}return i.amplitude=function(a){return t(a,e*Pn)},i.period=function(a){return t(n,a)},i}(Hi,Ui),T2=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=Pn);function i(a){return 1-n*Jt(a=+a)*Math.sin((a+r)/e)}return i.amplitude=function(a){return t(a,e*Pn)},i.period=function(a){return t(n,a)},i}(Hi,Ui),z2=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=Pn);function i(a){return((a=a*2-1)<0?n*Jt(-a)*Math.sin((r-a)/e):2-n*Jt(a)*Math.sin((r+a)/e))/2}return i.amplitude=function(a){return t(a,e*Pn)},i.period=function(a){return t(n,a)},i}(Hi,Ui),Ol={time:null,delay:0,duration:250,ease:Rl};function Dl(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))throw new Error(`transition ${n} not found`);return e}function Ll(t){var n,e;t instanceof Bt?(n=t._id,t=t._name):(n=rf(),(e=Ol).time=wr(),t=t==null?null:t+"");for(var r=this._groups,i=r.length,a=0;a<i;++a)for(var o=r[a],f=o.length,c,u=0;u<f;++u)(c=o[u])&&_r(c,t,n,u,o,e||Dl(c,n));return new Bt(r,this._parents,t,n)}Cn.prototype.interrupt=$s;Cn.prototype.transition=Ll;var Fl=[null];function k2(t,n){var e=t.__transition,r,i;if(e){n=n==null?null:n+"";for(i in e)if((r=e[i]).state>ni&&r.name===n)return new Bt([[t]],Fl,n,+i)}return null}const $r=t=>()=>t;function ql(t,{sourceEvent:n,target:e,selection:r,mode:i,dispatch:a}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},selection:{value:r,enumerable:!0,configurable:!0},mode:{value:i,enumerable:!0,configurable:!0},_:{value:a}})}function Yl(t){t.stopImmediatePropagation()}function Rr(t){t.preventDefault(),t.stopImmediatePropagation()}var Aa={name:"drag"},Pr={name:"space"},dn={name:"handle"},gn={name:"center"};const{abs:Ea,max:ft,min:ct}=Math;function $a(t){return[+t[0],+t[1]]}function oi(t){return[$a(t[0]),$a(t[1])]}var Be={name:"x",handles:["w","e"].map(fe),input:function(t,n){return t==null?null:[[+t[0],n[0][1]],[+t[1],n[1][1]]]},output:function(t){return t&&[t[0][0],t[1][0]]}},Oe={name:"y",handles:["n","s"].map(fe),input:function(t,n){return t==null?null:[[n[0][0],+t[0]],[n[1][0],+t[1]]]},output:function(t){return t&&[t[0][1],t[1][1]]}},Gl={name:"xy",handles:["n","w","e","s","nw","ne","sw","se"].map(fe),input:function(t){return t==null?null:oi(t)},output:function(t){return t}},Ft={overlay:"crosshair",selection:"move",n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"},Ra={e:"w",w:"e",nw:"ne",ne:"nw",se:"sw",sw:"se"},Pa={n:"s",s:"n",nw:"sw",ne:"se",se:"ne",sw:"nw"},Vl={overlay:1,selection:1,n:null,e:1,s:null,w:-1,nw:-1,ne:1,se:1,sw:-1},Xl={overlay:1,selection:1,n:-1,e:null,s:1,w:null,nw:-1,ne:-1,se:1,sw:1};function fe(t){return{type:t}}function Hl(t){return!t.ctrlKey&&!t.button}function Ul(){var t=this.ownerSVGElement||this;return t.hasAttribute("viewBox")?(t=t.viewBox.baseVal,[[t.x,t.y],[t.x+t.width,t.y+t.height]]):[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]}function Wl(){return navigator.maxTouchPoints||"ontouchstart"in this}function Ir(t){for(;!t.__brush;)if(!(t=t.parentNode))return;return t.__brush}function Kl(t){return t[0][0]===t[1][0]||t[0][1]===t[1][1]}function C2(t){var n=t.__brush;return n?n.dim.output(n.selection):null}function B2(){return Wi(Be)}function O2(){return Wi(Oe)}function D2(){return Wi(Gl)}function Wi(t){var n=Ul,e=Hl,r=Wl,i=!0,a=kn("start","brush","end"),o=6,f;function c(g){var p=g.property("__brush",m).selectAll(".overlay").data([fe("overlay")]);p.enter().append("rect").attr("class","overlay").attr("pointer-events","all").attr("cursor",Ft.overlay).merge(p).each(function(){var w=Ir(this).extent;bt(this).attr("x",w[0][0]).attr("y",w[0][1]).attr("width",w[1][0]-w[0][0]).attr("height",w[1][1]-w[0][1])}),g.selectAll(".selection").data([fe("selection")]).enter().append("rect").attr("class","selection").attr("cursor",Ft.selection).attr("fill","#777").attr("fill-opacity",.3).attr("stroke","#fff").attr("shape-rendering","crispEdges");var y=g.selectAll(".handle").data(t.handles,function(w){return w.type});y.exit().remove(),y.enter().append("rect").attr("class",function(w){return"handle handle--"+w.type}).attr("cursor",function(w){return Ft[w.type]}),g.each(u).attr("fill","none").attr("pointer-events","all").on("mousedown.brush",l).filter(r).on("touchstart.brush",l).on("touchmove.brush",d).on("touchend.brush touchcancel.brush",v).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}c.move=function(g,p,y){g.tween?g.on("start.brush",function(w){s(this,arguments).beforestart().start(w)}).on("interrupt.brush end.brush",function(w){s(this,arguments).end(w)}).tween("brush",function(){var w=this,b=w.__brush,_=s(w,arguments),M=b.selection,I=t.input(typeof p=="function"?p.apply(this,arguments):p,b.extent),P=vc(M,I);function R(E){b.selection=E===1&&I===null?null:P(E),u.call(w),_.brush()}return M!==null&&I!==null?R:R(1)}):g.each(function(){var w=this,b=arguments,_=w.__brush,M=t.input(typeof p=="function"?p.apply(w,b):p,_.extent),I=s(w,b).beforestart();An(w),_.selection=M===null?null:M,u.call(w),I.start(y).brush(y).end(y)})},c.clear=function(g,p){c.move(g,null,p)};function u(){var g=bt(this),p=Ir(this).selection;p?(g.selectAll(".selection").style("display",null).attr("x",p[0][0]).attr("y",p[0][1]).attr("width",p[1][0]-p[0][0]).attr("height",p[1][1]-p[0][1]),g.selectAll(".handle").style("display",null).attr("x",function(y){return y.type[y.type.length-1]==="e"?p[1][0]-o/2:p[0][0]-o/2}).attr("y",function(y){return y.type[0]==="s"?p[1][1]-o/2:p[0][1]-o/2}).attr("width",function(y){return y.type==="n"||y.type==="s"?p[1][0]-p[0][0]+o:o}).attr("height",function(y){return y.type==="e"||y.type==="w"?p[1][1]-p[0][1]+o:o})):g.selectAll(".selection,.handle").style("display","none").attr("x",null).attr("y",null).attr("width",null).attr("height",null)}function s(g,p,y){var w=g.__brush.emitter;return w&&(!y||!w.clean)?w:new h(g,p,y)}function h(g,p,y){this.that=g,this.args=p,this.state=g.__brush,this.active=0,this.clean=y}h.prototype={beforestart:function(){return++this.active===1&&(this.state.emitter=this,this.starting=!0),this},start:function(g,p){return this.starting?(this.starting=!1,this.emit("start",g,p)):this.emit("brush",g),this},brush:function(g,p){return this.emit("brush",g,p),this},end:function(g,p){return--this.active===0&&(delete this.state.emitter,this.emit("end",g,p)),this},emit:function(g,p,y){var w=bt(this.that).datum();a.call(g,this.that,new ql(g,{sourceEvent:p,target:c,selection:t.output(this.state.selection),mode:y,dispatch:a}),w)}};function l(g){if(f&&!g.touches||!e.apply(this,arguments))return;var p=this,y=g.target.__data__.type,w=(i&&g.metaKey?y="overlay":y)==="selection"?Aa:i&&g.altKey?gn:dn,b=t===Oe?null:Vl[y],_=t===Be?null:Xl[y],M=Ir(p),I=M.extent,P=M.selection,R=I[0][0],E,$,z=I[0][1],T,x,A=I[1][0],S,N,C=I[1][1],k,B,L=0,Y=0,Q,U=b&&_&&i&&g.shiftKey,J,Mt,Z=Array.from(g.touches||[g],V=>{const rt=V.identifier;return V=Rt(V,p),V.point0=V.slice(),V.identifier=rt,V});An(p);var lt=s(p,arguments,!0).beforestart();if(y==="overlay"){P&&(Q=!0);const V=[Z[0],Z[1]||Z[0]];M.selection=P=[[E=t===Oe?R:ct(V[0][0],V[1][0]),T=t===Be?z:ct(V[0][1],V[1][1])],[S=t===Oe?A:ft(V[0][0],V[1][0]),k=t===Be?C:ft(V[0][1],V[1][1])]],Z.length>1&&jt(g)}else E=P[0][0],T=P[0][1],S=P[1][0],k=P[1][1];$=E,x=T,N=S,B=k;var hn=bt(p).attr("pointer-events","none"),On=hn.selectAll(".overlay").attr("cursor",Ft[y]);if(g.touches)lt.moved=ga,lt.ended=pa;else{var da=bt(g.view).on("mousemove.brush",ga,!0).on("mouseup.brush",pa,!0);i&&da.on("keydown.brush",hc,!0).on("keyup.brush",dc,!0),Li(g.view)}u.call(p),lt.start(g,w.name);function ga(V){for(const rt of V.changedTouches||[V])for(const Dn of Z)Dn.identifier===rt.identifier&&(Dn.cur=Rt(rt,p));if(U&&!J&&!Mt&&Z.length===1){const rt=Z[0];Ea(rt.cur[0]-rt[0])>Ea(rt.cur[1]-rt[1])?Mt=!0:J=!0}for(const rt of Z)rt.cur&&(rt[0]=rt.cur[0],rt[1]=rt.cur[1]);Q=!0,Rr(V),jt(V)}function jt(V){const rt=Z[0],Dn=rt.point0;var Ut;switch(L=rt[0]-Dn[0],Y=rt[1]-Dn[1],w){case Pr:case Aa:{b&&(L=ft(R-E,ct(A-S,L)),$=E+L,N=S+L),_&&(Y=ft(z-T,ct(C-k,Y)),x=T+Y,B=k+Y);break}case dn:{Z[1]?(b&&($=ft(R,ct(A,Z[0][0])),N=ft(R,ct(A,Z[1][0])),b=1),_&&(x=ft(z,ct(C,Z[0][1])),B=ft(z,ct(C,Z[1][1])),_=1)):(b<0?(L=ft(R-E,ct(A-E,L)),$=E+L,N=S):b>0&&(L=ft(R-S,ct(A-S,L)),$=E,N=S+L),_<0?(Y=ft(z-T,ct(C-T,Y)),x=T+Y,B=k):_>0&&(Y=ft(z-k,ct(C-k,Y)),x=T,B=k+Y));break}case gn:{b&&($=ft(R,ct(A,E-L*b)),N=ft(R,ct(A,S+L*b))),_&&(x=ft(z,ct(C,T-Y*_)),B=ft(z,ct(C,k+Y*_)));break}}N<$&&(b*=-1,Ut=E,E=S,S=Ut,Ut=$,$=N,N=Ut,y in Ra&&On.attr("cursor",Ft[y=Ra[y]])),B<x&&(_*=-1,Ut=T,T=k,k=Ut,Ut=x,x=B,B=Ut,y in Pa&&On.attr("cursor",Ft[y=Pa[y]])),M.selection&&(P=M.selection),J&&($=P[0][0],N=P[1][0]),Mt&&(x=P[0][1],B=P[1][1]),(P[0][0]!==$||P[0][1]!==x||P[1][0]!==N||P[1][1]!==B)&&(M.selection=[[$,x],[N,B]],u.call(p),lt.brush(V,w.name))}function pa(V){if(Yl(V),V.touches){if(V.touches.length)return;f&&clearTimeout(f),f=setTimeout(function(){f=null},500)}else Fi(V.view,Q),da.on("keydown.brush keyup.brush mousemove.brush mouseup.brush",null);hn.attr("pointer-events","all"),On.attr("cursor",Ft.overlay),M.selection&&(P=M.selection),Kl(P)&&(M.selection=null,u.call(p)),lt.end(V,w.name)}function hc(V){switch(V.keyCode){case 16:{U=b&&_;break}case 18:{w===dn&&(b&&(S=N-L*b,E=$+L*b),_&&(k=B-Y*_,T=x+Y*_),w=gn,jt(V));break}case 32:{(w===dn||w===gn)&&(b<0?S=N-L:b>0&&(E=$-L),_<0?k=B-Y:_>0&&(T=x-Y),w=Pr,On.attr("cursor",Ft.selection),jt(V));break}default:return}Rr(V)}function dc(V){switch(V.keyCode){case 16:{U&&(J=Mt=U=!1,jt(V));break}case 18:{w===gn&&(b<0?S=N:b>0&&(E=$),_<0?k=B:_>0&&(T=x),w=dn,jt(V));break}case 32:{w===Pr&&(V.altKey?(b&&(S=N-L*b,E=$+L*b),_&&(k=B-Y*_,T=x+Y*_),w=gn):(b<0?S=N:b>0&&(E=$),_<0?k=B:_>0&&(T=x),w=dn),On.attr("cursor",Ft[y]),jt(V));break}default:return}Rr(V)}}function d(g){s(this,arguments).moved(g)}function v(g){s(this,arguments).ended(g)}function m(){var g=this.__brush||{selection:null};return g.extent=oi(n.apply(this,arguments)),g.dim=t,g}return c.extent=function(g){return arguments.length?(n=typeof g=="function"?g:$r(oi(g)),c):n},c.filter=function(g){return arguments.length?(e=typeof g=="function"?g:$r(!!g),c):e},c.touchable=function(g){return arguments.length?(r=typeof g=="function"?g:$r(!!g),c):r},c.handleSize=function(g){return arguments.length?(o=+g,c):o},c.keyModifiers=function(g){return arguments.length?(i=!!g,c):i},c.on=function(){var g=a.on.apply(a,arguments);return g===a?c:g},c}var Ia=Math.abs,pn=Math.cos,bn=Math.sin,ff=Math.PI,me=ff/2,Na=ff*2,Ta=Math.max,Nr=1e-12;function Tr(t,n){return Array.from({length:n-t},(e,r)=>t+r)}function Zl(t){return function(n,e){return t(n.source.value+n.target.value,e.source.value+e.target.value)}}function L2(){return Ki(!1,!1)}function F2(){return Ki(!1,!0)}function q2(){return Ki(!0,!1)}function Ki(t,n){var e=0,r=null,i=null,a=null;function o(f){var c=f.length,u=new Array(c),s=Tr(0,c),h=new Array(c*c),l=new Array(c),d=0,v;f=Float64Array.from({length:c*c},n?(m,g)=>f[g%c][g/c|0]:(m,g)=>f[g/c|0][g%c]);for(let m=0;m<c;++m){let g=0;for(let p=0;p<c;++p)g+=f[m*c+p]+t*f[p*c+m];d+=u[m]=g}d=Ta(0,Na-e*c)/d,v=d?e:Na/c;{let m=0;r&&s.sort((g,p)=>r(u[g],u[p]));for(const g of s){const p=m;if(t){const y=Tr(~c+1,c).filter(w=>w<0?f[~w*c+g]:f[g*c+w]);i&&y.sort((w,b)=>i(w<0?-f[~w*c+g]:f[g*c+w],b<0?-f[~b*c+g]:f[g*c+b]));for(const w of y)if(w<0){const b=h[~w*c+g]||(h[~w*c+g]={source:null,target:null});b.target={index:g,startAngle:m,endAngle:m+=f[~w*c+g]*d,value:f[~w*c+g]}}else{const b=h[g*c+w]||(h[g*c+w]={source:null,target:null});b.source={index:g,startAngle:m,endAngle:m+=f[g*c+w]*d,value:f[g*c+w]}}l[g]={index:g,startAngle:p,endAngle:m,value:u[g]}}else{const y=Tr(0,c).filter(w=>f[g*c+w]||f[w*c+g]);i&&y.sort((w,b)=>i(f[g*c+w],f[g*c+b]));for(const w of y){let b;if(g<w?(b=h[g*c+w]||(h[g*c+w]={source:null,target:null}),b.source={index:g,startAngle:m,endAngle:m+=f[g*c+w]*d,value:f[g*c+w]}):(b=h[w*c+g]||(h[w*c+g]={source:null,target:null}),b.target={index:g,startAngle:m,endAngle:m+=f[g*c+w]*d,value:f[g*c+w]},g===w&&(b.source=b.target)),b.source&&b.target&&b.source.value<b.target.value){const _=b.source;b.source=b.target,b.target=_}}l[g]={index:g,startAngle:p,endAngle:m,value:u[g]}}m+=v}}return h=Object.values(h),h.groups=l,a?h.sort(a):h}return o.padAngle=function(f){return arguments.length?(e=Ta(0,f),o):e},o.sortGroups=function(f){return arguments.length?(r=f,o):r},o.sortSubgroups=function(f){return arguments.length?(i=f,o):i},o.sortChords=function(f){return arguments.length?(f==null?a=null:(a=Zl(f))._=f,o):a&&a._},o}var Ql=Array.prototype.slice;function tn(t){return function(){return t}}function Jl(t){return t.source}function jl(t){return t.target}function za(t){return t.radius}function t0(t){return t.startAngle}function n0(t){return t.endAngle}function e0(){return 0}function r0(){return 10}function cf(t){var n=Jl,e=jl,r=za,i=za,a=t0,o=n0,f=e0,c=null;function u(){var s,h=n.apply(this,arguments),l=e.apply(this,arguments),d=f.apply(this,arguments)/2,v=Ql.call(arguments),m=+r.apply(this,(v[0]=h,v)),g=a.apply(this,v)-me,p=o.apply(this,v)-me,y=+i.apply(this,(v[0]=l,v)),w=a.apply(this,v)-me,b=o.apply(this,v)-me;if(c||(c=s=yc()),d>Nr&&(Ia(p-g)>d*2+Nr?p>g?(g+=d,p-=d):(g-=d,p+=d):g=p=(g+p)/2,Ia(b-w)>d*2+Nr?b>w?(w+=d,b-=d):(w-=d,b+=d):w=b=(w+b)/2),c.moveTo(m*pn(g),m*bn(g)),c.arc(0,0,m,g,p),g!==w||p!==b)if(t){var _=+t.apply(this,arguments),M=y-_,I=(w+b)/2;c.quadraticCurveTo(0,0,M*pn(w),M*bn(w)),c.lineTo(y*pn(I),y*bn(I)),c.lineTo(M*pn(b),M*bn(b))}else c.quadraticCurveTo(0,0,y*pn(w),y*bn(w)),c.arc(0,0,y,w,b);if(c.quadraticCurveTo(0,0,m*pn(g),m*bn(g)),c.closePath(),s)return c=null,s+""||null}return t&&(u.headRadius=function(s){return arguments.length?(t=typeof s=="function"?s:tn(+s),u):t}),u.radius=function(s){return arguments.length?(r=i=typeof s=="function"?s:tn(+s),u):r},u.sourceRadius=function(s){return arguments.length?(r=typeof s=="function"?s:tn(+s),u):r},u.targetRadius=function(s){return arguments.length?(i=typeof s=="function"?s:tn(+s),u):i},u.startAngle=function(s){return arguments.length?(a=typeof s=="function"?s:tn(+s),u):a},u.endAngle=function(s){return arguments.length?(o=typeof s=="function"?s:tn(+s),u):o},u.padAngle=function(s){return arguments.length?(f=typeof s=="function"?s:tn(+s),u):f},u.source=function(s){return arguments.length?(n=s,u):n},u.target=function(s){return arguments.length?(e=s,u):e},u.context=function(s){return arguments.length?(c=s??null,u):c},u}function Y2(){return cf()}function G2(){return cf(r0)}var i0=Array.prototype,uf=i0.slice;function a0(t,n){return t-n}function o0(t){for(var n=0,e=t.length,r=t[e-1][1]*t[0][0]-t[e-1][0]*t[0][1];++n<e;)r+=t[n-1][1]*t[n][0]-t[n-1][0]*t[n][1];return r}const Wt=t=>()=>t;function f0(t,n){for(var e=-1,r=n.length,i;++e<r;)if(i=c0(t,n[e]))return i;return 0}function c0(t,n){for(var e=n[0],r=n[1],i=-1,a=0,o=t.length,f=o-1;a<o;f=a++){var c=t[a],u=c[0],s=c[1],h=t[f],l=h[0],d=h[1];if(u0(c,h,n))return 0;s>r!=d>r&&e<(l-u)*(r-s)/(d-s)+u&&(i=-i)}return i}function u0(t,n,e){var r;return s0(t,n,e)&&l0(t[r=+(t[0]===n[0])],e[r],n[r])}function s0(t,n,e){return(n[0]-t[0])*(e[1]-t[1])===(e[0]-t[0])*(n[1]-t[1])}function l0(t,n,e){return t<=n&&n<=e||e<=n&&n<=t}function h0(){}var qt=[[],[[[1,1.5],[.5,1]]],[[[1.5,1],[1,1.5]]],[[[1.5,1],[.5,1]]],[[[1,.5],[1.5,1]]],[[[1,1.5],[.5,1]],[[1,.5],[1.5,1]]],[[[1,.5],[1,1.5]]],[[[1,.5],[.5,1]]],[[[.5,1],[1,.5]]],[[[1,1.5],[1,.5]]],[[[.5,1],[1,.5]],[[1.5,1],[1,1.5]]],[[[1.5,1],[1,.5]]],[[[.5,1],[1.5,1]]],[[[1,1.5],[1.5,1]]],[[[.5,1],[1,1.5]]],[]];function ka(){var t=1,n=1,e=wc,r=c;function i(u){var s=e(u);if(Array.isArray(s))s=s.slice().sort(a0);else{const h=_c(u,d0);for(s=Do(...xc(h[0],h[1],s),s);s[s.length-1]>=h[1];)s.pop();for(;s[1]<h[0];)s.shift()}return s.map(h=>a(u,h))}function a(u,s){const h=s==null?NaN:+s;if(isNaN(h))throw new Error(`invalid value: ${s}`);var l=[],d=[];return o(u,h,function(v){r(v,u,h),o0(v)>0?l.push([v]):d.push(v)}),d.forEach(function(v){for(var m=0,g=l.length,p;m<g;++m)if(f0((p=l[m])[0],v)!==-1){p.push(v);return}}),{type:"MultiPolygon",value:s,coordinates:l}}function o(u,s,h){var l=new Array,d=new Array,v,m,g,p,y,w;for(v=m=-1,p=nn(u[0],s),qt[p<<1].forEach(b);++v<t-1;)g=p,p=nn(u[v+1],s),qt[g|p<<1].forEach(b);for(qt[p<<0].forEach(b);++m<n-1;){for(v=-1,p=nn(u[m*t+t],s),y=nn(u[m*t],s),qt[p<<1|y<<2].forEach(b);++v<t-1;)g=p,p=nn(u[m*t+t+v+1],s),w=y,y=nn(u[m*t+v+1],s),qt[g|p<<1|y<<2|w<<3].forEach(b);qt[p|y<<3].forEach(b)}for(v=-1,y=u[m*t]>=s,qt[y<<2].forEach(b);++v<t-1;)w=y,y=nn(u[m*t+v+1],s),qt[y<<2|w<<3].forEach(b);qt[y<<3].forEach(b);function b(_){var M=[_[0][0]+v,_[0][1]+m],I=[_[1][0]+v,_[1][1]+m],P=f(M),R=f(I),E,$;(E=d[P])?($=l[R])?(delete d[E.end],delete l[$.start],E===$?(E.ring.push(I),h(E.ring)):l[E.start]=d[$.end]={start:E.start,end:$.end,ring:E.ring.concat($.ring)}):(delete d[E.end],E.ring.push(I),d[E.end=R]=E):(E=l[R])?($=d[P])?(delete l[E.start],delete d[$.end],E===$?(E.ring.push(I),h(E.ring)):l[$.start]=d[E.end]={start:$.start,end:E.end,ring:$.ring.concat(E.ring)}):(delete l[E.start],E.ring.unshift(M),l[E.start=P]=E):l[P]=d[R]={start:P,end:R,ring:[M,I]}}}function f(u){return u[0]*2+u[1]*(t+1)*4}function c(u,s,h){u.forEach(function(l){var d=l[0],v=l[1],m=d|0,g=v|0,p=zr(s[g*t+m]);d>0&&d<t&&m===d&&(l[0]=Ca(d,zr(s[g*t+m-1]),p,h)),v>0&&v<n&&g===v&&(l[1]=Ca(v,zr(s[(g-1)*t+m]),p,h))})}return i.contour=a,i.size=function(u){if(!arguments.length)return[t,n];var s=Math.floor(u[0]),h=Math.floor(u[1]);if(!(s>=0&&h>=0))throw new Error("invalid size");return t=s,n=h,i},i.thresholds=function(u){return arguments.length?(e=typeof u=="function"?u:Array.isArray(u)?Wt(uf.call(u)):Wt(u),i):e},i.smooth=function(u){return arguments.length?(r=u?c:h0,i):r===c},i}function d0(t){return isFinite(t)?t:NaN}function nn(t,n){return t==null?!1:+t>=n}function zr(t){return t==null||isNaN(t=+t)?-1/0:t}function Ca(t,n,e,r){const i=r-n,a=e-n,o=isFinite(i)||isFinite(a)?i/a:Math.sign(i)/Math.sign(a);return isNaN(o)?t:t+o-.5}function g0(t){return t[0]}function p0(t){return t[1]}function b0(){return 1}function V2(){var t=g0,n=p0,e=b0,r=960,i=500,a=20,o=2,f=a*3,c=r+f*2>>o,u=i+f*2>>o,s=Wt(20);function h(y){var w=new Float32Array(c*u),b=Math.pow(2,-o),_=-1;for(const T of y){var M=(t(T,++_,y)+f)*b,I=(n(T,_,y)+f)*b,P=+e(T,_,y);if(P&&M>=0&&M<c&&I>=0&&I<u){var R=Math.floor(M),E=Math.floor(I),$=M-R-.5,z=I-E-.5;w[R+E*c]+=(1-$)*(1-z)*P,w[R+1+E*c]+=$*(1-z)*P,w[R+1+(E+1)*c]+=$*z*P,w[R+(E+1)*c]+=(1-$)*z*P}}return Mc({data:w,width:c,height:u},a*b),w}function l(y){var w=h(y),b=s(w),_=Math.pow(2,2*o);return Array.isArray(b)||(b=Do(Number.MIN_VALUE,va(w)/_,b)),ka().size([c,u]).thresholds(b.map(M=>M*_))(w).map((M,I)=>(M.value=+b[I],d(M)))}l.contours=function(y){var w=h(y),b=ka().size([c,u]),_=Math.pow(2,2*o),M=I=>{I=+I;var P=d(b.contour(w,I*_));return P.value=I,P};return Object.defineProperty(M,"max",{get:()=>va(w)/_}),M};function d(y){return y.coordinates.forEach(v),y}function v(y){y.forEach(m)}function m(y){y.forEach(g)}function g(y){y[0]=y[0]*Math.pow(2,o)-f,y[1]=y[1]*Math.pow(2,o)-f}function p(){return f=a*3,c=r+f*2>>o,u=i+f*2>>o,l}return l.x=function(y){return arguments.length?(t=typeof y=="function"?y:Wt(+y),l):t},l.y=function(y){return arguments.length?(n=typeof y=="function"?y:Wt(+y),l):n},l.weight=function(y){return arguments.length?(e=typeof y=="function"?y:Wt(+y),l):e},l.size=function(y){if(!arguments.length)return[r,i];var w=+y[0],b=+y[1];if(!(w>=0&&b>=0))throw new Error("invalid size");return r=w,i=b,p()},l.cellSize=function(y){if(!arguments.length)return 1<<o;if(!((y=+y)>=1))throw new Error("invalid cell size");return o=Math.floor(Math.log(y)/Math.LN2),p()},l.thresholds=function(y){return arguments.length?(s=typeof y=="function"?y:Array.isArray(y)?Wt(uf.call(y)):Wt(y),l):s},l.bandwidth=function(y){if(!arguments.length)return Math.sqrt(a*(a+1));if(!((y=+y)>=0))throw new Error("invalid bandwidth");return a=(Math.sqrt(4*y*y+1)-1)/2,p()},l}const Vt=11102230246251565e-32,ut=134217729,m0=(3+8*Vt)*Vt;function kr(t,n,e,r,i){let a,o,f,c,u=n[0],s=r[0],h=0,l=0;s>u==s>-u?(a=u,u=n[++h]):(a=s,s=r[++l]);let d=0;if(h<t&&l<e)for(s>u==s>-u?(o=u+a,f=a-(o-u),u=n[++h]):(o=s+a,f=a-(o-s),s=r[++l]),a=o,f!==0&&(i[d++]=f);h<t&&l<e;)s>u==s>-u?(o=a+u,c=o-a,f=a-(o-c)+(u-c),u=n[++h]):(o=a+s,c=o-a,f=a-(o-c)+(s-c),s=r[++l]),a=o,f!==0&&(i[d++]=f);for(;h<t;)o=a+u,c=o-a,f=a-(o-c)+(u-c),u=n[++h],a=o,f!==0&&(i[d++]=f);for(;l<e;)o=a+s,c=o-a,f=a-(o-c)+(s-c),s=r[++l],a=o,f!==0&&(i[d++]=f);return(a!==0||d===0)&&(i[d++]=a),d}function v0(t,n){let e=n[0];for(let r=1;r<t;r++)e+=n[r];return e}function le(t){return new Float64Array(t)}const y0=(3+16*Vt)*Vt,w0=(2+12*Vt)*Vt,_0=(9+64*Vt)*Vt*Vt,mn=le(4),Ba=le(8),Oa=le(12),Da=le(16),ht=le(4);function x0(t,n,e,r,i,a,o){let f,c,u,s,h,l,d,v,m,g,p,y,w,b,_,M,I,P;const R=t-i,E=e-i,$=n-a,z=r-a;b=R*z,l=ut*R,d=l-(l-R),v=R-d,l=ut*z,m=l-(l-z),g=z-m,_=v*g-(b-d*m-v*m-d*g),M=$*E,l=ut*$,d=l-(l-$),v=$-d,l=ut*E,m=l-(l-E),g=E-m,I=v*g-(M-d*m-v*m-d*g),p=_-I,h=_-p,mn[0]=_-(p+h)+(h-I),y=b+p,h=y-b,w=b-(y-h)+(p-h),p=w-M,h=w-p,mn[1]=w-(p+h)+(h-M),P=y+p,h=P-y,mn[2]=y-(P-h)+(p-h),mn[3]=P;let T=v0(4,mn),x=w0*o;if(T>=x||-T>=x||(h=t-R,f=t-(R+h)+(h-i),h=e-E,u=e-(E+h)+(h-i),h=n-$,c=n-($+h)+(h-a),h=r-z,s=r-(z+h)+(h-a),f===0&&c===0&&u===0&&s===0)||(x=_0*o+m0*Math.abs(T),T+=R*s+z*f-($*u+E*c),T>=x||-T>=x))return T;b=f*z,l=ut*f,d=l-(l-f),v=f-d,l=ut*z,m=l-(l-z),g=z-m,_=v*g-(b-d*m-v*m-d*g),M=c*E,l=ut*c,d=l-(l-c),v=c-d,l=ut*E,m=l-(l-E),g=E-m,I=v*g-(M-d*m-v*m-d*g),p=_-I,h=_-p,ht[0]=_-(p+h)+(h-I),y=b+p,h=y-b,w=b-(y-h)+(p-h),p=w-M,h=w-p,ht[1]=w-(p+h)+(h-M),P=y+p,h=P-y,ht[2]=y-(P-h)+(p-h),ht[3]=P;const A=kr(4,mn,4,ht,Ba);b=R*s,l=ut*R,d=l-(l-R),v=R-d,l=ut*s,m=l-(l-s),g=s-m,_=v*g-(b-d*m-v*m-d*g),M=$*u,l=ut*$,d=l-(l-$),v=$-d,l=ut*u,m=l-(l-u),g=u-m,I=v*g-(M-d*m-v*m-d*g),p=_-I,h=_-p,ht[0]=_-(p+h)+(h-I),y=b+p,h=y-b,w=b-(y-h)+(p-h),p=w-M,h=w-p,ht[1]=w-(p+h)+(h-M),P=y+p,h=P-y,ht[2]=y-(P-h)+(p-h),ht[3]=P;const S=kr(A,Ba,4,ht,Oa);b=f*s,l=ut*f,d=l-(l-f),v=f-d,l=ut*s,m=l-(l-s),g=s-m,_=v*g-(b-d*m-v*m-d*g),M=c*u,l=ut*c,d=l-(l-c),v=c-d,l=ut*u,m=l-(l-u),g=u-m,I=v*g-(M-d*m-v*m-d*g),p=_-I,h=_-p,ht[0]=_-(p+h)+(h-I),y=b+p,h=y-b,w=b-(y-h)+(p-h),p=w-M,h=w-p,ht[1]=w-(p+h)+(h-M),P=y+p,h=P-y,ht[2]=y-(P-h)+(p-h),ht[3]=P;const N=kr(S,Oa,4,ht,Da);return Da[N-1]}function ve(t,n,e,r,i,a){const o=(n-a)*(e-i),f=(t-i)*(r-a),c=o-f,u=Math.abs(o+f);return Math.abs(c)>=y0*u?c:-x0(t,n,e,r,i,a,u)}const La=Math.pow(2,-52),ye=new Uint32Array(512);class He{static from(n,e=$0,r=R0){const i=n.length,a=new Float64Array(i*2);for(let o=0;o<i;o++){const f=n[o];a[2*o]=e(f),a[2*o+1]=r(f)}return new He(a)}constructor(n){const e=n.length>>1;if(e>0&&typeof n[0]!="number")throw new Error("Expected coords to contain numbers.");this.coords=n;const r=Math.max(2*e-5,0);this._triangles=new Uint32Array(r*3),this._halfedges=new Int32Array(r*3),this._hashSize=Math.ceil(Math.sqrt(e)),this._hullPrev=new Uint32Array(e),this._hullNext=new Uint32Array(e),this._hullTri=new Uint32Array(e),this._hullHash=new Int32Array(this._hashSize),this._ids=new Uint32Array(e),this._dists=new Float64Array(e),this.update()}update(){const{coords:n,_hullPrev:e,_hullNext:r,_hullTri:i,_hullHash:a}=this,o=n.length>>1;let f=1/0,c=1/0,u=-1/0,s=-1/0;for(let R=0;R<o;R++){const E=n[2*R],$=n[2*R+1];E<f&&(f=E),$<c&&(c=$),E>u&&(u=E),$>s&&(s=$),this._ids[R]=R}const h=(f+u)/2,l=(c+s)/2;let d,v,m;for(let R=0,E=1/0;R<o;R++){const $=Cr(h,l,n[2*R],n[2*R+1]);$<E&&(d=R,E=$)}const g=n[2*d],p=n[2*d+1];for(let R=0,E=1/0;R<o;R++){if(R===d)continue;const $=Cr(g,p,n[2*R],n[2*R+1]);$<E&&$>0&&(v=R,E=$)}let y=n[2*v],w=n[2*v+1],b=1/0;for(let R=0;R<o;R++){if(R===d||R===v)continue;const E=A0(g,p,y,w,n[2*R],n[2*R+1]);E<b&&(m=R,b=E)}let _=n[2*m],M=n[2*m+1];if(b===1/0){for(let $=0;$<o;$++)this._dists[$]=n[2*$]-n[0]||n[2*$+1]-n[1];xn(this._ids,this._dists,0,o-1);const R=new Uint32Array(o);let E=0;for(let $=0,z=-1/0;$<o;$++){const T=this._ids[$],x=this._dists[T];x>z&&(R[E++]=T,z=x)}this.hull=R.subarray(0,E),this.triangles=new Uint32Array(0),this.halfedges=new Uint32Array(0);return}if(ve(g,p,y,w,_,M)<0){const R=v,E=y,$=w;v=m,y=_,w=M,m=R,_=E,M=$}const I=E0(g,p,y,w,_,M);this._cx=I.x,this._cy=I.y;for(let R=0;R<o;R++)this._dists[R]=Cr(n[2*R],n[2*R+1],I.x,I.y);xn(this._ids,this._dists,0,o-1),this._hullStart=d;let P=3;r[d]=e[m]=v,r[v]=e[d]=m,r[m]=e[v]=d,i[d]=0,i[v]=1,i[m]=2,a.fill(-1),a[this._hashKey(g,p)]=d,a[this._hashKey(y,w)]=v,a[this._hashKey(_,M)]=m,this.trianglesLen=0,this._addTriangle(d,v,m,-1,-1,-1);for(let R=0,E,$;R<this._ids.length;R++){const z=this._ids[R],T=n[2*z],x=n[2*z+1];if(R>0&&Math.abs(T-E)<=La&&Math.abs(x-$)<=La||(E=T,$=x,z===d||z===v||z===m))continue;let A=0;for(let B=0,L=this._hashKey(T,x);B<this._hashSize&&(A=a[(L+B)%this._hashSize],!(A!==-1&&A!==r[A]));B++);A=e[A];let S=A,N;for(;N=r[S],ve(T,x,n[2*S],n[2*S+1],n[2*N],n[2*N+1])>=0;)if(S=N,S===A){S=-1;break}if(S===-1)continue;let C=this._addTriangle(S,z,r[S],-1,-1,i[S]);i[z]=this._legalize(C+2),i[S]=C,P++;let k=r[S];for(;N=r[k],ve(T,x,n[2*k],n[2*k+1],n[2*N],n[2*N+1])<0;)C=this._addTriangle(k,z,N,i[z],-1,i[k]),i[z]=this._legalize(C+2),r[k]=k,P--,k=N;if(S===A)for(;N=e[S],ve(T,x,n[2*N],n[2*N+1],n[2*S],n[2*S+1])<0;)C=this._addTriangle(N,z,S,-1,i[S],i[N]),this._legalize(C+2),i[N]=C,r[S]=S,P--,S=N;this._hullStart=e[z]=S,r[S]=e[k]=z,r[z]=k,a[this._hashKey(T,x)]=z,a[this._hashKey(n[2*S],n[2*S+1])]=S}this.hull=new Uint32Array(P);for(let R=0,E=this._hullStart;R<P;R++)this.hull[R]=E,E=r[E];this.triangles=this._triangles.subarray(0,this.trianglesLen),this.halfedges=this._halfedges.subarray(0,this.trianglesLen)}_hashKey(n,e){return Math.floor(M0(n-this._cx,e-this._cy)*this._hashSize)%this._hashSize}_legalize(n){const{_triangles:e,_halfedges:r,coords:i}=this;let a=0,o=0;for(;;){const f=r[n],c=n-n%3;if(o=c+(n+2)%3,f===-1){if(a===0)break;n=ye[--a];continue}const u=f-f%3,s=c+(n+1)%3,h=u+(f+2)%3,l=e[o],d=e[n],v=e[s],m=e[h];if(S0(i[2*l],i[2*l+1],i[2*d],i[2*d+1],i[2*v],i[2*v+1],i[2*m],i[2*m+1])){e[n]=m,e[f]=l;const p=r[h];if(p===-1){let w=this._hullStart;do{if(this._hullTri[w]===h){this._hullTri[w]=n;break}w=this._hullPrev[w]}while(w!==this._hullStart)}this._link(n,p),this._link(f,r[o]),this._link(o,h);const y=u+(f+1)%3;a<ye.length&&(ye[a++]=y)}else{if(a===0)break;n=ye[--a]}}return o}_link(n,e){this._halfedges[n]=e,e!==-1&&(this._halfedges[e]=n)}_addTriangle(n,e,r,i,a,o){const f=this.trianglesLen;return this._triangles[f]=n,this._triangles[f+1]=e,this._triangles[f+2]=r,this._link(f,i),this._link(f+1,a),this._link(f+2,o),this.trianglesLen+=3,f}}function M0(t,n){const e=t/(Math.abs(t)+Math.abs(n));return(n>0?3-e:1+e)/4}function Cr(t,n,e,r){const i=t-e,a=n-r;return i*i+a*a}function S0(t,n,e,r,i,a,o,f){const c=t-o,u=n-f,s=e-o,h=r-f,l=i-o,d=a-f,v=c*c+u*u,m=s*s+h*h,g=l*l+d*d;return c*(h*g-m*d)-u*(s*g-m*l)+v*(s*d-h*l)<0}function A0(t,n,e,r,i,a){const o=e-t,f=r-n,c=i-t,u=a-n,s=o*o+f*f,h=c*c+u*u,l=.5/(o*u-f*c),d=(u*s-f*h)*l,v=(o*h-c*s)*l;return d*d+v*v}function E0(t,n,e,r,i,a){const o=e-t,f=r-n,c=i-t,u=a-n,s=o*o+f*f,h=c*c+u*u,l=.5/(o*u-f*c),d=t+(u*s-f*h)*l,v=n+(o*h-c*s)*l;return{x:d,y:v}}function xn(t,n,e,r){if(r-e<=20)for(let i=e+1;i<=r;i++){const a=t[i],o=n[a];let f=i-1;for(;f>=e&&n[t[f]]>o;)t[f+1]=t[f--];t[f+1]=a}else{const i=e+r>>1;let a=e+1,o=r;Fn(t,i,a),n[t[e]]>n[t[r]]&&Fn(t,e,r),n[t[a]]>n[t[r]]&&Fn(t,a,r),n[t[e]]>n[t[a]]&&Fn(t,e,a);const f=t[a],c=n[f];for(;;){do a++;while(n[t[a]]<c);do o--;while(n[t[o]]>c);if(o<a)break;Fn(t,a,o)}t[e+1]=t[o],t[o]=f,r-a+1>=o-e?(xn(t,n,a,r),xn(t,n,e,o-1)):(xn(t,n,e,o-1),xn(t,n,a,r))}}function Fn(t,n,e){const r=t[n];t[n]=t[e],t[e]=r}function $0(t){return t[0]}function R0(t){return t[1]}const Fa=1e-6;class an{constructor(){this._x0=this._y0=this._x1=this._y1=null,this._=""}moveTo(n,e){this._+=`M${this._x0=this._x1=+n},${this._y0=this._y1=+e}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")}lineTo(n,e){this._+=`L${this._x1=+n},${this._y1=+e}`}arc(n,e,r){n=+n,e=+e,r=+r;const i=n+r,a=e;if(r<0)throw new Error("negative radius");this._x1===null?this._+=`M${i},${a}`:(Math.abs(this._x1-i)>Fa||Math.abs(this._y1-a)>Fa)&&(this._+="L"+i+","+a),r&&(this._+=`A${r},${r},0,1,1,${n-r},${e}A${r},${r},0,1,1,${this._x1=i},${this._y1=a}`)}rect(n,e,r,i){this._+=`M${this._x0=this._x1=+n},${this._y0=this._y1=+e}h${+r}v${+i}h${-r}Z`}value(){return this._||null}}class fi{constructor(){this._=[]}moveTo(n,e){this._.push([n,e])}closePath(){this._.push(this._[0].slice())}lineTo(n,e){this._.push([n,e])}value(){return this._.length?this._:null}}class P0{constructor(n,[e,r,i,a]=[0,0,960,500]){if(!((i=+i)>=(e=+e))||!((a=+a)>=(r=+r)))throw new Error("invalid bounds");this.delaunay=n,this._circumcenters=new Float64Array(n.points.length*2),this.vectors=new Float64Array(n.points.length*2),this.xmax=i,this.xmin=e,this.ymax=a,this.ymin=r,this._init()}update(){return this.delaunay.update(),this._init(),this}_init(){const{delaunay:{points:n,hull:e,triangles:r},vectors:i}=this;let a,o;const f=this.circumcenters=this._circumcenters.subarray(0,r.length/3*2);for(let m=0,g=0,p=r.length,y,w;m<p;m+=3,g+=2){const b=r[m]*2,_=r[m+1]*2,M=r[m+2]*2,I=n[b],P=n[b+1],R=n[_],E=n[_+1],$=n[M],z=n[M+1],T=R-I,x=E-P,A=$-I,S=z-P,N=(T*S-x*A)*2;if(Math.abs(N)<1e-9){if(a===void 0){a=o=0;for(const k of e)a+=n[k*2],o+=n[k*2+1];a/=e.length,o/=e.length}const C=1e9*Math.sign((a-I)*S-(o-P)*A);y=(I+$)/2-C*S,w=(P+z)/2+C*A}else{const C=1/N,k=T*T+x*x,B=A*A+S*S;y=I+(S*k-x*B)*C,w=P+(T*B-A*k)*C}f[g]=y,f[g+1]=w}let c=e[e.length-1],u,s=c*4,h,l=n[2*c],d,v=n[2*c+1];i.fill(0);for(let m=0;m<e.length;++m)c=e[m],u=s,h=l,d=v,s=c*4,l=n[2*c],v=n[2*c+1],i[u+2]=i[s]=d-v,i[u+3]=i[s+1]=l-h}render(n){const e=n==null?n=new an:void 0,{delaunay:{halfedges:r,inedges:i,hull:a},circumcenters:o,vectors:f}=this;if(a.length<=1)return null;for(let s=0,h=r.length;s<h;++s){const l=r[s];if(l<s)continue;const d=Math.floor(s/3)*2,v=Math.floor(l/3)*2,m=o[d],g=o[d+1],p=o[v],y=o[v+1];this._renderSegment(m,g,p,y,n)}let c,u=a[a.length-1];for(let s=0;s<a.length;++s){c=u,u=a[s];const h=Math.floor(i[u]/3)*2,l=o[h],d=o[h+1],v=c*4,m=this._project(l,d,f[v+2],f[v+3]);m&&this._renderSegment(l,d,m[0],m[1],n)}return e&&e.value()}renderBounds(n){const e=n==null?n=new an:void 0;return n.rect(this.xmin,this.ymin,this.xmax-this.xmin,this.ymax-this.ymin),e&&e.value()}renderCell(n,e){const r=e==null?e=new an:void 0,i=this._clip(n);if(i===null||!i.length)return;e.moveTo(i[0],i[1]);let a=i.length;for(;i[0]===i[a-2]&&i[1]===i[a-1]&&a>1;)a-=2;for(let o=2;o<a;o+=2)(i[o]!==i[o-2]||i[o+1]!==i[o-1])&&e.lineTo(i[o],i[o+1]);return e.closePath(),r&&r.value()}*cellPolygons(){const{delaunay:{points:n}}=this;for(let e=0,r=n.length/2;e<r;++e){const i=this.cellPolygon(e);i&&(i.index=e,yield i)}}cellPolygon(n){const e=new fi;return this.renderCell(n,e),e.value()}_renderSegment(n,e,r,i,a){let o;const f=this._regioncode(n,e),c=this._regioncode(r,i);f===0&&c===0?(a.moveTo(n,e),a.lineTo(r,i)):(o=this._clipSegment(n,e,r,i,f,c))&&(a.moveTo(o[0],o[1]),a.lineTo(o[2],o[3]))}contains(n,e,r){return e=+e,e!==e||(r=+r,r!==r)?!1:this.delaunay._step(n,e,r)===n}*neighbors(n){const e=this._clip(n);if(e)for(const r of this.delaunay.neighbors(n)){const i=this._clip(r);if(i){t:for(let a=0,o=e.length;a<o;a+=2)for(let f=0,c=i.length;f<c;f+=2)if(e[a]===i[f]&&e[a+1]===i[f+1]&&e[(a+2)%o]===i[(f+c-2)%c]&&e[(a+3)%o]===i[(f+c-1)%c]){yield r;break t}}}}_cell(n){const{circumcenters:e,delaunay:{inedges:r,halfedges:i,triangles:a}}=this,o=r[n];if(o===-1)return null;const f=[];let c=o;do{const u=Math.floor(c/3);if(f.push(e[u*2],e[u*2+1]),c=c%3===2?c-2:c+1,a[c]!==n)break;c=i[c]}while(c!==o&&c!==-1);return f}_clip(n){if(n===0&&this.delaunay.hull.length===1)return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];const e=this._cell(n);if(e===null)return null;const{vectors:r}=this,i=n*4;return this._simplify(r[i]||r[i+1]?this._clipInfinite(n,e,r[i],r[i+1],r[i+2],r[i+3]):this._clipFinite(n,e))}_clipFinite(n,e){const r=e.length;let i=null,a,o,f=e[r-2],c=e[r-1],u,s=this._regioncode(f,c),h,l=0;for(let d=0;d<r;d+=2)if(a=f,o=c,f=e[d],c=e[d+1],u=s,s=this._regioncode(f,c),u===0&&s===0)h=l,l=0,i?i.push(f,c):i=[f,c];else{let v,m,g,p,y;if(u===0){if((v=this._clipSegment(a,o,f,c,u,s))===null)continue;[m,g,p,y]=v}else{if((v=this._clipSegment(f,c,a,o,s,u))===null)continue;[p,y,m,g]=v,h=l,l=this._edgecode(m,g),h&&l&&this._edge(n,h,l,i,i.length),i?i.push(m,g):i=[m,g]}h=l,l=this._edgecode(p,y),h&&l&&this._edge(n,h,l,i,i.length),i?i.push(p,y):i=[p,y]}if(i)h=l,l=this._edgecode(i[0],i[1]),h&&l&&this._edge(n,h,l,i,i.length);else if(this.contains(n,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2))return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];return i}_clipSegment(n,e,r,i,a,o){const f=a<o;for(f&&([n,e,r,i,a,o]=[r,i,n,e,o,a]);;){if(a===0&&o===0)return f?[r,i,n,e]:[n,e,r,i];if(a&o)return null;let c,u,s=a||o;s&8?(c=n+(r-n)*(this.ymax-e)/(i-e),u=this.ymax):s&4?(c=n+(r-n)*(this.ymin-e)/(i-e),u=this.ymin):s&2?(u=e+(i-e)*(this.xmax-n)/(r-n),c=this.xmax):(u=e+(i-e)*(this.xmin-n)/(r-n),c=this.xmin),a?(n=c,e=u,a=this._regioncode(n,e)):(r=c,i=u,o=this._regioncode(r,i))}}_clipInfinite(n,e,r,i,a,o){let f=Array.from(e),c;if((c=this._project(f[0],f[1],r,i))&&f.unshift(c[0],c[1]),(c=this._project(f[f.length-2],f[f.length-1],a,o))&&f.push(c[0],c[1]),f=this._clipFinite(n,f))for(let u=0,s=f.length,h,l=this._edgecode(f[s-2],f[s-1]);u<s;u+=2)h=l,l=this._edgecode(f[u],f[u+1]),h&&l&&(u=this._edge(n,h,l,f,u),s=f.length);else this.contains(n,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2)&&(f=[this.xmin,this.ymin,this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax]);return f}_edge(n,e,r,i,a){for(;e!==r;){let o,f;switch(e){case 5:e=4;continue;case 4:e=6,o=this.xmax,f=this.ymin;break;case 6:e=2;continue;case 2:e=10,o=this.xmax,f=this.ymax;break;case 10:e=8;continue;case 8:e=9,o=this.xmin,f=this.ymax;break;case 9:e=1;continue;case 1:e=5,o=this.xmin,f=this.ymin;break}(i[a]!==o||i[a+1]!==f)&&this.contains(n,o,f)&&(i.splice(a,0,o,f),a+=2)}return a}_project(n,e,r,i){let a=1/0,o,f,c;if(i<0){if(e<=this.ymin)return null;(o=(this.ymin-e)/i)<a&&(c=this.ymin,f=n+(a=o)*r)}else if(i>0){if(e>=this.ymax)return null;(o=(this.ymax-e)/i)<a&&(c=this.ymax,f=n+(a=o)*r)}if(r>0){if(n>=this.xmax)return null;(o=(this.xmax-n)/r)<a&&(f=this.xmax,c=e+(a=o)*i)}else if(r<0){if(n<=this.xmin)return null;(o=(this.xmin-n)/r)<a&&(f=this.xmin,c=e+(a=o)*i)}return[f,c]}_edgecode(n,e){return(n===this.xmin?1:n===this.xmax?2:0)|(e===this.ymin?4:e===this.ymax?8:0)}_regioncode(n,e){return(n<this.xmin?1:n>this.xmax?2:0)|(e<this.ymin?4:e>this.ymax?8:0)}_simplify(n){if(n&&n.length>4){for(let e=0;e<n.length;e+=2){const r=(e+2)%n.length,i=(e+4)%n.length;(n[e]===n[r]&&n[r]===n[i]||n[e+1]===n[r+1]&&n[r+1]===n[i+1])&&(n.splice(r,2),e-=2)}n.length||(n=null)}return n}}const I0=2*Math.PI,vn=Math.pow;function N0(t){return t[0]}function T0(t){return t[1]}function z0(t){const{triangles:n,coords:e}=t;for(let r=0;r<n.length;r+=3){const i=2*n[r],a=2*n[r+1],o=2*n[r+2];if((e[o]-e[i])*(e[a+1]-e[i+1])-(e[a]-e[i])*(e[o+1]-e[i+1])>1e-10)return!1}return!0}function k0(t,n,e){return[t+Math.sin(t+n)*e,n+Math.cos(t-n)*e]}class sf{static from(n,e=N0,r=T0,i){return new sf("length"in n?C0(n,e,r,i):Float64Array.from(B0(n,e,r,i)))}constructor(n){this._delaunator=new He(n),this.inedges=new Int32Array(n.length/2),this._hullIndex=new Int32Array(n.length/2),this.points=this._delaunator.coords,this._init()}update(){return this._delaunator.update(),this._init(),this}_init(){const n=this._delaunator,e=this.points;if(n.hull&&n.hull.length>2&&z0(n)){this.collinear=Int32Array.from({length:e.length/2},(l,d)=>d).sort((l,d)=>e[2*l]-e[2*d]||e[2*l+1]-e[2*d+1]);const c=this.collinear[0],u=this.collinear[this.collinear.length-1],s=[e[2*c],e[2*c+1],e[2*u],e[2*u+1]],h=1e-8*Math.hypot(s[3]-s[1],s[2]-s[0]);for(let l=0,d=e.length/2;l<d;++l){const v=k0(e[2*l],e[2*l+1],h);e[2*l]=v[0],e[2*l+1]=v[1]}this._delaunator=new He(e)}else delete this.collinear;const r=this.halfedges=this._delaunator.halfedges,i=this.hull=this._delaunator.hull,a=this.triangles=this._delaunator.triangles,o=this.inedges.fill(-1),f=this._hullIndex.fill(-1);for(let c=0,u=r.length;c<u;++c){const s=a[c%3===2?c-2:c+1];(r[c]===-1||o[s]===-1)&&(o[s]=c)}for(let c=0,u=i.length;c<u;++c)f[i[c]]=c;i.length<=2&&i.length>0&&(this.triangles=new Int32Array(3).fill(-1),this.halfedges=new Int32Array(3).fill(-1),this.triangles[0]=i[0],o[i[0]]=1,i.length===2&&(o[i[1]]=0,this.triangles[1]=i[1],this.triangles[2]=i[1]))}voronoi(n){return new P0(this,n)}*neighbors(n){const{inedges:e,hull:r,_hullIndex:i,halfedges:a,triangles:o,collinear:f}=this;if(f){const h=f.indexOf(n);h>0&&(yield f[h-1]),h<f.length-1&&(yield f[h+1]);return}const c=e[n];if(c===-1)return;let u=c,s=-1;do{if(yield s=o[u],u=u%3===2?u-2:u+1,o[u]!==n)return;if(u=a[u],u===-1){const h=r[(i[n]+1)%r.length];h!==s&&(yield h);return}}while(u!==c)}find(n,e,r=0){if(n=+n,n!==n||(e=+e,e!==e))return-1;const i=r;let a;for(;(a=this._step(r,n,e))>=0&&a!==r&&a!==i;)r=a;return a}_step(n,e,r){const{inedges:i,hull:a,_hullIndex:o,halfedges:f,triangles:c,points:u}=this;if(i[n]===-1||!u.length)return(n+1)%(u.length>>1);let s=n,h=vn(e-u[n*2],2)+vn(r-u[n*2+1],2);const l=i[n];let d=l;do{let v=c[d];const m=vn(e-u[v*2],2)+vn(r-u[v*2+1],2);if(m<h&&(h=m,s=v),d=d%3===2?d-2:d+1,c[d]!==n)break;if(d=f[d],d===-1){if(d=a[(o[n]+1)%a.length],d!==v&&vn(e-u[d*2],2)+vn(r-u[d*2+1],2)<h)return d;break}}while(d!==l);return s}render(n){const e=n==null?n=new an:void 0,{points:r,halfedges:i,triangles:a}=this;for(let o=0,f=i.length;o<f;++o){const c=i[o];if(c<o)continue;const u=a[o]*2,s=a[c]*2;n.moveTo(r[u],r[u+1]),n.lineTo(r[s],r[s+1])}return this.renderHull(n),e&&e.value()}renderPoints(n,e){e===void 0&&(!n||typeof n.moveTo!="function")&&(e=n,n=null),e=e==null?2:+e;const r=n==null?n=new an:void 0,{points:i}=this;for(let a=0,o=i.length;a<o;a+=2){const f=i[a],c=i[a+1];n.moveTo(f+e,c),n.arc(f,c,e,0,I0)}return r&&r.value()}renderHull(n){const e=n==null?n=new an:void 0,{hull:r,points:i}=this,a=r[0]*2,o=r.length;n.moveTo(i[a],i[a+1]);for(let f=1;f<o;++f){const c=2*r[f];n.lineTo(i[c],i[c+1])}return n.closePath(),e&&e.value()}hullPolygon(){const n=new fi;return this.renderHull(n),n.value()}renderTriangle(n,e){const r=e==null?e=new an:void 0,{points:i,triangles:a}=this,o=a[n*=3]*2,f=a[n+1]*2,c=a[n+2]*2;return e.moveTo(i[o],i[o+1]),e.lineTo(i[f],i[f+1]),e.lineTo(i[c],i[c+1]),e.closePath(),r&&r.value()}*trianglePolygons(){const{triangles:n}=this;for(let e=0,r=n.length/3;e<r;++e)yield this.trianglePolygon(e)}trianglePolygon(n){const e=new fi;return this.renderTriangle(n,e),e.value()}}function C0(t,n,e,r){const i=t.length,a=new Float64Array(i*2);for(let o=0;o<i;++o){const f=t[o];a[o*2]=n.call(r,f,o,t),a[o*2+1]=e.call(r,f,o,t)}return a}function*B0(t,n,e,r){let i=0;for(const a of t)yield n.call(r,a,i,t),yield e.call(r,a,i,t),++i}var qa={},Br={},Or=34,qn=10,Dr=13;function lf(t){return new Function("d","return {"+t.map(function(n,e){return JSON.stringify(n)+": d["+e+'] || ""'}).join(",")+"}")}function O0(t,n){var e=lf(t);return function(r,i){return n(e(r),i,t)}}function Ya(t){var n=Object.create(null),e=[];return t.forEach(function(r){for(var i in r)i in n||e.push(n[i]=i)}),e}function St(t,n){var e=t+"",r=e.length;return r<n?new Array(n-r+1).join(0)+e:e}function D0(t){return t<0?"-"+St(-t,6):t>9999?"+"+St(t,6):St(t,4)}function L0(t){var n=t.getUTCHours(),e=t.getUTCMinutes(),r=t.getUTCSeconds(),i=t.getUTCMilliseconds();return isNaN(t)?"Invalid Date":D0(t.getUTCFullYear())+"-"+St(t.getUTCMonth()+1,2)+"-"+St(t.getUTCDate(),2)+(i?"T"+St(n,2)+":"+St(e,2)+":"+St(r,2)+"."+St(i,3)+"Z":r?"T"+St(n,2)+":"+St(e,2)+":"+St(r,2)+"Z":e||n?"T"+St(n,2)+":"+St(e,2)+"Z":"")}function Zi(t){var n=new RegExp('["'+t+`
\r]`),e=t.charCodeAt(0);function r(h,l){var d,v,m=i(h,function(g,p){if(d)return d(g,p-1);v=g,d=l?O0(g,l):lf(g)});return m.columns=v||[],m}function i(h,l){var d=[],v=h.length,m=0,g=0,p,y=v<=0,w=!1;h.charCodeAt(v-1)===qn&&--v,h.charCodeAt(v-1)===Dr&&--v;function b(){if(y)return Br;if(w)return w=!1,qa;var M,I=m,P;if(h.charCodeAt(I)===Or){for(;m++<v&&h.charCodeAt(m)!==Or||h.charCodeAt(++m)===Or;);return(M=m)>=v?y=!0:(P=h.charCodeAt(m++))===qn?w=!0:P===Dr&&(w=!0,h.charCodeAt(m)===qn&&++m),h.slice(I+1,M-1).replace(/""/g,'"')}for(;m<v;){if((P=h.charCodeAt(M=m++))===qn)w=!0;else if(P===Dr)w=!0,h.charCodeAt(m)===qn&&++m;else if(P!==e)continue;return h.slice(I,M)}return y=!0,h.slice(I,v)}for(;(p=b())!==Br;){for(var _=[];p!==qa&&p!==Br;)_.push(p),p=b();l&&(_=l(_,g++))==null||d.push(_)}return d}function a(h,l){return h.map(function(d){return l.map(function(v){return s(d[v])}).join(t)})}function o(h,l){return l==null&&(l=Ya(h)),[l.map(s).join(t)].concat(a(h,l)).join(`
`)}function f(h,l){return l==null&&(l=Ya(h)),a(h,l).join(`
`)}function c(h){return h.map(u).join(`
`)}function u(h){return h.map(s).join(t)}function s(h){return h==null?"":h instanceof Date?L0(h):n.test(h+="")?'"'+h.replace(/"/g,'""')+'"':h}return{parse:r,parseRows:i,format:o,formatBody:f,formatRows:c,formatRow:u,formatValue:s}}var sn=Zi(","),F0=sn.parse,X2=sn.parseRows,H2=sn.format,U2=sn.formatBody,W2=sn.formatRows,K2=sn.formatRow,Z2=sn.formatValue,ln=Zi("	"),q0=ln.parse,Q2=ln.parseRows,J2=ln.format,j2=ln.formatBody,tg=ln.formatRows,ng=ln.formatRow,eg=ln.formatValue;function rg(t){for(var n in t){var e=t[n].trim(),r,i;if(!e)e=null;else if(e==="true")e=!0;else if(e==="false")e=!1;else if(e==="NaN")e=NaN;else if(!isNaN(r=+e))e=r;else if(i=e.match(/^([-+]\d{2})?\d{4}(-\d{2}(-\d{2})?)?(T\d{2}:\d{2}(:\d{2}(\.\d{3})?)?(Z|[-+]\d{2}:\d{2})?)?$/))Y0&&i[4]&&!i[7]&&(e=e.replace(/-/g,"/").replace(/T/," ")),e=new Date(e);else continue;t[n]=e}return t}const Y0=new Date("2019-01-01T00:00").getHours()||new Date("2019-07-01T00:00").getHours();function G0(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.blob()}function ig(t,n){return fetch(t,n).then(G0)}function V0(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.arrayBuffer()}function ag(t,n){return fetch(t,n).then(V0)}function X0(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.text()}function Qi(t,n){return fetch(t,n).then(X0)}function hf(t){return function(n,e,r){return arguments.length===2&&typeof e=="function"&&(r=e,e=void 0),Qi(n,e).then(function(i){return t(i,r)})}}function og(t,n,e,r){arguments.length===3&&typeof e=="function"&&(r=e,e=void 0);var i=Zi(t);return Qi(n,e).then(function(a){return i.parse(a,r)})}var fg=hf(F0),cg=hf(q0);function ug(t,n){return new Promise(function(e,r){var i=new Image;for(var a in n)i[a]=n[a];i.onerror=r,i.onload=function(){e(i)},i.src=t})}function H0(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);if(!(t.status===204||t.status===205))return t.json()}function sg(t,n){return fetch(t,n).then(H0)}function Ji(t){return(n,e)=>Qi(n,e).then(r=>new DOMParser().parseFromString(r,t))}const lg=Ji("application/xml");var hg=Ji("text/html"),dg=Ji("image/svg+xml");function gg(t,n){var e,r=1;t==null&&(t=0),n==null&&(n=0);function i(){var a,o=e.length,f,c=0,u=0;for(a=0;a<o;++a)f=e[a],c+=f.x,u+=f.y;for(c=(c/o-t)*r,u=(u/o-n)*r,a=0;a<o;++a)f=e[a],f.x-=c,f.y-=u}return i.initialize=function(a){e=a},i.x=function(a){return arguments.length?(t=+a,i):t},i.y=function(a){return arguments.length?(n=+a,i):n},i.strength=function(a){return arguments.length?(r=+a,i):r},i}function U0(t){const n=+this._x.call(null,t),e=+this._y.call(null,t);return df(this.cover(n,e),n,e,t)}function df(t,n,e,r){if(isNaN(n)||isNaN(e))return t;var i,a=t._root,o={data:r},f=t._x0,c=t._y0,u=t._x1,s=t._y1,h,l,d,v,m,g,p,y;if(!a)return t._root=o,t;for(;a.length;)if((m=n>=(h=(f+u)/2))?f=h:u=h,(g=e>=(l=(c+s)/2))?c=l:s=l,i=a,!(a=a[p=g<<1|m]))return i[p]=o,t;if(d=+t._x.call(null,a.data),v=+t._y.call(null,a.data),n===d&&e===v)return o.next=a,i?i[p]=o:t._root=o,t;do i=i?i[p]=new Array(4):t._root=new Array(4),(m=n>=(h=(f+u)/2))?f=h:u=h,(g=e>=(l=(c+s)/2))?c=l:s=l;while((p=g<<1|m)===(y=(v>=l)<<1|d>=h));return i[y]=a,i[p]=o,t}function W0(t){var n,e,r=t.length,i,a,o=new Array(r),f=new Array(r),c=1/0,u=1/0,s=-1/0,h=-1/0;for(e=0;e<r;++e)isNaN(i=+this._x.call(null,n=t[e]))||isNaN(a=+this._y.call(null,n))||(o[e]=i,f[e]=a,i<c&&(c=i),i>s&&(s=i),a<u&&(u=a),a>h&&(h=a));if(c>s||u>h)return this;for(this.cover(c,u).cover(s,h),e=0;e<r;++e)df(this,o[e],f[e],t[e]);return this}function K0(t,n){if(isNaN(t=+t)||isNaN(n=+n))return this;var e=this._x0,r=this._y0,i=this._x1,a=this._y1;if(isNaN(e))i=(e=Math.floor(t))+1,a=(r=Math.floor(n))+1;else{for(var o=i-e||1,f=this._root,c,u;e>t||t>=i||r>n||n>=a;)switch(u=(n<r)<<1|t<e,c=new Array(4),c[u]=f,f=c,o*=2,u){case 0:i=e+o,a=r+o;break;case 1:e=i-o,a=r+o;break;case 2:i=e+o,r=a-o;break;case 3:e=i-o,r=a-o;break}this._root&&this._root.length&&(this._root=f)}return this._x0=e,this._y0=r,this._x1=i,this._y1=a,this}function Z0(){var t=[];return this.visit(function(n){if(!n.length)do t.push(n.data);while(n=n.next)}),t}function Q0(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]}function mt(t,n,e,r,i){this.node=t,this.x0=n,this.y0=e,this.x1=r,this.y1=i}function J0(t,n,e){var r,i=this._x0,a=this._y0,o,f,c,u,s=this._x1,h=this._y1,l=[],d=this._root,v,m;for(d&&l.push(new mt(d,i,a,s,h)),e==null?e=1/0:(i=t-e,a=n-e,s=t+e,h=n+e,e*=e);v=l.pop();)if(!(!(d=v.node)||(o=v.x0)>s||(f=v.y0)>h||(c=v.x1)<i||(u=v.y1)<a))if(d.length){var g=(o+c)/2,p=(f+u)/2;l.push(new mt(d[3],g,p,c,u),new mt(d[2],o,p,g,u),new mt(d[1],g,f,c,p),new mt(d[0],o,f,g,p)),(m=(n>=p)<<1|t>=g)&&(v=l[l.length-1],l[l.length-1]=l[l.length-1-m],l[l.length-1-m]=v)}else{var y=t-+this._x.call(null,d.data),w=n-+this._y.call(null,d.data),b=y*y+w*w;if(b<e){var _=Math.sqrt(e=b);i=t-_,a=n-_,s=t+_,h=n+_,r=d.data}}return r}function j0(t){if(isNaN(s=+this._x.call(null,t))||isNaN(h=+this._y.call(null,t)))return this;var n,e=this._root,r,i,a,o=this._x0,f=this._y0,c=this._x1,u=this._y1,s,h,l,d,v,m,g,p;if(!e)return this;if(e.length)for(;;){if((v=s>=(l=(o+c)/2))?o=l:c=l,(m=h>=(d=(f+u)/2))?f=d:u=d,n=e,!(e=e[g=m<<1|v]))return this;if(!e.length)break;(n[g+1&3]||n[g+2&3]||n[g+3&3])&&(r=n,p=g)}for(;e.data!==t;)if(i=e,!(e=e.next))return this;return(a=e.next)&&delete e.next,i?(a?i.next=a:delete i.next,this):n?(a?n[g]=a:delete n[g],(e=n[0]||n[1]||n[2]||n[3])&&e===(n[3]||n[2]||n[1]||n[0])&&!e.length&&(r?r[p]=e:this._root=e),this):(this._root=a,this)}function th(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this}function nh(){return this._root}function eh(){var t=0;return this.visit(function(n){if(!n.length)do++t;while(n=n.next)}),t}function rh(t){var n=[],e,r=this._root,i,a,o,f,c;for(r&&n.push(new mt(r,this._x0,this._y0,this._x1,this._y1));e=n.pop();)if(!t(r=e.node,a=e.x0,o=e.y0,f=e.x1,c=e.y1)&&r.length){var u=(a+f)/2,s=(o+c)/2;(i=r[3])&&n.push(new mt(i,u,s,f,c)),(i=r[2])&&n.push(new mt(i,a,s,u,c)),(i=r[1])&&n.push(new mt(i,u,o,f,s)),(i=r[0])&&n.push(new mt(i,a,o,u,s))}return this}function ih(t){var n=[],e=[],r;for(this._root&&n.push(new mt(this._root,this._x0,this._y0,this._x1,this._y1));r=n.pop();){var i=r.node;if(i.length){var a,o=r.x0,f=r.y0,c=r.x1,u=r.y1,s=(o+c)/2,h=(f+u)/2;(a=i[0])&&n.push(new mt(a,o,f,s,h)),(a=i[1])&&n.push(new mt(a,s,f,c,h)),(a=i[2])&&n.push(new mt(a,o,h,s,u)),(a=i[3])&&n.push(new mt(a,s,h,c,u))}e.push(r)}for(;r=e.pop();)t(r.node,r.x0,r.y0,r.x1,r.y1);return this}function ah(t){return t[0]}function oh(t){return arguments.length?(this._x=t,this):this._x}function fh(t){return t[1]}function ch(t){return arguments.length?(this._y=t,this):this._y}function ji(t,n,e){var r=new ta(n??ah,e??fh,NaN,NaN,NaN,NaN);return t==null?r:r.addAll(t)}function ta(t,n,e,r,i,a){this._x=t,this._y=n,this._x0=e,this._y0=r,this._x1=i,this._y1=a,this._root=void 0}function Ga(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}var xt=ji.prototype=ta.prototype;xt.copy=function(){var t=new ta(this._x,this._y,this._x0,this._y0,this._x1,this._y1),n=this._root,e,r;if(!n)return t;if(!n.length)return t._root=Ga(n),t;for(e=[{source:n,target:t._root=new Array(4)}];n=e.pop();)for(var i=0;i<4;++i)(r=n.source[i])&&(r.length?e.push({source:r,target:n.target[i]=new Array(4)}):n.target[i]=Ga(r));return t};xt.add=U0;xt.addAll=W0;xt.cover=K0;xt.data=Z0;xt.extent=Q0;xt.find=J0;xt.remove=j0;xt.removeAll=th;xt.root=nh;xt.size=eh;xt.visit=rh;xt.visitAfter=ih;xt.x=oh;xt.y=ch;function it(t){return function(){return t}}function Qt(t){return(t()-.5)*1e-6}function uh(t){return t.x+t.vx}function sh(t){return t.y+t.vy}function pg(t){var n,e,r,i=1,a=1;typeof t!="function"&&(t=it(t==null?1:+t));function o(){for(var u,s=n.length,h,l,d,v,m,g,p=0;p<a;++p)for(h=ji(n,uh,sh).visitAfter(f),u=0;u<s;++u)l=n[u],m=e[l.index],g=m*m,d=l.x+l.vx,v=l.y+l.vy,h.visit(y);function y(w,b,_,M,I){var P=w.data,R=w.r,E=m+R;if(P){if(P.index>l.index){var $=d-P.x-P.vx,z=v-P.y-P.vy,T=$*$+z*z;T<E*E&&($===0&&($=Qt(r),T+=$*$),z===0&&(z=Qt(r),T+=z*z),T=(E-(T=Math.sqrt(T)))/T*i,l.vx+=($*=T)*(E=(R*=R)/(g+R)),l.vy+=(z*=T)*E,P.vx-=$*(E=1-E),P.vy-=z*E)}return}return b>d+E||M<d-E||_>v+E||I<v-E}}function f(u){if(u.data)return u.r=e[u.data.index];for(var s=u.r=0;s<4;++s)u[s]&&u[s].r>u.r&&(u.r=u[s].r)}function c(){if(n){var u,s=n.length,h;for(e=new Array(s),u=0;u<s;++u)h=n[u],e[h.index]=+t(h,u,n)}}return o.initialize=function(u,s){n=u,r=s,c()},o.iterations=function(u){return arguments.length?(a=+u,o):a},o.strength=function(u){return arguments.length?(i=+u,o):i},o.radius=function(u){return arguments.length?(t=typeof u=="function"?u:it(+u),c(),o):t},o}function lh(t){return t.index}function Va(t,n){var e=t.get(n);if(!e)throw new Error("node not found: "+n);return e}function bg(t){var n=lh,e=h,r,i=it(30),a,o,f,c,u,s=1;t==null&&(t=[]);function h(g){return 1/Math.min(f[g.source.index],f[g.target.index])}function l(g){for(var p=0,y=t.length;p<s;++p)for(var w=0,b,_,M,I,P,R,E;w<y;++w)b=t[w],_=b.source,M=b.target,I=M.x+M.vx-_.x-_.vx||Qt(u),P=M.y+M.vy-_.y-_.vy||Qt(u),R=Math.sqrt(I*I+P*P),R=(R-a[w])/R*g*r[w],I*=R,P*=R,M.vx-=I*(E=c[w]),M.vy-=P*E,_.vx+=I*(E=1-E),_.vy+=P*E}function d(){if(o){var g,p=o.length,y=t.length,w=new Map(o.map((_,M)=>[n(_,M,o),_])),b;for(g=0,f=new Array(p);g<y;++g)b=t[g],b.index=g,typeof b.source!="object"&&(b.source=Va(w,b.source)),typeof b.target!="object"&&(b.target=Va(w,b.target)),f[b.source.index]=(f[b.source.index]||0)+1,f[b.target.index]=(f[b.target.index]||0)+1;for(g=0,c=new Array(y);g<y;++g)b=t[g],c[g]=f[b.source.index]/(f[b.source.index]+f[b.target.index]);r=new Array(y),v(),a=new Array(y),m()}}function v(){if(o)for(var g=0,p=t.length;g<p;++g)r[g]=+e(t[g],g,t)}function m(){if(o)for(var g=0,p=t.length;g<p;++g)a[g]=+i(t[g],g,t)}return l.initialize=function(g,p){o=g,u=p,d()},l.links=function(g){return arguments.length?(t=g,d(),l):t},l.id=function(g){return arguments.length?(n=g,l):n},l.iterations=function(g){return arguments.length?(s=+g,l):s},l.strength=function(g){return arguments.length?(e=typeof g=="function"?g:it(+g),v(),l):e},l.distance=function(g){return arguments.length?(i=typeof g=="function"?g:it(+g),m(),l):i},l}const hh=1664525,dh=1013904223,Xa=4294967296;function gh(){let t=1;return()=>(t=(hh*t+dh)%Xa)/Xa}function ph(t){return t.x}function bh(t){return t.y}var mh=10,vh=Math.PI*(3-Math.sqrt(5));function mg(t){var n,e=1,r=.001,i=1-Math.pow(r,1/300),a=0,o=.6,f=new Map,c=qi(h),u=kn("tick","end"),s=gh();t==null&&(t=[]);function h(){l(),u.call("tick",n),e<r&&(c.stop(),u.call("end",n))}function l(m){var g,p=t.length,y;m===void 0&&(m=1);for(var w=0;w<m;++w)for(e+=(a-e)*i,f.forEach(function(b){b(e)}),g=0;g<p;++g)y=t[g],y.fx==null?y.x+=y.vx*=o:(y.x=y.fx,y.vx=0),y.fy==null?y.y+=y.vy*=o:(y.y=y.fy,y.vy=0);return n}function d(){for(var m=0,g=t.length,p;m<g;++m){if(p=t[m],p.index=m,p.fx!=null&&(p.x=p.fx),p.fy!=null&&(p.y=p.fy),isNaN(p.x)||isNaN(p.y)){var y=mh*Math.sqrt(.5+m),w=m*vh;p.x=y*Math.cos(w),p.y=y*Math.sin(w)}(isNaN(p.vx)||isNaN(p.vy))&&(p.vx=p.vy=0)}}function v(m){return m.initialize&&m.initialize(t,s),m}return d(),n={tick:l,restart:function(){return c.restart(h),n},stop:function(){return c.stop(),n},nodes:function(m){return arguments.length?(t=m,d(),f.forEach(v),n):t},alpha:function(m){return arguments.length?(e=+m,n):e},alphaMin:function(m){return arguments.length?(r=+m,n):r},alphaDecay:function(m){return arguments.length?(i=+m,n):+i},alphaTarget:function(m){return arguments.length?(a=+m,n):a},velocityDecay:function(m){return arguments.length?(o=1-m,n):1-o},randomSource:function(m){return arguments.length?(s=m,f.forEach(v),n):s},force:function(m,g){return arguments.length>1?(g==null?f.delete(m):f.set(m,v(g)),n):f.get(m)},find:function(m,g,p){var y=0,w=t.length,b,_,M,I,P;for(p==null?p=1/0:p*=p,y=0;y<w;++y)I=t[y],b=m-I.x,_=g-I.y,M=b*b+_*_,M<p&&(P=I,p=M);return P},on:function(m,g){return arguments.length>1?(u.on(m,g),n):u.on(m)}}}function vg(){var t,n,e,r,i=it(-30),a,o=1,f=1/0,c=.81;function u(d){var v,m=t.length,g=ji(t,ph,bh).visitAfter(h);for(r=d,v=0;v<m;++v)n=t[v],g.visit(l)}function s(){if(t){var d,v=t.length,m;for(a=new Array(v),d=0;d<v;++d)m=t[d],a[m.index]=+i(m,d,t)}}function h(d){var v=0,m,g,p=0,y,w,b;if(d.length){for(y=w=b=0;b<4;++b)(m=d[b])&&(g=Math.abs(m.value))&&(v+=m.value,p+=g,y+=g*m.x,w+=g*m.y);d.x=y/p,d.y=w/p}else{m=d,m.x=m.data.x,m.y=m.data.y;do v+=a[m.data.index];while(m=m.next)}d.value=v}function l(d,v,m,g){if(!d.value)return!0;var p=d.x-n.x,y=d.y-n.y,w=g-v,b=p*p+y*y;if(w*w/c<b)return b<f&&(p===0&&(p=Qt(e),b+=p*p),y===0&&(y=Qt(e),b+=y*y),b<o&&(b=Math.sqrt(o*b)),n.vx+=p*d.value*r/b,n.vy+=y*d.value*r/b),!0;if(d.length||b>=f)return;(d.data!==n||d.next)&&(p===0&&(p=Qt(e),b+=p*p),y===0&&(y=Qt(e),b+=y*y),b<o&&(b=Math.sqrt(o*b)));do d.data!==n&&(w=a[d.data.index]*r/b,n.vx+=p*w,n.vy+=y*w);while(d=d.next)}return u.initialize=function(d,v){t=d,e=v,s()},u.strength=function(d){return arguments.length?(i=typeof d=="function"?d:it(+d),s(),u):i},u.distanceMin=function(d){return arguments.length?(o=d*d,u):Math.sqrt(o)},u.distanceMax=function(d){return arguments.length?(f=d*d,u):Math.sqrt(f)},u.theta=function(d){return arguments.length?(c=d*d,u):Math.sqrt(c)},u}function yg(t,n,e){var r,i=it(.1),a,o;typeof t!="function"&&(t=it(+t)),n==null&&(n=0),e==null&&(e=0);function f(u){for(var s=0,h=r.length;s<h;++s){var l=r[s],d=l.x-n||1e-6,v=l.y-e||1e-6,m=Math.sqrt(d*d+v*v),g=(o[s]-m)*a[s]*u/m;l.vx+=d*g,l.vy+=v*g}}function c(){if(r){var u,s=r.length;for(a=new Array(s),o=new Array(s),u=0;u<s;++u)o[u]=+t(r[u],u,r),a[u]=isNaN(o[u])?0:+i(r[u],u,r)}}return f.initialize=function(u){r=u,c()},f.strength=function(u){return arguments.length?(i=typeof u=="function"?u:it(+u),c(),f):i},f.radius=function(u){return arguments.length?(t=typeof u=="function"?u:it(+u),c(),f):t},f.x=function(u){return arguments.length?(n=+u,f):n},f.y=function(u){return arguments.length?(e=+u,f):e},f}function wg(t){var n=it(.1),e,r,i;typeof t!="function"&&(t=it(t==null?0:+t));function a(f){for(var c=0,u=e.length,s;c<u;++c)s=e[c],s.vx+=(i[c]-s.x)*r[c]*f}function o(){if(e){var f,c=e.length;for(r=new Array(c),i=new Array(c),f=0;f<c;++f)r[f]=isNaN(i[f]=+t(e[f],f,e))?0:+n(e[f],f,e)}}return a.initialize=function(f){e=f,o()},a.strength=function(f){return arguments.length?(n=typeof f=="function"?f:it(+f),o(),a):n},a.x=function(f){return arguments.length?(t=typeof f=="function"?f:it(+f),o(),a):t},a}function _g(t){var n=it(.1),e,r,i;typeof t!="function"&&(t=it(t==null?0:+t));function a(f){for(var c=0,u=e.length,s;c<u;++c)s=e[c],s.vy+=(i[c]-s.y)*r[c]*f}function o(){if(e){var f,c=e.length;for(r=new Array(c),i=new Array(c),f=0;f<c;++f)r[f]=isNaN(i[f]=+t(e[f],f,e))?0:+n(e[f],f,e)}}return a.initialize=function(f){e=f,o()},a.strength=function(f){return arguments.length?(n=typeof f=="function"?f:it(+f),o(),a):n},a.y=function(f){return arguments.length?(t=typeof f=="function"?f:it(+f),o(),a):t},a}var F=1e-6,ce=1e-12,X=Math.PI,et=X/2,Ue=X/4,yt=X*2,W=180/X,q=X/180,H=Math.abs,Bn=Math.atan,wt=Math.atan2,D=Math.cos,we=Math.ceil,gf=Math.exp,ci=Math.hypot,We=Math.log,Lr=Math.pow,O=Math.sin,Pt=Math.sign||function(t){return t>0?1:t<0?-1:0},at=Math.sqrt,na=Math.tan;function pf(t){return t>1?0:t<-1?X:Math.acos(t)}function _t(t){return t>1?et:t<-1?-et:Math.asin(t)}function Ha(t){return(t=O(t/2))*t}function tt(){}function Ke(t,n){t&&Wa.hasOwnProperty(t.type)&&Wa[t.type](t,n)}var Ua={Feature:function(t,n){Ke(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)Ke(e[r].geometry,n)}},Wa={Sphere:function(t,n){n.sphere()},Point:function(t,n){t=t.coordinates,n.point(t[0],t[1],t[2])},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)t=e[r],n.point(t[0],t[1],t[2])},LineString:function(t,n){ui(t.coordinates,n,0)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)ui(e[r],n,0)},Polygon:function(t,n){Ka(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)Ka(e[r],n)},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)Ke(e[r],n)}};function ui(t,n,e){var r=-1,i=t.length-e,a;for(n.lineStart();++r<i;)a=t[r],n.point(a[0],a[1],a[2]);n.lineEnd()}function Ka(t,n){var e=-1,r=t.length;for(n.polygonStart();++e<r;)ui(t[e],n,1);n.polygonEnd()}function zt(t,n){t&&Ua.hasOwnProperty(t.type)?Ua[t.type](t,n):Ke(t,n)}var Ze=new st,Qe=new st,bf,mf,si,li,hi,Ot={point:tt,lineStart:tt,lineEnd:tt,polygonStart:function(){Ze=new st,Ot.lineStart=yh,Ot.lineEnd=wh},polygonEnd:function(){var t=+Ze;Qe.add(t<0?yt+t:t),this.lineStart=this.lineEnd=this.point=tt},sphere:function(){Qe.add(yt)}};function yh(){Ot.point=_h}function wh(){vf(bf,mf)}function _h(t,n){Ot.point=vf,bf=t,mf=n,t*=q,n*=q,si=t,li=D(n=n/2+Ue),hi=O(n)}function vf(t,n){t*=q,n*=q,n=n/2+Ue;var e=t-si,r=e>=0?1:-1,i=r*e,a=D(n),o=O(n),f=hi*o,c=li*a+f*D(i),u=f*r*O(i);Ze.add(wt(u,c)),si=t,li=a,hi=o}function xg(t){return Qe=new st,zt(t,Ot),Qe*2}function Je(t){return[wt(t[1],t[0]),_t(t[2])]}function cn(t){var n=t[0],e=t[1],r=D(e);return[r*D(n),r*O(n),O(e)]}function _e(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]}function In(t,n){return[t[1]*n[2]-t[2]*n[1],t[2]*n[0]-t[0]*n[2],t[0]*n[1]-t[1]*n[0]]}function Fr(t,n){t[0]+=n[0],t[1]+=n[1],t[2]+=n[2]}function xe(t,n){return[t[0]*n,t[1]*n,t[2]*n]}function je(t){var n=at(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=n,t[1]/=n,t[2]/=n}var j,At,nt,$t,en,yf,wf,En,jn,Kt,Xt,Yt={point:di,lineStart:Za,lineEnd:Qa,polygonStart:function(){Yt.point=xf,Yt.lineStart=xh,Yt.lineEnd=Mh,jn=new st,Ot.polygonStart()},polygonEnd:function(){Ot.polygonEnd(),Yt.point=di,Yt.lineStart=Za,Yt.lineEnd=Qa,Ze<0?(j=-(nt=180),At=-($t=90)):jn>F?$t=90:jn<-F&&(At=-90),Xt[0]=j,Xt[1]=nt},sphere:function(){j=-(nt=180),At=-($t=90)}};function di(t,n){Kt.push(Xt=[j=t,nt=t]),n<At&&(At=n),n>$t&&($t=n)}function _f(t,n){var e=cn([t*q,n*q]);if(En){var r=In(En,e),i=[r[1],-r[0],0],a=In(i,r);je(a),a=Je(a);var o=t-en,f=o>0?1:-1,c=a[0]*W*f,u,s=H(o)>180;s^(f*en<c&&c<f*t)?(u=a[1]*W,u>$t&&($t=u)):(c=(c+360)%360-180,s^(f*en<c&&c<f*t)?(u=-a[1]*W,u<At&&(At=u)):(n<At&&(At=n),n>$t&&($t=n))),s?t<en?Et(j,t)>Et(j,nt)&&(nt=t):Et(t,nt)>Et(j,nt)&&(j=t):nt>=j?(t<j&&(j=t),t>nt&&(nt=t)):t>en?Et(j,t)>Et(j,nt)&&(nt=t):Et(t,nt)>Et(j,nt)&&(j=t)}else Kt.push(Xt=[j=t,nt=t]);n<At&&(At=n),n>$t&&($t=n),En=e,en=t}function Za(){Yt.point=_f}function Qa(){Xt[0]=j,Xt[1]=nt,Yt.point=di,En=null}function xf(t,n){if(En){var e=t-en;jn.add(H(e)>180?e+(e>0?360:-360):e)}else yf=t,wf=n;Ot.point(t,n),_f(t,n)}function xh(){Ot.lineStart()}function Mh(){xf(yf,wf),Ot.lineEnd(),H(jn)>F&&(j=-(nt=180)),Xt[0]=j,Xt[1]=nt,En=null}function Et(t,n){return(n-=t)<0?n+360:n}function Sh(t,n){return t[0]-n[0]}function Ja(t,n){return t[0]<=t[1]?t[0]<=n&&n<=t[1]:n<t[0]||t[1]<n}function Mg(t){var n,e,r,i,a,o,f;if($t=nt=-(j=At=1/0),Kt=[],zt(t,Yt),e=Kt.length){for(Kt.sort(Sh),n=1,r=Kt[0],a=[r];n<e;++n)i=Kt[n],Ja(r,i[0])||Ja(r,i[1])?(Et(r[0],i[1])>Et(r[0],r[1])&&(r[1]=i[1]),Et(i[0],r[1])>Et(r[0],r[1])&&(r[0]=i[0])):a.push(r=i);for(o=-1/0,e=a.length-1,n=0,r=a[e];n<=e;r=i,++n)i=a[n],(f=Et(r[1],i[0]))>o&&(o=f,j=i[0],nt=r[1])}return Kt=Xt=null,j===1/0||At===1/0?[[NaN,NaN],[NaN,NaN]]:[[j,At],[nt,$t]]}var Hn,tr,nr,er,rr,ir,ar,or,gi,pi,bi,Mf,Sf,dt,gt,pt,Nt={sphere:tt,point:ea,lineStart:ja,lineEnd:to,polygonStart:function(){Nt.lineStart=$h,Nt.lineEnd=Rh},polygonEnd:function(){Nt.lineStart=ja,Nt.lineEnd=to}};function ea(t,n){t*=q,n*=q;var e=D(n);he(e*D(t),e*O(t),O(n))}function he(t,n,e){++Hn,nr+=(t-nr)/Hn,er+=(n-er)/Hn,rr+=(e-rr)/Hn}function ja(){Nt.point=Ah}function Ah(t,n){t*=q,n*=q;var e=D(n);dt=e*D(t),gt=e*O(t),pt=O(n),Nt.point=Eh,he(dt,gt,pt)}function Eh(t,n){t*=q,n*=q;var e=D(n),r=e*D(t),i=e*O(t),a=O(n),o=wt(at((o=gt*a-pt*i)*o+(o=pt*r-dt*a)*o+(o=dt*i-gt*r)*o),dt*r+gt*i+pt*a);tr+=o,ir+=o*(dt+(dt=r)),ar+=o*(gt+(gt=i)),or+=o*(pt+(pt=a)),he(dt,gt,pt)}function to(){Nt.point=ea}function $h(){Nt.point=Ph}function Rh(){Af(Mf,Sf),Nt.point=ea}function Ph(t,n){Mf=t,Sf=n,t*=q,n*=q,Nt.point=Af;var e=D(n);dt=e*D(t),gt=e*O(t),pt=O(n),he(dt,gt,pt)}function Af(t,n){t*=q,n*=q;var e=D(n),r=e*D(t),i=e*O(t),a=O(n),o=gt*a-pt*i,f=pt*r-dt*a,c=dt*i-gt*r,u=ci(o,f,c),s=_t(u),h=u&&-s/u;gi.add(h*o),pi.add(h*f),bi.add(h*c),tr+=s,ir+=s*(dt+(dt=r)),ar+=s*(gt+(gt=i)),or+=s*(pt+(pt=a)),he(dt,gt,pt)}function Sg(t){Hn=tr=nr=er=rr=ir=ar=or=0,gi=new st,pi=new st,bi=new st,zt(t,Nt);var n=+gi,e=+pi,r=+bi,i=ci(n,e,r);return i<ce&&(n=ir,e=ar,r=or,tr<F&&(n=nr,e=er,r=rr),i=ci(n,e,r),i<ce)?[NaN,NaN]:[wt(e,n)*W,_t(r/i)*W]}function yn(t){return function(){return t}}function mi(t,n){function e(r,i){return r=t(r,i),n(r[0],r[1])}return t.invert&&n.invert&&(e.invert=function(r,i){return r=n.invert(r,i),r&&t.invert(r[0],r[1])}),e}function vi(t,n){return H(t)>X&&(t-=Math.round(t/yt)*yt),[t,n]}vi.invert=vi;function ra(t,n,e){return(t%=yt)?n||e?mi(eo(t),ro(n,e)):eo(t):n||e?ro(n,e):vi}function no(t){return function(n,e){return n+=t,H(n)>X&&(n-=Math.round(n/yt)*yt),[n,e]}}function eo(t){var n=no(t);return n.invert=no(-t),n}function ro(t,n){var e=D(t),r=O(t),i=D(n),a=O(n);function o(f,c){var u=D(c),s=D(f)*u,h=O(f)*u,l=O(c),d=l*e+s*r;return[wt(h*i-d*a,s*e-l*r),_t(d*i+h*a)]}return o.invert=function(f,c){var u=D(c),s=D(f)*u,h=O(f)*u,l=O(c),d=l*i-h*a;return[wt(h*i+l*a,s*e+d*r),_t(d*e-s*r)]},o}function Ih(t){t=ra(t[0]*q,t[1]*q,t.length>2?t[2]*q:0);function n(e){return e=t(e[0]*q,e[1]*q),e[0]*=W,e[1]*=W,e}return n.invert=function(e){return e=t.invert(e[0]*q,e[1]*q),e[0]*=W,e[1]*=W,e},n}function Ef(t,n,e,r,i,a){if(e){var o=D(n),f=O(n),c=r*e;i==null?(i=n+r*yt,a=n-c/2):(i=io(o,i),a=io(o,a),(r>0?i<a:i>a)&&(i+=r*yt));for(var u,s=i;r>0?s>a:s<a;s-=c)u=Je([o,-f*D(s),-f*O(s)]),t.point(u[0],u[1])}}function io(t,n){n=cn(n),n[0]-=t,je(n);var e=pf(-n[1]);return((-n[2]<0?-e:e)+yt-F)%yt}function Ag(){var t=yn([0,0]),n=yn(90),e=yn(2),r,i,a={point:o};function o(c,u){r.push(c=i(c,u)),c[0]*=W,c[1]*=W}function f(){var c=t.apply(this,arguments),u=n.apply(this,arguments)*q,s=e.apply(this,arguments)*q;return r=[],i=ra(-c[0]*q,-c[1]*q,0).invert,Ef(a,u,s,1),c={type:"Polygon",coordinates:[r]},r=i=null,c}return f.center=function(c){return arguments.length?(t=typeof c=="function"?c:yn([+c[0],+c[1]]),f):t},f.radius=function(c){return arguments.length?(n=typeof c=="function"?c:yn(+c),f):n},f.precision=function(c){return arguments.length?(e=typeof c=="function"?c:yn(+c),f):e},f}function $f(){var t=[],n;return{point:function(e,r,i){n.push([e,r,i])},lineStart:function(){t.push(n=[])},lineEnd:tt,rejoin:function(){t.length>1&&t.push(t.pop().concat(t.shift()))},result:function(){var e=t;return t=[],n=null,e}}}function De(t,n){return H(t[0]-n[0])<F&&H(t[1]-n[1])<F}function Me(t,n,e,r){this.x=t,this.z=n,this.o=e,this.e=r,this.v=!1,this.n=this.p=null}function Rf(t,n,e,r,i){var a=[],o=[],f,c;if(t.forEach(function(v){if(!((m=v.length-1)<=0)){var m,g=v[0],p=v[m],y;if(De(g,p)){if(!g[2]&&!p[2]){for(i.lineStart(),f=0;f<m;++f)i.point((g=v[f])[0],g[1]);i.lineEnd();return}p[0]+=2*F}a.push(y=new Me(g,v,null,!0)),o.push(y.o=new Me(g,null,y,!1)),a.push(y=new Me(p,v,null,!1)),o.push(y.o=new Me(p,null,y,!0))}}),!!a.length){for(o.sort(n),ao(a),ao(o),f=0,c=o.length;f<c;++f)o[f].e=e=!e;for(var u=a[0],s,h;;){for(var l=u,d=!0;l.v;)if((l=l.n)===u)return;s=l.z,i.lineStart();do{if(l.v=l.o.v=!0,l.e){if(d)for(f=0,c=s.length;f<c;++f)i.point((h=s[f])[0],h[1]);else r(l.x,l.n.x,1,i);l=l.n}else{if(d)for(s=l.p.z,f=s.length-1;f>=0;--f)i.point((h=s[f])[0],h[1]);else r(l.x,l.p.x,-1,i);l=l.p}l=l.o,s=l.z,d=!d}while(!l.v);i.lineEnd()}}}function ao(t){if(n=t.length){for(var n,e=0,r=t[0],i;++e<n;)r.n=i=t[e],i.p=r,r=i;r.n=i=t[0],i.p=r}}function qr(t){return H(t[0])<=X?t[0]:Pt(t[0])*((H(t[0])+X)%yt-X)}function Pf(t,n){var e=qr(n),r=n[1],i=O(r),a=[O(e),-D(e),0],o=0,f=0,c=new st;i===1?r=et+F:i===-1&&(r=-et-F);for(var u=0,s=t.length;u<s;++u)if(l=(h=t[u]).length)for(var h,l,d=h[l-1],v=qr(d),m=d[1]/2+Ue,g=O(m),p=D(m),y=0;y<l;++y,v=b,g=M,p=I,d=w){var w=h[y],b=qr(w),_=w[1]/2+Ue,M=O(_),I=D(_),P=b-v,R=P>=0?1:-1,E=R*P,$=E>X,z=g*M;if(c.add(wt(z*R*O(E),p*I+z*D(E))),o+=$?P+R*yt:P,$^v>=e^b>=e){var T=In(cn(d),cn(w));je(T);var x=In(a,T);je(x);var A=($^P>=0?-1:1)*_t(x[2]);(r>A||r===A&&(T[0]||T[1]))&&(f+=$^P>=0?1:-1)}}return(o<-F||o<F&&c<-ce)^f&1}function If(t,n,e,r){return function(i){var a=n(i),o=$f(),f=n(o),c=!1,u,s,h,l={point:d,lineStart:m,lineEnd:g,polygonStart:function(){l.point=p,l.lineStart=y,l.lineEnd=w,s=[],u=[]},polygonEnd:function(){l.point=d,l.lineStart=m,l.lineEnd=g,s=Lo(s);var b=Pf(u,r);s.length?(c||(i.polygonStart(),c=!0),Rf(s,Th,b,e,i)):b&&(c||(i.polygonStart(),c=!0),i.lineStart(),e(null,null,1,i),i.lineEnd()),c&&(i.polygonEnd(),c=!1),s=u=null},sphere:function(){i.polygonStart(),i.lineStart(),e(null,null,1,i),i.lineEnd(),i.polygonEnd()}};function d(b,_){t(b,_)&&i.point(b,_)}function v(b,_){a.point(b,_)}function m(){l.point=v,a.lineStart()}function g(){l.point=d,a.lineEnd()}function p(b,_){h.push([b,_]),f.point(b,_)}function y(){f.lineStart(),h=[]}function w(){p(h[0][0],h[0][1]),f.lineEnd();var b=f.clean(),_=o.result(),M,I=_.length,P,R,E;if(h.pop(),u.push(h),h=null,!!I){if(b&1){if(R=_[0],(P=R.length-1)>0){for(c||(i.polygonStart(),c=!0),i.lineStart(),M=0;M<P;++M)i.point((E=R[M])[0],E[1]);i.lineEnd()}return}I>1&&b&2&&_.push(_.pop().concat(_.shift())),s.push(_.filter(Nh))}}return l}}function Nh(t){return t.length>1}function Th(t,n){return((t=t.x)[0]<0?t[1]-et-F:et-t[1])-((n=n.x)[0]<0?n[1]-et-F:et-n[1])}const oo=If(function(){return!0},zh,Ch,[-X,-et]);function zh(t){var n=NaN,e=NaN,r=NaN,i;return{lineStart:function(){t.lineStart(),i=1},point:function(a,o){var f=a>0?X:-X,c=H(a-n);H(c-X)<F?(t.point(n,e=(e+o)/2>0?et:-et),t.point(r,e),t.lineEnd(),t.lineStart(),t.point(f,e),t.point(a,e),i=0):r!==f&&c>=X&&(H(n-r)<F&&(n-=r*F),H(a-f)<F&&(a-=f*F),e=kh(n,e,a,o),t.point(r,e),t.lineEnd(),t.lineStart(),t.point(f,e),i=0),t.point(n=a,e=o),r=f},lineEnd:function(){t.lineEnd(),n=e=NaN},clean:function(){return 2-i}}}function kh(t,n,e,r){var i,a,o=O(t-e);return H(o)>F?Bn((O(n)*(a=D(r))*O(e)-O(r)*(i=D(n))*O(t))/(i*a*o)):(n+r)/2}function Ch(t,n,e,r){var i;if(t==null)i=e*et,r.point(-X,i),r.point(0,i),r.point(X,i),r.point(X,0),r.point(X,-i),r.point(0,-i),r.point(-X,-i),r.point(-X,0),r.point(-X,i);else if(H(t[0]-n[0])>F){var a=t[0]<n[0]?X:-X;i=e*a/2,r.point(-a,i),r.point(0,i),r.point(a,i)}else r.point(n[0],n[1])}function Bh(t){var n=D(t),e=2*q,r=n>0,i=H(n)>F;function a(s,h,l,d){Ef(d,t,e,l,s,h)}function o(s,h){return D(s)*D(h)>n}function f(s){var h,l,d,v,m;return{lineStart:function(){v=d=!1,m=1},point:function(g,p){var y=[g,p],w,b=o(g,p),_=r?b?0:u(g,p):b?u(g+(g<0?X:-X),p):0;if(!h&&(v=d=b)&&s.lineStart(),b!==d&&(w=c(h,y),(!w||De(h,w)||De(y,w))&&(y[2]=1)),b!==d)m=0,b?(s.lineStart(),w=c(y,h),s.point(w[0],w[1])):(w=c(h,y),s.point(w[0],w[1],2),s.lineEnd()),h=w;else if(i&&h&&r^b){var M;!(_&l)&&(M=c(y,h,!0))&&(m=0,r?(s.lineStart(),s.point(M[0][0],M[0][1]),s.point(M[1][0],M[1][1]),s.lineEnd()):(s.point(M[1][0],M[1][1]),s.lineEnd(),s.lineStart(),s.point(M[0][0],M[0][1],3)))}b&&(!h||!De(h,y))&&s.point(y[0],y[1]),h=y,d=b,l=_},lineEnd:function(){d&&s.lineEnd(),h=null},clean:function(){return m|(v&&d)<<1}}}function c(s,h,l){var d=cn(s),v=cn(h),m=[1,0,0],g=In(d,v),p=_e(g,g),y=g[0],w=p-y*y;if(!w)return!l&&s;var b=n*p/w,_=-n*y/w,M=In(m,g),I=xe(m,b),P=xe(g,_);Fr(I,P);var R=M,E=_e(I,R),$=_e(R,R),z=E*E-$*(_e(I,I)-1);if(!(z<0)){var T=at(z),x=xe(R,(-E-T)/$);if(Fr(x,I),x=Je(x),!l)return x;var A=s[0],S=h[0],N=s[1],C=h[1],k;S<A&&(k=A,A=S,S=k);var B=S-A,L=H(B-X)<F,Y=L||B<F;if(!L&&C<N&&(k=N,N=C,C=k),Y?L?N+C>0^x[1]<(H(x[0]-A)<F?N:C):N<=x[1]&&x[1]<=C:B>X^(A<=x[0]&&x[0]<=S)){var Q=xe(R,(-E+T)/$);return Fr(Q,I),[x,Je(Q)]}}}function u(s,h){var l=r?t:X-t,d=0;return s<-l?d|=1:s>l&&(d|=2),h<-l?d|=4:h>l&&(d|=8),d}return If(o,f,a,r?[0,-t]:[-X,t-X])}function Oh(t,n,e,r,i,a){var o=t[0],f=t[1],c=n[0],u=n[1],s=0,h=1,l=c-o,d=u-f,v;if(v=e-o,!(!l&&v>0)){if(v/=l,l<0){if(v<s)return;v<h&&(h=v)}else if(l>0){if(v>h)return;v>s&&(s=v)}if(v=i-o,!(!l&&v<0)){if(v/=l,l<0){if(v>h)return;v>s&&(s=v)}else if(l>0){if(v<s)return;v<h&&(h=v)}if(v=r-f,!(!d&&v>0)){if(v/=d,d<0){if(v<s)return;v<h&&(h=v)}else if(d>0){if(v>h)return;v>s&&(s=v)}if(v=a-f,!(!d&&v<0)){if(v/=d,d<0){if(v>h)return;v>s&&(s=v)}else if(d>0){if(v<s)return;v<h&&(h=v)}return s>0&&(t[0]=o+s*l,t[1]=f+s*d),h<1&&(n[0]=o+h*l,n[1]=f+h*d),!0}}}}}var Un=1e9,Se=-Un;function ia(t,n,e,r){function i(u,s){return t<=u&&u<=e&&n<=s&&s<=r}function a(u,s,h,l){var d=0,v=0;if(u==null||(d=o(u,h))!==(v=o(s,h))||c(u,s)<0^h>0)do l.point(d===0||d===3?t:e,d>1?r:n);while((d=(d+h+4)%4)!==v);else l.point(s[0],s[1])}function o(u,s){return H(u[0]-t)<F?s>0?0:3:H(u[0]-e)<F?s>0?2:1:H(u[1]-n)<F?s>0?1:0:s>0?3:2}function f(u,s){return c(u.x,s.x)}function c(u,s){var h=o(u,1),l=o(s,1);return h!==l?h-l:h===0?s[1]-u[1]:h===1?u[0]-s[0]:h===2?u[1]-s[1]:s[0]-u[0]}return function(u){var s=u,h=$f(),l,d,v,m,g,p,y,w,b,_,M,I={point:P,lineStart:z,lineEnd:T,polygonStart:E,polygonEnd:$};function P(A,S){i(A,S)&&s.point(A,S)}function R(){for(var A=0,S=0,N=d.length;S<N;++S)for(var C=d[S],k=1,B=C.length,L=C[0],Y,Q,U=L[0],J=L[1];k<B;++k)Y=U,Q=J,L=C[k],U=L[0],J=L[1],Q<=r?J>r&&(U-Y)*(r-Q)>(J-Q)*(t-Y)&&++A:J<=r&&(U-Y)*(r-Q)<(J-Q)*(t-Y)&&--A;return A}function E(){s=h,l=[],d=[],M=!0}function $(){var A=R(),S=M&&A,N=(l=Lo(l)).length;(S||N)&&(u.polygonStart(),S&&(u.lineStart(),a(null,null,1,u),u.lineEnd()),N&&Rf(l,f,A,a,u),u.polygonEnd()),s=u,l=d=v=null}function z(){I.point=x,d&&d.push(v=[]),_=!0,b=!1,y=w=NaN}function T(){l&&(x(m,g),p&&b&&h.rejoin(),l.push(h.result())),I.point=P,b&&s.lineEnd()}function x(A,S){var N=i(A,S);if(d&&v.push([A,S]),_)m=A,g=S,p=N,_=!1,N&&(s.lineStart(),s.point(A,S));else if(N&&b)s.point(A,S);else{var C=[y=Math.max(Se,Math.min(Un,y)),w=Math.max(Se,Math.min(Un,w))],k=[A=Math.max(Se,Math.min(Un,A)),S=Math.max(Se,Math.min(Un,S))];Oh(C,k,t,n,e,r)?(b||(s.lineStart(),s.point(C[0],C[1])),s.point(k[0],k[1]),N||s.lineEnd(),M=!1):N&&(s.lineStart(),s.point(A,S),M=!1)}y=A,w=S,b=N}return I}}function Eg(){var t=0,n=0,e=960,r=500,i,a,o;return o={stream:function(f){return i&&a===f?i:i=ia(t,n,e,r)(a=f)},extent:function(f){return arguments.length?(t=+f[0][0],n=+f[0][1],e=+f[1][0],r=+f[1][1],i=a=null,o):[[t,n],[e,r]]}}}var yi,wi,Le,Fe,Nn={sphere:tt,point:tt,lineStart:Dh,lineEnd:tt,polygonStart:tt,polygonEnd:tt};function Dh(){Nn.point=Fh,Nn.lineEnd=Lh}function Lh(){Nn.point=Nn.lineEnd=tt}function Fh(t,n){t*=q,n*=q,wi=t,Le=O(n),Fe=D(n),Nn.point=qh}function qh(t,n){t*=q,n*=q;var e=O(n),r=D(n),i=H(t-wi),a=D(i),o=O(i),f=r*o,c=Fe*e-Le*r*a,u=Le*e+Fe*r*a;yi.add(wt(at(f*f+c*c),u)),wi=t,Le=e,Fe=r}function Yh(t){return yi=new st,zt(t,Nn),+yi}var _i=[null,null],Gh={type:"LineString",coordinates:_i};function xi(t,n){return _i[0]=t,_i[1]=n,Yh(Gh)}var fo={Feature:function(t,n){return fr(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)if(fr(e[r].geometry,n))return!0;return!1}},co={Sphere:function(){return!0},Point:function(t,n){return uo(t.coordinates,n)},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(uo(e[r],n))return!0;return!1},LineString:function(t,n){return so(t.coordinates,n)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(so(e[r],n))return!0;return!1},Polygon:function(t,n){return lo(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(lo(e[r],n))return!0;return!1},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)if(fr(e[r],n))return!0;return!1}};function fr(t,n){return t&&co.hasOwnProperty(t.type)?co[t.type](t,n):!1}function uo(t,n){return xi(t,n)===0}function so(t,n){for(var e,r,i,a=0,o=t.length;a<o;a++){if(r=xi(t[a],n),r===0||a>0&&(i=xi(t[a],t[a-1]),i>0&&e<=i&&r<=i&&(e+r-i)*(1-Math.pow((e-r)/i,2))<ce*i))return!0;e=r}return!1}function lo(t,n){return!!Pf(t.map(Vh),Nf(n))}function Vh(t){return t=t.map(Nf),t.pop(),t}function Nf(t){return[t[0]*q,t[1]*q]}function $g(t,n){return(t&&fo.hasOwnProperty(t.type)?fo[t.type]:fr)(t,n)}function ho(t,n,e){var r=_n(t,n-F,e).concat(n);return function(i){return r.map(function(a){return[i,a]})}}function go(t,n,e){var r=_n(t,n-F,e).concat(n);return function(i){return r.map(function(a){return[a,i]})}}function Xh(){var t,n,e,r,i,a,o,f,c=10,u=c,s=90,h=360,l,d,v,m,g=2.5;function p(){return{type:"MultiLineString",coordinates:y()}}function y(){return _n(we(r/s)*s,e,s).map(v).concat(_n(we(f/h)*h,o,h).map(m)).concat(_n(we(n/c)*c,t,c).filter(function(w){return H(w%s)>F}).map(l)).concat(_n(we(a/u)*u,i,u).filter(function(w){return H(w%h)>F}).map(d))}return p.lines=function(){return y().map(function(w){return{type:"LineString",coordinates:w}})},p.outline=function(){return{type:"Polygon",coordinates:[v(r).concat(m(o).slice(1),v(e).reverse().slice(1),m(f).reverse().slice(1))]}},p.extent=function(w){return arguments.length?p.extentMajor(w).extentMinor(w):p.extentMinor()},p.extentMajor=function(w){return arguments.length?(r=+w[0][0],e=+w[1][0],f=+w[0][1],o=+w[1][1],r>e&&(w=r,r=e,e=w),f>o&&(w=f,f=o,o=w),p.precision(g)):[[r,f],[e,o]]},p.extentMinor=function(w){return arguments.length?(n=+w[0][0],t=+w[1][0],a=+w[0][1],i=+w[1][1],n>t&&(w=n,n=t,t=w),a>i&&(w=a,a=i,i=w),p.precision(g)):[[n,a],[t,i]]},p.step=function(w){return arguments.length?p.stepMajor(w).stepMinor(w):p.stepMinor()},p.stepMajor=function(w){return arguments.length?(s=+w[0],h=+w[1],p):[s,h]},p.stepMinor=function(w){return arguments.length?(c=+w[0],u=+w[1],p):[c,u]},p.precision=function(w){return arguments.length?(g=+w,l=ho(a,i,90),d=go(n,t,g),v=ho(f,o,90),m=go(r,e,g),p):g},p.extentMajor([[-180,-90+F],[180,90-F]]).extentMinor([[-180,-80-F],[180,80+F]])}function Rg(){return Xh()()}function Pg(t,n){var e=t[0]*q,r=t[1]*q,i=n[0]*q,a=n[1]*q,o=D(r),f=O(r),c=D(a),u=O(a),s=o*D(e),h=o*O(e),l=c*D(i),d=c*O(i),v=2*_t(at(Ha(a-r)+o*c*Ha(i-e))),m=O(v),g=v?function(p){var y=O(p*=v)/m,w=O(v-p)/m,b=w*s+y*l,_=w*h+y*d,M=w*f+y*u;return[wt(_,b)*W,wt(M,at(b*b+_*_))*W]}:function(){return[e*W,r*W]};return g.distance=v,g}const ue=t=>t;var Yr=new st,Mi=new st,Tf,zf,Si,Ai,Zt={point:tt,lineStart:tt,lineEnd:tt,polygonStart:function(){Zt.lineStart=Hh,Zt.lineEnd=Wh},polygonEnd:function(){Zt.lineStart=Zt.lineEnd=Zt.point=tt,Yr.add(H(Mi)),Mi=new st},result:function(){var t=Yr/2;return Yr=new st,t}};function Hh(){Zt.point=Uh}function Uh(t,n){Zt.point=kf,Tf=Si=t,zf=Ai=n}function kf(t,n){Mi.add(Ai*t-Si*n),Si=t,Ai=n}function Wh(){kf(Tf,zf)}const po=Zt;var Tn=1/0,cr=Tn,se=-Tn,ur=se,Kh={point:Zh,lineStart:tt,lineEnd:tt,polygonStart:tt,polygonEnd:tt,result:function(){var t=[[Tn,cr],[se,ur]];return se=ur=-(cr=Tn=1/0),t}};function Zh(t,n){t<Tn&&(Tn=t),t>se&&(se=t),n<cr&&(cr=n),n>ur&&(ur=n)}const sr=Kh;var Ei=0,$i=0,Wn=0,lr=0,hr=0,Mn=0,Ri=0,Pi=0,Kn=0,Cf,Bf,kt,Ct,It={point:un,lineStart:bo,lineEnd:mo,polygonStart:function(){It.lineStart=jh,It.lineEnd=td},polygonEnd:function(){It.point=un,It.lineStart=bo,It.lineEnd=mo},result:function(){var t=Kn?[Ri/Kn,Pi/Kn]:Mn?[lr/Mn,hr/Mn]:Wn?[Ei/Wn,$i/Wn]:[NaN,NaN];return Ei=$i=Wn=lr=hr=Mn=Ri=Pi=Kn=0,t}};function un(t,n){Ei+=t,$i+=n,++Wn}function bo(){It.point=Qh}function Qh(t,n){It.point=Jh,un(kt=t,Ct=n)}function Jh(t,n){var e=t-kt,r=n-Ct,i=at(e*e+r*r);lr+=i*(kt+t)/2,hr+=i*(Ct+n)/2,Mn+=i,un(kt=t,Ct=n)}function mo(){It.point=un}function jh(){It.point=nd}function td(){Of(Cf,Bf)}function nd(t,n){It.point=Of,un(Cf=kt=t,Bf=Ct=n)}function Of(t,n){var e=t-kt,r=n-Ct,i=at(e*e+r*r);lr+=i*(kt+t)/2,hr+=i*(Ct+n)/2,Mn+=i,i=Ct*t-kt*n,Ri+=i*(kt+t),Pi+=i*(Ct+n),Kn+=i*3,un(kt=t,Ct=n)}const vo=It;function Df(t){this._context=t}Df.prototype={_radius:4.5,pointRadius:function(t){return this._radius=t,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){this._line===0&&this._context.closePath(),this._point=NaN},point:function(t,n){switch(this._point){case 0:{this._context.moveTo(t,n),this._point=1;break}case 1:{this._context.lineTo(t,n);break}default:{this._context.moveTo(t+this._radius,n),this._context.arc(t,n,this._radius,0,yt);break}}},result:tt};var Ii=new st,Gr,Lf,Ff,Zn,Qn,dr={point:tt,lineStart:function(){dr.point=ed},lineEnd:function(){Gr&&qf(Lf,Ff),dr.point=tt},polygonStart:function(){Gr=!0},polygonEnd:function(){Gr=null},result:function(){var t=+Ii;return Ii=new st,t}};function ed(t,n){dr.point=qf,Lf=Zn=t,Ff=Qn=n}function qf(t,n){Zn-=t,Qn-=n,Ii.add(at(Zn*Zn+Qn*Qn)),Zn=t,Qn=n}const yo=dr;let wo,gr,_o,xo;class Mo{constructor(n){this._append=n==null?Yf:rd(n),this._radius=4.5,this._=""}pointRadius(n){return this._radius=+n,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){this._line===0&&(this._+="Z"),this._point=NaN}point(n,e){switch(this._point){case 0:{this._append`M${n},${e}`,this._point=1;break}case 1:{this._append`L${n},${e}`;break}default:{if(this._append`M${n},${e}`,this._radius!==_o||this._append!==gr){const r=this._radius,i=this._;this._="",this._append`m0,${r}a${r},${r} 0 1,1 0,${-2*r}a${r},${r} 0 1,1 0,${2*r}z`,_o=r,gr=this._append,xo=this._,this._=i}this._+=xo;break}}}result(){const n=this._;return this._="",n.length?n:null}}function Yf(t){let n=1;this._+=t[0];for(const e=t.length;n<e;++n)this._+=arguments[n]+t[n]}function rd(t){const n=Math.floor(t);if(!(n>=0))throw new RangeError(`invalid digits: ${t}`);if(n>15)return Yf;if(n!==wo){const e=10**n;wo=n,gr=function(i){let a=1;this._+=i[0];for(const o=i.length;a<o;++a)this._+=Math.round(arguments[a]*e)/e+i[a]}}return gr}function Ig(t,n){let e=3,r=4.5,i,a;function o(f){return f&&(typeof r=="function"&&a.pointRadius(+r.apply(this,arguments)),zt(f,i(a))),a.result()}return o.area=function(f){return zt(f,i(po)),po.result()},o.measure=function(f){return zt(f,i(yo)),yo.result()},o.bounds=function(f){return zt(f,i(sr)),sr.result()},o.centroid=function(f){return zt(f,i(vo)),vo.result()},o.projection=function(f){return arguments.length?(i=f==null?(t=null,ue):(t=f).stream,o):t},o.context=function(f){return arguments.length?(a=f==null?(n=null,new Mo(e)):new Df(n=f),typeof r!="function"&&a.pointRadius(r),o):n},o.pointRadius=function(f){return arguments.length?(r=typeof f=="function"?f:(a.pointRadius(+f),+f),o):r},o.digits=function(f){if(!arguments.length)return e;if(f==null)e=null;else{const c=Math.floor(f);if(!(c>=0))throw new RangeError(`invalid digits: ${f}`);e=c}return n===null&&(a=new Mo(e)),o},o.projection(t).digits(e).context(n)}function Ng(t){return{stream:de(t)}}function de(t){return function(n){var e=new Ni;for(var r in t)e[r]=t[r];return e.stream=n,e}}function Ni(){}Ni.prototype={constructor:Ni,point:function(t,n){this.stream.point(t,n)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};function aa(t,n,e){var r=t.clipExtent&&t.clipExtent();return t.scale(150).translate([0,0]),r!=null&&t.clipExtent(null),zt(e,t.stream(sr)),n(sr.result()),r!=null&&t.clipExtent(r),t}function xr(t,n,e){return aa(t,function(r){var i=n[1][0]-n[0][0],a=n[1][1]-n[0][1],o=Math.min(i/(r[1][0]-r[0][0]),a/(r[1][1]-r[0][1])),f=+n[0][0]+(i-o*(r[1][0]+r[0][0]))/2,c=+n[0][1]+(a-o*(r[1][1]+r[0][1]))/2;t.scale(150*o).translate([f,c])},e)}function oa(t,n,e){return xr(t,[[0,0],n],e)}function fa(t,n,e){return aa(t,function(r){var i=+n,a=i/(r[1][0]-r[0][0]),o=(i-a*(r[1][0]+r[0][0]))/2,f=-a*r[0][1];t.scale(150*a).translate([o,f])},e)}function ca(t,n,e){return aa(t,function(r){var i=+n,a=i/(r[1][1]-r[0][1]),o=-a*r[0][0],f=(i-a*(r[1][1]+r[0][1]))/2;t.scale(150*a).translate([o,f])},e)}var So=16,id=D(30*q);function Ao(t,n){return+n?od(t,n):ad(t)}function ad(t){return de({point:function(n,e){n=t(n,e),this.stream.point(n[0],n[1])}})}function od(t,n){function e(r,i,a,o,f,c,u,s,h,l,d,v,m,g){var p=u-r,y=s-i,w=p*p+y*y;if(w>4*n&&m--){var b=o+l,_=f+d,M=c+v,I=at(b*b+_*_+M*M),P=_t(M/=I),R=H(H(M)-1)<F||H(a-h)<F?(a+h)/2:wt(_,b),E=t(R,P),$=E[0],z=E[1],T=$-r,x=z-i,A=y*T-p*x;(A*A/w>n||H((p*T+y*x)/w-.5)>.3||o*l+f*d+c*v<id)&&(e(r,i,a,o,f,c,$,z,R,b/=I,_/=I,M,m,g),g.point($,z),e($,z,R,b,_,M,u,s,h,l,d,v,m,g))}}return function(r){var i,a,o,f,c,u,s,h,l,d,v,m,g={point:p,lineStart:y,lineEnd:b,polygonStart:function(){r.polygonStart(),g.lineStart=_},polygonEnd:function(){r.polygonEnd(),g.lineStart=y}};function p(P,R){P=t(P,R),r.point(P[0],P[1])}function y(){h=NaN,g.point=w,r.lineStart()}function w(P,R){var E=cn([P,R]),$=t(P,R);e(h,l,s,d,v,m,h=$[0],l=$[1],s=P,d=E[0],v=E[1],m=E[2],So,r),r.point(h,l)}function b(){g.point=p,r.lineEnd()}function _(){y(),g.point=M,g.lineEnd=I}function M(P,R){w(i=P,R),a=h,o=l,f=d,c=v,u=m,g.point=w}function I(){e(h,l,s,d,v,m,a,o,i,f,c,u,So,r),g.lineEnd=b,b()}return g}}var fd=de({point:function(t,n){this.stream.point(t*q,n*q)}});function cd(t){return de({point:function(n,e){var r=t(n,e);return this.stream.point(r[0],r[1])}})}function ud(t,n,e,r,i){function a(o,f){return o*=r,f*=i,[n+t*o,e-t*f]}return a.invert=function(o,f){return[(o-n)/t*r,(e-f)/t*i]},a}function Eo(t,n,e,r,i,a){if(!a)return ud(t,n,e,r,i);var o=D(a),f=O(a),c=o*t,u=f*t,s=o/t,h=f/t,l=(f*e-o*n)/t,d=(f*n+o*e)/t;function v(m,g){return m*=r,g*=i,[c*m-u*g+n,e-u*m-c*g]}return v.invert=function(m,g){return[r*(s*m-h*g+l),i*(d-h*m-s*g)]},v}function Ht(t){return Gf(function(){return t})()}function Gf(t){var n,e=150,r=480,i=250,a=0,o=0,f=0,c=0,u=0,s,h=0,l=1,d=1,v=null,m=oo,g=null,p,y,w,b=ue,_=.5,M,I,P,R,E;function $(A){return P(A[0]*q,A[1]*q)}function z(A){return A=P.invert(A[0],A[1]),A&&[A[0]*W,A[1]*W]}$.stream=function(A){return R&&E===A?R:R=fd(cd(s)(m(M(b(E=A)))))},$.preclip=function(A){return arguments.length?(m=A,v=void 0,x()):m},$.postclip=function(A){return arguments.length?(b=A,g=p=y=w=null,x()):b},$.clipAngle=function(A){return arguments.length?(m=+A?Bh(v=A*q):(v=null,oo),x()):v*W},$.clipExtent=function(A){return arguments.length?(b=A==null?(g=p=y=w=null,ue):ia(g=+A[0][0],p=+A[0][1],y=+A[1][0],w=+A[1][1]),x()):g==null?null:[[g,p],[y,w]]},$.scale=function(A){return arguments.length?(e=+A,T()):e},$.translate=function(A){return arguments.length?(r=+A[0],i=+A[1],T()):[r,i]},$.center=function(A){return arguments.length?(a=A[0]%360*q,o=A[1]%360*q,T()):[a*W,o*W]},$.rotate=function(A){return arguments.length?(f=A[0]%360*q,c=A[1]%360*q,u=A.length>2?A[2]%360*q:0,T()):[f*W,c*W,u*W]},$.angle=function(A){return arguments.length?(h=A%360*q,T()):h*W},$.reflectX=function(A){return arguments.length?(l=A?-1:1,T()):l<0},$.reflectY=function(A){return arguments.length?(d=A?-1:1,T()):d<0},$.precision=function(A){return arguments.length?(M=Ao(I,_=A*A),x()):at(_)},$.fitExtent=function(A,S){return xr($,A,S)},$.fitSize=function(A,S){return oa($,A,S)},$.fitWidth=function(A,S){return fa($,A,S)},$.fitHeight=function(A,S){return ca($,A,S)};function T(){var A=Eo(e,0,0,l,d,h).apply(null,n(a,o)),S=Eo(e,r-A[0],i-A[1],l,d,h);return s=ra(f,c,u),I=mi(n,S),P=mi(s,I),M=Ao(I,_),x()}function x(){return R=E=null,$}return function(){return n=t.apply(this,arguments),$.invert=n.invert&&z,T()}}function ua(t){var n=0,e=X/3,r=Gf(t),i=r(n,e);return i.parallels=function(a){return arguments.length?r(n=a[0]*q,e=a[1]*q):[n*W,e*W]},i}function sd(t){var n=D(t);function e(r,i){return[r*n,O(i)/n]}return e.invert=function(r,i){return[r/n,_t(i*n)]},e}function ld(t,n){var e=O(t),r=(e+O(n))/2;if(H(r)<F)return sd(t);var i=1+e*(2*r-e),a=at(i)/r;function o(f,c){var u=at(i-2*r*O(c))/r;return[u*O(f*=r),a-u*D(f)]}return o.invert=function(f,c){var u=a-c,s=wt(f,H(u))*Pt(u);return u*r<0&&(s-=X*Pt(f)*Pt(u)),[s/r,_t((i-(f*f+u*u)*r*r)/(2*r))]},o}function Ti(){return ua(ld).scale(155.424).center([0,33.6442])}function hd(){return Ti().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7])}function dd(t){var n=t.length;return{point:function(e,r){for(var i=-1;++i<n;)t[i].point(e,r)},sphere:function(){for(var e=-1;++e<n;)t[e].sphere()},lineStart:function(){for(var e=-1;++e<n;)t[e].lineStart()},lineEnd:function(){for(var e=-1;++e<n;)t[e].lineEnd()},polygonStart:function(){for(var e=-1;++e<n;)t[e].polygonStart()},polygonEnd:function(){for(var e=-1;++e<n;)t[e].polygonEnd()}}}function Tg(){var t,n,e=hd(),r,i=Ti().rotate([154,0]).center([-2,58.5]).parallels([55,65]),a,o=Ti().rotate([157,0]).center([-3,19.9]).parallels([8,18]),f,c,u={point:function(l,d){c=[l,d]}};function s(l){var d=l[0],v=l[1];return c=null,r.point(d,v),c||(a.point(d,v),c)||(f.point(d,v),c)}s.invert=function(l){var d=e.scale(),v=e.translate(),m=(l[0]-v[0])/d,g=(l[1]-v[1])/d;return(g>=.12&&g<.234&&m>=-.425&&m<-.214?i:g>=.166&&g<.234&&m>=-.214&&m<-.115?o:e).invert(l)},s.stream=function(l){return t&&n===l?t:t=dd([e.stream(n=l),i.stream(l),o.stream(l)])},s.precision=function(l){return arguments.length?(e.precision(l),i.precision(l),o.precision(l),h()):e.precision()},s.scale=function(l){return arguments.length?(e.scale(l),i.scale(l*.35),o.scale(l),s.translate(e.translate())):e.scale()},s.translate=function(l){if(!arguments.length)return e.translate();var d=e.scale(),v=+l[0],m=+l[1];return r=e.translate(l).clipExtent([[v-.455*d,m-.238*d],[v+.455*d,m+.238*d]]).stream(u),a=i.translate([v-.307*d,m+.201*d]).clipExtent([[v-.425*d+F,m+.12*d+F],[v-.214*d-F,m+.234*d-F]]).stream(u),f=o.translate([v-.205*d,m+.212*d]).clipExtent([[v-.214*d+F,m+.166*d+F],[v-.115*d-F,m+.234*d-F]]).stream(u),h()},s.fitExtent=function(l,d){return xr(s,l,d)},s.fitSize=function(l,d){return oa(s,l,d)},s.fitWidth=function(l,d){return fa(s,l,d)},s.fitHeight=function(l,d){return ca(s,l,d)};function h(){return t=n=null,s}return s.scale(1070)}function Vf(t){return function(n,e){var r=D(n),i=D(e),a=t(r*i);return a===1/0?[2,0]:[a*i*O(n),a*O(e)]}}function ge(t){return function(n,e){var r=at(n*n+e*e),i=t(r),a=O(i),o=D(i);return[wt(n*a,r*o),_t(r&&e*a/r)]}}var Xf=Vf(function(t){return at(2/(1+t))});Xf.invert=ge(function(t){return 2*_t(t/2)});function zg(){return Ht(Xf).scale(124.75).clipAngle(180-.001)}var Hf=Vf(function(t){return(t=pf(t))&&t/O(t)});Hf.invert=ge(function(t){return t});function kg(){return Ht(Hf).scale(79.4188).clipAngle(180-.001)}function Mr(t,n){return[t,We(na((et+n)/2))]}Mr.invert=function(t,n){return[t,2*Bn(gf(n))-et]};function Cg(){return Uf(Mr).scale(961/yt)}function Uf(t){var n=Ht(t),e=n.center,r=n.scale,i=n.translate,a=n.clipExtent,o=null,f,c,u;n.scale=function(h){return arguments.length?(r(h),s()):r()},n.translate=function(h){return arguments.length?(i(h),s()):i()},n.center=function(h){return arguments.length?(e(h),s()):e()},n.clipExtent=function(h){return arguments.length?(h==null?o=f=c=u=null:(o=+h[0][0],f=+h[0][1],c=+h[1][0],u=+h[1][1]),s()):o==null?null:[[o,f],[c,u]]};function s(){var h=X*r(),l=n(Ih(n.rotate()).invert([0,0]));return a(o==null?[[l[0]-h,l[1]-h],[l[0]+h,l[1]+h]]:t===Mr?[[Math.max(l[0]-h,o),f],[Math.min(l[0]+h,c),u]]:[[o,Math.max(l[1]-h,f)],[c,Math.min(l[1]+h,u)]])}return s()}function Ae(t){return na((et+t)/2)}function gd(t,n){var e=D(t),r=t===n?O(t):We(e/D(n))/We(Ae(n)/Ae(t)),i=e*Lr(Ae(t),r)/r;if(!r)return Mr;function a(o,f){i>0?f<-et+F&&(f=-et+F):f>et-F&&(f=et-F);var c=i/Lr(Ae(f),r);return[c*O(r*o),i-c*D(r*o)]}return a.invert=function(o,f){var c=i-f,u=Pt(r)*at(o*o+c*c),s=wt(o,H(c))*Pt(c);return c*r<0&&(s-=X*Pt(o)*Pt(c)),[s/r,2*Bn(Lr(i/u,1/r))-et]},a}function Bg(){return ua(gd).scale(109.5).parallels([30,30])}function pr(t,n){return[t,n]}pr.invert=pr;function Og(){return Ht(pr).scale(152.63)}function pd(t,n){var e=D(t),r=t===n?O(t):(e-D(n))/(n-t),i=e/r+t;if(H(r)<F)return pr;function a(o,f){var c=i-f,u=r*o;return[c*O(u),i-c*D(u)]}return a.invert=function(o,f){var c=i-f,u=wt(o,H(c))*Pt(c);return c*r<0&&(u-=X*Pt(o)*Pt(c)),[u/r,i-Pt(r)*at(o*o+c*c)]},a}function Dg(){return ua(pd).scale(131.154).center([0,13.9389])}var te=1.340264,ne=-.081106,ee=893e-6,re=.003796,br=at(3)/2,bd=12;function Wf(t,n){var e=_t(br*O(n)),r=e*e,i=r*r*r;return[t*D(e)/(br*(te+3*ne*r+i*(7*ee+9*re*r))),e*(te+ne*r+i*(ee+re*r))]}Wf.invert=function(t,n){for(var e=n,r=e*e,i=r*r*r,a=0,o,f,c;a<bd&&(f=e*(te+ne*r+i*(ee+re*r))-n,c=te+3*ne*r+i*(7*ee+9*re*r),e-=o=f/c,r=e*e,i=r*r*r,!(H(o)<ce));++a);return[br*t*(te+3*ne*r+i*(7*ee+9*re*r))/D(e),_t(O(e)/br)]};function Lg(){return Ht(Wf).scale(177.158)}function Kf(t,n){var e=D(n),r=D(t)*e;return[e*O(t)/r,O(n)/r]}Kf.invert=ge(Bn);function Fg(){return Ht(Kf).scale(144.049).clipAngle(60)}function qg(){var t=1,n=0,e=0,r=1,i=1,a=0,o,f,c=null,u,s,h,l=1,d=1,v=de({point:function(b,_){var M=w([b,_]);this.stream.point(M[0],M[1])}}),m=ue,g,p;function y(){return l=t*r,d=t*i,g=p=null,w}function w(b){var _=b[0]*l,M=b[1]*d;if(a){var I=M*o-_*f;_=_*o+M*f,M=I}return[_+n,M+e]}return w.invert=function(b){var _=b[0]-n,M=b[1]-e;if(a){var I=M*o+_*f;_=_*o-M*f,M=I}return[_/l,M/d]},w.stream=function(b){return g&&p===b?g:g=v(m(p=b))},w.postclip=function(b){return arguments.length?(m=b,c=u=s=h=null,y()):m},w.clipExtent=function(b){return arguments.length?(m=b==null?(c=u=s=h=null,ue):ia(c=+b[0][0],u=+b[0][1],s=+b[1][0],h=+b[1][1]),y()):c==null?null:[[c,u],[s,h]]},w.scale=function(b){return arguments.length?(t=+b,y()):t},w.translate=function(b){return arguments.length?(n=+b[0],e=+b[1],y()):[n,e]},w.angle=function(b){return arguments.length?(a=b%360*q,f=O(a),o=D(a),y()):a*W},w.reflectX=function(b){return arguments.length?(r=b?-1:1,y()):r<0},w.reflectY=function(b){return arguments.length?(i=b?-1:1,y()):i<0},w.fitExtent=function(b,_){return xr(w,b,_)},w.fitSize=function(b,_){return oa(w,b,_)},w.fitWidth=function(b,_){return fa(w,b,_)},w.fitHeight=function(b,_){return ca(w,b,_)},w}function Zf(t,n){var e=n*n,r=e*e;return[t*(.8707-.131979*e+r*(-.013791+r*(.003971*e-.001529*r))),n*(1.007226+e*(.015085+r*(-.044475+.028874*e-.005916*r)))]}Zf.invert=function(t,n){var e=n,r=25,i;do{var a=e*e,o=a*a;e-=i=(e*(1.007226+a*(.015085+o*(-.044475+.028874*a-.005916*o)))-n)/(1.007226+a*(.015085*3+o*(-.044475*7+.028874*9*a-.005916*11*o)))}while(H(i)>F&&--r>0);return[t/(.8707+(a=e*e)*(-.131979+a*(-.013791+a*a*a*(.003971-.001529*a)))),e]};function Yg(){return Ht(Zf).scale(175.295)}function Qf(t,n){return[D(n)*O(t),O(n)]}Qf.invert=ge(_t);function Gg(){return Ht(Qf).scale(249.5).clipAngle(90+F)}function Jf(t,n){var e=D(n),r=1+D(t)*e;return[e*O(t)/r,O(n)/r]}Jf.invert=ge(function(t){return 2*Bn(t)});function Vg(){return Ht(Jf).scale(250).clipAngle(142)}function jf(t,n){return[We(na((et+n)/2)),-t]}jf.invert=function(t,n){return[-n,2*Bn(gf(t))-et]};function Xg(){var t=Uf(jf),n=t.center,e=t.rotate;return t.center=function(r){return arguments.length?n([-r[1],r[0]]):(r=n(),[r[1],-r[0]])},t.rotate=function(r){return arguments.length?e([r[0],r[1],r.length>2?r[2]+90:90]):(r=e(),[r[0],r[1],r[2]-90])},e([0,0,90]).scale(159.155)}function md(t,n){return t.parent===n.parent?1:2}function vd(t){return t.reduce(yd,0)/t.length}function yd(t,n){return t+n.x}function wd(t){return 1+t.reduce(_d,0)}function _d(t,n){return Math.max(t,n.y)}function xd(t){for(var n;n=t.children;)t=n[0];return t}function Md(t){for(var n;n=t.children;)t=n[n.length-1];return t}function Hg(){var t=md,n=1,e=1,r=!1;function i(a){var o,f=0;a.eachAfter(function(l){var d=l.children;d?(l.x=vd(d),l.y=wd(d)):(l.x=o?f+=t(l,o):0,l.y=0,o=l)});var c=xd(a),u=Md(a),s=c.x-t(c,u)/2,h=u.x+t(u,c)/2;return a.eachAfter(r?function(l){l.x=(l.x-a.x)*n,l.y=(a.y-l.y)*e}:function(l){l.x=(l.x-s)/(h-s)*n,l.y=(1-(a.y?l.y/a.y:1))*e})}return i.separation=function(a){return arguments.length?(t=a,i):t},i.size=function(a){return arguments.length?(r=!1,n=+a[0],e=+a[1],i):r?null:[n,e]},i.nodeSize=function(a){return arguments.length?(r=!0,n=+a[0],e=+a[1],i):r?[n,e]:null},i}function Sd(t){var n=0,e=t.children,r=e&&e.length;if(!r)n=1;else for(;--r>=0;)n+=e[r].value;t.value=n}function Ad(){return this.eachAfter(Sd)}function Ed(t,n){let e=-1;for(const r of this)t.call(n,r,++e,this);return this}function $d(t,n){for(var e=this,r=[e],i,a,o=-1;e=r.pop();)if(t.call(n,e,++o,this),i=e.children)for(a=i.length-1;a>=0;--a)r.push(i[a]);return this}function Rd(t,n){for(var e=this,r=[e],i=[],a,o,f,c=-1;e=r.pop();)if(i.push(e),a=e.children)for(o=0,f=a.length;o<f;++o)r.push(a[o]);for(;e=i.pop();)t.call(n,e,++c,this);return this}function Pd(t,n){let e=-1;for(const r of this)if(t.call(n,r,++e,this))return r}function Id(t){return this.eachAfter(function(n){for(var e=+t(n.data)||0,r=n.children,i=r&&r.length;--i>=0;)e+=r[i].value;n.value=e})}function Nd(t){return this.eachBefore(function(n){n.children&&n.children.sort(t)})}function Td(t){for(var n=this,e=zd(n,t),r=[n];n!==e;)n=n.parent,r.push(n);for(var i=r.length;t!==e;)r.splice(i,0,t),t=t.parent;return r}function zd(t,n){if(t===n)return t;var e=t.ancestors(),r=n.ancestors(),i=null;for(t=e.pop(),n=r.pop();t===n;)i=t,t=e.pop(),n=r.pop();return i}function kd(){for(var t=this,n=[t];t=t.parent;)n.push(t);return n}function Cd(){return Array.from(this)}function Bd(){var t=[];return this.eachBefore(function(n){n.children||t.push(n)}),t}function Od(){var t=this,n=[];return t.each(function(e){e!==t&&n.push({source:e.parent,target:e})}),n}function*Dd(){var t=this,n,e=[t],r,i,a;do for(n=e.reverse(),e=[];t=n.pop();)if(yield t,r=t.children)for(i=0,a=r.length;i<a;++i)e.push(r[i]);while(e.length)}function tc(t,n){t instanceof Map?(t=[void 0,t],n===void 0&&(n=qd)):n===void 0&&(n=Fd);for(var e=new zn(t),r,i=[e],a,o,f,c;r=i.pop();)if((o=n(r.data))&&(c=(o=Array.from(o)).length))for(r.children=o,f=c-1;f>=0;--f)i.push(a=o[f]=new zn(o[f])),a.parent=r,a.depth=r.depth+1;return e.eachBefore(nc)}function Ld(){return tc(this).eachBefore(Yd)}function Fd(t){return t.children}function qd(t){return Array.isArray(t)?t[1]:null}function Yd(t){t.data.value!==void 0&&(t.value=t.data.value),t.data=t.data.data}function nc(t){var n=0;do t.height=n;while((t=t.parent)&&t.height<++n)}function zn(t){this.data=t,this.depth=this.height=0,this.parent=null}zn.prototype=tc.prototype={constructor:zn,count:Ad,each:Ed,eachAfter:Rd,eachBefore:$d,find:Pd,sum:Id,sort:Nd,path:Td,ancestors:kd,descendants:Cd,leaves:Bd,links:Od,copy:Ld,[Symbol.iterator]:Dd};function qe(t){return t==null?null:ec(t)}function ec(t){if(typeof t!="function")throw new Error;return t}function rn(){return 0}function wn(t){return function(){return t}}const Gd=1664525,Vd=1013904223,$o=4294967296;function sa(){let t=1;return()=>(t=(Gd*t+Vd)%$o)/$o}function Xd(t){return typeof t=="object"&&"length"in t?t:Array.from(t)}function Hd(t,n){let e=t.length,r,i;for(;e;)i=n()*e--|0,r=t[e],t[e]=t[i],t[i]=r;return t}function Ug(t){return rc(t,sa())}function rc(t,n){for(var e=0,r=(t=Hd(Array.from(t),n)).length,i=[],a,o;e<r;)a=t[e],o&&ic(o,a)?++e:(o=Wd(i=Ud(i,a)),e=0);return o}function Ud(t,n){var e,r;if(Vr(n,t))return[n];for(e=0;e<t.length;++e)if(Ee(n,t[e])&&Vr(Jn(t[e],n),t))return[t[e],n];for(e=0;e<t.length-1;++e)for(r=e+1;r<t.length;++r)if(Ee(Jn(t[e],t[r]),n)&&Ee(Jn(t[e],n),t[r])&&Ee(Jn(t[r],n),t[e])&&Vr(ac(t[e],t[r],n),t))return[t[e],t[r],n];throw new Error}function Ee(t,n){var e=t.r-n.r,r=n.x-t.x,i=n.y-t.y;return e<0||e*e<r*r+i*i}function ic(t,n){var e=t.r-n.r+Math.max(t.r,n.r,1)*1e-9,r=n.x-t.x,i=n.y-t.y;return e>0&&e*e>r*r+i*i}function Vr(t,n){for(var e=0;e<n.length;++e)if(!ic(t,n[e]))return!1;return!0}function Wd(t){switch(t.length){case 1:return Kd(t[0]);case 2:return Jn(t[0],t[1]);case 3:return ac(t[0],t[1],t[2])}}function Kd(t){return{x:t.x,y:t.y,r:t.r}}function Jn(t,n){var e=t.x,r=t.y,i=t.r,a=n.x,o=n.y,f=n.r,c=a-e,u=o-r,s=f-i,h=Math.sqrt(c*c+u*u);return{x:(e+a+c/h*s)/2,y:(r+o+u/h*s)/2,r:(h+i+f)/2}}function ac(t,n,e){var r=t.x,i=t.y,a=t.r,o=n.x,f=n.y,c=n.r,u=e.x,s=e.y,h=e.r,l=r-o,d=r-u,v=i-f,m=i-s,g=c-a,p=h-a,y=r*r+i*i-a*a,w=y-o*o-f*f+c*c,b=y-u*u-s*s+h*h,_=d*v-l*m,M=(v*b-m*w)/(_*2)-r,I=(m*g-v*p)/_,P=(d*w-l*b)/(_*2)-i,R=(l*p-d*g)/_,E=I*I+R*R-1,$=2*(a+M*I+P*R),z=M*M+P*P-a*a,T=-(Math.abs(E)>1e-6?($+Math.sqrt($*$-4*E*z))/(2*E):z/$);return{x:r+M+I*T,y:i+P+R*T,r:T}}function Ro(t,n,e){var r=t.x-n.x,i,a,o=t.y-n.y,f,c,u=r*r+o*o;u?(a=n.r+e.r,a*=a,c=t.r+e.r,c*=c,a>c?(i=(u+c-a)/(2*u),f=Math.sqrt(Math.max(0,c/u-i*i)),e.x=t.x-i*r-f*o,e.y=t.y-i*o+f*r):(i=(u+a-c)/(2*u),f=Math.sqrt(Math.max(0,a/u-i*i)),e.x=n.x+i*r-f*o,e.y=n.y+i*o+f*r)):(e.x=n.x+e.r,e.y=n.y)}function Po(t,n){var e=t.r+n.r-1e-6,r=n.x-t.x,i=n.y-t.y;return e>0&&e*e>r*r+i*i}function Io(t){var n=t._,e=t.next._,r=n.r+e.r,i=(n.x*e.r+e.x*n.r)/r,a=(n.y*e.r+e.y*n.r)/r;return i*i+a*a}function $e(t){this._=t,this.next=null,this.previous=null}function oc(t,n){if(!(a=(t=Xd(t)).length))return 0;var e,r,i,a,o,f,c,u,s,h,l;if(e=t[0],e.x=0,e.y=0,!(a>1))return e.r;if(r=t[1],e.x=-r.r,r.x=e.r,r.y=0,!(a>2))return e.r+r.r;Ro(r,e,i=t[2]),e=new $e(e),r=new $e(r),i=new $e(i),e.next=i.previous=r,r.next=e.previous=i,i.next=r.previous=e;t:for(c=3;c<a;++c){Ro(e._,r._,i=t[c]),i=new $e(i),u=r.next,s=e.previous,h=r._.r,l=e._.r;do if(h<=l){if(Po(u._,i._)){r=u,e.next=r,r.previous=e,--c;continue t}h+=u._.r,u=u.next}else{if(Po(s._,i._)){e=s,e.next=r,r.previous=e,--c;continue t}l+=s._.r,s=s.previous}while(u!==s.next);for(i.previous=e,i.next=r,e.next=r.previous=r=i,o=Io(e);(i=i.next)!==r;)(f=Io(i))<o&&(e=i,o=f);r=e.next}for(e=[r._],i=r;(i=i.next)!==r;)e.push(i._);for(i=rc(e,n),c=0;c<a;++c)e=t[c],e.x-=i.x,e.y-=i.y;return i.r}function Wg(t){return oc(t,sa()),t}function Zd(t){return Math.sqrt(t.value)}function Kg(){var t=null,n=1,e=1,r=rn;function i(a){const o=sa();return a.x=n/2,a.y=e/2,t?a.eachBefore(No(t)).eachAfter(Xr(r,.5,o)).eachBefore(To(1)):a.eachBefore(No(Zd)).eachAfter(Xr(rn,1,o)).eachAfter(Xr(r,a.r/Math.min(n,e),o)).eachBefore(To(Math.min(n,e)/(2*a.r))),a}return i.radius=function(a){return arguments.length?(t=qe(a),i):t},i.size=function(a){return arguments.length?(n=+a[0],e=+a[1],i):[n,e]},i.padding=function(a){return arguments.length?(r=typeof a=="function"?a:wn(+a),i):r},i}function No(t){return function(n){n.children||(n.r=Math.max(0,+t(n)||0))}}function Xr(t,n,e){return function(r){if(i=r.children){var i,a,o=i.length,f=t(r)*n||0,c;if(f)for(a=0;a<o;++a)i[a].r+=f;if(c=oc(i,e),f)for(a=0;a<o;++a)i[a].r-=f;r.r=c+f}}}function To(t){return function(n){var e=n.parent;n.r*=t,e&&(n.x=e.x+t*n.x,n.y=e.y+t*n.y)}}function fc(t){t.x0=Math.round(t.x0),t.y0=Math.round(t.y0),t.x1=Math.round(t.x1),t.y1=Math.round(t.y1)}function Sr(t,n,e,r,i){for(var a=t.children,o,f=-1,c=a.length,u=t.value&&(r-n)/t.value;++f<c;)o=a[f],o.y0=e,o.y1=i,o.x0=n,o.x1=n+=o.value*u}function Zg(){var t=1,n=1,e=0,r=!1;function i(o){var f=o.height+1;return o.x0=o.y0=e,o.x1=t,o.y1=n/f,o.eachBefore(a(n,f)),r&&o.eachBefore(fc),o}function a(o,f){return function(c){c.children&&Sr(c,c.x0,o*(c.depth+1)/f,c.x1,o*(c.depth+2)/f);var u=c.x0,s=c.y0,h=c.x1-e,l=c.y1-e;h<u&&(u=h=(u+h)/2),l<s&&(s=l=(s+l)/2),c.x0=u,c.y0=s,c.x1=h,c.y1=l}}return i.round=function(o){return arguments.length?(r=!!o,i):r},i.size=function(o){return arguments.length?(t=+o[0],n=+o[1],i):[t,n]},i.padding=function(o){return arguments.length?(e=+o,i):e},i}var Qd={depth:-1},zo={},Hr={};function Jd(t){return t.id}function jd(t){return t.parentId}function Qg(){var t=Jd,n=jd,e;function r(i){var a=Array.from(i),o=t,f=n,c,u,s,h,l,d,v,m,g=new Map;if(e!=null){const p=a.map((b,_)=>t1(e(b,_,i))),y=p.map(ko),w=new Set(p).add("");for(const b of y)w.has(b)||(w.add(b),p.push(b),y.push(ko(b)),a.push(Hr));o=(b,_)=>p[_],f=(b,_)=>y[_]}for(s=0,c=a.length;s<c;++s)u=a[s],d=a[s]=new zn(u),(v=o(u,s,i))!=null&&(v+="")&&(m=d.id=v,g.set(m,g.has(m)?zo:d)),(v=f(u,s,i))!=null&&(v+="")&&(d.parent=v);for(s=0;s<c;++s)if(d=a[s],v=d.parent){if(l=g.get(v),!l)throw new Error("missing: "+v);if(l===zo)throw new Error("ambiguous: "+v);l.children?l.children.push(d):l.children=[d],d.parent=l}else{if(h)throw new Error("multiple roots");h=d}if(!h)throw new Error("no root");if(e!=null){for(;h.data===Hr&&h.children.length===1;)h=h.children[0],--c;for(let p=a.length-1;p>=0&&(d=a[p],d.data===Hr);--p)d.data=null}if(h.parent=Qd,h.eachBefore(function(p){p.depth=p.parent.depth+1,--c}).eachBefore(nc),h.parent=null,c>0)throw new Error("cycle");return h}return r.id=function(i){return arguments.length?(t=qe(i),r):t},r.parentId=function(i){return arguments.length?(n=qe(i),r):n},r.path=function(i){return arguments.length?(e=qe(i),r):e},r}function t1(t){t=`${t}`;let n=t.length;return zi(t,n-1)&&!zi(t,n-2)&&(t=t.slice(0,-1)),t[0]==="/"?t:`/${t}`}function ko(t){let n=t.length;if(n<2)return"";for(;--n>1&&!zi(t,n););return t.slice(0,n)}function zi(t,n){if(t[n]==="/"){let e=0;for(;n>0&&t[--n]==="\\";)++e;if(!(e&1))return!0}return!1}function n1(t,n){return t.parent===n.parent?1:2}function Ur(t){var n=t.children;return n?n[0]:t.t}function Wr(t){var n=t.children;return n?n[n.length-1]:t.t}function e1(t,n,e){var r=e/(n.i-t.i);n.c-=r,n.s+=e,t.c+=r,n.z+=e,n.m+=e}function r1(t){for(var n=0,e=0,r=t.children,i=r.length,a;--i>=0;)a=r[i],a.z+=n,a.m+=n,n+=a.s+(e+=a.c)}function i1(t,n,e){return t.a.parent===n.parent?t.a:e}function Ye(t,n){this._=t,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=n}Ye.prototype=Object.create(zn.prototype);function a1(t){for(var n=new Ye(t,0),e,r=[n],i,a,o,f;e=r.pop();)if(a=e._.children)for(e.children=new Array(f=a.length),o=f-1;o>=0;--o)r.push(i=e.children[o]=new Ye(a[o],o)),i.parent=e;return(n.parent=new Ye(null,0)).children=[n],n}function Jg(){var t=n1,n=1,e=1,r=null;function i(u){var s=a1(u);if(s.eachAfter(a),s.parent.m=-s.z,s.eachBefore(o),r)u.eachBefore(c);else{var h=u,l=u,d=u;u.eachBefore(function(y){y.x<h.x&&(h=y),y.x>l.x&&(l=y),y.depth>d.depth&&(d=y)});var v=h===l?1:t(h,l)/2,m=v-h.x,g=n/(l.x+v+m),p=e/(d.depth||1);u.eachBefore(function(y){y.x=(y.x+m)*g,y.y=y.depth*p})}return u}function a(u){var s=u.children,h=u.parent.children,l=u.i?h[u.i-1]:null;if(s){r1(u);var d=(s[0].z+s[s.length-1].z)/2;l?(u.z=l.z+t(u._,l._),u.m=u.z-d):u.z=d}else l&&(u.z=l.z+t(u._,l._));u.parent.A=f(u,l,u.parent.A||h[0])}function o(u){u._.x=u.z+u.parent.m,u.m+=u.parent.m}function f(u,s,h){if(s){for(var l=u,d=u,v=s,m=l.parent.children[0],g=l.m,p=d.m,y=v.m,w=m.m,b;v=Wr(v),l=Ur(l),v&&l;)m=Ur(m),d=Wr(d),d.a=u,b=v.z+y-l.z-g+t(v._,l._),b>0&&(e1(i1(v,u,h),u,b),g+=b,p+=b),y+=v.m,g+=l.m,w+=m.m,p+=d.m;v&&!Wr(d)&&(d.t=v,d.m+=y-p),l&&!Ur(m)&&(m.t=l,m.m+=g-w,h=u)}return h}function c(u){u.x*=n,u.y=u.depth*e}return i.separation=function(u){return arguments.length?(t=u,i):t},i.size=function(u){return arguments.length?(r=!1,n=+u[0],e=+u[1],i):r?null:[n,e]},i.nodeSize=function(u){return arguments.length?(r=!0,n=+u[0],e=+u[1],i):r?[n,e]:null},i}function la(t,n,e,r,i){for(var a=t.children,o,f=-1,c=a.length,u=t.value&&(i-e)/t.value;++f<c;)o=a[f],o.x0=n,o.x1=r,o.y0=e,o.y1=e+=o.value*u}var cc=(1+Math.sqrt(5))/2;function uc(t,n,e,r,i,a){for(var o=[],f=n.children,c,u,s=0,h=0,l=f.length,d,v,m=n.value,g,p,y,w,b,_,M;s<l;){d=i-e,v=a-r;do g=f[h++].value;while(!g&&h<l);for(p=y=g,_=Math.max(v/d,d/v)/(m*t),M=g*g*_,b=Math.max(y/M,M/p);h<l;++h){if(g+=u=f[h].value,u<p&&(p=u),u>y&&(y=u),M=g*g*_,w=Math.max(y/M,M/p),w>b){g-=u;break}b=w}o.push(c={value:g,dice:d<v,children:f.slice(s,h)}),c.dice?Sr(c,e,r,i,m?r+=v*g/m:a):la(c,e,r,m?e+=d*g/m:i,a),m-=g,s=h}return o}const o1=function t(n){function e(r,i,a,o,f){uc(n,r,i,a,o,f)}return e.ratio=function(r){return t((r=+r)>1?r:1)},e}(cc);function jg(){var t=o1,n=!1,e=1,r=1,i=[0],a=rn,o=rn,f=rn,c=rn,u=rn;function s(l){return l.x0=l.y0=0,l.x1=e,l.y1=r,l.eachBefore(h),i=[0],n&&l.eachBefore(fc),l}function h(l){var d=i[l.depth],v=l.x0+d,m=l.y0+d,g=l.x1-d,p=l.y1-d;g<v&&(v=g=(v+g)/2),p<m&&(m=p=(m+p)/2),l.x0=v,l.y0=m,l.x1=g,l.y1=p,l.children&&(d=i[l.depth+1]=a(l)/2,v+=u(l)-d,m+=o(l)-d,g-=f(l)-d,p-=c(l)-d,g<v&&(v=g=(v+g)/2),p<m&&(m=p=(m+p)/2),t(l,v,m,g,p))}return s.round=function(l){return arguments.length?(n=!!l,s):n},s.size=function(l){return arguments.length?(e=+l[0],r=+l[1],s):[e,r]},s.tile=function(l){return arguments.length?(t=ec(l),s):t},s.padding=function(l){return arguments.length?s.paddingInner(l).paddingOuter(l):s.paddingInner()},s.paddingInner=function(l){return arguments.length?(a=typeof l=="function"?l:wn(+l),s):a},s.paddingOuter=function(l){return arguments.length?s.paddingTop(l).paddingRight(l).paddingBottom(l).paddingLeft(l):s.paddingTop()},s.paddingTop=function(l){return arguments.length?(o=typeof l=="function"?l:wn(+l),s):o},s.paddingRight=function(l){return arguments.length?(f=typeof l=="function"?l:wn(+l),s):f},s.paddingBottom=function(l){return arguments.length?(c=typeof l=="function"?l:wn(+l),s):c},s.paddingLeft=function(l){return arguments.length?(u=typeof l=="function"?l:wn(+l),s):u},s}function tp(t,n,e,r,i){var a=t.children,o,f=a.length,c,u=new Array(f+1);for(u[0]=c=o=0;o<f;++o)u[o+1]=c+=a[o].value;s(0,f,t.value,n,e,r,i);function s(h,l,d,v,m,g,p){if(h>=l-1){var y=a[h];y.x0=v,y.y0=m,y.x1=g,y.y1=p;return}for(var w=u[h],b=d/2+w,_=h+1,M=l-1;_<M;){var I=_+M>>>1;u[I]<b?_=I+1:M=I}b-u[_-1]<u[_]-b&&h+1<_&&--_;var P=u[_]-w,R=d-P;if(g-v>p-m){var E=d?(v*R+g*P)/d:g;s(h,_,P,v,m,E,p),s(_,l,R,E,m,g,p)}else{var $=d?(m*R+p*P)/d:p;s(h,_,P,v,m,g,$),s(_,l,R,v,$,g,p)}}}function np(t,n,e,r,i){(t.depth&1?la:Sr)(t,n,e,r,i)}const ep=function t(n){function e(r,i,a,o,f){if((c=r._squarify)&&c.ratio===n)for(var c,u,s,h,l=-1,d,v=c.length,m=r.value;++l<v;){for(u=c[l],s=u.children,h=u.value=0,d=s.length;h<d;++h)u.value+=s[h].value;u.dice?Sr(u,i,a,o,m?a+=(f-a)*u.value/m:f):la(u,i,a,m?i+=(o-i)*u.value/m:o,f),m-=u.value}else r._squarify=c=uc(n,r,i,a,o,f),c.ratio=n}return e.ratio=function(r){return t((r=+r)>1?r:1)},e}(cc);function rp(t){for(var n=-1,e=t.length,r,i=t[e-1],a=0;++n<e;)r=i,i=t[n],a+=r[1]*i[0]-r[0]*i[1];return a/2}function ip(t){for(var n=-1,e=t.length,r=0,i=0,a,o=t[e-1],f,c=0;++n<e;)a=o,o=t[n],c+=f=a[0]*o[1]-o[0]*a[1],r+=(a[0]+o[0])*f,i+=(a[1]+o[1])*f;return c*=3,[r/c,i/c]}function f1(t,n,e){return(n[0]-t[0])*(e[1]-t[1])-(n[1]-t[1])*(e[0]-t[0])}function c1(t,n){return t[0]-n[0]||t[1]-n[1]}function Co(t){const n=t.length,e=[0,1];let r=2,i;for(i=2;i<n;++i){for(;r>1&&f1(t[e[r-2]],t[e[r-1]],t[i])<=0;)--r;e[r++]=i}return e.slice(0,r)}function ap(t){if((e=t.length)<3)return null;var n,e,r=new Array(e),i=new Array(e);for(n=0;n<e;++n)r[n]=[+t[n][0],+t[n][1],n];for(r.sort(c1),n=0;n<e;++n)i[n]=[r[n][0],-r[n][1]];var a=Co(r),o=Co(i),f=o[0]===a[0],c=o[o.length-1]===a[a.length-1],u=[];for(n=a.length-1;n>=0;--n)u.push(t[r[a[n]][2]]);for(n=+f;n<o.length-c;++n)u.push(t[r[o[n]][2]]);return u}function op(t,n){for(var e=t.length,r=t[e-1],i=n[0],a=n[1],o=r[0],f=r[1],c,u,s=!1,h=0;h<e;++h)r=t[h],c=r[0],u=r[1],u>a!=f>a&&i<(o-c)*(a-u)/(f-u)+c&&(s=!s),o=c,f=u;return s}function fp(t){for(var n=-1,e=t.length,r=t[e-1],i,a,o=r[0],f=r[1],c=0;++n<e;)i=o,a=f,r=t[n],o=r[0],f=r[1],i-=o,a-=f,c+=Math.hypot(i,a);return c}const ot=Math.random,cp=function t(n){function e(r,i){return r=r==null?0:+r,i=i==null?1:+i,arguments.length===1?(i=r,r=0):i-=r,function(){return n()*i+r}}return e.source=t,e}(ot),up=function t(n){function e(r,i){return arguments.length<2&&(i=r,r=0),r=Math.floor(r),i=Math.floor(i)-r,function(){return Math.floor(n()*i+r)}}return e.source=t,e}(ot),sc=function t(n){function e(r,i){var a,o;return r=r==null?0:+r,i=i==null?1:+i,function(){var f;if(a!=null)f=a,a=null;else do a=n()*2-1,f=n()*2-1,o=a*a+f*f;while(!o||o>1);return r+i*f*Math.sqrt(-2*Math.log(o)/o)}}return e.source=t,e}(ot),sp=function t(n){var e=sc.source(n);function r(){var i=e.apply(this,arguments);return function(){return Math.exp(i())}}return r.source=t,r}(ot),u1=function t(n){function e(r){return(r=+r)<=0?()=>0:function(){for(var i=0,a=r;a>1;--a)i+=n();return i+a*n()}}return e.source=t,e}(ot),lp=function t(n){var e=u1.source(n);function r(i){if((i=+i)==0)return n;var a=e(i);return function(){return a()/i}}return r.source=t,r}(ot),hp=function t(n){function e(r){return function(){return-Math.log1p(-n())/r}}return e.source=t,e}(ot),dp=function t(n){function e(r){if((r=+r)<0)throw new RangeError("invalid alpha");return r=1/-r,function(){return Math.pow(1-n(),r)}}return e.source=t,e}(ot),gp=function t(n){function e(r){if((r=+r)<0||r>1)throw new RangeError("invalid p");return function(){return Math.floor(n()+r)}}return e.source=t,e}(ot),s1=function t(n){function e(r){if((r=+r)<0||r>1)throw new RangeError("invalid p");return r===0?()=>1/0:r===1?()=>1:(r=Math.log1p(-r),function(){return 1+Math.floor(Math.log1p(-n())/r)})}return e.source=t,e}(ot),lc=function t(n){var e=sc.source(n)();function r(i,a){if((i=+i)<0)throw new RangeError("invalid k");if(i===0)return()=>0;if(a=a==null?1:+a,i===1)return()=>-Math.log1p(-n())*a;var o=(i<1?i+1:i)-1/3,f=1/(3*Math.sqrt(o)),c=i<1?()=>Math.pow(n(),1/i):()=>1;return function(){do{do var u=e(),s=1+f*u;while(s<=0);s*=s*s;var h=1-n()}while(h>=1-.0331*u*u*u*u&&Math.log(h)>=.5*u*u+o*(1-s+Math.log(s)));return o*s*c()*a}}return r.source=t,r}(ot),l1=function t(n){var e=lc.source(n);function r(i,a){var o=e(i),f=e(a);return function(){var c=o();return c===0?0:c/(c+f())}}return r.source=t,r}(ot),h1=function t(n){var e=s1.source(n),r=l1.source(n);function i(a,o){return a=+a,(o=+o)>=1?()=>a:o<=0?()=>0:function(){for(var f=0,c=a,u=o;c*u>16&&c*(1-u)>16;){var s=Math.floor((c+1)*u),h=r(s,c-s+1)();h<=u?(f+=s,c-=s,u=(u-h)/(1-h)):(c=s-1,u/=h)}for(var l=u<.5,d=l?u:1-u,v=e(d),m=v(),g=0;m<=c;++g)m+=v();return f+(l?g:c-g)}}return i.source=t,i}(ot),pp=function t(n){function e(r,i,a){var o;return(r=+r)==0?o=f=>-Math.log(f):(r=1/r,o=f=>Math.pow(f,r)),i=i==null?0:+i,a=a==null?1:+a,function(){return i+a*o(-Math.log1p(-n()))}}return e.source=t,e}(ot),bp=function t(n){function e(r,i){return r=r==null?0:+r,i=i==null?1:+i,function(){return r+i*Math.tan(Math.PI*n())}}return e.source=t,e}(ot),mp=function t(n){function e(r,i){return r=r==null?0:+r,i=i==null?1:+i,function(){var a=n();return r+i*Math.log(a/(1-a))}}return e.source=t,e}(ot),vp=function t(n){var e=lc.source(n),r=h1.source(n);function i(a){return function(){for(var o=0,f=a;f>16;){var c=Math.floor(.875*f),u=e(c)();if(u>f)return o+r(c-1,f/u)();o+=c,f-=u}for(var s=-Math.log1p(-n()),h=0;s<=f;++h)s-=Math.log1p(-n());return o+h}}return i.source=t,i}(ot),d1=1664525,g1=1013904223,Bo=1/4294967296;function yp(t=Math.random()){let n=(0<=t&&t<1?t/Bo:Math.abs(t))|0;return()=>(n=d1*n+g1|0,Bo*(n>>>0))}function G(t){for(var n=t.length/6|0,e=new Array(n),r=0;r<n;)e[r]="#"+t.slice(r*6,++r*6);return e}const wp=G("1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf"),_p=G("7fc97fbeaed4fdc086ffff99386cb0f0027fbf5b17666666"),xp=G("1b9e77d95f027570b3e7298a66a61ee6ab02a6761d666666"),Mp=G("4269d0efb118ff725c6cc5b03ca951ff8ab7a463f297bbf59c6b4e9498a0"),Sp=G("a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928"),Ap=G("fbb4aeb3cde3ccebc5decbe4fed9a6ffffcce5d8bdfddaecf2f2f2"),Ep=G("b3e2cdfdcdaccbd5e8f4cae4e6f5c9fff2aef1e2cccccccc"),$p=G("e41a1c377eb84daf4a984ea3ff7f00ffff33a65628f781bf999999"),Rp=G("66c2a5fc8d628da0cbe78ac3a6d854ffd92fe5c494b3b3b3"),Pp=G("8dd3c7ffffb3bebadafb807280b1d3fdb462b3de69fccde5d9d9d9bc80bdccebc5ffed6f"),Ip=G("4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab"),K=t=>Sc(t[t.length-1]);var p1=new Array(3).concat("d8b365f5f5f55ab4ac","a6611adfc27d80cdc1018571","a6611adfc27df5f5f580cdc1018571","8c510ad8b365f6e8c3c7eae55ab4ac01665e","8c510ad8b365f6e8c3f5f5f5c7eae55ab4ac01665e","8c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e","8c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e","5430058c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e003c30","5430058c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e003c30").map(G);const Np=K(p1);var b1=new Array(3).concat("af8dc3f7f7f77fbf7b","7b3294c2a5cfa6dba0008837","7b3294c2a5cff7f7f7a6dba0008837","762a83af8dc3e7d4e8d9f0d37fbf7b1b7837","762a83af8dc3e7d4e8f7f7f7d9f0d37fbf7b1b7837","762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b7837","762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b7837","40004b762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b783700441b","40004b762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b783700441b").map(G);const Tp=K(b1);var m1=new Array(3).concat("e9a3c9f7f7f7a1d76a","d01c8bf1b6dab8e1864dac26","d01c8bf1b6daf7f7f7b8e1864dac26","c51b7de9a3c9fde0efe6f5d0a1d76a4d9221","c51b7de9a3c9fde0eff7f7f7e6f5d0a1d76a4d9221","c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221","c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221","8e0152c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221276419","8e0152c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221276419").map(G);const zp=K(m1);var v1=new Array(3).concat("998ec3f7f7f7f1a340","5e3c99b2abd2fdb863e66101","5e3c99b2abd2f7f7f7fdb863e66101","542788998ec3d8daebfee0b6f1a340b35806","542788998ec3d8daebf7f7f7fee0b6f1a340b35806","5427888073acb2abd2d8daebfee0b6fdb863e08214b35806","5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b35806","2d004b5427888073acb2abd2d8daebfee0b6fdb863e08214b358067f3b08","2d004b5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b358067f3b08").map(G);const kp=K(v1);var y1=new Array(3).concat("ef8a62f7f7f767a9cf","ca0020f4a58292c5de0571b0","ca0020f4a582f7f7f792c5de0571b0","b2182bef8a62fddbc7d1e5f067a9cf2166ac","b2182bef8a62fddbc7f7f7f7d1e5f067a9cf2166ac","b2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac","b2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac","67001fb2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac053061","67001fb2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac053061").map(G);const Cp=K(y1);var w1=new Array(3).concat("ef8a62ffffff999999","ca0020f4a582bababa404040","ca0020f4a582ffffffbababa404040","b2182bef8a62fddbc7e0e0e09999994d4d4d","b2182bef8a62fddbc7ffffffe0e0e09999994d4d4d","b2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d","b2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d","67001fb2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d1a1a1a","67001fb2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d1a1a1a").map(G);const Bp=K(w1);var _1=new Array(3).concat("fc8d59ffffbf91bfdb","d7191cfdae61abd9e92c7bb6","d7191cfdae61ffffbfabd9e92c7bb6","d73027fc8d59fee090e0f3f891bfdb4575b4","d73027fc8d59fee090ffffbfe0f3f891bfdb4575b4","d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4","d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4","a50026d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4313695","a50026d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4313695").map(G);const Op=K(_1);var x1=new Array(3).concat("fc8d59ffffbf91cf60","d7191cfdae61a6d96a1a9641","d7191cfdae61ffffbfa6d96a1a9641","d73027fc8d59fee08bd9ef8b91cf601a9850","d73027fc8d59fee08bffffbfd9ef8b91cf601a9850","d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850","d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850","a50026d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850006837","a50026d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850006837").map(G);const Dp=K(x1);var M1=new Array(3).concat("fc8d59ffffbf99d594","d7191cfdae61abdda42b83ba","d7191cfdae61ffffbfabdda42b83ba","d53e4ffc8d59fee08be6f59899d5943288bd","d53e4ffc8d59fee08bffffbfe6f59899d5943288bd","d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd","d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd","9e0142d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd5e4fa2","9e0142d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd5e4fa2").map(G);const Lp=K(M1);var S1=new Array(3).concat("e5f5f999d8c92ca25f","edf8fbb2e2e266c2a4238b45","edf8fbb2e2e266c2a42ca25f006d2c","edf8fbccece699d8c966c2a42ca25f006d2c","edf8fbccece699d8c966c2a441ae76238b45005824","f7fcfde5f5f9ccece699d8c966c2a441ae76238b45005824","f7fcfde5f5f9ccece699d8c966c2a441ae76238b45006d2c00441b").map(G);const Fp=K(S1);var A1=new Array(3).concat("e0ecf49ebcda8856a7","edf8fbb3cde38c96c688419d","edf8fbb3cde38c96c68856a7810f7c","edf8fbbfd3e69ebcda8c96c68856a7810f7c","edf8fbbfd3e69ebcda8c96c68c6bb188419d6e016b","f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d6e016b","f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d810f7c4d004b").map(G);const qp=K(A1);var E1=new Array(3).concat("e0f3dba8ddb543a2ca","f0f9e8bae4bc7bccc42b8cbe","f0f9e8bae4bc7bccc443a2ca0868ac","f0f9e8ccebc5a8ddb57bccc443a2ca0868ac","f0f9e8ccebc5a8ddb57bccc44eb3d32b8cbe08589e","f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe08589e","f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe0868ac084081").map(G);const Yp=K(E1);var $1=new Array(3).concat("fee8c8fdbb84e34a33","fef0d9fdcc8afc8d59d7301f","fef0d9fdcc8afc8d59e34a33b30000","fef0d9fdd49efdbb84fc8d59e34a33b30000","fef0d9fdd49efdbb84fc8d59ef6548d7301f990000","fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301f990000","fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301fb300007f0000").map(G);const Gp=K($1);var R1=new Array(3).concat("ece2f0a6bddb1c9099","f6eff7bdc9e167a9cf02818a","f6eff7bdc9e167a9cf1c9099016c59","f6eff7d0d1e6a6bddb67a9cf1c9099016c59","f6eff7d0d1e6a6bddb67a9cf3690c002818a016450","fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016450","fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016c59014636").map(G);const Vp=K(R1);var P1=new Array(3).concat("ece7f2a6bddb2b8cbe","f1eef6bdc9e174a9cf0570b0","f1eef6bdc9e174a9cf2b8cbe045a8d","f1eef6d0d1e6a6bddb74a9cf2b8cbe045a8d","f1eef6d0d1e6a6bddb74a9cf3690c00570b0034e7b","fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0034e7b","fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0045a8d023858").map(G);const Xp=K(P1);var I1=new Array(3).concat("e7e1efc994c7dd1c77","f1eef6d7b5d8df65b0ce1256","f1eef6d7b5d8df65b0dd1c77980043","f1eef6d4b9dac994c7df65b0dd1c77980043","f1eef6d4b9dac994c7df65b0e7298ace125691003f","f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125691003f","f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125698004367001f").map(G);const Hp=K(I1);var N1=new Array(3).concat("fde0ddfa9fb5c51b8a","feebe2fbb4b9f768a1ae017e","feebe2fbb4b9f768a1c51b8a7a0177","feebe2fcc5c0fa9fb5f768a1c51b8a7a0177","feebe2fcc5c0fa9fb5f768a1dd3497ae017e7a0177","fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a0177","fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a017749006a").map(G);const Up=K(N1);var T1=new Array(3).concat("edf8b17fcdbb2c7fb8","ffffcca1dab441b6c4225ea8","ffffcca1dab441b6c42c7fb8253494","ffffccc7e9b47fcdbb41b6c42c7fb8253494","ffffccc7e9b47fcdbb41b6c41d91c0225ea80c2c84","ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea80c2c84","ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea8253494081d58").map(G);const Wp=K(T1);var z1=new Array(3).concat("f7fcb9addd8e31a354","ffffccc2e69978c679238443","ffffccc2e69978c67931a354006837","ffffccd9f0a3addd8e78c67931a354006837","ffffccd9f0a3addd8e78c67941ab5d238443005a32","ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443005a32","ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443006837004529").map(G);const Kp=K(z1);var k1=new Array(3).concat("fff7bcfec44fd95f0e","ffffd4fed98efe9929cc4c02","ffffd4fed98efe9929d95f0e993404","ffffd4fee391fec44ffe9929d95f0e993404","ffffd4fee391fec44ffe9929ec7014cc4c028c2d04","ffffe5fff7bcfee391fec44ffe9929ec7014cc4c028c2d04","ffffe5fff7bcfee391fec44ffe9929ec7014cc4c02993404662506").map(G);const Zp=K(k1);var C1=new Array(3).concat("ffeda0feb24cf03b20","ffffb2fecc5cfd8d3ce31a1c","ffffb2fecc5cfd8d3cf03b20bd0026","ffffb2fed976feb24cfd8d3cf03b20bd0026","ffffb2fed976feb24cfd8d3cfc4e2ae31a1cb10026","ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cb10026","ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cbd0026800026").map(G);const Qp=K(C1);var B1=new Array(3).concat("deebf79ecae13182bd","eff3ffbdd7e76baed62171b5","eff3ffbdd7e76baed63182bd08519c","eff3ffc6dbef9ecae16baed63182bd08519c","eff3ffc6dbef9ecae16baed64292c62171b5084594","f7fbffdeebf7c6dbef9ecae16baed64292c62171b5084594","f7fbffdeebf7c6dbef9ecae16baed64292c62171b508519c08306b").map(G);const Jp=K(B1);var O1=new Array(3).concat("e5f5e0a1d99b31a354","edf8e9bae4b374c476238b45","edf8e9bae4b374c47631a354006d2c","edf8e9c7e9c0a1d99b74c47631a354006d2c","edf8e9c7e9c0a1d99b74c47641ab5d238b45005a32","f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45005a32","f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45006d2c00441b").map(G);const jp=K(O1);var D1=new Array(3).concat("f0f0f0bdbdbd636363","f7f7f7cccccc969696525252","f7f7f7cccccc969696636363252525","f7f7f7d9d9d9bdbdbd969696636363252525","f7f7f7d9d9d9bdbdbd969696737373525252252525","fffffff0f0f0d9d9d9bdbdbd969696737373525252252525","fffffff0f0f0d9d9d9bdbdbd969696737373525252252525000000").map(G);const tb=K(D1);var L1=new Array(3).concat("efedf5bcbddc756bb1","f2f0f7cbc9e29e9ac86a51a3","f2f0f7cbc9e29e9ac8756bb154278f","f2f0f7dadaebbcbddc9e9ac8756bb154278f","f2f0f7dadaebbcbddc9e9ac8807dba6a51a34a1486","fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a34a1486","fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a354278f3f007d").map(G);const nb=K(L1);var F1=new Array(3).concat("fee0d2fc9272de2d26","fee5d9fcae91fb6a4acb181d","fee5d9fcae91fb6a4ade2d26a50f15","fee5d9fcbba1fc9272fb6a4ade2d26a50f15","fee5d9fcbba1fc9272fb6a4aef3b2ccb181d99000d","fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181d99000d","fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181da50f1567000d").map(G);const eb=K(F1);var q1=new Array(3).concat("fee6cefdae6be6550d","feeddefdbe85fd8d3cd94701","feeddefdbe85fd8d3ce6550da63603","feeddefdd0a2fdae6bfd8d3ce6550da63603","feeddefdd0a2fdae6bfd8d3cf16913d948018c2d04","fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d948018c2d04","fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d94801a636037f2704").map(G);const rb=K(q1);function ib(t){return t=Math.max(0,Math.min(1,t)),"rgb("+Math.max(0,Math.min(255,Math.round(-4.54-t*(35.34-t*(2381.73-t*(6402.7-t*(7024.72-t*2710.57)))))))+", "+Math.max(0,Math.min(255,Math.round(32.49+t*(170.73+t*(52.82-t*(131.46-t*(176.58-t*67.37)))))))+", "+Math.max(0,Math.min(255,Math.round(81.24+t*(442.36-t*(2482.43-t*(6167.24-t*(6614.94-t*2475.67)))))))+")"}const ab=ki(on(300,.5,0),on(-240,.5,1));var ob=ki(on(-100,.75,.35),on(80,1.5,.8)),fb=ki(on(260,.75,.35),on(80,1.5,.8)),Re=on();function cb(t){(t<0||t>1)&&(t-=Math.floor(t));var n=Math.abs(t-.5);return Re.h=360*t-100,Re.s=1.5-1.5*n,Re.l=.8-.9*n,Re+""}var Pe=Ac(),Y1=Math.PI/3,G1=Math.PI*2/3;function ub(t){var n;return t=(.5-t)*Math.PI,Pe.r=255*(n=Math.sin(t))*n,Pe.g=255*(n=Math.sin(t+Y1))*n,Pe.b=255*(n=Math.sin(t+G1))*n,Pe+""}function sb(t){return t=Math.max(0,Math.min(1,t)),"rgb("+Math.max(0,Math.min(255,Math.round(34.61+t*(1172.33-t*(10793.56-t*(33300.12-t*(38394.49-t*14825.05)))))))+", "+Math.max(0,Math.min(255,Math.round(23.31+t*(557.33+t*(1225.33-t*(3574.96-t*(1073.77+t*707.56)))))))+", "+Math.max(0,Math.min(255,Math.round(27.2+t*(3211.1-t*(15327.97-t*(27814-t*(22569.18-t*6838.66)))))))+")"}function Ar(t){var n=t.length;return function(e){return t[Math.max(0,Math.min(n-1,Math.floor(e*n)))]}}const lb=Ar(G("44015444025645045745055946075a46085c460a5d460b5e470d60470e6147106347116447136548146748166848176948186a481a6c481b6d481c6e481d6f481f70482071482173482374482475482576482677482878482979472a7a472c7a472d7b472e7c472f7d46307e46327e46337f463480453581453781453882443983443a83443b84433d84433e85423f854240864241864142874144874045884046883f47883f48893e49893e4a893e4c8a3d4d8a3d4e8a3c4f8a3c508b3b518b3b528b3a538b3a548c39558c39568c38588c38598c375a8c375b8d365c8d365d8d355e8d355f8d34608d34618d33628d33638d32648e32658e31668e31678e31688e30698e306a8e2f6b8e2f6c8e2e6d8e2e6e8e2e6f8e2d708e2d718e2c718e2c728e2c738e2b748e2b758e2a768e2a778e2a788e29798e297a8e297b8e287c8e287d8e277e8e277f8e27808e26818e26828e26828e25838e25848e25858e24868e24878e23888e23898e238a8d228b8d228c8d228d8d218e8d218f8d21908d21918c20928c20928c20938c1f948c1f958b1f968b1f978b1f988b1f998a1f9a8a1e9b8a1e9c891e9d891f9e891f9f881fa0881fa1881fa1871fa28720a38620a48621a58521a68522a78522a88423a98324aa8325ab8225ac8226ad8127ad8128ae8029af7f2ab07f2cb17e2db27d2eb37c2fb47c31b57b32b67a34b67935b77937b87838b9773aba763bbb753dbc743fbc7340bd7242be7144bf7046c06f48c16e4ac16d4cc26c4ec36b50c46a52c56954c56856c66758c7655ac8645cc8635ec96260ca6063cb5f65cb5e67cc5c69cd5b6ccd5a6ece5870cf5773d05675d05477d1537ad1517cd2507fd34e81d34d84d44b86d54989d5488bd6468ed64590d74393d74195d84098d83e9bd93c9dd93ba0da39a2da37a5db36a8db34aadc32addc30b0dd2fb2dd2db5de2bb8de29bade28bddf26c0df25c2df23c5e021c8e020cae11fcde11dd0e11cd2e21bd5e21ad8e219dae319dde318dfe318e2e418e5e419e7e419eae51aece51befe51cf1e51df4e61ef6e620f8e621fbe723fde725"));var hb=Ar(G("00000401000501010601010802010902020b02020d03030f03031204041405041606051806051a07061c08071e0907200a08220b09240c09260d0a290e0b2b100b2d110c2f120d31130d34140e36150e38160f3b180f3d19103f1a10421c10441d11471e114920114b21114e22115024125325125527125829115a2a115c2c115f2d11612f116331116533106734106936106b38106c390f6e3b0f703d0f713f0f72400f74420f75440f764510774710784910784a10794c117a4e117b4f127b51127c52137c54137d56147d57157e59157e5a167e5c167f5d177f5f187f601880621980641a80651a80671b80681c816a1c816b1d816d1d816e1e81701f81721f817320817521817621817822817922827b23827c23827e24828025828125818326818426818627818827818928818b29818c29818e2a81902a81912b81932b80942c80962c80982d80992d809b2e7f9c2e7f9e2f7fa02f7fa1307ea3307ea5317ea6317da8327daa337dab337cad347cae347bb0357bb2357bb3367ab5367ab73779b83779ba3878bc3978bd3977bf3a77c03a76c23b75c43c75c53c74c73d73c83e73ca3e72cc3f71cd4071cf4070d0416fd2426fd3436ed5446dd6456cd8456cd9466bdb476adc4869de4968df4a68e04c67e24d66e34e65e44f64e55064e75263e85362e95462ea5661eb5760ec5860ed5a5fee5b5eef5d5ef05f5ef1605df2625df2645cf3655cf4675cf4695cf56b5cf66c5cf66e5cf7705cf7725cf8745cf8765cf9785df9795df97b5dfa7d5efa7f5efa815ffb835ffb8560fb8761fc8961fc8a62fc8c63fc8e64fc9065fd9266fd9467fd9668fd9869fd9a6afd9b6bfe9d6cfe9f6dfea16efea36ffea571fea772fea973feaa74feac76feae77feb078feb27afeb47bfeb67cfeb77efeb97ffebb81febd82febf84fec185fec287fec488fec68afec88cfeca8dfecc8ffecd90fecf92fed194fed395fed597fed799fed89afdda9cfddc9efddea0fde0a1fde2a3fde3a5fde5a7fde7a9fde9aafdebacfcecaefceeb0fcf0b2fcf2b4fcf4b6fcf6b8fcf7b9fcf9bbfcfbbdfcfdbf")),db=Ar(G("00000401000501010601010802010a02020c02020e03021004031204031405041706041907051b08051d09061f0a07220b07240c08260d08290e092b10092d110a30120a32140b34150b37160b39180c3c190c3e1b0c411c0c431e0c451f0c48210c4a230c4c240c4f260c51280b53290b552b0b572d0b592f0a5b310a5c320a5e340a5f3609613809623909633b09643d09653e0966400a67420a68440a68450a69470b6a490b6a4a0c6b4c0c6b4d0d6c4f0d6c510e6c520e6d540f6d550f6d57106e59106e5a116e5c126e5d126e5f136e61136e62146e64156e65156e67166e69166e6a176e6c186e6d186e6f196e71196e721a6e741a6e751b6e771c6d781c6d7a1d6d7c1d6d7d1e6d7f1e6c801f6c82206c84206b85216b87216b88226a8a226a8c23698d23698f24699025689225689326679526679727669827669a28659b29649d29649f2a63a02a63a22b62a32c61a52c60a62d60a82e5fa92e5eab2f5ead305dae305cb0315bb1325ab3325ab43359b63458b73557b93556ba3655bc3754bd3853bf3952c03a51c13a50c33b4fc43c4ec63d4dc73e4cc83f4bca404acb4149cc4248ce4347cf4446d04545d24644d34743d44842d54a41d74b3fd84c3ed94d3dda4e3cdb503bdd513ade5238df5337e05536e15635e25734e35933e45a31e55c30e65d2fe75e2ee8602de9612bea632aeb6429eb6628ec6726ed6925ee6a24ef6c23ef6e21f06f20f1711ff1731df2741cf3761bf37819f47918f57b17f57d15f67e14f68013f78212f78410f8850ff8870ef8890cf98b0bf98c0af98e09fa9008fa9207fa9407fb9606fb9706fb9906fb9b06fb9d07fc9f07fca108fca309fca50afca60cfca80dfcaa0ffcac11fcae12fcb014fcb216fcb418fbb61afbb81dfbba1ffbbc21fbbe23fac026fac228fac42afac62df9c72ff9c932f9cb35f8cd37f8cf3af7d13df7d340f6d543f6d746f5d949f5db4cf4dd4ff4df53f4e156f3e35af3e55df2e661f2e865f2ea69f1ec6df1ed71f1ef75f1f179f2f27df2f482f3f586f3f68af4f88ef5f992f6fa96f8fb9af9fc9dfafda1fcffa4")),gb=Ar(G("0d088710078813078916078a19068c1b068d1d068e20068f2206902406912605912805922a05932c05942e05952f059631059733059735049837049938049a3a049a3c049b3e049c3f049c41049d43039e44039e46039f48039f4903a04b03a14c02a14e02a25002a25102a35302a35502a45601a45801a45901a55b01a55c01a65e01a66001a66100a76300a76400a76600a76700a86900a86a00a86c00a86e00a86f00a87100a87201a87401a87501a87701a87801a87a02a87b02a87d03a87e03a88004a88104a78305a78405a78606a68707a68808a68a09a58b0aa58d0ba58e0ca48f0da4910ea3920fa39410a29511a19613a19814a099159f9a169f9c179e9d189d9e199da01a9ca11b9ba21d9aa31e9aa51f99a62098a72197a82296aa2395ab2494ac2694ad2793ae2892b02991b12a90b22b8fb32c8eb42e8db52f8cb6308bb7318ab83289ba3388bb3488bc3587bd3786be3885bf3984c03a83c13b82c23c81c33d80c43e7fc5407ec6417dc7427cc8437bc9447aca457acb4679cc4778cc4977cd4a76ce4b75cf4c74d04d73d14e72d24f71d35171d45270d5536fd5546ed6556dd7566cd8576bd9586ada5a6ada5b69db5c68dc5d67dd5e66de5f65de6164df6263e06363e16462e26561e26660e3685fe4695ee56a5de56b5de66c5ce76e5be76f5ae87059e97158e97257ea7457eb7556eb7655ec7754ed7953ed7a52ee7b51ef7c51ef7e50f07f4ff0804ef1814df1834cf2844bf3854bf3874af48849f48948f58b47f58c46f68d45f68f44f79044f79143f79342f89441f89540f9973ff9983ef99a3efa9b3dfa9c3cfa9e3bfb9f3afba139fba238fca338fca537fca636fca835fca934fdab33fdac33fdae32fdaf31fdb130fdb22ffdb42ffdb52efeb72dfeb82cfeba2cfebb2bfebd2afebe2afec029fdc229fdc328fdc527fdc627fdc827fdca26fdcb26fccd25fcce25fcd025fcd225fbd324fbd524fbd724fad824fada24f9dc24f9dd25f8df25f8e125f7e225f7e425f6e626f6e826f5e926f5eb27f4ed27f3ee27f3f027f2f227f1f426f1f525f0f724f0f921"));const Ie=t=>()=>t;function V1(t,{sourceEvent:n,target:e,transform:r,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:i}})}function Gt(t,n,e){this.k=t,this.x=n,this.y=e}Gt.prototype={constructor:Gt,scale:function(t){return t===1?this:new Gt(this.k*t,this.x,this.y)},translate:function(t,n){return t===0&n===0?this:new Gt(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var ha=new Gt(1,0,0);X1.prototype=Gt.prototype;function X1(t){for(;!t.__zoom;)if(!(t=t.parentNode))return ha;return t.__zoom}function Kr(t){t.stopImmediatePropagation()}function Yn(t){t.preventDefault(),t.stopImmediatePropagation()}function H1(t){return(!t.ctrlKey||t.type==="wheel")&&!t.button}function U1(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t,t.hasAttribute("viewBox")?(t=t.viewBox.baseVal,[[t.x,t.y],[t.x+t.width,t.y+t.height]]):[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]):[[0,0],[t.clientWidth,t.clientHeight]]}function Oo(){return this.__zoom||ha}function W1(t){return-t.deltaY*(t.deltaMode===1?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function K1(){return navigator.maxTouchPoints||"ontouchstart"in this}function Z1(t,n,e){var r=t.invertX(n[0][0])-e[0][0],i=t.invertX(n[1][0])-e[1][0],a=t.invertY(n[0][1])-e[0][1],o=t.invertY(n[1][1])-e[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),o>a?(a+o)/2:Math.min(0,a)||Math.max(0,o))}function pb(){var t=H1,n=U1,e=Z1,r=W1,i=K1,a=[0,1/0],o=[[-1/0,-1/0],[1/0,1/0]],f=250,c=Ec,u=kn("start","zoom","end"),s,h,l,d=500,v=150,m=0,g=10;function p(x){x.property("__zoom",Oo).on("wheel.zoom",P,{passive:!1}).on("mousedown.zoom",R).on("dblclick.zoom",E).filter(i).on("touchstart.zoom",$).on("touchmove.zoom",z).on("touchend.zoom touchcancel.zoom",T).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}p.transform=function(x,A,S,N){var C=x.selection?x.selection():x;C.property("__zoom",Oo),x!==C?_(x,A,S,N):C.interrupt().each(function(){M(this,arguments).event(N).start().zoom(null,typeof A=="function"?A.apply(this,arguments):A).end()})},p.scaleBy=function(x,A,S,N){p.scaleTo(x,function(){var C=this.__zoom.k,k=typeof A=="function"?A.apply(this,arguments):A;return C*k},S,N)},p.scaleTo=function(x,A,S,N){p.transform(x,function(){var C=n.apply(this,arguments),k=this.__zoom,B=S==null?b(C):typeof S=="function"?S.apply(this,arguments):S,L=k.invert(B),Y=typeof A=="function"?A.apply(this,arguments):A;return e(w(y(k,Y),B,L),C,o)},S,N)},p.translateBy=function(x,A,S,N){p.transform(x,function(){return e(this.__zoom.translate(typeof A=="function"?A.apply(this,arguments):A,typeof S=="function"?S.apply(this,arguments):S),n.apply(this,arguments),o)},null,N)},p.translateTo=function(x,A,S,N,C){p.transform(x,function(){var k=n.apply(this,arguments),B=this.__zoom,L=N==null?b(k):typeof N=="function"?N.apply(this,arguments):N;return e(ha.translate(L[0],L[1]).scale(B.k).translate(typeof A=="function"?-A.apply(this,arguments):-A,typeof S=="function"?-S.apply(this,arguments):-S),k,o)},N,C)};function y(x,A){return A=Math.max(a[0],Math.min(a[1],A)),A===x.k?x:new Gt(A,x.x,x.y)}function w(x,A,S){var N=A[0]-S[0]*x.k,C=A[1]-S[1]*x.k;return N===x.x&&C===x.y?x:new Gt(x.k,N,C)}function b(x){return[(+x[0][0]+ +x[1][0])/2,(+x[0][1]+ +x[1][1])/2]}function _(x,A,S,N){x.on("start.zoom",function(){M(this,arguments).event(N).start()}).on("interrupt.zoom end.zoom",function(){M(this,arguments).event(N).end()}).tween("zoom",function(){var C=this,k=arguments,B=M(C,k).event(N),L=n.apply(C,k),Y=S==null?b(L):typeof S=="function"?S.apply(C,k):S,Q=Math.max(L[1][0]-L[0][0],L[1][1]-L[0][1]),U=C.__zoom,J=typeof A=="function"?A.apply(C,k):A,Mt=c(U.invert(Y).concat(Q/U.k),J.invert(Y).concat(Q/J.k));return function(Z){if(Z===1)Z=J;else{var lt=Mt(Z),hn=Q/lt[2];Z=new Gt(hn,Y[0]-lt[0]*hn,Y[1]-lt[1]*hn)}B.zoom(null,Z)}})}function M(x,A,S){return!S&&x.__zooming||new I(x,A)}function I(x,A){this.that=x,this.args=A,this.active=0,this.sourceEvent=null,this.extent=n.apply(x,A),this.taps=0}I.prototype={event:function(x){return x&&(this.sourceEvent=x),this},start:function(){return++this.active===1&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(x,A){return this.mouse&&x!=="mouse"&&(this.mouse[1]=A.invert(this.mouse[0])),this.touch0&&x!=="touch"&&(this.touch0[1]=A.invert(this.touch0[0])),this.touch1&&x!=="touch"&&(this.touch1[1]=A.invert(this.touch1[0])),this.that.__zoom=A,this.emit("zoom"),this},end:function(){return--this.active===0&&(delete this.that.__zooming,this.emit("end")),this},emit:function(x){var A=bt(this.that).datum();u.call(x,this.that,new V1(x,{sourceEvent:this.sourceEvent,target:p,type:x,transform:this.that.__zoom,dispatch:u}),A)}};function P(x,...A){if(!t.apply(this,arguments))return;var S=M(this,A).event(x),N=this.__zoom,C=Math.max(a[0],Math.min(a[1],N.k*Math.pow(2,r.apply(this,arguments)))),k=Rt(x);if(S.wheel)(S.mouse[0][0]!==k[0]||S.mouse[0][1]!==k[1])&&(S.mouse[1]=N.invert(S.mouse[0]=k)),clearTimeout(S.wheel);else{if(N.k===C)return;S.mouse=[k,N.invert(k)],An(this),S.start()}Yn(x),S.wheel=setTimeout(B,v),S.zoom("mouse",e(w(y(N,C),S.mouse[0],S.mouse[1]),S.extent,o));function B(){S.wheel=null,S.end()}}function R(x,...A){if(l||!t.apply(this,arguments))return;var S=x.currentTarget,N=M(this,A,!0).event(x),C=bt(x.view).on("mousemove.zoom",Y,!0).on("mouseup.zoom",Q,!0),k=Rt(x,S),B=x.clientX,L=x.clientY;Li(x.view),Kr(x),N.mouse=[k,this.__zoom.invert(k)],An(this),N.start();function Y(U){if(Yn(U),!N.moved){var J=U.clientX-B,Mt=U.clientY-L;N.moved=J*J+Mt*Mt>m}N.event(U).zoom("mouse",e(w(N.that.__zoom,N.mouse[0]=Rt(U,S),N.mouse[1]),N.extent,o))}function Q(U){C.on("mousemove.zoom mouseup.zoom",null),Fi(U.view,N.moved),Yn(U),N.event(U).end()}}function E(x,...A){if(t.apply(this,arguments)){var S=this.__zoom,N=Rt(x.changedTouches?x.changedTouches[0]:x,this),C=S.invert(N),k=S.k*(x.shiftKey?.5:2),B=e(w(y(S,k),N,C),n.apply(this,A),o);Yn(x),f>0?bt(this).transition().duration(f).call(_,B,N,x):bt(this).call(p.transform,B,N,x)}}function $(x,...A){if(t.apply(this,arguments)){var S=x.touches,N=S.length,C=M(this,A,x.changedTouches.length===N).event(x),k,B,L,Y;for(Kr(x),B=0;B<N;++B)L=S[B],Y=Rt(L,this),Y=[Y,this.__zoom.invert(Y),L.identifier],C.touch0?!C.touch1&&C.touch0[2]!==Y[2]&&(C.touch1=Y,C.taps=0):(C.touch0=Y,k=!0,C.taps=1+!!s);s&&(s=clearTimeout(s)),k&&(C.taps<2&&(h=Y[0],s=setTimeout(function(){s=null},d)),An(this),C.start())}}function z(x,...A){if(this.__zooming){var S=M(this,A).event(x),N=x.changedTouches,C=N.length,k,B,L,Y;for(Yn(x),k=0;k<C;++k)B=N[k],L=Rt(B,this),S.touch0&&S.touch0[2]===B.identifier?S.touch0[0]=L:S.touch1&&S.touch1[2]===B.identifier&&(S.touch1[0]=L);if(B=S.that.__zoom,S.touch1){var Q=S.touch0[0],U=S.touch0[1],J=S.touch1[0],Mt=S.touch1[1],Z=(Z=J[0]-Q[0])*Z+(Z=J[1]-Q[1])*Z,lt=(lt=Mt[0]-U[0])*lt+(lt=Mt[1]-U[1])*lt;B=y(B,Math.sqrt(Z/lt)),L=[(Q[0]+J[0])/2,(Q[1]+J[1])/2],Y=[(U[0]+Mt[0])/2,(U[1]+Mt[1])/2]}else if(S.touch0)L=S.touch0[0],Y=S.touch0[1];else return;S.zoom("touch",e(w(B,L,Y),S.extent,o))}}function T(x,...A){if(this.__zooming){var S=M(this,A).event(x),N=x.changedTouches,C=N.length,k,B;for(Kr(x),l&&clearTimeout(l),l=setTimeout(function(){l=null},d),k=0;k<C;++k)B=N[k],S.touch0&&S.touch0[2]===B.identifier?delete S.touch0:S.touch1&&S.touch1[2]===B.identifier&&delete S.touch1;if(S.touch1&&!S.touch0&&(S.touch0=S.touch1,delete S.touch1),S.touch0)S.touch0[1]=this.__zoom.invert(S.touch0[0]);else if(S.end(),S.taps===2&&(B=Rt(B,this),Math.hypot(h[0]-B[0],h[1]-B[1])<g)){var L=bt(this).on("dblclick.zoom");L&&L.apply(this,arguments)}}}return p.wheelDelta=function(x){return arguments.length?(r=typeof x=="function"?x:Ie(+x),p):r},p.filter=function(x){return arguments.length?(t=typeof x=="function"?x:Ie(!!x),p):t},p.touchable=function(x){return arguments.length?(i=typeof x=="function"?x:Ie(!!x),p):i},p.extent=function(x){return arguments.length?(n=typeof x=="function"?x:Ie([[+x[0][0],+x[0][1]],[+x[1][0],+x[1][1]]]),p):n},p.scaleExtent=function(x){return arguments.length?(a[0]=+x[0],a[1]=+x[1],p):[a[0],a[1]]},p.translateExtent=function(x){return arguments.length?(o[0][0]=+x[0][0],o[1][0]=+x[1][0],o[0][1]=+x[0][1],o[1][1]=+x[1][1],p):[[o[0][0],o[0][1]],[o[1][0],o[1][1]]]},p.constrain=function(x){return arguments.length?(e=x,p):e},p.duration=function(x){return arguments.length?(f=+x,p):f},p.interpolate=function(x){return arguments.length?(c=x,p):c},p.on=function(){var x=u.on.apply(u,arguments);return x===u?p:x},p.clickDistance=function(x){return arguments.length?(m=(x=+x)*x,p):Math.sqrt(m)},p.tapDistance=function(x){return arguments.length?(g=+x,p):g},p}export{st as Adder,sf as Delaunay,vb as FormatSpecifier,yb as InternMap,wb as InternSet,zn as Node,_b as Path,P0 as Voronoi,Gt as ZoomTransform,k2 as active,xb as arc,Mb as area,Sb as areaRadial,Ab as ascending,rg as autoType,n2 as axisBottom,e2 as axisLeft,t2 as axisRight,j1 as axisTop,Eb as bin,$b as bisect,Rb as bisectCenter,Pb as bisectLeft,Ib as bisectRight,Nb as bisector,ig as blob,Tb as blur,Mc as blur2,zb as blurImage,D2 as brush,C2 as brushSelection,B2 as brushX,O2 as brushY,ag as buffer,L2 as chord,q2 as chordDirected,F2 as chordTranspose,Hg as cluster,ba as color,V2 as contourDensity,ka as contours,kb as count,r2 as create,Ci as creator,Cb as cross,fg as csv,H2 as csvFormat,U2 as csvFormatBody,K2 as csvFormatRow,W2 as csvFormatRows,Z2 as csvFormatValue,F0 as csvParse,X2 as csvParseRows,on as cubehelix,Bb as cumsum,Ob as curveBasis,Db as curveBasisClosed,Lb as curveBasisOpen,Fb as curveBumpX,qb as curveBumpY,Yb as curveBundle,Gb as curveCardinal,Vb as curveCardinalClosed,Xb as curveCardinalOpen,Hb as curveCatmullRom,Ub as curveCatmullRomClosed,Wb as curveCatmullRomOpen,Kb as curveLinear,Zb as curveLinearClosed,Qb as curveMonotoneX,Jb as curveMonotoneY,jb as curveNatural,tm as curveStep,nm as curveStepAfter,em as curveStepBefore,rm as descending,im as deviation,am as difference,om as disjoint,kn as dispatch,o2 as drag,Li as dragDisable,Fi as dragEnable,og as dsv,Zi as dsvFormat,I2 as easeBack,R2 as easeBackIn,I2 as easeBackInOut,P2 as easeBackOut,ai as easeBounce,E2 as easeBounceIn,$2 as easeBounceInOut,ai as easeBounceOut,A2 as easeCircle,M2 as easeCircleIn,A2 as easeCircleInOut,S2 as easeCircleOut,Rl as easeCubic,h2 as easeCubicIn,Rl as easeCubicInOut,d2 as easeCubicOut,T2 as easeElastic,N2 as easeElasticIn,z2 as easeElasticInOut,T2 as easeElasticOut,x2 as easeExp,w2 as easeExpIn,x2 as easeExpInOut,_2 as easeExpOut,c2 as easeLinear,b2 as easePoly,g2 as easePolyIn,b2 as easePolyInOut,p2 as easePolyOut,l2 as easeQuad,u2 as easeQuadIn,l2 as easeQuadInOut,s2 as easeQuadOut,y2 as easeSin,m2 as easeSinIn,y2 as easeSinInOut,v2 as easeSinOut,fm as every,_c as extent,cm as fcumsum,um as filter,sm as flatGroup,lm as flatRollup,gg as forceCenter,pg as forceCollide,bg as forceLink,vg as forceManyBody,yg as forceRadial,mg as forceSimulation,wg as forceX,_g as forceY,hm as format,dm as formatDefaultLocale,gm as formatLocale,pm as formatPrefix,bm as formatSpecifier,mm as fsum,hd as geoAlbers,Tg as geoAlbersUsa,xg as geoArea,zg as geoAzimuthalEqualArea,Xf as geoAzimuthalEqualAreaRaw,kg as geoAzimuthalEquidistant,Hf as geoAzimuthalEquidistantRaw,Mg as geoBounds,Sg as geoCentroid,Ag as geoCircle,oo as geoClipAntimeridian,Bh as geoClipCircle,Eg as geoClipExtent,ia as geoClipRectangle,Bg as geoConicConformal,gd as geoConicConformalRaw,Ti as geoConicEqualArea,ld as geoConicEqualAreaRaw,Dg as geoConicEquidistant,pd as geoConicEquidistantRaw,$g as geoContains,xi as geoDistance,Lg as geoEqualEarth,Wf as geoEqualEarthRaw,Og as geoEquirectangular,pr as geoEquirectangularRaw,Fg as geoGnomonic,Kf as geoGnomonicRaw,Xh as geoGraticule,Rg as geoGraticule10,qg as geoIdentity,Pg as geoInterpolate,Yh as geoLength,Cg as geoMercator,Mr as geoMercatorRaw,Yg as geoNaturalEarth1,Zf as geoNaturalEarth1Raw,Gg as geoOrthographic,Qf as geoOrthographicRaw,Ig as geoPath,Ht as geoProjection,Gf as geoProjectionMutator,Ih as geoRotation,Vg as geoStereographic,Jf as geoStereographicRaw,zt as geoStream,Ng as geoTransform,Xg as geoTransverseMercator,jf as geoTransverseMercatorRaw,vm as gray,ym as greatest,wm as greatestIndex,_m as group,xm as groupSort,Mm as groups,Sm as hcl,tc as hierarchy,Am as histogram,Em as hsl,hg as html,ug as image,$m as index,Rm as indexes,vc as interpolate,Pm as interpolateArray,Im as interpolateBasis,Nm as interpolateBasisClosed,Jp as interpolateBlues,Np as interpolateBrBG,Fp as interpolateBuGn,qp as interpolateBuPu,ib as interpolateCividis,fb as interpolateCool,Tm as interpolateCubehelix,ab as interpolateCubehelixDefault,ki as interpolateCubehelixLong,zm as interpolateDate,km as interpolateDiscrete,Yp as interpolateGnBu,jp as interpolateGreens,tb as interpolateGreys,Cm as interpolateHcl,Bm as interpolateHclLong,Om as interpolateHsl,Dm as interpolateHslLong,Lm as interpolateHue,db as interpolateInferno,Fm as interpolateLab,hb as interpolateMagma,gc as interpolateNumber,qm as interpolateNumberArray,Ym as interpolateObject,Gp as interpolateOrRd,rb as interpolateOranges,Tp as interpolatePRGn,zp as interpolatePiYG,gb as interpolatePlasma,Xp as interpolatePuBu,Vp as interpolatePuBuGn,kp as interpolatePuOr,Hp as interpolatePuRd,nb as interpolatePurples,cb as interpolateRainbow,Cp as interpolateRdBu,Bp as interpolateRdGy,Up as interpolateRdPu,Op as interpolateRdYlBu,Dp as interpolateRdYlGn,eb as interpolateReds,ma as interpolateRgb,Sc as interpolateRgbBasis,Gm as interpolateRgbBasisClosed,Vm as interpolateRound,ub as interpolateSinebow,Lp as interpolateSpectral,pc as interpolateString,mc as interpolateTransformCss,bc as interpolateTransformSvg,sb as interpolateTurbo,lb as interpolateViridis,ob as interpolateWarm,Kp as interpolateYlGn,Wp as interpolateYlGnBu,Zp as interpolateYlOrBr,Qp as interpolateYlOrRd,Ec as interpolateZoom,An as interrupt,Xm as intersection,f2 as interval,Hm as isoFormat,Um as isoParse,sg as json,Wm as lab,Km as lch,Zm as least,Qm as leastIndex,Jm as line,jm as lineRadial,t6 as link,n6 as linkHorizontal,e6 as linkRadial,r6 as linkVertical,gs as local,i6 as map,Yo as matcher,va as max,a6 as maxIndex,o6 as mean,f6 as median,c6 as medianIndex,Lo as merge,u6 as min,s6 as minIndex,l6 as mode,vr as namespace,_a as namespaces,xc as nice,wr as now,Kg as pack,Ug as packEnclose,Wg as packSiblings,h6 as pairs,Zg as partition,yc as path,d6 as pathRound,g6 as permute,p6 as pie,b6 as piecewise,m6 as pointRadial,Rt as pointer,i2 as pointers,rp as polygonArea,ip as polygonCentroid,op as polygonContains,ap as polygonHull,fp as polygonLength,v6 as precisionFixed,y6 as precisionPrefix,w6 as precisionRound,ji as quadtree,_6 as quantile,x6 as quantileIndex,M6 as quantileSorted,S6 as quantize,A6 as quickselect,E6 as radialArea,$6 as radialLine,lp as randomBates,gp as randomBernoulli,l1 as randomBeta,h1 as randomBinomial,bp as randomCauchy,hp as randomExponential,lc as randomGamma,s1 as randomGeometric,up as randomInt,u1 as randomIrwinHall,yp as randomLcg,sp as randomLogNormal,mp as randomLogistic,sc as randomNormal,dp as randomPareto,vp as randomPoisson,cp as randomUniform,pp as randomWeibull,_n as range,R6 as rank,P6 as reduce,I6 as reverse,Ac as rgb,Y2 as ribbon,G2 as ribbonArrow,N6 as rollup,T6 as rollups,z6 as scaleBand,k6 as scaleDiverging,C6 as scaleDivergingLog,B6 as scaleDivergingPow,O6 as scaleDivergingSqrt,D6 as scaleDivergingSymlog,L6 as scaleIdentity,F6 as scaleImplicit,q6 as scaleLinear,Y6 as scaleLog,G6 as scaleOrdinal,V6 as scalePoint,X6 as scalePow,H6 as scaleQuantile,U6 as scaleQuantize,W6 as scaleRadial,K6 as scaleSequential,Z6 as scaleSequentialLog,Q6 as scaleSequentialPow,J6 as scaleSequentialQuantile,j6 as scaleSequentialSqrt,tv as scaleSequentialSymlog,nv as scaleSqrt,ev as scaleSymlog,rv as scaleThreshold,iv as scaleTime,av as scaleUtc,ov as scan,_p as schemeAccent,B1 as schemeBlues,p1 as schemeBrBG,S1 as schemeBuGn,A1 as schemeBuPu,wp as schemeCategory10,xp as schemeDark2,E1 as schemeGnBu,O1 as schemeGreens,D1 as schemeGreys,Mp as schemeObservable10,$1 as schemeOrRd,q1 as schemeOranges,b1 as schemePRGn,Sp as schemePaired,Ap as schemePastel1,Ep as schemePastel2,m1 as schemePiYG,P1 as schemePuBu,R1 as schemePuBuGn,v1 as schemePuOr,I1 as schemePuRd,L1 as schemePurples,y1 as schemeRdBu,w1 as schemeRdGy,N1 as schemeRdPu,_1 as schemeRdYlBu,x1 as schemeRdYlGn,F1 as schemeReds,$p as schemeSet1,Rp as schemeSet2,Pp as schemeSet3,M1 as schemeSpectral,Ip as schemeTableau10,z1 as schemeYlGn,T1 as schemeYlGnBu,k1 as schemeYlOrBr,C1 as schemeYlOrRd,bt as select,a2 as selectAll,Cn as selection,Bi as selector,qo as selectorAll,fv as shuffle,cv as shuffler,uv as some,sv as sort,lv as stack,hv as stackOffsetDiverging,dv as stackOffsetExpand,gv as stackOffsetNone,pv as stackOffsetSilhouette,bv as stackOffsetWiggle,mv as stackOrderAppearance,vv as stackOrderAscending,yv as stackOrderDescending,wv as stackOrderInsideOut,_v as stackOrderNone,xv as stackOrderReverse,Qg as stratify,$n as style,Mv as subset,Sv as sum,Av as superset,dg as svg,Ev as symbol,$v as symbolAsterisk,Rv as symbolCircle,Pv as symbolCross,Iv as symbolDiamond,Nv as symbolDiamond2,Tv as symbolPlus,zv as symbolSquare,kv as symbolSquare2,Cv as symbolStar,Bv as symbolTimes,Ov as symbolTriangle,Dv as symbolTriangle2,Lv as symbolWye,Fv as symbolX,qv as symbols,Yv as symbolsFill,Gv as symbolsStroke,Qi as text,Vv as thresholdFreedmanDiaconis,Xv as thresholdScott,wc as thresholdSturges,Hv as tickFormat,Uv as tickIncrement,Wv as tickStep,Do as ticks,Kv as timeDay,Zv as timeDays,Qv as timeFormat,Jv as timeFormatDefaultLocale,jv as timeFormatLocale,t8 as timeFriday,n8 as timeFridays,e8 as timeHour,r8 as timeHours,i8 as timeInterval,a8 as timeMillisecond,o8 as timeMilliseconds,f8 as timeMinute,c8 as timeMinutes,u8 as timeMonday,s8 as timeMondays,l8 as timeMonth,h8 as timeMonths,d8 as timeParse,g8 as timeSaturday,p8 as timeSaturdays,b8 as timeSecond,m8 as timeSeconds,v8 as timeSunday,y8 as timeSundays,w8 as timeThursday,_8 as timeThursdays,x8 as timeTickInterval,M8 as timeTicks,S8 as timeTuesday,A8 as timeTuesdays,E8 as timeWednesday,$8 as timeWednesdays,R8 as timeWeek,P8 as timeWeeks,I8 as timeYear,N8 as timeYears,Ma as timeout,qi as timer,_s as timerFlush,$l as transition,T8 as transpose,Jg as tree,jg as treemap,tp as treemapBinary,Sr as treemapDice,ep as treemapResquarify,la as treemapSlice,np as treemapSliceDice,o1 as treemapSquarify,cg as tsv,J2 as tsvFormat,j2 as tsvFormatBody,ng as tsvFormatRow,tg as tsvFormatRows,eg as tsvFormatValue,q0 as tsvParse,Q2 as tsvParseRows,z8 as union,k8 as unixDay,C8 as unixDays,B8 as utcDay,O8 as utcDays,D8 as utcFormat,L8 as utcFriday,F8 as utcFridays,q8 as utcHour,Y8 as utcHours,G8 as utcMillisecond,V8 as utcMilliseconds,X8 as utcMinute,H8 as utcMinutes,U8 as utcMonday,W8 as utcMondays,K8 as utcMonth,Z8 as utcMonths,Q8 as utcParse,J8 as utcSaturday,j8 as utcSaturdays,ty as utcSecond,ny as utcSeconds,ey as utcSunday,ry as utcSundays,iy as utcThursday,ay as utcThursdays,oy as utcTickInterval,fy as utcTicks,cy as utcTuesday,uy as utcTuesdays,sy as utcWednesday,ly as utcWednesdays,hy as utcWeek,dy as utcWeeks,gy as utcYear,py as utcYears,by as variance,Xo as window,lg as xml,my as zip,pb as zoom,ha as zoomIdentity,X1 as zoomTransform};
//# sourceMappingURL=index-e3558beb.js.map
