import{r as c,R as se}from"./vendor-b69f2a9f.js";/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function L(){return L=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},L.apply(this,arguments)}var P;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(P||(P={}));const z="popstate";function ue(e){e===void 0&&(e={});function t(r,a){let{pathname:i,search:l,hash:s}=r.location;return $("",{pathname:i,search:l,hash:s},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:N(a)}return fe(t,n,null,e)}function g(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Q(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function ce(){return Math.random().toString(36).substr(2,8)}function D(e,t){return{usr:e.state,key:e.key,idx:t}}function $(e,t,n,r){return n===void 0&&(n=null),L({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?B(t):t,{state:n,key:t&&t.key||r||ce()})}function N(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function B(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function fe(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:i=!1}=r,l=a.history,s=P.Pop,o=null,f=d();f==null&&(f=0,l.replaceState(L({},l.state,{idx:f}),""));function d(){return(l.state||{idx:null}).idx}function u(){s=P.Pop;let h=d(),x=h==null?null:h-f;f=h,o&&o({action:s,location:m.location,delta:x})}function p(h,x){s=P.Push;let C=$(m.location,h,x);n&&n(C,h),f=d()+1;let E=D(C,f),R=m.createHref(C);try{l.pushState(E,"",R)}catch(U){if(U instanceof DOMException&&U.name==="DataCloneError")throw U;a.location.assign(R)}i&&o&&o({action:s,location:m.location,delta:1})}function y(h,x){s=P.Replace;let C=$(m.location,h,x);n&&n(C,h),f=d();let E=D(C,f),R=m.createHref(C);l.replaceState(E,"",R),i&&o&&o({action:s,location:m.location,delta:0})}function v(h){let x=a.location.origin!=="null"?a.location.origin:a.location.href,C=typeof h=="string"?h:N(h);return C=C.replace(/ $/,"%20"),g(x,"No window.location.(origin|href) available to create URL for href: "+C),new URL(C,x)}let m={get action(){return s},get location(){return e(a,l)},listen(h){if(o)throw new Error("A history only accepts one active listener");return a.addEventListener(z,u),o=h,()=>{a.removeEventListener(z,u),o=null}},createHref(h){return t(a,h)},createURL:v,encodeLocation(h){let x=v(h);return{pathname:x.pathname,search:x.search,hash:x.hash}},push:p,replace:y,go(h){return l.go(h)}};return m}var A;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(A||(A={}));function de(e,t,n){return n===void 0&&(n="/"),he(e,t,n,!1)}function he(e,t,n,r){let a=typeof t=="string"?B(t):t,i=F(a.pathname||"/",n);if(i==null)return null;let l=Y(e);pe(l);let s=null;for(let o=0;s==null&&o<l.length;++o){let f=be(i);s=Pe(l[o],f,r)}return s}function Y(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(i,l,s)=>{let o={relativePath:s===void 0?i.path||"":s,caseSensitive:i.caseSensitive===!0,childrenIndex:l,route:i};o.relativePath.startsWith("/")&&(g(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),o.relativePath=o.relativePath.slice(r.length));let f=w([r,o.relativePath]),d=n.concat(o);i.children&&i.children.length>0&&(g(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+f+'".')),Y(i.children,t,d,f)),!(i.path==null&&!i.index)&&t.push({path:f,score:Ce(f,i.index),routesMeta:d})};return e.forEach((i,l)=>{var s;if(i.path===""||!((s=i.path)!=null&&s.includes("?")))a(i,l);else for(let o of Z(i.path))a(i,l,o)}),t}function Z(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return a?[i,""]:[i];let l=Z(r.join("/")),s=[];return s.push(...l.map(o=>o===""?i:[i,o].join("/"))),a&&s.push(...l),s.map(o=>e.startsWith("/")&&o===""?"/":o)}function pe(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Re(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const me=/^:[\w-]+$/,ve=3,ge=2,ye=1,xe=10,Ee=-2,J=e=>e==="*";function Ce(e,t){let n=e.split("/"),r=n.length;return n.some(J)&&(r+=Ee),t&&(r+=ge),n.filter(a=>!J(a)).reduce((a,i)=>a+(me.test(i)?ve:i===""?ye:xe),r)}function Re(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function Pe(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,a={},i="/",l=[];for(let s=0;s<r.length;++s){let o=r[s],f=s===r.length-1,d=i==="/"?t:t.slice(i.length)||"/",u=K({path:o.relativePath,caseSensitive:o.caseSensitive,end:f},d),p=o.route;if(!u&&f&&n&&!r[r.length-1].route.index&&(u=K({path:o.relativePath,caseSensitive:o.caseSensitive,end:!1},d)),!u)return null;Object.assign(a,u.params),l.push({params:a,pathname:w([i,u.pathname]),pathnameBase:Le(w([i,u.pathnameBase])),route:p}),u.pathnameBase!=="/"&&(i=w([i,u.pathnameBase]))}return l}function K(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=we(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let i=a[0],l=i.replace(/(.)\/+$/,"$1"),s=a.slice(1);return{params:r.reduce((f,d,u)=>{let{paramName:p,isOptional:y}=d;if(p==="*"){let m=s[u]||"";l=i.slice(0,i.length-m.length).replace(/(.)\/+$/,"$1")}const v=s[u];return y&&!v?f[p]=void 0:f[p]=(v||"").replace(/%2F/g,"/"),f},{}),pathname:i,pathnameBase:l,pattern:e}}function we(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Q(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(l,s,o)=>(r.push({paramName:s,isOptional:o!=null}),o?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function be(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Q(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function F(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Se(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?B(e):e;return{pathname:n?n.startsWith("/")?n:Ue(n,t):t,search:Oe(r),hash:Ie(a)}}function Ue(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function j(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Be(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function ee(e,t){let n=Be(e);return t?n.map((r,a)=>a===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function te(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=B(e):(a=L({},e),g(!a.pathname||!a.pathname.includes("?"),j("?","pathname","search",a)),g(!a.pathname||!a.pathname.includes("#"),j("#","pathname","hash",a)),g(!a.search||!a.search.includes("#"),j("#","search","hash",a)));let i=e===""||a.pathname==="",l=i?"/":a.pathname,s;if(l==null)s=n;else{let u=t.length-1;if(!r&&l.startsWith("..")){let p=l.split("/");for(;p[0]==="..";)p.shift(),u-=1;a.pathname=p.join("/")}s=u>=0?t[u]:"/"}let o=Se(a,s),f=l&&l!=="/"&&l.endsWith("/"),d=(i||l===".")&&n.endsWith("/");return!o.pathname.endsWith("/")&&(f||d)&&(o.pathname+="/"),o}const w=e=>e.join("/").replace(/\/\/+/g,"/"),Le=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Oe=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ie=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Ne(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const ne=["post","put","patch","delete"];new Set(ne);const Te=["get",...ne];new Set(Te);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function O(){return O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},O.apply(this,arguments)}const V=c.createContext(null),_e=c.createContext(null),b=c.createContext(null),_=c.createContext(null),S=c.createContext({outlet:null,matches:[],isDataRoute:!1}),re=c.createContext(null);function ke(e,t){let{relative:n}=t===void 0?{}:t;I()||g(!1);let{basename:r,navigator:a}=c.useContext(b),{hash:i,pathname:l,search:s}=ie(e,{relative:n}),o=l;return r!=="/"&&(o=l==="/"?r:w([r,l])),a.createHref({pathname:o,search:s,hash:i})}function I(){return c.useContext(_)!=null}function k(){return I()||g(!1),c.useContext(_).location}function ae(e){c.useContext(b).static||c.useLayoutEffect(e)}function je(){let{isDataRoute:e}=c.useContext(S);return e?He():$e()}function $e(){I()||g(!1);let e=c.useContext(V),{basename:t,future:n,navigator:r}=c.useContext(b),{matches:a}=c.useContext(S),{pathname:i}=k(),l=JSON.stringify(ee(a,n.v7_relativeSplatPath)),s=c.useRef(!1);return ae(()=>{s.current=!0}),c.useCallback(function(f,d){if(d===void 0&&(d={}),!s.current)return;if(typeof f=="number"){r.go(f);return}let u=te(f,JSON.parse(l),i,d.relative==="path");e==null&&t!=="/"&&(u.pathname=u.pathname==="/"?t:w([t,u.pathname])),(d.replace?r.replace:r.push)(u,d.state,d)},[t,r,l,i,e])}function ie(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=c.useContext(b),{matches:a}=c.useContext(S),{pathname:i}=k(),l=JSON.stringify(ee(a,r.v7_relativeSplatPath));return c.useMemo(()=>te(e,JSON.parse(l),i,n==="path"),[e,l,i,n])}function We(e,t){return Me(e,t)}function Me(e,t,n,r){I()||g(!1);let{navigator:a}=c.useContext(b),{matches:i}=c.useContext(S),l=i[i.length-1],s=l?l.params:{};l&&l.pathname;let o=l?l.pathnameBase:"/";l&&l.route;let f=k(),d;if(t){var u;let h=typeof t=="string"?B(t):t;o==="/"||(u=h.pathname)!=null&&u.startsWith(o)||g(!1),d=h}else d=f;let p=d.pathname||"/",y=p;if(o!=="/"){let h=o.replace(/^\//,"").split("/");y="/"+p.replace(/^\//,"").split("/").slice(h.length).join("/")}let v=de(e,{pathname:y}),m=Ae(v&&v.map(h=>Object.assign({},h,{params:Object.assign({},s,h.params),pathname:w([o,a.encodeLocation?a.encodeLocation(h.pathname).pathname:h.pathname]),pathnameBase:h.pathnameBase==="/"?o:w([o,a.encodeLocation?a.encodeLocation(h.pathnameBase).pathname:h.pathnameBase])})),i,n,r);return t&&m?c.createElement(_.Provider,{value:{location:O({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:P.Pop}},m):m}function Fe(){let e=Ge(),t=Ne(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},i=null;return c.createElement(c.Fragment,null,c.createElement("h2",null,"Unexpected Application Error!"),c.createElement("h3",{style:{fontStyle:"italic"}},t),n?c.createElement("pre",{style:a},n):null,i)}const Ve=c.createElement(Fe,null);class ze extends c.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?c.createElement(S.Provider,{value:this.props.routeContext},c.createElement(re.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function De(e){let{routeContext:t,match:n,children:r}=e,a=c.useContext(V);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),c.createElement(S.Provider,{value:t},r)}function Ae(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let l=e,s=(a=n)==null?void 0:a.errors;if(s!=null){let d=l.findIndex(u=>u.route.id&&(s==null?void 0:s[u.route.id])!==void 0);d>=0||g(!1),l=l.slice(0,Math.min(l.length,d+1))}let o=!1,f=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<l.length;d++){let u=l[d];if((u.route.HydrateFallback||u.route.hydrateFallbackElement)&&(f=d),u.route.id){let{loaderData:p,errors:y}=n,v=u.route.loader&&p[u.route.id]===void 0&&(!y||y[u.route.id]===void 0);if(u.route.lazy||v){o=!0,f>=0?l=l.slice(0,f+1):l=[l[0]];break}}}return l.reduceRight((d,u,p)=>{let y,v=!1,m=null,h=null;n&&(y=s&&u.route.id?s[u.route.id]:void 0,m=u.route.errorElement||Ve,o&&(f<0&&p===0?(Xe("route-fallback",!1),v=!0,h=null):f===p&&(v=!0,h=u.route.hydrateFallbackElement||null)));let x=t.concat(l.slice(0,p+1)),C=()=>{let E;return y?E=m:v?E=h:u.route.Component?E=c.createElement(u.route.Component,null):u.route.element?E=u.route.element:E=d,c.createElement(De,{match:u,routeContext:{outlet:d,matches:x,isDataRoute:n!=null},children:E})};return n&&(u.route.ErrorBoundary||u.route.errorElement||p===0)?c.createElement(ze,{location:n.location,revalidation:n.revalidation,component:m,error:y,children:C(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):C()},null)}var le=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(le||{}),T=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(T||{});function Je(e){let t=c.useContext(V);return t||g(!1),t}function Ke(e){let t=c.useContext(_e);return t||g(!1),t}function qe(e){let t=c.useContext(S);return t||g(!1),t}function oe(e){let t=qe(),n=t.matches[t.matches.length-1];return n.route.id||g(!1),n.route.id}function Ge(){var e;let t=c.useContext(re),n=Ke(T.UseRouteError),r=oe(T.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function He(){let{router:e}=Je(le.UseNavigateStable),t=oe(T.UseNavigateStable),n=c.useRef(!1);return ae(()=>{n.current=!0}),c.useCallback(function(a,i){i===void 0&&(i={}),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,O({fromRouteId:t},i)))},[e,t])}const q={};function Xe(e,t,n){!t&&!q[e]&&(q[e]=!0)}function Qe(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function Ye(e){g(!1)}function Ze(e){let{basename:t="/",children:n=null,location:r,navigationType:a=P.Pop,navigator:i,static:l=!1,future:s}=e;I()&&g(!1);let o=t.replace(/^\/*/,"/"),f=c.useMemo(()=>({basename:o,navigator:i,static:l,future:O({v7_relativeSplatPath:!1},s)}),[o,s,i,l]);typeof r=="string"&&(r=B(r));let{pathname:d="/",search:u="",hash:p="",state:y=null,key:v="default"}=r,m=c.useMemo(()=>{let h=F(d,o);return h==null?null:{location:{pathname:h,search:u,hash:p,state:y,key:v},navigationType:a}},[o,d,u,p,y,v,a]);return m==null?null:c.createElement(b.Provider,{value:f},c.createElement(_.Provider,{children:n,value:m}))}function ct(e){let{children:t,location:n}=e;return We(W(t),n)}new Promise(()=>{});function W(e,t){t===void 0&&(t=[]);let n=[];return c.Children.forEach(e,(r,a)=>{if(!c.isValidElement(r))return;let i=[...t,a];if(r.type===c.Fragment){n.push.apply(n,W(r.props.children,i));return}r.type!==Ye&&g(!1),!r.props.index||!r.props.children||g(!1);let l={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(l.children=W(r.props.children,i)),n.push(l)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function M(){return M=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},M.apply(this,arguments)}function et(e,t){if(e==null)return{};var n={},r=Object.keys(e),a,i;for(i=0;i<r.length;i++)a=r[i],!(t.indexOf(a)>=0)&&(n[a]=e[a]);return n}function tt(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function nt(e,t){return e.button===0&&(!t||t==="_self")&&!tt(e)}const rt=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],at="6";try{window.__reactRouterVersion=at}catch{}const it="startTransition",G=se[it];function ft(e){let{basename:t,children:n,future:r,window:a}=e,i=c.useRef();i.current==null&&(i.current=ue({window:a,v5Compat:!0}));let l=i.current,[s,o]=c.useState({action:l.action,location:l.location}),{v7_startTransition:f}=r||{},d=c.useCallback(u=>{f&&G?G(()=>o(u)):o(u)},[o,f]);return c.useLayoutEffect(()=>l.listen(d),[l,d]),c.useEffect(()=>Qe(r),[r]),c.createElement(Ze,{basename:t,children:n,location:s.location,navigationType:s.action,navigator:l,future:r})}const lt=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",ot=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,dt=c.forwardRef(function(t,n){let{onClick:r,relative:a,reloadDocument:i,replace:l,state:s,target:o,to:f,preventScrollReset:d,viewTransition:u}=t,p=et(t,rt),{basename:y}=c.useContext(b),v,m=!1;if(typeof f=="string"&&ot.test(f)&&(v=f,lt))try{let E=new URL(window.location.href),R=f.startsWith("//")?new URL(E.protocol+f):new URL(f),U=F(R.pathname,y);R.origin===E.origin&&U!=null?f=U+R.search+R.hash:m=!0}catch{}let h=ke(f,{relative:a}),x=st(f,{replace:l,state:s,target:o,preventScrollReset:d,relative:a,viewTransition:u});function C(E){r&&r(E),E.defaultPrevented||x(E)}return c.createElement("a",M({},p,{href:v||h,onClick:m||i?r:C,ref:n,target:o}))});var H;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(H||(H={}));var X;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(X||(X={}));function st(e,t){let{target:n,replace:r,state:a,preventScrollReset:i,relative:l,viewTransition:s}=t===void 0?{}:t,o=je(),f=k(),d=ie(e,{relative:l});return c.useCallback(u=>{if(nt(u,n)){u.preventDefault();let p=r!==void 0?r:N(f)===N(d);o(e,{replace:p,state:a,preventScrollReset:i,relative:l,viewTransition:s})}},[f,o,d,r,a,n,e,i,l,s])}export{ft as B,dt as L,ct as R,Ye as a,k as u};
//# sourceMappingURL=router-a94a9c26.js.map
