import"./vendor-b69f2a9f.js";function ai(n){var t,e,r="";if(typeof n=="string"||typeof n=="number")r+=n;else if(typeof n=="object")if(Array.isArray(n)){var i=n.length;for(t=0;t<i;t++)n[t]&&(e=ai(n[t]))&&(r&&(r+=" "),r+=e)}else for(e in n)n[e]&&(r&&(r+=" "),r+=e);return r}function Uc(){for(var n,t,e=0,r="",i=arguments.length;e<i;e++)(n=arguments[e])&&(t=ai(n))&&(r&&(r+=" "),r+=t);return r}function F(n,t){return n==null||t==null?NaN:n<t?-1:n>t?1:n>=t?0:NaN}function ko(n,t){return n==null||t==null?NaN:t<n?-1:t>n?1:t>=n?0:NaN}function De(n){let t,e,r;n.length!==2?(t=F,e=(a,c)=>F(n(a),c),r=(a,c)=>n(a)-c):(t=n===F||n===ko?n:No,e=n,r=n);function i(a,c,s=0,l=a.length){if(s<l){if(t(c,c)!==0)return l;do{const f=s+l>>>1;e(a[f],c)<0?s=f+1:l=f}while(s<l)}return s}function o(a,c,s=0,l=a.length){if(s<l){if(t(c,c)!==0)return l;do{const f=s+l>>>1;e(a[f],c)<=0?s=f+1:l=f}while(s<l)}return s}function u(a,c,s=0,l=a.length){const f=i(a,c,s,l-1);return f>s&&r(a[f-1],c)>-r(a[f],c)?f-1:f}return{left:i,center:u,right:o}}function No(){return 0}function $t(n){return n===null?NaN:+n}function*So(n,t){if(t===void 0)for(let e of n)e!=null&&(e=+e)>=e&&(yield e);else{let e=-1;for(let r of n)(r=t(r,++e,n))!=null&&(r=+r)>=r&&(yield r)}}const si=De(F),$o=si.right,Dc=si.left,Ec=De($t).center,Jn=$o;function Rc(n,t){if(!((t=+t)>=0))throw new RangeError("invalid r");let e=n.length;if(!((e=Math.floor(e))>=0))throw new RangeError("invalid length");if(!e||!t)return n;const r=Ee(t),i=n.slice();return r(n,i,0,e,1),r(i,n,0,e,1),r(n,i,0,e,1),n}const Yc=ci(Ee),Fc=ci(Co);function ci(n){return function(t,e,r=e){if(!((e=+e)>=0))throw new RangeError("invalid rx");if(!((r=+r)>=0))throw new RangeError("invalid ry");let{data:i,width:o,height:u}=t;if(!((o=Math.floor(o))>=0))throw new RangeError("invalid width");if(!((u=Math.floor(u!==void 0?u:i.length/o))>=0))throw new RangeError("invalid height");if(!o||!u||!e&&!r)return t;const a=e&&n(e),c=r&&n(r),s=i.slice();return a&&c?(In(a,s,i,o,u),In(a,i,s,o,u),In(a,s,i,o,u),Pn(c,i,s,o,u),Pn(c,s,i,o,u),Pn(c,i,s,o,u)):a?(In(a,i,s,o,u),In(a,s,i,o,u),In(a,i,s,o,u)):c&&(Pn(c,i,s,o,u),Pn(c,s,i,o,u),Pn(c,i,s,o,u)),t}}function In(n,t,e,r,i){for(let o=0,u=r*i;o<u;)n(t,e,o,o+=r,1)}function Pn(n,t,e,r,i){for(let o=0,u=r*i;o<r;++o)n(t,e,o,o+u,r)}function Co(n){const t=Ee(n);return(e,r,i,o,u)=>{i<<=2,o<<=2,u<<=2,t(e,r,i+0,o+0,u),t(e,r,i+1,o+1,u),t(e,r,i+2,o+2,u),t(e,r,i+3,o+3,u)}}function Ee(n){const t=Math.floor(n);if(t===n)return Ao(n);const e=n-t,r=2*n+1;return(i,o,u,a,c)=>{if(!((a-=c)>=u))return;let s=t*o[u];const l=c*t,f=l+c;for(let h=u,g=u+l;h<g;h+=c)s+=o[Math.min(a,h)];for(let h=u,g=a;h<=g;h+=c)s+=o[Math.min(a,h+l)],i[h]=(s+e*(o[Math.max(u,h-f)]+o[Math.min(a,h+f)]))/r,s-=o[Math.max(u,h-l)]}}function Ao(n){const t=2*n+1;return(e,r,i,o,u)=>{if(!((o-=u)>=i))return;let a=n*r[i];const c=u*n;for(let s=i,l=i+c;s<l;s+=u)a+=r[Math.min(o,s)];for(let s=i,l=o;s<=l;s+=u)a+=r[Math.min(o,s+c)],e[s]=a/t,a-=r[Math.max(i,s-c)]}}function Re(n,t){let e=0;if(t===void 0)for(let r of n)r!=null&&(r=+r)>=r&&++e;else{let r=-1;for(let i of n)(i=t(i,++r,n))!=null&&(i=+i)>=i&&++e}return e}function Uo(n){return n.length|0}function Do(n){return!(n>0)}function Eo(n){return typeof n!="object"||"length"in n?n:Array.from(n)}function Ro(n){return t=>n(...t)}function qc(...n){const t=typeof n[n.length-1]=="function"&&Ro(n.pop());n=n.map(Eo);const e=n.map(Uo),r=n.length-1,i=new Array(r+1).fill(0),o=[];if(r<0||e.some(Do))return o;for(;;){o.push(i.map((a,c)=>n[c][a]));let u=r;for(;++i[u]===e[u];){if(u===0)return t?o.map(t):o;i[u--]=0}}}function Ic(n,t){var e=0,r=0;return Float64Array.from(n,t===void 0?i=>e+=+i||0:i=>e+=+t(i,r++,n)||0)}function Yo(n,t){let e=0,r,i=0,o=0;if(t===void 0)for(let u of n)u!=null&&(u=+u)>=u&&(r=u-i,i+=r/++e,o+=r*(u-i));else{let u=-1;for(let a of n)(a=t(a,++u,n))!=null&&(a=+a)>=a&&(r=a-i,i+=r/++e,o+=r*(a-i))}if(e>1)return o/(e-1)}function Fo(n,t){const e=Yo(n,t);return e&&Math.sqrt(e)}function ie(n,t){let e,r;if(t===void 0)for(const i of n)i!=null&&(e===void 0?i>=i&&(e=r=i):(e>i&&(e=i),r<i&&(r=i)));else{let i=-1;for(let o of n)(o=t(o,++i,n))!=null&&(e===void 0?o>=o&&(e=r=o):(e>o&&(e=o),r<o&&(r=o)))}return[e,r]}class fi{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){const e=this._partials;let r=0;for(let i=0;i<this._n&&i<32;i++){const o=e[i],u=t+o,a=Math.abs(t)<Math.abs(o)?t-(u-o):o-(u-t);a&&(e[r++]=a),t=u}return e[r]=t,this._n=r+1,this}valueOf(){const t=this._partials;let e=this._n,r,i,o,u=0;if(e>0){for(u=t[--e];e>0&&(r=u,i=t[--e],u=r+i,o=i-(u-r),!o););e>0&&(o<0&&t[e-1]<0||o>0&&t[e-1]>0)&&(i=o*2,r=u+i,i==r-u&&(u=r))}return u}}function Pc(n,t){const e=new fi;if(t===void 0)for(let r of n)(r=+r)&&e.add(r);else{let r=-1;for(let i of n)(i=+t(i,++r,n))&&e.add(i)}return+e}function Hc(n,t){const e=new fi;let r=-1;return Float64Array.from(n,t===void 0?i=>e.add(+i||0):i=>e.add(+t(i,++r,n)||0))}class Ct extends Map{constructor(t,e=gi){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),t!=null)for(const[r,i]of t)this.set(r,i)}get(t){return super.get(xe(this,t))}has(t){return super.has(xe(this,t))}set(t,e){return super.set(li(this,t),e)}delete(t){return super.delete(hi(this,t))}}class zn extends Set{constructor(t,e=gi){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),t!=null)for(const r of t)this.add(r)}has(t){return super.has(xe(this,t))}add(t){return super.add(li(this,t))}delete(t){return super.delete(hi(this,t))}}function xe({_intern:n,_key:t},e){const r=t(e);return n.has(r)?n.get(r):e}function li({_intern:n,_key:t},e){const r=t(e);return n.has(r)?n.get(r):(n.set(r,e),e)}function hi({_intern:n,_key:t},e){const r=t(e);return n.has(r)&&(e=n.get(r),n.delete(r)),e}function gi(n){return n!==null&&typeof n=="object"?n.valueOf():n}function Xn(n){return n}function qo(n,...t){return Kn(n,Xn,Xn,t)}function Io(n,...t){return Kn(n,Array.from,Xn,t)}function mi(n,t){for(let e=1,r=t.length;e<r;++e)n=n.flatMap(i=>i.pop().map(([o,u])=>[...i,o,u]));return n}function Lc(n,...t){return mi(Io(n,...t),t)}function Oc(n,t,...e){return mi(Ho(n,t,...e),e)}function Po(n,t,...e){return Kn(n,Xn,t,e)}function Ho(n,t,...e){return Kn(n,Array.from,t,e)}function Wc(n,...t){return Kn(n,Xn,_i,t)}function zc(n,...t){return Kn(n,Array.from,_i,t)}function _i(n){if(n.length!==1)throw new Error("duplicate key");return n[0]}function Kn(n,t,e,r){return function i(o,u){if(u>=r.length)return e(o);const a=new Ct,c=r[u++];let s=-1;for(const l of o){const f=c(l,++s,o),h=a.get(f);h?h.push(l):a.set(f,[l])}for(const[l,f]of a)a.set(l,i(f,u));return t(a)}(n,0)}function Lo(n,t){return Array.from(t,e=>n[e])}function gr(n,...t){if(typeof n[Symbol.iterator]!="function")throw new TypeError("values is not iterable");n=Array.from(n);let[e]=t;if(e&&e.length!==2||t.length>1){const r=Uint32Array.from(n,(i,o)=>o);return t.length>1?(t=t.map(i=>n.map(i)),r.sort((i,o)=>{for(const u of t){const a=Vn(u[i],u[o]);if(a)return a}})):(e=n.map(e),r.sort((i,o)=>Vn(e[i],e[o]))),Lo(n,r)}return n.sort(Ye(e))}function Ye(n=F){if(n===F)return Vn;if(typeof n!="function")throw new TypeError("compare is not a function");return(t,e)=>{const r=n(t,e);return r||r===0?r:(n(e,e)===0)-(n(t,t)===0)}}function Vn(n,t){return(n==null||!(n>=n))-(t==null||!(t>=t))||(n<t?-1:n>t?1:0)}function Xc(n,t,e){return(t.length!==2?gr(Po(n,t,e),([r,i],[o,u])=>F(i,u)||F(r,o)):gr(qo(n,e),([r,i],[o,u])=>t(i,u)||F(r,o))).map(([r])=>r)}var Oo=Array.prototype,Wo=Oo.slice;function oe(n){return()=>n}const zo=Math.sqrt(50),Xo=Math.sqrt(10),Vo=Math.sqrt(2);function At(n,t,e){const r=(t-n)/Math.max(0,e),i=Math.floor(Math.log10(r)),o=r/Math.pow(10,i),u=o>=zo?10:o>=Xo?5:o>=Vo?2:1;let a,c,s;return i<0?(s=Math.pow(10,-i)/u,a=Math.round(n*s),c=Math.round(t*s),a/s<n&&++a,c/s>t&&--c,s=-s):(s=Math.pow(10,i)*u,a=Math.round(n/s),c=Math.round(t/s),a*s<n&&++a,c*s>t&&--c),c<a&&.5<=e&&e<2?At(n,t,e*2):[a,c,s]}function Ut(n,t,e){if(t=+t,n=+n,e=+e,!(e>0))return[];if(n===t)return[n];const r=t<n,[i,o,u]=r?At(t,n,e):At(n,t,e);if(!(o>=i))return[];const a=o-i+1,c=new Array(a);if(r)if(u<0)for(let s=0;s<a;++s)c[s]=(o-s)/-u;else for(let s=0;s<a;++s)c[s]=(o-s)*u;else if(u<0)for(let s=0;s<a;++s)c[s]=(i+s)/-u;else for(let s=0;s<a;++s)c[s]=(i+s)*u;return c}function Zn(n,t,e){return t=+t,n=+n,e=+e,At(n,t,e)[2]}function we(n,t,e){t=+t,n=+n,e=+e;const r=t<n,i=r?Zn(t,n,e):Zn(n,t,e);return(r?-1:1)*(i<0?1/-i:i)}function Zo(n,t,e){let r;for(;;){const i=Zn(n,t,e);if(i===r||i===0||!isFinite(i))return[n,t];i>0?(n=Math.floor(n/i)*i,t=Math.ceil(t/i)*i):i<0&&(n=Math.ceil(n*i)/i,t=Math.floor(t*i)/i),r=i}}function Bo(n){return Math.max(1,Math.ceil(Math.log(Re(n))/Math.LN2)+1)}function Vc(){var n=Xn,t=ie,e=Bo;function r(i){Array.isArray(i)||(i=Array.from(i));var o,u=i.length,a,c,s=new Array(u);for(o=0;o<u;++o)s[o]=n(i[o],o,i);var l=t(s),f=l[0],h=l[1],g=e(s,f,h);if(!Array.isArray(g)){const M=h,v=+g;if(t===ie&&([f,h]=Zo(f,h,v)),g=Ut(f,h,v),g[0]<=f&&(c=Zn(f,h,v)),g[g.length-1]>=h)if(M>=h&&t===ie){const T=Zn(f,h,v);isFinite(T)&&(T>0?h=(Math.floor(h/T)+1)*T:T<0&&(h=(Math.ceil(h*-T)+1)/-T))}else g.pop()}for(var m=g.length,_=0,w=m;g[_]<=f;)++_;for(;g[w-1]>h;)--w;(_||w<m)&&(g=g.slice(_,w),m=w-_);var d=new Array(m+1),S;for(o=0;o<=m;++o)S=d[o]=[],S.x0=o>0?g[o-1]:f,S.x1=o<m?g[o]:h;if(isFinite(c)){if(c>0)for(o=0;o<u;++o)(a=s[o])!=null&&f<=a&&a<=h&&d[Math.min(m,Math.floor((a-f)/c))].push(i[o]);else if(c<0){for(o=0;o<u;++o)if((a=s[o])!=null&&f<=a&&a<=h){const M=Math.floor((f-a)*c);d[Math.min(m,M+(g[M]<=a))].push(i[o])}}}else for(o=0;o<u;++o)(a=s[o])!=null&&f<=a&&a<=h&&d[Jn(g,a,0,m)].push(i[o]);return d}return r.value=function(i){return arguments.length?(n=typeof i=="function"?i:oe(i),r):n},r.domain=function(i){return arguments.length?(t=typeof i=="function"?i:oe([i[0],i[1]]),r):t},r.thresholds=function(i){return arguments.length?(e=typeof i=="function"?i:oe(Array.isArray(i)?Wo.call(i):i),r):e},r}function mr(n,t){let e;if(t===void 0)for(const r of n)r!=null&&(e<r||e===void 0&&r>=r)&&(e=r);else{let r=-1;for(let i of n)(i=t(i,++r,n))!=null&&(e<i||e===void 0&&i>=i)&&(e=i)}return e}function pi(n,t){let e,r=-1,i=-1;if(t===void 0)for(const o of n)++i,o!=null&&(e<o||e===void 0&&o>=o)&&(e=o,r=i);else for(let o of n)(o=t(o,++i,n))!=null&&(e<o||e===void 0&&o>=o)&&(e=o,r=i);return r}function be(n,t){let e;if(t===void 0)for(const r of n)r!=null&&(e>r||e===void 0&&r>=r)&&(e=r);else{let r=-1;for(let i of n)(i=t(i,++r,n))!=null&&(e>i||e===void 0&&i>=i)&&(e=i)}return e}function di(n,t){let e,r=-1,i=-1;if(t===void 0)for(const o of n)++i,o!=null&&(e>o||e===void 0&&o>=o)&&(e=o,r=i);else for(let o of n)(o=t(o,++i,n))!=null&&(e>o||e===void 0&&o>=o)&&(e=o,r=i);return r}function Fe(n,t,e=0,r=1/0,i){if(t=Math.floor(t),e=Math.floor(Math.max(0,e)),r=Math.floor(Math.min(n.length-1,r)),!(e<=t&&t<=r))return n;for(i=i===void 0?Vn:Ye(i);r>e;){if(r-e>600){const c=r-e+1,s=t-e+1,l=Math.log(c),f=.5*Math.exp(2*l/3),h=.5*Math.sqrt(l*f*(c-f)/c)*(s-c/2<0?-1:1),g=Math.max(e,Math.floor(t-s*f/c+h)),m=Math.min(r,Math.floor(t+(c-s)*f/c+h));Fe(n,t,g,m,i)}const o=n[t];let u=e,a=r;for(at(n,e,t),i(n[r],o)>0&&at(n,e,r);u<a;){for(at(n,u,a),++u,--a;i(n[u],o)<0;)++u;for(;i(n[a],o)>0;)--a}i(n[e],o)===0?at(n,e,a):(++a,at(n,a,r)),a<=t&&(e=a+1),t<=a&&(r=a-1)}return n}function at(n,t,e){const r=n[t];n[t]=n[e],n[e]=r}function jo(n,t=F){let e,r=!1;if(t.length===1){let i;for(const o of n){const u=t(o);(r?F(u,i)>0:F(u,u)===0)&&(e=o,i=u,r=!0)}}else for(const i of n)(r?t(i,e)>0:t(i,i)===0)&&(e=i,r=!0);return e}function Dt(n,t,e){if(n=Float64Array.from(So(n,e)),!(!(r=n.length)||isNaN(t=+t))){if(t<=0||r<2)return be(n);if(t>=1)return mr(n);var r,i=(r-1)*t,o=Math.floor(i),u=mr(Fe(n,o).subarray(0,o+1)),a=be(n.subarray(o+1));return u+(a-u)*(i-o)}}function Qo(n,t,e=$t){if(!(!(r=n.length)||isNaN(t=+t))){if(t<=0||r<2)return+e(n[0],0,n);if(t>=1)return+e(n[r-1],r-1,n);var r,i=(r-1)*t,o=Math.floor(i),u=+e(n[o],o,n),a=+e(n[o+1],o+1,n);return u+(a-u)*(i-o)}}function Go(n,t,e=$t){if(!isNaN(t=+t)){if(r=Float64Array.from(n,(a,c)=>$t(e(n[c],c,n))),t<=0)return di(r);if(t>=1)return pi(r);var r,i=Uint32Array.from(n,(a,c)=>c),o=r.length-1,u=Math.floor(o*t);return Fe(i,u,0,o,(a,c)=>Vn(r[a],r[c])),u=jo(i.subarray(0,u+1),a=>r[a]),u>=0?u:-1}}function Zc(n,t,e){const r=Re(n),i=Dt(n,.75)-Dt(n,.25);return r&&i?Math.ceil((e-t)/(2*i*Math.pow(r,-1/3))):1}function Bc(n,t,e){const r=Re(n),i=Fo(n);return r&&i?Math.ceil((e-t)*Math.cbrt(r)/(3.49*i)):1}function jc(n,t){let e=0,r=0;if(t===void 0)for(let i of n)i!=null&&(i=+i)>=i&&(++e,r+=i);else{let i=-1;for(let o of n)(o=t(o,++i,n))!=null&&(o=+o)>=o&&(++e,r+=o)}if(e)return r/e}function Qc(n,t){return Dt(n,.5,t)}function Gc(n,t){return Go(n,.5,t)}function*Jo(n){for(const t of n)yield*t}function Jc(n){return Array.from(Jo(n))}function Kc(n,t){const e=new Ct;if(t===void 0)for(let o of n)o!=null&&o>=o&&e.set(o,(e.get(o)||0)+1);else{let o=-1;for(let u of n)(u=t(u,++o,n))!=null&&u>=u&&e.set(u,(e.get(u)||0)+1)}let r,i=0;for(const[o,u]of e)u>i&&(i=u,r=o);return r}function nf(n,t=Ko){const e=[];let r,i=!1;for(const o of n)i&&e.push(t(r,o)),r=o,i=!0;return e}function Ko(n,t){return[n,t]}function nu(n,t,e){n=+n,t=+t,e=(i=arguments.length)<2?(t=n,n=0,1):i<3?1:+e;for(var r=-1,i=Math.max(0,Math.ceil((t-n)/e))|0,o=new Array(i);++r<i;)o[r]=n+r*e;return o}function tf(n,t=F){if(typeof n[Symbol.iterator]!="function")throw new TypeError("values is not iterable");let e=Array.from(n);const r=new Float64Array(e.length);t.length!==2&&(e=e.map(t),t=F);const i=(a,c)=>t(e[a],e[c]);let o,u;return n=Uint32Array.from(e,(a,c)=>c),n.sort(t===F?(a,c)=>Vn(e[a],e[c]):Ye(i)),n.forEach((a,c)=>{const s=i(a,o===void 0?a:o);s>=0?((o===void 0||s>0)&&(o=a,u=c),r[a]=u):r[a]=NaN}),r}function ef(n,t=F){let e,r=!1;if(t.length===1){let i;for(const o of n){const u=t(o);(r?F(u,i)<0:F(u,u)===0)&&(e=o,i=u,r=!0)}}else for(const i of n)(r?t(i,e)<0:t(i,i)===0)&&(e=i,r=!0);return e}function tu(n,t=F){if(t.length===1)return di(n,t);let e,r=-1,i=-1;for(const o of n)++i,(r<0?t(o,o)===0:t(o,e)<0)&&(e=o,r=i);return r}function rf(n,t=F){if(t.length===1)return pi(n,t);let e,r=-1,i=-1;for(const o of n)++i,(r<0?t(o,o)===0:t(o,e)>0)&&(e=o,r=i);return r}function of(n,t){const e=tu(n,t);return e<0?void 0:e}const uf=eu(Math.random);function eu(n){return function(e,r=0,i=e.length){let o=i-(r=+r);for(;o;){const u=n()*o--|0,a=e[o+r];e[o+r]=e[u+r],e[u+r]=a}return e}}function af(n,t){let e=0;if(t===void 0)for(let r of n)(r=+r)&&(e+=r);else{let r=-1;for(let i of n)(i=+t(i,++r,n))&&(e+=i)}return e}function ru(n){if(!(o=n.length))return[];for(var t=-1,e=be(n,iu),r=new Array(e);++t<e;)for(var i=-1,o,u=r[t]=new Array(o);++i<o;)u[i]=n[i][t];return r}function iu(n){return n.length}function sf(){return ru(arguments)}function cf(n,t){if(typeof t!="function")throw new TypeError("test is not a function");let e=-1;for(const r of n)if(!t(r,++e,n))return!1;return!0}function ff(n,t){if(typeof t!="function")throw new TypeError("test is not a function");let e=-1;for(const r of n)if(t(r,++e,n))return!0;return!1}function lf(n,t){if(typeof t!="function")throw new TypeError("test is not a function");const e=[];let r=-1;for(const i of n)t(i,++r,n)&&e.push(i);return e}function hf(n,t){if(typeof n[Symbol.iterator]!="function")throw new TypeError("values is not iterable");if(typeof t!="function")throw new TypeError("mapper is not a function");return Array.from(n,(e,r)=>t(e,r,n))}function gf(n,t,e){if(typeof t!="function")throw new TypeError("reducer is not a function");const r=n[Symbol.iterator]();let i,o,u=-1;if(arguments.length<3){if({done:i,value:e}=r.next(),i)return;++u}for(;{done:i,value:o}=r.next(),!i;)e=t(e,o,++u,n);return e}function mf(n){if(typeof n[Symbol.iterator]!="function")throw new TypeError("values is not iterable");return Array.from(n).reverse()}function _f(n,...t){n=new zn(n);for(const e of t)for(const r of e)n.delete(r);return n}function pf(n,t){const e=t[Symbol.iterator](),r=new zn;for(const i of n){if(r.has(i))return!1;let o,u;for(;({value:o,done:u}=e.next())&&!u;){if(Object.is(i,o))return!1;r.add(o)}}return!0}function df(n,...t){n=new zn(n),t=t.map(ou);n:for(const e of n)for(const r of t)if(!r.has(e)){n.delete(e);continue n}return n}function ou(n){return n instanceof zn?n:new zn(n)}function uu(n,t){const e=n[Symbol.iterator](),r=new Set;for(const i of t){const o=_r(i);if(r.has(o))continue;let u,a;for(;{value:u,done:a}=e.next();){if(a)return!1;const c=_r(u);if(r.add(c),Object.is(o,c))break}}return!0}function _r(n){return n!==null&&typeof n=="object"?n.valueOf():n}function yf(n,t){return uu(t,n)}function xf(...n){const t=new zn;for(const e of n)for(const r of e)t.add(r);return t}function nt(n,t,e){n.prototype=t.prototype=e,e.constructor=n}function yt(n,t){var e=Object.create(n.prototype);for(var r in t)e[r]=t[r];return e}function vn(){}var Dn=.7,Bn=1/Dn,Ln="\\s*([+-]?\\d+)\\s*",ht="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",hn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",au=/^#([0-9a-f]{3,8})$/,su=new RegExp(`^rgb\\(${Ln},${Ln},${Ln}\\)$`),cu=new RegExp(`^rgb\\(${hn},${hn},${hn}\\)$`),fu=new RegExp(`^rgba\\(${Ln},${Ln},${Ln},${ht}\\)$`),lu=new RegExp(`^rgba\\(${hn},${hn},${hn},${ht}\\)$`),hu=new RegExp(`^hsl\\(${ht},${hn},${hn}\\)$`),gu=new RegExp(`^hsla\\(${ht},${hn},${hn},${ht}\\)$`),pr={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};nt(vn,gt,{copy(n){return Object.assign(new this.constructor,this,n)},displayable(){return this.rgb().displayable()},hex:dr,formatHex:dr,formatHex8:mu,formatHsl:_u,formatRgb:yr,toString:yr});function dr(){return this.rgb().formatHex()}function mu(){return this.rgb().formatHex8()}function _u(){return yi(this).formatHsl()}function yr(){return this.rgb().formatRgb()}function gt(n){var t,e;return n=(n+"").trim().toLowerCase(),(t=au.exec(n))?(e=t[1].length,t=parseInt(t[1],16),e===6?xr(t):e===3?new H(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):e===8?Mt(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):e===4?Mt(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=su.exec(n))?new H(t[1],t[2],t[3],1):(t=cu.exec(n))?new H(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=fu.exec(n))?Mt(t[1],t[2],t[3],t[4]):(t=lu.exec(n))?Mt(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=hu.exec(n))?Mr(t[1],t[2]/100,t[3]/100,1):(t=gu.exec(n))?Mr(t[1],t[2]/100,t[3]/100,t[4]):pr.hasOwnProperty(n)?xr(pr[n]):n==="transparent"?new H(NaN,NaN,NaN,0):null}function xr(n){return new H(n>>16&255,n>>8&255,n&255,1)}function Mt(n,t,e,r){return r<=0&&(n=t=e=NaN),new H(n,t,e,r)}function qe(n){return n instanceof vn||(n=gt(n)),n?(n=n.rgb(),new H(n.r,n.g,n.b,n.opacity)):new H}function Et(n,t,e,r){return arguments.length===1?qe(n):new H(n,t,e,r??1)}function H(n,t,e,r){this.r=+n,this.g=+t,this.b=+e,this.opacity=+r}nt(H,Et,yt(vn,{brighter(n){return n=n==null?Bn:Math.pow(Bn,n),new H(this.r*n,this.g*n,this.b*n,this.opacity)},darker(n){return n=n==null?Dn:Math.pow(Dn,n),new H(this.r*n,this.g*n,this.b*n,this.opacity)},rgb(){return this},clamp(){return new H(An(this.r),An(this.g),An(this.b),Rt(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:wr,formatHex:wr,formatHex8:pu,formatRgb:br,toString:br}));function wr(){return`#${$n(this.r)}${$n(this.g)}${$n(this.b)}`}function pu(){return`#${$n(this.r)}${$n(this.g)}${$n(this.b)}${$n((isNaN(this.opacity)?1:this.opacity)*255)}`}function br(){const n=Rt(this.opacity);return`${n===1?"rgb(":"rgba("}${An(this.r)}, ${An(this.g)}, ${An(this.b)}${n===1?")":`, ${n})`}`}function Rt(n){return isNaN(n)?1:Math.max(0,Math.min(1,n))}function An(n){return Math.max(0,Math.min(255,Math.round(n)||0))}function $n(n){return n=An(n),(n<16?"0":"")+n.toString(16)}function Mr(n,t,e,r){return r<=0?n=t=e=NaN:e<=0||e>=1?n=t=NaN:t<=0&&(n=NaN),new sn(n,t,e,r)}function yi(n){if(n instanceof sn)return new sn(n.h,n.s,n.l,n.opacity);if(n instanceof vn||(n=gt(n)),!n)return new sn;if(n instanceof sn)return n;n=n.rgb();var t=n.r/255,e=n.g/255,r=n.b/255,i=Math.min(t,e,r),o=Math.max(t,e,r),u=NaN,a=o-i,c=(o+i)/2;return a?(t===o?u=(e-r)/a+(e<r)*6:e===o?u=(r-t)/a+2:u=(t-e)/a+4,a/=c<.5?o+i:2-o-i,u*=60):a=c>0&&c<1?0:u,new sn(u,a,c,n.opacity)}function Me(n,t,e,r){return arguments.length===1?yi(n):new sn(n,t,e,r??1)}function sn(n,t,e,r){this.h=+n,this.s=+t,this.l=+e,this.opacity=+r}nt(sn,Me,yt(vn,{brighter(n){return n=n==null?Bn:Math.pow(Bn,n),new sn(this.h,this.s,this.l*n,this.opacity)},darker(n){return n=n==null?Dn:Math.pow(Dn,n),new sn(this.h,this.s,this.l*n,this.opacity)},rgb(){var n=this.h%360+(this.h<0)*360,t=isNaN(n)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*t,i=2*e-r;return new H(ue(n>=240?n-240:n+120,i,r),ue(n,i,r),ue(n<120?n+240:n-120,i,r),this.opacity)},clamp(){return new sn(vr(this.h),vt(this.s),vt(this.l),Rt(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const n=Rt(this.opacity);return`${n===1?"hsl(":"hsla("}${vr(this.h)}, ${vt(this.s)*100}%, ${vt(this.l)*100}%${n===1?")":`, ${n})`}`}}));function vr(n){return n=(n||0)%360,n<0?n+360:n}function vt(n){return Math.max(0,Math.min(1,n||0))}function ue(n,t,e){return(n<60?t+(e-t)*n/60:n<180?e:n<240?t+(e-t)*(240-n)/60:t)*255}const xi=Math.PI/180,wi=180/Math.PI,Yt=18,bi=.96422,Mi=1,vi=.82521,Ti=4/29,On=6/29,ki=3*On*On,du=On*On*On;function Ni(n){if(n instanceof cn)return new cn(n.l,n.a,n.b,n.opacity);if(n instanceof ln)return $i(n);n instanceof H||(n=qe(n));var t=fe(n.r),e=fe(n.g),r=fe(n.b),i=ae((.2225045*t+.7168786*e+.0606169*r)/Mi),o,u;return t===e&&e===r?o=u=i:(o=ae((.4360747*t+.3850649*e+.1430804*r)/bi),u=ae((.0139322*t+.0971045*e+.7141733*r)/vi)),new cn(116*i-16,500*(o-i),200*(i-u),n.opacity)}function wf(n,t){return new cn(n,0,0,t??1)}function ve(n,t,e,r){return arguments.length===1?Ni(n):new cn(n,t,e,r??1)}function cn(n,t,e,r){this.l=+n,this.a=+t,this.b=+e,this.opacity=+r}nt(cn,ve,yt(vn,{brighter(n){return new cn(this.l+Yt*(n??1),this.a,this.b,this.opacity)},darker(n){return new cn(this.l-Yt*(n??1),this.a,this.b,this.opacity)},rgb(){var n=(this.l+16)/116,t=isNaN(this.a)?n:n+this.a/500,e=isNaN(this.b)?n:n-this.b/200;return t=bi*se(t),n=Mi*se(n),e=vi*se(e),new H(ce(3.1338561*t-1.6168667*n-.4906146*e),ce(-.9787684*t+1.9161415*n+.033454*e),ce(.0719453*t-.2289914*n+1.4052427*e),this.opacity)}}));function ae(n){return n>du?Math.pow(n,1/3):n/ki+Ti}function se(n){return n>On?n*n*n:ki*(n-Ti)}function ce(n){return 255*(n<=.0031308?12.92*n:1.055*Math.pow(n,1/2.4)-.055)}function fe(n){return(n/=255)<=.04045?n/12.92:Math.pow((n+.055)/1.055,2.4)}function Si(n){if(n instanceof ln)return new ln(n.h,n.c,n.l,n.opacity);if(n instanceof cn||(n=Ni(n)),n.a===0&&n.b===0)return new ln(NaN,0<n.l&&n.l<100?0:NaN,n.l,n.opacity);var t=Math.atan2(n.b,n.a)*wi;return new ln(t<0?t+360:t,Math.sqrt(n.a*n.a+n.b*n.b),n.l,n.opacity)}function bf(n,t,e,r){return arguments.length===1?Si(n):new ln(e,t,n,r??1)}function Te(n,t,e,r){return arguments.length===1?Si(n):new ln(n,t,e,r??1)}function ln(n,t,e,r){this.h=+n,this.c=+t,this.l=+e,this.opacity=+r}function $i(n){if(isNaN(n.h))return new cn(n.l,0,0,n.opacity);var t=n.h*xi;return new cn(n.l,Math.cos(t)*n.c,Math.sin(t)*n.c,n.opacity)}nt(ln,Te,yt(vn,{brighter(n){return new ln(this.h,this.c,this.l+Yt*(n??1),this.opacity)},darker(n){return new ln(this.h,this.c,this.l-Yt*(n??1),this.opacity)},rgb(){return $i(this).rgb()}}));var Ci=-.14861,Ie=1.78277,Pe=-.29227,Xt=-.90649,mt=1.97294,Tr=mt*Xt,kr=mt*Ie,Nr=Ie*Pe-Xt*Ci;function yu(n){if(n instanceof Un)return new Un(n.h,n.s,n.l,n.opacity);n instanceof H||(n=qe(n));var t=n.r/255,e=n.g/255,r=n.b/255,i=(Nr*r+Tr*t-kr*e)/(Nr+Tr-kr),o=r-i,u=(mt*(e-i)-Pe*o)/Xt,a=Math.sqrt(u*u+o*o)/(mt*i*(1-i)),c=a?Math.atan2(u,o)*wi-120:NaN;return new Un(c<0?c+360:c,a,i,n.opacity)}function ke(n,t,e,r){return arguments.length===1?yu(n):new Un(n,t,e,r??1)}function Un(n,t,e,r){this.h=+n,this.s=+t,this.l=+e,this.opacity=+r}nt(Un,ke,yt(vn,{brighter(n){return n=n==null?Bn:Math.pow(Bn,n),new Un(this.h,this.s,this.l*n,this.opacity)},darker(n){return n=n==null?Dn:Math.pow(Dn,n),new Un(this.h,this.s,this.l*n,this.opacity)},rgb(){var n=isNaN(this.h)?0:(this.h+120)*xi,t=+this.l,e=isNaN(this.s)?0:this.s*t*(1-t),r=Math.cos(n),i=Math.sin(n);return new H(255*(t+e*(Ci*r+Ie*i)),255*(t+e*(Pe*r+Xt*i)),255*(t+e*(mt*r)),this.opacity)}}));function Ai(n,t,e,r,i){var o=n*n,u=o*n;return((1-3*n+3*o-u)*t+(4-6*o+3*u)*e+(1+3*n+3*o-3*u)*r+u*i)/6}function xu(n){var t=n.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,t-1):Math.floor(e*t),i=n[r],o=n[r+1],u=r>0?n[r-1]:2*i-o,a=r<t-1?n[r+2]:2*o-i;return Ai((e-r/t)*t,u,i,o,a)}}function wu(n){var t=n.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*t),i=n[(r+t-1)%t],o=n[r%t],u=n[(r+1)%t],a=n[(r+2)%t];return Ai((e-r/t)*t,i,o,u,a)}}const Vt=n=>()=>n;function Ui(n,t){return function(e){return n+e*t}}function bu(n,t,e){return n=Math.pow(n,e),t=Math.pow(t,e)-n,e=1/e,function(r){return Math.pow(n+r*t,e)}}function Zt(n,t){var e=t-n;return e?Ui(n,e>180||e<-180?e-360*Math.round(e/360):e):Vt(isNaN(n)?t:n)}function Mu(n){return(n=+n)==1?L:function(t,e){return e-t?bu(t,e,n):Vt(isNaN(t)?e:t)}}function L(n,t){var e=t-n;return e?Ui(n,e):Vt(isNaN(n)?t:n)}const Sr=function n(t){var e=Mu(t);function r(i,o){var u=e((i=Et(i)).r,(o=Et(o)).r),a=e(i.g,o.g),c=e(i.b,o.b),s=L(i.opacity,o.opacity);return function(l){return i.r=u(l),i.g=a(l),i.b=c(l),i.opacity=s(l),i+""}}return r.gamma=n,r}(1);function Di(n){return function(t){var e=t.length,r=new Array(e),i=new Array(e),o=new Array(e),u,a;for(u=0;u<e;++u)a=Et(t[u]),r[u]=a.r||0,i[u]=a.g||0,o[u]=a.b||0;return r=n(r),i=n(i),o=n(o),a.opacity=1,function(c){return a.r=r(c),a.g=i(c),a.b=o(c),a+""}}}var Mf=Di(xu),vf=Di(wu);function Ei(n,t){t||(t=[]);var e=n?Math.min(t.length,n.length):0,r=t.slice(),i;return function(o){for(i=0;i<e;++i)r[i]=n[i]*(1-o)+t[i]*o;return r}}function Ri(n){return ArrayBuffer.isView(n)&&!(n instanceof DataView)}function Tf(n,t){return(Ri(t)?Ei:Yi)(n,t)}function Yi(n,t){var e=t?t.length:0,r=n?Math.min(e,n.length):0,i=new Array(r),o=new Array(e),u;for(u=0;u<r;++u)i[u]=tt(n[u],t[u]);for(;u<e;++u)o[u]=t[u];return function(a){for(u=0;u<r;++u)o[u]=i[u](a);return o}}function vu(n,t){var e=new Date;return n=+n,t=+t,function(r){return e.setTime(n*(1-r)+t*r),e}}function fn(n,t){return n=+n,t=+t,function(e){return n*(1-e)+t*e}}function Tu(n,t){var e={},r={},i;(n===null||typeof n!="object")&&(n={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in n?e[i]=tt(n[i],t[i]):r[i]=t[i];return function(o){for(i in e)r[i]=e[i](o);return r}}var Ne=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,le=new RegExp(Ne.source,"g");function ku(n){return function(){return n}}function Nu(n){return function(t){return n(t)+""}}function Su(n,t){var e=Ne.lastIndex=le.lastIndex=0,r,i,o,u=-1,a=[],c=[];for(n=n+"",t=t+"";(r=Ne.exec(n))&&(i=le.exec(t));)(o=i.index)>e&&(o=t.slice(e,o),a[u]?a[u]+=o:a[++u]=o),(r=r[0])===(i=i[0])?a[u]?a[u]+=i:a[++u]=i:(a[++u]=null,c.push({i:u,x:fn(r,i)})),e=le.lastIndex;return e<t.length&&(o=t.slice(e),a[u]?a[u]+=o:a[++u]=o),a.length<2?c[0]?Nu(c[0].x):ku(t):(t=c.length,function(s){for(var l=0,f;l<t;++l)a[(f=c[l]).i]=f.x(s);return a.join("")})}function tt(n,t){var e=typeof t,r;return t==null||e==="boolean"?Vt(t):(e==="number"?fn:e==="string"?(r=gt(t))?(t=r,Sr):Su:t instanceof gt?Sr:t instanceof Date?vu:Ri(t)?Ei:Array.isArray(t)?Yi:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?Tu:fn)(n,t)}function kf(n){var t=n.length;return function(e){return n[Math.max(0,Math.min(t-1,Math.floor(e*t)))]}}function Nf(n,t){var e=Zt(+n,+t);return function(r){var i=e(r);return i-360*Math.floor(i/360)}}function He(n,t){return n=+n,t=+t,function(e){return Math.round(n*(1-e)+t*e)}}var $r=180/Math.PI,Se={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Fi(n,t,e,r,i,o){var u,a,c;return(u=Math.sqrt(n*n+t*t))&&(n/=u,t/=u),(c=n*e+t*r)&&(e-=n*c,r-=t*c),(a=Math.sqrt(e*e+r*r))&&(e/=a,r/=a,c/=a),n*r<t*e&&(n=-n,t=-t,c=-c,u=-u),{translateX:i,translateY:o,rotate:Math.atan2(t,n)*$r,skewX:Math.atan(c)*$r,scaleX:u,scaleY:a}}var Tt;function $u(n){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(n+"");return t.isIdentity?Se:Fi(t.a,t.b,t.c,t.d,t.e,t.f)}function Cu(n){return n==null||(Tt||(Tt=document.createElementNS("http://www.w3.org/2000/svg","g")),Tt.setAttribute("transform",n),!(n=Tt.transform.baseVal.consolidate()))?Se:(n=n.matrix,Fi(n.a,n.b,n.c,n.d,n.e,n.f))}function qi(n,t,e,r){function i(s){return s.length?s.pop()+" ":""}function o(s,l,f,h,g,m){if(s!==f||l!==h){var _=g.push("translate(",null,t,null,e);m.push({i:_-4,x:fn(s,f)},{i:_-2,x:fn(l,h)})}else(f||h)&&g.push("translate("+f+t+h+e)}function u(s,l,f,h){s!==l?(s-l>180?l+=360:l-s>180&&(s+=360),h.push({i:f.push(i(f)+"rotate(",null,r)-2,x:fn(s,l)})):l&&f.push(i(f)+"rotate("+l+r)}function a(s,l,f,h){s!==l?h.push({i:f.push(i(f)+"skewX(",null,r)-2,x:fn(s,l)}):l&&f.push(i(f)+"skewX("+l+r)}function c(s,l,f,h,g,m){if(s!==f||l!==h){var _=g.push(i(g)+"scale(",null,",",null,")");m.push({i:_-4,x:fn(s,f)},{i:_-2,x:fn(l,h)})}else(f!==1||h!==1)&&g.push(i(g)+"scale("+f+","+h+")")}return function(s,l){var f=[],h=[];return s=n(s),l=n(l),o(s.translateX,s.translateY,l.translateX,l.translateY,f,h),u(s.rotate,l.rotate,f,h),a(s.skewX,l.skewX,f,h),c(s.scaleX,s.scaleY,l.scaleX,l.scaleY,f,h),s=l=null,function(g){for(var m=-1,_=h.length,w;++m<_;)f[(w=h[m]).i]=w.x(g);return f.join("")}}}var Sf=qi($u,"px, ","px)","deg)"),$f=qi(Cu,", ",")",")"),Au=1e-12;function Cr(n){return((n=Math.exp(n))+1/n)/2}function Uu(n){return((n=Math.exp(n))-1/n)/2}function Du(n){return((n=Math.exp(2*n))-1)/(n+1)}const Cf=function n(t,e,r){function i(o,u){var a=o[0],c=o[1],s=o[2],l=u[0],f=u[1],h=u[2],g=l-a,m=f-c,_=g*g+m*m,w,d;if(_<Au)d=Math.log(h/s)/t,w=function(D){return[a+D*g,c+D*m,s*Math.exp(t*D*d)]};else{var S=Math.sqrt(_),M=(h*h-s*s+r*_)/(2*s*e*S),v=(h*h-s*s-r*_)/(2*h*e*S),T=Math.log(Math.sqrt(M*M+1)-M),x=Math.log(Math.sqrt(v*v+1)-v);d=(x-T)/t,w=function(D){var q=D*d,V=Cr(T),P=s/(e*S)*(V*Du(t*q+T)-Uu(T));return[a+P*g,c+P*m,s*V/Cr(t*q+T)]}}return w.duration=d*1e3*t/Math.SQRT2,w}return i.rho=function(o){var u=Math.max(.001,+o),a=u*u,c=a*a;return n(u,a,c)},i}(Math.SQRT2,2,4);function Ii(n){return function(t,e){var r=n((t=Me(t)).h,(e=Me(e)).h),i=L(t.s,e.s),o=L(t.l,e.l),u=L(t.opacity,e.opacity);return function(a){return t.h=r(a),t.s=i(a),t.l=o(a),t.opacity=u(a),t+""}}}const Af=Ii(Zt);var Uf=Ii(L);function Df(n,t){var e=L((n=ve(n)).l,(t=ve(t)).l),r=L(n.a,t.a),i=L(n.b,t.b),o=L(n.opacity,t.opacity);return function(u){return n.l=e(u),n.a=r(u),n.b=i(u),n.opacity=o(u),n+""}}function Pi(n){return function(t,e){var r=n((t=Te(t)).h,(e=Te(e)).h),i=L(t.c,e.c),o=L(t.l,e.l),u=L(t.opacity,e.opacity);return function(a){return t.h=r(a),t.c=i(a),t.l=o(a),t.opacity=u(a),t+""}}}const Ef=Pi(Zt);var Rf=Pi(L);function Hi(n){return function t(e){e=+e;function r(i,o){var u=n((i=ke(i)).h,(o=ke(o)).h),a=L(i.s,o.s),c=L(i.l,o.l),s=L(i.opacity,o.opacity);return function(l){return i.h=u(l),i.s=a(l),i.l=c(Math.pow(l,e)),i.opacity=s(l),i+""}}return r.gamma=t,r}(1)}const Yf=Hi(Zt);var Ff=Hi(L);function Eu(n,t){t===void 0&&(t=n,n=tt);for(var e=0,r=t.length-1,i=t[0],o=new Array(r<0?0:r);e<r;)o[e]=n(i,i=t[++e]);return function(u){var a=Math.max(0,Math.min(r-1,Math.floor(u*=r)));return o[a](u-a)}}function qf(n,t){for(var e=new Array(t),r=0;r<t;++r)e[r]=n(r/(t-1));return e}const $e=Math.PI,Ce=2*$e,Sn=1e-6,Ru=Ce-Sn;function Li(n){this._+=n[0];for(let t=1,e=n.length;t<e;++t)this._+=arguments[t]+n[t]}function Yu(n){let t=Math.floor(n);if(!(t>=0))throw new Error(`invalid digits: ${n}`);if(t>15)return Li;const e=10**t;return function(r){this._+=r[0];for(let i=1,o=r.length;i<o;++i)this._+=Math.round(arguments[i]*e)/e+r[i]}}class Bt{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?Li:Yu(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,i){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+i}`}bezierCurveTo(t,e,r,i,o,u){this._append`C${+t},${+e},${+r},${+i},${this._x1=+o},${this._y1=+u}`}arcTo(t,e,r,i,o){if(t=+t,e=+e,r=+r,i=+i,o=+o,o<0)throw new Error(`negative radius: ${o}`);let u=this._x1,a=this._y1,c=r-t,s=i-e,l=u-t,f=a-e,h=l*l+f*f;if(this._x1===null)this._append`M${this._x1=t},${this._y1=e}`;else if(h>Sn)if(!(Math.abs(f*c-s*l)>Sn)||!o)this._append`L${this._x1=t},${this._y1=e}`;else{let g=r-u,m=i-a,_=c*c+s*s,w=g*g+m*m,d=Math.sqrt(_),S=Math.sqrt(h),M=o*Math.tan(($e-Math.acos((_+h-w)/(2*d*S)))/2),v=M/S,T=M/d;Math.abs(v-1)>Sn&&this._append`L${t+v*l},${e+v*f}`,this._append`A${o},${o},0,0,${+(f*g>l*m)},${this._x1=t+T*c},${this._y1=e+T*s}`}}arc(t,e,r,i,o,u){if(t=+t,e=+e,r=+r,u=!!u,r<0)throw new Error(`negative radius: ${r}`);let a=r*Math.cos(i),c=r*Math.sin(i),s=t+a,l=e+c,f=1^u,h=u?i-o:o-i;this._x1===null?this._append`M${s},${l}`:(Math.abs(this._x1-s)>Sn||Math.abs(this._y1-l)>Sn)&&this._append`L${s},${l}`,r&&(h<0&&(h=h%Ce+Ce),h>Ru?this._append`A${r},${r},0,1,${f},${t-a},${e-c}A${r},${r},0,1,${f},${this._x1=s},${this._y1=l}`:h>Sn&&this._append`A${r},${r},0,${+(h>=$e)},${f},${this._x1=t+r*Math.cos(o)},${this._y1=e+r*Math.sin(o)}`)}rect(t,e,r,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r=+r}v${+i}h${-r}Z`}toString(){return this._}}function Fu(){return new Bt}Fu.prototype=Bt.prototype;function If(n=3){return new Bt(+n)}function qu(n){return Math.abs(n=Math.round(n))>=1e21?n.toLocaleString("en").replace(/,/g,""):n.toString(10)}function Ft(n,t){if((e=(n=t?n.toExponential(t-1):n.toExponential()).indexOf("e"))<0)return null;var e,r=n.slice(0,e);return[r.length>1?r[0]+r.slice(2):r,+n.slice(e+1)]}function jn(n){return n=Ft(Math.abs(n)),n?n[1]:NaN}function Iu(n,t){return function(e,r){for(var i=e.length,o=[],u=0,a=n[0],c=0;i>0&&a>0&&(c+a+1>r&&(a=Math.max(1,r-c)),o.push(e.substring(i-=a,i+a)),!((c+=a+1)>r));)a=n[u=(u+1)%n.length];return o.reverse().join(t)}}function Pu(n){return function(t){return t.replace(/[0-9]/g,function(e){return n[+e]})}}var Hu=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function _t(n){if(!(t=Hu.exec(n)))throw new Error("invalid format: "+n);var t;return new Le({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}_t.prototype=Le.prototype;function Le(n){this.fill=n.fill===void 0?" ":n.fill+"",this.align=n.align===void 0?">":n.align+"",this.sign=n.sign===void 0?"-":n.sign+"",this.symbol=n.symbol===void 0?"":n.symbol+"",this.zero=!!n.zero,this.width=n.width===void 0?void 0:+n.width,this.comma=!!n.comma,this.precision=n.precision===void 0?void 0:+n.precision,this.trim=!!n.trim,this.type=n.type===void 0?"":n.type+""}Le.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function Lu(n){n:for(var t=n.length,e=1,r=-1,i;e<t;++e)switch(n[e]){case".":r=i=e;break;case"0":r===0&&(r=e),i=e;break;default:if(!+n[e])break n;r>0&&(r=0);break}return r>0?n.slice(0,r)+n.slice(i+1):n}var Oi;function Ou(n,t){var e=Ft(n,t);if(!e)return n+"";var r=e[0],i=e[1],o=i-(Oi=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,u=r.length;return o===u?r:o>u?r+new Array(o-u+1).join("0"):o>0?r.slice(0,o)+"."+r.slice(o):"0."+new Array(1-o).join("0")+Ft(n,Math.max(0,t+o-1))[0]}function Ar(n,t){var e=Ft(n,t);if(!e)return n+"";var r=e[0],i=e[1];return i<0?"0."+new Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+new Array(i-r.length+2).join("0")}const Ur={"%":(n,t)=>(n*100).toFixed(t),b:n=>Math.round(n).toString(2),c:n=>n+"",d:qu,e:(n,t)=>n.toExponential(t),f:(n,t)=>n.toFixed(t),g:(n,t)=>n.toPrecision(t),o:n=>Math.round(n).toString(8),p:(n,t)=>Ar(n*100,t),r:Ar,s:Ou,X:n=>Math.round(n).toString(16).toUpperCase(),x:n=>Math.round(n).toString(16)};function Dr(n){return n}var Er=Array.prototype.map,Rr=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Wu(n){var t=n.grouping===void 0||n.thousands===void 0?Dr:Iu(Er.call(n.grouping,Number),n.thousands+""),e=n.currency===void 0?"":n.currency[0]+"",r=n.currency===void 0?"":n.currency[1]+"",i=n.decimal===void 0?".":n.decimal+"",o=n.numerals===void 0?Dr:Pu(Er.call(n.numerals,String)),u=n.percent===void 0?"%":n.percent+"",a=n.minus===void 0?"−":n.minus+"",c=n.nan===void 0?"NaN":n.nan+"";function s(f){f=_t(f);var h=f.fill,g=f.align,m=f.sign,_=f.symbol,w=f.zero,d=f.width,S=f.comma,M=f.precision,v=f.trim,T=f.type;T==="n"?(S=!0,T="g"):Ur[T]||(M===void 0&&(M=12),v=!0,T="g"),(w||h==="0"&&g==="=")&&(w=!0,h="0",g="=");var x=_==="$"?e:_==="#"&&/[boxX]/.test(T)?"0"+T.toLowerCase():"",D=_==="$"?r:/[%p]/.test(T)?u:"",q=Ur[T],V=/[defgprs%]/.test(T);M=M===void 0?6:/[gprs]/.test(T)?Math.max(1,Math.min(21,M)):Math.max(0,Math.min(20,M));function P(k){var Y=x,b=D,C,nn,un;if(T==="c")b=q(k)+b,k="";else{k=+k;var Q=k<0||1/k<0;if(k=isNaN(k)?c:q(Math.abs(k),M),v&&(k=Lu(k)),Q&&+k==0&&m!=="+"&&(Q=!1),Y=(Q?m==="("?m:a:m==="-"||m==="("?"":m)+Y,b=(T==="s"?Rr[8+Oi/3]:"")+b+(Q&&m==="("?")":""),V){for(C=-1,nn=k.length;++C<nn;)if(un=k.charCodeAt(C),48>un||un>57){b=(un===46?i+k.slice(C+1):k.slice(C))+b,k=k.slice(0,C);break}}}S&&!w&&(k=t(k,1/0));var G=Y.length+k.length+b.length,W=G<d?new Array(d-G+1).join(h):"";switch(S&&w&&(k=t(W+k,W.length?d-b.length:1/0),W=""),g){case"<":k=Y+k+b+W;break;case"=":k=Y+W+k+b;break;case"^":k=W.slice(0,G=W.length>>1)+Y+k+b+W.slice(G);break;default:k=W+Y+k+b;break}return o(k)}return P.toString=function(){return f+""},P}function l(f,h){var g=s((f=_t(f),f.type="f",f)),m=Math.max(-8,Math.min(8,Math.floor(jn(h)/3)))*3,_=Math.pow(10,-m),w=Rr[8+m/3];return function(d){return g(_*d)+w}}return{format:s,formatPrefix:l}}var kt,Oe,Wi;zu({thousands:",",grouping:[3],currency:["$",""]});function zu(n){return kt=Wu(n),Oe=kt.format,Wi=kt.formatPrefix,kt}function Xu(n){return Math.max(0,-jn(Math.abs(n)))}function Vu(n,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(jn(t)/3)))*3-jn(Math.abs(n)))}function Zu(n,t){return n=Math.abs(n),t=Math.abs(t)-n,Math.max(0,jn(t)-jn(n))+1}function on(n,t){switch(arguments.length){case 0:break;case 1:this.range(n);break;default:this.range(t).domain(n);break}return this}function xn(n,t){switch(arguments.length){case 0:break;case 1:{typeof n=="function"?this.interpolator(n):this.range(n);break}default:{this.domain(n),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const Yr=Symbol("implicit");function zi(){var n=new Ct,t=[],e=[],r=Yr;function i(o){let u=n.get(o);if(u===void 0){if(r!==Yr)return r;n.set(o,u=t.push(o)-1)}return e[u%e.length]}return i.domain=function(o){if(!arguments.length)return t.slice();t=[],n=new Ct;for(const u of o)n.has(u)||n.set(u,t.push(u)-1);return i},i.range=function(o){return arguments.length?(e=Array.from(o),i):e.slice()},i.unknown=function(o){return arguments.length?(r=o,i):r},i.copy=function(){return zi(t,e).unknown(r)},on.apply(i,arguments),i}function Xi(){var n=zi().unknown(void 0),t=n.domain,e=n.range,r=0,i=1,o,u,a=!1,c=0,s=0,l=.5;delete n.unknown;function f(){var h=t().length,g=i<r,m=g?i:r,_=g?r:i;o=(_-m)/Math.max(1,h-c+s*2),a&&(o=Math.floor(o)),m+=(_-m-o*(h-c))*l,u=o*(1-c),a&&(m=Math.round(m),u=Math.round(u));var w=nu(h).map(function(d){return m+o*d});return e(g?w.reverse():w)}return n.domain=function(h){return arguments.length?(t(h),f()):t()},n.range=function(h){return arguments.length?([r,i]=h,r=+r,i=+i,f()):[r,i]},n.rangeRound=function(h){return[r,i]=h,r=+r,i=+i,a=!0,f()},n.bandwidth=function(){return u},n.step=function(){return o},n.round=function(h){return arguments.length?(a=!!h,f()):a},n.padding=function(h){return arguments.length?(c=Math.min(1,s=+h),f()):c},n.paddingInner=function(h){return arguments.length?(c=Math.min(1,h),f()):c},n.paddingOuter=function(h){return arguments.length?(s=+h,f()):s},n.align=function(h){return arguments.length?(l=Math.max(0,Math.min(1,h)),f()):l},n.copy=function(){return Xi(t(),[r,i]).round(a).paddingInner(c).paddingOuter(s).align(l)},on.apply(f(),arguments)}function Vi(n){var t=n.copy;return n.padding=n.paddingOuter,delete n.paddingInner,delete n.paddingOuter,n.copy=function(){return Vi(t())},n}function Pf(){return Vi(Xi.apply(null,arguments).paddingInner(1))}function Bu(n){return function(){return n}}function qt(n){return+n}var Fr=[0,1];function Z(n){return n}function Ae(n,t){return(t-=n=+n)?function(e){return(e-n)/t}:Bu(isNaN(t)?NaN:.5)}function ju(n,t){var e;return n>t&&(e=n,n=t,t=e),function(r){return Math.max(n,Math.min(t,r))}}function Qu(n,t,e){var r=n[0],i=n[1],o=t[0],u=t[1];return i<r?(r=Ae(i,r),o=e(u,o)):(r=Ae(r,i),o=e(o,u)),function(a){return o(r(a))}}function Gu(n,t,e){var r=Math.min(n.length,t.length)-1,i=new Array(r),o=new Array(r),u=-1;for(n[r]<n[0]&&(n=n.slice().reverse(),t=t.slice().reverse());++u<r;)i[u]=Ae(n[u],n[u+1]),o[u]=e(t[u],t[u+1]);return function(a){var c=Jn(n,a,1,r)-1;return o[c](i[c](a))}}function xt(n,t){return t.domain(n.domain()).range(n.range()).interpolate(n.interpolate()).clamp(n.clamp()).unknown(n.unknown())}function jt(){var n=Fr,t=Fr,e=tt,r,i,o,u=Z,a,c,s;function l(){var h=Math.min(n.length,t.length);return u!==Z&&(u=ju(n[0],n[h-1])),a=h>2?Gu:Qu,c=s=null,f}function f(h){return h==null||isNaN(h=+h)?o:(c||(c=a(n.map(r),t,e)))(r(u(h)))}return f.invert=function(h){return u(i((s||(s=a(t,n.map(r),fn)))(h)))},f.domain=function(h){return arguments.length?(n=Array.from(h,qt),l()):n.slice()},f.range=function(h){return arguments.length?(t=Array.from(h),l()):t.slice()},f.rangeRound=function(h){return t=Array.from(h),e=He,l()},f.clamp=function(h){return arguments.length?(u=h?!0:Z,l()):u!==Z},f.interpolate=function(h){return arguments.length?(e=h,l()):e},f.unknown=function(h){return arguments.length?(o=h,f):o},function(h,g){return r=h,i=g,l()}}function We(){return jt()(Z,Z)}function Ju(n,t,e,r){var i=we(n,t,e),o;switch(r=_t(r??",f"),r.type){case"s":{var u=Math.max(Math.abs(n),Math.abs(t));return r.precision==null&&!isNaN(o=Vu(i,u))&&(r.precision=o),Wi(r,u)}case"":case"e":case"g":case"p":case"r":{r.precision==null&&!isNaN(o=Zu(i,Math.max(Math.abs(n),Math.abs(t))))&&(r.precision=o-(r.type==="e"));break}case"f":case"%":{r.precision==null&&!isNaN(o=Xu(i))&&(r.precision=o-(r.type==="%")*2);break}}return Oe(r)}function Tn(n){var t=n.domain;return n.ticks=function(e){var r=t();return Ut(r[0],r[r.length-1],e??10)},n.tickFormat=function(e,r){var i=t();return Ju(i[0],i[i.length-1],e??10,r)},n.nice=function(e){e==null&&(e=10);var r=t(),i=0,o=r.length-1,u=r[i],a=r[o],c,s,l=10;for(a<u&&(s=u,u=a,a=s,s=i,i=o,o=s);l-- >0;){if(s=Zn(u,a,e),s===c)return r[i]=u,r[o]=a,t(r);if(s>0)u=Math.floor(u/s)*s,a=Math.ceil(a/s)*s;else if(s<0)u=Math.ceil(u*s)/s,a=Math.floor(a*s)/s;else break;c=s}return n},n}function Ku(){var n=We();return n.copy=function(){return xt(n,Ku())},on.apply(n,arguments),Tn(n)}function na(n){var t;function e(r){return r==null||isNaN(r=+r)?t:r}return e.invert=e,e.domain=e.range=function(r){return arguments.length?(n=Array.from(r,qt),e):n.slice()},e.unknown=function(r){return arguments.length?(t=r,e):t},e.copy=function(){return na(n).unknown(t)},n=arguments.length?Array.from(n,qt):[0,1],Tn(e)}function Zi(n,t){n=n.slice();var e=0,r=n.length-1,i=n[e],o=n[r],u;return o<i&&(u=e,e=r,r=u,u=i,i=o,o=u),n[e]=t.floor(i),n[r]=t.ceil(o),n}function qr(n){return Math.log(n)}function Ir(n){return Math.exp(n)}function ta(n){return-Math.log(-n)}function ea(n){return-Math.exp(-n)}function ra(n){return isFinite(n)?+("1e"+n):n<0?0:n}function ia(n){return n===10?ra:n===Math.E?Math.exp:t=>Math.pow(n,t)}function oa(n){return n===Math.E?Math.log:n===10&&Math.log10||n===2&&Math.log2||(n=Math.log(n),t=>Math.log(t)/n)}function Pr(n){return(t,e)=>-n(-t,e)}function ze(n){const t=n(qr,Ir),e=t.domain;let r=10,i,o;function u(){return i=oa(r),o=ia(r),e()[0]<0?(i=Pr(i),o=Pr(o),n(ta,ea)):n(qr,Ir),t}return t.base=function(a){return arguments.length?(r=+a,u()):r},t.domain=function(a){return arguments.length?(e(a),u()):e()},t.ticks=a=>{const c=e();let s=c[0],l=c[c.length-1];const f=l<s;f&&([s,l]=[l,s]);let h=i(s),g=i(l),m,_;const w=a==null?10:+a;let d=[];if(!(r%1)&&g-h<w){if(h=Math.floor(h),g=Math.ceil(g),s>0){for(;h<=g;++h)for(m=1;m<r;++m)if(_=h<0?m/o(-h):m*o(h),!(_<s)){if(_>l)break;d.push(_)}}else for(;h<=g;++h)for(m=r-1;m>=1;--m)if(_=h>0?m/o(-h):m*o(h),!(_<s)){if(_>l)break;d.push(_)}d.length*2<w&&(d=Ut(s,l,w))}else d=Ut(h,g,Math.min(g-h,w)).map(o);return f?d.reverse():d},t.tickFormat=(a,c)=>{if(a==null&&(a=10),c==null&&(c=r===10?"s":","),typeof c!="function"&&(!(r%1)&&(c=_t(c)).precision==null&&(c.trim=!0),c=Oe(c)),a===1/0)return c;const s=Math.max(1,r*a/t.ticks().length);return l=>{let f=l/o(Math.round(i(l)));return f*r<r-.5&&(f*=r),f<=s?c(l):""}},t.nice=()=>e(Zi(e(),{floor:a=>o(Math.floor(i(a))),ceil:a=>o(Math.ceil(i(a)))})),t}function ua(){const n=ze(jt()).domain([1,10]);return n.copy=()=>xt(n,ua()).base(n.base()),on.apply(n,arguments),n}function Hr(n){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/n))}}function Lr(n){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*n}}function Xe(n){var t=1,e=n(Hr(t),Lr(t));return e.constant=function(r){return arguments.length?n(Hr(t=+r),Lr(t)):t},Tn(e)}function aa(){var n=Xe(jt());return n.copy=function(){return xt(n,aa()).constant(n.constant())},on.apply(n,arguments)}function Or(n){return function(t){return t<0?-Math.pow(-t,n):Math.pow(t,n)}}function sa(n){return n<0?-Math.sqrt(-n):Math.sqrt(n)}function ca(n){return n<0?-n*n:n*n}function Ve(n){var t=n(Z,Z),e=1;function r(){return e===1?n(Z,Z):e===.5?n(sa,ca):n(Or(e),Or(1/e))}return t.exponent=function(i){return arguments.length?(e=+i,r()):e},Tn(t)}function Bi(){var n=Ve(jt());return n.copy=function(){return xt(n,Bi()).exponent(n.exponent())},on.apply(n,arguments),n}function Hf(){return Bi.apply(null,arguments).exponent(.5)}function Wr(n){return Math.sign(n)*n*n}function fa(n){return Math.sign(n)*Math.sqrt(Math.abs(n))}function la(){var n=We(),t=[0,1],e=!1,r;function i(o){var u=fa(n(o));return isNaN(u)?r:e?Math.round(u):u}return i.invert=function(o){return n.invert(Wr(o))},i.domain=function(o){return arguments.length?(n.domain(o),i):n.domain()},i.range=function(o){return arguments.length?(n.range((t=Array.from(o,qt)).map(Wr)),i):t.slice()},i.rangeRound=function(o){return i.range(o).round(!0)},i.round=function(o){return arguments.length?(e=!!o,i):e},i.clamp=function(o){return arguments.length?(n.clamp(o),i):n.clamp()},i.unknown=function(o){return arguments.length?(r=o,i):r},i.copy=function(){return la(n.domain(),t).round(e).clamp(n.clamp()).unknown(r)},on.apply(i,arguments),Tn(i)}function ha(){var n=[],t=[],e=[],r;function i(){var u=0,a=Math.max(1,t.length);for(e=new Array(a-1);++u<a;)e[u-1]=Qo(n,u/a);return o}function o(u){return u==null||isNaN(u=+u)?r:t[Jn(e,u)]}return o.invertExtent=function(u){var a=t.indexOf(u);return a<0?[NaN,NaN]:[a>0?e[a-1]:n[0],a<e.length?e[a]:n[n.length-1]]},o.domain=function(u){if(!arguments.length)return n.slice();n=[];for(let a of u)a!=null&&!isNaN(a=+a)&&n.push(a);return n.sort(F),i()},o.range=function(u){return arguments.length?(t=Array.from(u),i()):t.slice()},o.unknown=function(u){return arguments.length?(r=u,o):r},o.quantiles=function(){return e.slice()},o.copy=function(){return ha().domain(n).range(t).unknown(r)},on.apply(o,arguments)}function ga(){var n=0,t=1,e=1,r=[.5],i=[0,1],o;function u(c){return c!=null&&c<=c?i[Jn(r,c,0,e)]:o}function a(){var c=-1;for(r=new Array(e);++c<e;)r[c]=((c+1)*t-(c-e)*n)/(e+1);return u}return u.domain=function(c){return arguments.length?([n,t]=c,n=+n,t=+t,a()):[n,t]},u.range=function(c){return arguments.length?(e=(i=Array.from(c)).length-1,a()):i.slice()},u.invertExtent=function(c){var s=i.indexOf(c);return s<0?[NaN,NaN]:s<1?[n,r[0]]:s>=e?[r[e-1],t]:[r[s-1],r[s]]},u.unknown=function(c){return arguments.length&&(o=c),u},u.thresholds=function(){return r.slice()},u.copy=function(){return ga().domain([n,t]).range(i).unknown(o)},on.apply(Tn(u),arguments)}function ma(){var n=[.5],t=[0,1],e,r=1;function i(o){return o!=null&&o<=o?t[Jn(n,o,0,r)]:e}return i.domain=function(o){return arguments.length?(n=Array.from(o),r=Math.min(n.length,t.length-1),i):n.slice()},i.range=function(o){return arguments.length?(t=Array.from(o),r=Math.min(n.length,t.length-1),i):t.slice()},i.invertExtent=function(o){var u=t.indexOf(o);return[n[u-1],n[u]]},i.unknown=function(o){return arguments.length?(e=o,i):e},i.copy=function(){return ma().domain(n).range(t).unknown(e)},on.apply(i,arguments)}const he=new Date,ge=new Date;function I(n,t,e,r){function i(o){return n(o=arguments.length===0?new Date:new Date(+o)),o}return i.floor=o=>(n(o=new Date(+o)),o),i.ceil=o=>(n(o=new Date(o-1)),t(o,1),n(o),o),i.round=o=>{const u=i(o),a=i.ceil(o);return o-u<a-o?u:a},i.offset=(o,u)=>(t(o=new Date(+o),u==null?1:Math.floor(u)),o),i.range=(o,u,a)=>{const c=[];if(o=i.ceil(o),a=a==null?1:Math.floor(a),!(o<u)||!(a>0))return c;let s;do c.push(s=new Date(+o)),t(o,a),n(o);while(s<o&&o<u);return c},i.filter=o=>I(u=>{if(u>=u)for(;n(u),!o(u);)u.setTime(u-1)},(u,a)=>{if(u>=u)if(a<0)for(;++a<=0;)for(;t(u,-1),!o(u););else for(;--a>=0;)for(;t(u,1),!o(u););}),e&&(i.count=(o,u)=>(he.setTime(+o),ge.setTime(+u),n(he),n(ge),Math.floor(e(he,ge))),i.every=o=>(o=Math.floor(o),!isFinite(o)||!(o>0)?null:o>1?i.filter(r?u=>r(u)%o===0:u=>i.count(0,u)%o===0):i)),i}const It=I(()=>{},(n,t)=>{n.setTime(+n+t)},(n,t)=>t-n);It.every=n=>(n=Math.floor(n),!isFinite(n)||!(n>0)?null:n>1?I(t=>{t.setTime(Math.floor(t/n)*n)},(t,e)=>{t.setTime(+t+e*n)},(t,e)=>(e-t)/n):It);const Lf=It.range,mn=1e3,rn=mn*60,_n=rn*60,pn=_n*24,Ze=pn*7,zr=pn*30,me=pn*365,Cn=I(n=>{n.setTime(n-n.getMilliseconds())},(n,t)=>{n.setTime(+n+t*mn)},(n,t)=>(t-n)/mn,n=>n.getUTCSeconds()),Of=Cn.range,Be=I(n=>{n.setTime(n-n.getMilliseconds()-n.getSeconds()*mn)},(n,t)=>{n.setTime(+n+t*rn)},(n,t)=>(t-n)/rn,n=>n.getMinutes()),Wf=Be.range,je=I(n=>{n.setUTCSeconds(0,0)},(n,t)=>{n.setTime(+n+t*rn)},(n,t)=>(t-n)/rn,n=>n.getUTCMinutes()),zf=je.range,Qe=I(n=>{n.setTime(n-n.getMilliseconds()-n.getSeconds()*mn-n.getMinutes()*rn)},(n,t)=>{n.setTime(+n+t*_n)},(n,t)=>(t-n)/_n,n=>n.getHours()),Xf=Qe.range,Ge=I(n=>{n.setUTCMinutes(0,0,0)},(n,t)=>{n.setTime(+n+t*_n)},(n,t)=>(t-n)/_n,n=>n.getUTCHours()),Vf=Ge.range,wt=I(n=>n.setHours(0,0,0,0),(n,t)=>n.setDate(n.getDate()+t),(n,t)=>(t-n-(t.getTimezoneOffset()-n.getTimezoneOffset())*rn)/pn,n=>n.getDate()-1),Zf=wt.range,Qt=I(n=>{n.setUTCHours(0,0,0,0)},(n,t)=>{n.setUTCDate(n.getUTCDate()+t)},(n,t)=>(t-n)/pn,n=>n.getUTCDate()-1),Bf=Qt.range,ji=I(n=>{n.setUTCHours(0,0,0,0)},(n,t)=>{n.setUTCDate(n.getUTCDate()+t)},(n,t)=>(t-n)/pn,n=>Math.floor(n/pn)),jf=ji.range;function En(n){return I(t=>{t.setDate(t.getDate()-(t.getDay()+7-n)%7),t.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+e*7)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*rn)/Ze)}const Gt=En(0),Pt=En(1),_a=En(2),pa=En(3),Qn=En(4),da=En(5),ya=En(6),Qf=Gt.range,Gf=Pt.range,Jf=_a.range,Kf=pa.range,nl=Qn.range,tl=da.range,el=ya.range;function Rn(n){return I(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-n)%7),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e*7)},(t,e)=>(e-t)/Ze)}const Jt=Rn(0),Ht=Rn(1),xa=Rn(2),wa=Rn(3),Gn=Rn(4),ba=Rn(5),Ma=Rn(6),rl=Jt.range,il=Ht.range,ol=xa.range,ul=wa.range,al=Gn.range,sl=ba.range,cl=Ma.range,Je=I(n=>{n.setDate(1),n.setHours(0,0,0,0)},(n,t)=>{n.setMonth(n.getMonth()+t)},(n,t)=>t.getMonth()-n.getMonth()+(t.getFullYear()-n.getFullYear())*12,n=>n.getMonth()),fl=Je.range,Ke=I(n=>{n.setUTCDate(1),n.setUTCHours(0,0,0,0)},(n,t)=>{n.setUTCMonth(n.getUTCMonth()+t)},(n,t)=>t.getUTCMonth()-n.getUTCMonth()+(t.getUTCFullYear()-n.getUTCFullYear())*12,n=>n.getUTCMonth()),ll=Ke.range,dn=I(n=>{n.setMonth(0,1),n.setHours(0,0,0,0)},(n,t)=>{n.setFullYear(n.getFullYear()+t)},(n,t)=>t.getFullYear()-n.getFullYear(),n=>n.getFullYear());dn.every=n=>!isFinite(n=Math.floor(n))||!(n>0)?null:I(t=>{t.setFullYear(Math.floor(t.getFullYear()/n)*n),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e*n)});const hl=dn.range,yn=I(n=>{n.setUTCMonth(0,1),n.setUTCHours(0,0,0,0)},(n,t)=>{n.setUTCFullYear(n.getUTCFullYear()+t)},(n,t)=>t.getUTCFullYear()-n.getUTCFullYear(),n=>n.getUTCFullYear());yn.every=n=>!isFinite(n=Math.floor(n))||!(n>0)?null:I(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/n)*n),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e*n)});const gl=yn.range;function Qi(n,t,e,r,i,o){const u=[[Cn,1,mn],[Cn,5,5*mn],[Cn,15,15*mn],[Cn,30,30*mn],[o,1,rn],[o,5,5*rn],[o,15,15*rn],[o,30,30*rn],[i,1,_n],[i,3,3*_n],[i,6,6*_n],[i,12,12*_n],[r,1,pn],[r,2,2*pn],[e,1,Ze],[t,1,zr],[t,3,3*zr],[n,1,me]];function a(s,l,f){const h=l<s;h&&([s,l]=[l,s]);const g=f&&typeof f.range=="function"?f:c(s,l,f),m=g?g.range(s,+l+1):[];return h?m.reverse():m}function c(s,l,f){const h=Math.abs(l-s)/f,g=De(([,,w])=>w).right(u,h);if(g===u.length)return n.every(we(s/me,l/me,f));if(g===0)return It.every(Math.max(we(s,l,f),1));const[m,_]=u[h/u[g-1][2]<u[g][2]/h?g-1:g];return m.every(_)}return[a,c]}const[va,Ta]=Qi(yn,Ke,Jt,ji,Ge,je),[ka,Na]=Qi(dn,Je,Gt,wt,Qe,Be);function _e(n){if(0<=n.y&&n.y<100){var t=new Date(-1,n.m,n.d,n.H,n.M,n.S,n.L);return t.setFullYear(n.y),t}return new Date(n.y,n.m,n.d,n.H,n.M,n.S,n.L)}function pe(n){if(0<=n.y&&n.y<100){var t=new Date(Date.UTC(-1,n.m,n.d,n.H,n.M,n.S,n.L));return t.setUTCFullYear(n.y),t}return new Date(Date.UTC(n.y,n.m,n.d,n.H,n.M,n.S,n.L))}function st(n,t,e){return{y:n,m:t,d:e,H:0,M:0,S:0,L:0}}function Sa(n){var t=n.dateTime,e=n.date,r=n.time,i=n.periods,o=n.days,u=n.shortDays,a=n.months,c=n.shortMonths,s=ct(i),l=ft(i),f=ct(o),h=ft(o),g=ct(u),m=ft(u),_=ct(a),w=ft(a),d=ct(c),S=ft(c),M={a:Q,A:G,b:W,B:Yn,c:null,d:Qr,e:Qr,f:Qa,g:us,G:ss,H:Za,I:Ba,j:ja,L:Gi,m:Ga,M:Ja,p:Fn,q:qn,Q:Kr,s:ni,S:Ka,u:ns,U:ts,V:es,w:rs,W:is,x:null,X:null,y:os,Y:as,Z:cs,"%":Jr},v={a:et,A:rt,b:an,B:it,c:null,d:Gr,e:Gr,f:gs,g:vs,G:ks,H:fs,I:ls,j:hs,L:Ki,m:ms,M:_s,p:ot,q:ut,Q:Kr,s:ni,S:ps,u:ds,U:ys,V:xs,w:ws,W:bs,x:null,X:null,y:Ms,Y:Ts,Z:Ns,"%":Jr},T={a:P,A:k,b:Y,B:b,c:C,d:Br,e:Br,f:Wa,g:Zr,G:Vr,H:jr,I:jr,j:Pa,L:Oa,m:Ia,M:Ha,p:V,q:qa,Q:Xa,s:Va,S:La,u:Da,U:Ea,V:Ra,w:Ua,W:Ya,x:nn,X:un,y:Zr,Y:Vr,Z:Fa,"%":za};M.x=x(e,M),M.X=x(r,M),M.c=x(t,M),v.x=x(e,v),v.X=x(r,v),v.c=x(t,v);function x(y,$){return function(A){var p=[],B=-1,E=0,J=y.length,K,Nn,hr;for(A instanceof Date||(A=new Date(+A));++B<J;)y.charCodeAt(B)===37&&(p.push(y.slice(E,B)),(Nn=Xr[K=y.charAt(++B)])!=null?K=y.charAt(++B):Nn=K==="e"?" ":"0",(hr=$[K])&&(K=hr(A,Nn)),p.push(K),E=B+1);return p.push(y.slice(E,B)),p.join("")}}function D(y,$){return function(A){var p=st(1900,void 0,1),B=q(p,y,A+="",0),E,J;if(B!=A.length)return null;if("Q"in p)return new Date(p.Q);if("s"in p)return new Date(p.s*1e3+("L"in p?p.L:0));if($&&!("Z"in p)&&(p.Z=0),"p"in p&&(p.H=p.H%12+p.p*12),p.m===void 0&&(p.m="q"in p?p.q:0),"V"in p){if(p.V<1||p.V>53)return null;"w"in p||(p.w=1),"Z"in p?(E=pe(st(p.y,0,1)),J=E.getUTCDay(),E=J>4||J===0?Ht.ceil(E):Ht(E),E=Qt.offset(E,(p.V-1)*7),p.y=E.getUTCFullYear(),p.m=E.getUTCMonth(),p.d=E.getUTCDate()+(p.w+6)%7):(E=_e(st(p.y,0,1)),J=E.getDay(),E=J>4||J===0?Pt.ceil(E):Pt(E),E=wt.offset(E,(p.V-1)*7),p.y=E.getFullYear(),p.m=E.getMonth(),p.d=E.getDate()+(p.w+6)%7)}else("W"in p||"U"in p)&&("w"in p||(p.w="u"in p?p.u%7:"W"in p?1:0),J="Z"in p?pe(st(p.y,0,1)).getUTCDay():_e(st(p.y,0,1)).getDay(),p.m=0,p.d="W"in p?(p.w+6)%7+p.W*7-(J+5)%7:p.w+p.U*7-(J+6)%7);return"Z"in p?(p.H+=p.Z/100|0,p.M+=p.Z%100,pe(p)):_e(p)}}function q(y,$,A,p){for(var B=0,E=$.length,J=A.length,K,Nn;B<E;){if(p>=J)return-1;if(K=$.charCodeAt(B++),K===37){if(K=$.charAt(B++),Nn=T[K in Xr?$.charAt(B++):K],!Nn||(p=Nn(y,A,p))<0)return-1}else if(K!=A.charCodeAt(p++))return-1}return p}function V(y,$,A){var p=s.exec($.slice(A));return p?(y.p=l.get(p[0].toLowerCase()),A+p[0].length):-1}function P(y,$,A){var p=g.exec($.slice(A));return p?(y.w=m.get(p[0].toLowerCase()),A+p[0].length):-1}function k(y,$,A){var p=f.exec($.slice(A));return p?(y.w=h.get(p[0].toLowerCase()),A+p[0].length):-1}function Y(y,$,A){var p=d.exec($.slice(A));return p?(y.m=S.get(p[0].toLowerCase()),A+p[0].length):-1}function b(y,$,A){var p=_.exec($.slice(A));return p?(y.m=w.get(p[0].toLowerCase()),A+p[0].length):-1}function C(y,$,A){return q(y,t,$,A)}function nn(y,$,A){return q(y,e,$,A)}function un(y,$,A){return q(y,r,$,A)}function Q(y){return u[y.getDay()]}function G(y){return o[y.getDay()]}function W(y){return c[y.getMonth()]}function Yn(y){return a[y.getMonth()]}function Fn(y){return i[+(y.getHours()>=12)]}function qn(y){return 1+~~(y.getMonth()/3)}function et(y){return u[y.getUTCDay()]}function rt(y){return o[y.getUTCDay()]}function an(y){return c[y.getUTCMonth()]}function it(y){return a[y.getUTCMonth()]}function ot(y){return i[+(y.getUTCHours()>=12)]}function ut(y){return 1+~~(y.getUTCMonth()/3)}return{format:function(y){var $=x(y+="",M);return $.toString=function(){return y},$},parse:function(y){var $=D(y+="",!1);return $.toString=function(){return y},$},utcFormat:function(y){var $=x(y+="",v);return $.toString=function(){return y},$},utcParse:function(y){var $=D(y+="",!0);return $.toString=function(){return y},$}}}var Xr={"-":"",_:" ",0:"0"},O=/^\s*\d+/,$a=/^%/,Ca=/[\\^$*+?|[\]().{}]/g;function U(n,t,e){var r=n<0?"-":"",i=(r?-n:n)+"",o=i.length;return r+(o<e?new Array(e-o+1).join(t)+i:i)}function Aa(n){return n.replace(Ca,"\\$&")}function ct(n){return new RegExp("^(?:"+n.map(Aa).join("|")+")","i")}function ft(n){return new Map(n.map((t,e)=>[t.toLowerCase(),e]))}function Ua(n,t,e){var r=O.exec(t.slice(e,e+1));return r?(n.w=+r[0],e+r[0].length):-1}function Da(n,t,e){var r=O.exec(t.slice(e,e+1));return r?(n.u=+r[0],e+r[0].length):-1}function Ea(n,t,e){var r=O.exec(t.slice(e,e+2));return r?(n.U=+r[0],e+r[0].length):-1}function Ra(n,t,e){var r=O.exec(t.slice(e,e+2));return r?(n.V=+r[0],e+r[0].length):-1}function Ya(n,t,e){var r=O.exec(t.slice(e,e+2));return r?(n.W=+r[0],e+r[0].length):-1}function Vr(n,t,e){var r=O.exec(t.slice(e,e+4));return r?(n.y=+r[0],e+r[0].length):-1}function Zr(n,t,e){var r=O.exec(t.slice(e,e+2));return r?(n.y=+r[0]+(+r[0]>68?1900:2e3),e+r[0].length):-1}function Fa(n,t,e){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(e,e+6));return r?(n.Z=r[1]?0:-(r[2]+(r[3]||"00")),e+r[0].length):-1}function qa(n,t,e){var r=O.exec(t.slice(e,e+1));return r?(n.q=r[0]*3-3,e+r[0].length):-1}function Ia(n,t,e){var r=O.exec(t.slice(e,e+2));return r?(n.m=r[0]-1,e+r[0].length):-1}function Br(n,t,e){var r=O.exec(t.slice(e,e+2));return r?(n.d=+r[0],e+r[0].length):-1}function Pa(n,t,e){var r=O.exec(t.slice(e,e+3));return r?(n.m=0,n.d=+r[0],e+r[0].length):-1}function jr(n,t,e){var r=O.exec(t.slice(e,e+2));return r?(n.H=+r[0],e+r[0].length):-1}function Ha(n,t,e){var r=O.exec(t.slice(e,e+2));return r?(n.M=+r[0],e+r[0].length):-1}function La(n,t,e){var r=O.exec(t.slice(e,e+2));return r?(n.S=+r[0],e+r[0].length):-1}function Oa(n,t,e){var r=O.exec(t.slice(e,e+3));return r?(n.L=+r[0],e+r[0].length):-1}function Wa(n,t,e){var r=O.exec(t.slice(e,e+6));return r?(n.L=Math.floor(r[0]/1e3),e+r[0].length):-1}function za(n,t,e){var r=$a.exec(t.slice(e,e+1));return r?e+r[0].length:-1}function Xa(n,t,e){var r=O.exec(t.slice(e));return r?(n.Q=+r[0],e+r[0].length):-1}function Va(n,t,e){var r=O.exec(t.slice(e));return r?(n.s=+r[0],e+r[0].length):-1}function Qr(n,t){return U(n.getDate(),t,2)}function Za(n,t){return U(n.getHours(),t,2)}function Ba(n,t){return U(n.getHours()%12||12,t,2)}function ja(n,t){return U(1+wt.count(dn(n),n),t,3)}function Gi(n,t){return U(n.getMilliseconds(),t,3)}function Qa(n,t){return Gi(n,t)+"000"}function Ga(n,t){return U(n.getMonth()+1,t,2)}function Ja(n,t){return U(n.getMinutes(),t,2)}function Ka(n,t){return U(n.getSeconds(),t,2)}function ns(n){var t=n.getDay();return t===0?7:t}function ts(n,t){return U(Gt.count(dn(n)-1,n),t,2)}function Ji(n){var t=n.getDay();return t>=4||t===0?Qn(n):Qn.ceil(n)}function es(n,t){return n=Ji(n),U(Qn.count(dn(n),n)+(dn(n).getDay()===4),t,2)}function rs(n){return n.getDay()}function is(n,t){return U(Pt.count(dn(n)-1,n),t,2)}function os(n,t){return U(n.getFullYear()%100,t,2)}function us(n,t){return n=Ji(n),U(n.getFullYear()%100,t,2)}function as(n,t){return U(n.getFullYear()%1e4,t,4)}function ss(n,t){var e=n.getDay();return n=e>=4||e===0?Qn(n):Qn.ceil(n),U(n.getFullYear()%1e4,t,4)}function cs(n){var t=n.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+U(t/60|0,"0",2)+U(t%60,"0",2)}function Gr(n,t){return U(n.getUTCDate(),t,2)}function fs(n,t){return U(n.getUTCHours(),t,2)}function ls(n,t){return U(n.getUTCHours()%12||12,t,2)}function hs(n,t){return U(1+Qt.count(yn(n),n),t,3)}function Ki(n,t){return U(n.getUTCMilliseconds(),t,3)}function gs(n,t){return Ki(n,t)+"000"}function ms(n,t){return U(n.getUTCMonth()+1,t,2)}function _s(n,t){return U(n.getUTCMinutes(),t,2)}function ps(n,t){return U(n.getUTCSeconds(),t,2)}function ds(n){var t=n.getUTCDay();return t===0?7:t}function ys(n,t){return U(Jt.count(yn(n)-1,n),t,2)}function no(n){var t=n.getUTCDay();return t>=4||t===0?Gn(n):Gn.ceil(n)}function xs(n,t){return n=no(n),U(Gn.count(yn(n),n)+(yn(n).getUTCDay()===4),t,2)}function ws(n){return n.getUTCDay()}function bs(n,t){return U(Ht.count(yn(n)-1,n),t,2)}function Ms(n,t){return U(n.getUTCFullYear()%100,t,2)}function vs(n,t){return n=no(n),U(n.getUTCFullYear()%100,t,2)}function Ts(n,t){return U(n.getUTCFullYear()%1e4,t,4)}function ks(n,t){var e=n.getUTCDay();return n=e>=4||e===0?Gn(n):Gn.ceil(n),U(n.getUTCFullYear()%1e4,t,4)}function Ns(){return"+0000"}function Jr(){return"%"}function Kr(n){return+n}function ni(n){return Math.floor(+n/1e3)}var Hn,to,Ss,nr,eo;$s({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function $s(n){return Hn=Sa(n),to=Hn.format,Ss=Hn.parse,nr=Hn.utcFormat,eo=Hn.utcParse,Hn}var ro="%Y-%m-%dT%H:%M:%S.%LZ";function Cs(n){return n.toISOString()}var As=Date.prototype.toISOString?Cs:nr(ro);const ml=As;function Us(n){var t=new Date(n);return isNaN(t)?null:t}var Ds=+new Date("2000-01-01T00:00:00.000Z")?Us:eo(ro);const _l=Ds;function Es(n){return new Date(n)}function Rs(n){return n instanceof Date?+n:+new Date(+n)}function tr(n,t,e,r,i,o,u,a,c,s){var l=We(),f=l.invert,h=l.domain,g=s(".%L"),m=s(":%S"),_=s("%I:%M"),w=s("%I %p"),d=s("%a %d"),S=s("%b %d"),M=s("%B"),v=s("%Y");function T(x){return(c(x)<x?g:a(x)<x?m:u(x)<x?_:o(x)<x?w:r(x)<x?i(x)<x?d:S:e(x)<x?M:v)(x)}return l.invert=function(x){return new Date(f(x))},l.domain=function(x){return arguments.length?h(Array.from(x,Rs)):h().map(Es)},l.ticks=function(x){var D=h();return n(D[0],D[D.length-1],x??10)},l.tickFormat=function(x,D){return D==null?T:s(D)},l.nice=function(x){var D=h();return(!x||typeof x.range!="function")&&(x=t(D[0],D[D.length-1],x??10)),x?h(Zi(D,x)):l},l.copy=function(){return xt(l,tr(n,t,e,r,i,o,u,a,c,s))},l}function pl(){return on.apply(tr(ka,Na,dn,Je,Gt,wt,Qe,Be,Cn,to).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function dl(){return on.apply(tr(va,Ta,yn,Ke,Jt,Qt,Ge,je,Cn,nr).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function Kt(){var n=0,t=1,e,r,i,o,u=Z,a=!1,c;function s(f){return f==null||isNaN(f=+f)?c:u(i===0?.5:(f=(o(f)-e)*i,a?Math.max(0,Math.min(1,f)):f))}s.domain=function(f){return arguments.length?([n,t]=f,e=o(n=+n),r=o(t=+t),i=e===r?0:1/(r-e),s):[n,t]},s.clamp=function(f){return arguments.length?(a=!!f,s):a},s.interpolator=function(f){return arguments.length?(u=f,s):u};function l(f){return function(h){var g,m;return arguments.length?([g,m]=h,u=f(g,m),s):[u(0),u(1)]}}return s.range=l(tt),s.rangeRound=l(He),s.unknown=function(f){return arguments.length?(c=f,s):c},function(f){return o=f,e=f(n),r=f(t),i=e===r?0:1/(r-e),s}}function kn(n,t){return t.domain(n.domain()).interpolator(n.interpolator()).clamp(n.clamp()).unknown(n.unknown())}function Ys(){var n=Tn(Kt()(Z));return n.copy=function(){return kn(n,Ys())},xn.apply(n,arguments)}function Fs(){var n=ze(Kt()).domain([1,10]);return n.copy=function(){return kn(n,Fs()).base(n.base())},xn.apply(n,arguments)}function qs(){var n=Xe(Kt());return n.copy=function(){return kn(n,qs()).constant(n.constant())},xn.apply(n,arguments)}function io(){var n=Ve(Kt());return n.copy=function(){return kn(n,io()).exponent(n.exponent())},xn.apply(n,arguments)}function yl(){return io.apply(null,arguments).exponent(.5)}function Is(){var n=[],t=Z;function e(r){if(r!=null&&!isNaN(r=+r))return t((Jn(n,r,1)-1)/(n.length-1))}return e.domain=function(r){if(!arguments.length)return n.slice();n=[];for(let i of r)i!=null&&!isNaN(i=+i)&&n.push(i);return n.sort(F),e},e.interpolator=function(r){return arguments.length?(t=r,e):t},e.range=function(){return n.map((r,i)=>t(i/(n.length-1)))},e.quantiles=function(r){return Array.from({length:r+1},(i,o)=>Dt(n,o/r))},e.copy=function(){return Is(t).domain(n)},xn.apply(e,arguments)}function ne(){var n=0,t=.5,e=1,r=1,i,o,u,a,c,s=Z,l,f=!1,h;function g(_){return isNaN(_=+_)?h:(_=.5+((_=+l(_))-o)*(r*_<r*o?a:c),s(f?Math.max(0,Math.min(1,_)):_))}g.domain=function(_){return arguments.length?([n,t,e]=_,i=l(n=+n),o=l(t=+t),u=l(e=+e),a=i===o?0:.5/(o-i),c=o===u?0:.5/(u-o),r=o<i?-1:1,g):[n,t,e]},g.clamp=function(_){return arguments.length?(f=!!_,g):f},g.interpolator=function(_){return arguments.length?(s=_,g):s};function m(_){return function(w){var d,S,M;return arguments.length?([d,S,M]=w,s=Eu(_,[d,S,M]),g):[s(0),s(.5),s(1)]}}return g.range=m(tt),g.rangeRound=m(He),g.unknown=function(_){return arguments.length?(h=_,g):h},function(_){return l=_,i=_(n),o=_(t),u=_(e),a=i===o?0:.5/(o-i),c=o===u?0:.5/(u-o),r=o<i?-1:1,g}}function Ps(){var n=Tn(ne()(Z));return n.copy=function(){return kn(n,Ps())},xn.apply(n,arguments)}function Hs(){var n=ze(ne()).domain([.1,1,10]);return n.copy=function(){return kn(n,Hs()).base(n.base())},xn.apply(n,arguments)}function Ls(){var n=Xe(ne());return n.copy=function(){return kn(n,Ls()).constant(n.constant())},xn.apply(n,arguments)}function oo(){var n=Ve(ne());return n.copy=function(){return kn(n,oo()).exponent(n.exponent())},xn.apply(n,arguments)}function xl(){return oo.apply(null,arguments).exponent(.5)}function N(n){return function(){return n}}const ti=Math.abs,z=Math.atan2,gn=Math.cos,Os=Math.max,Wn=Math.min,j=Math.sin,R=Math.sqrt,X=1e-12,bn=Math.PI,Lt=bn/2,wn=2*bn;function Ws(n){return n>1?0:n<-1?bn:Math.acos(n)}function ei(n){return n>=1?Lt:n<=-1?-Lt:Math.asin(n)}function bt(n){let t=3;return n.digits=function(e){if(!arguments.length)return t;if(e==null)t=null;else{const r=Math.floor(e);if(!(r>=0))throw new RangeError(`invalid digits: ${e}`);t=r}return n},()=>new Bt(t)}function zs(n){return n.innerRadius}function Xs(n){return n.outerRadius}function Vs(n){return n.startAngle}function Zs(n){return n.endAngle}function Bs(n){return n&&n.padAngle}function js(n,t,e,r,i,o,u,a){var c=e-n,s=r-t,l=u-i,f=a-o,h=f*c-l*s;if(!(h*h<X))return h=(l*(t-o)-f*(n-i))/h,[n+h*c,t+h*s]}function Nt(n,t,e,r,i,o,u){var a=n-e,c=t-r,s=(u?o:-o)/R(a*a+c*c),l=s*c,f=-s*a,h=n+l,g=t+f,m=e+l,_=r+f,w=(h+m)/2,d=(g+_)/2,S=m-h,M=_-g,v=S*S+M*M,T=i-o,x=h*_-m*g,D=(M<0?-1:1)*R(Os(0,T*T*v-x*x)),q=(x*M-S*D)/v,V=(-x*S-M*D)/v,P=(x*M+S*D)/v,k=(-x*S+M*D)/v,Y=q-w,b=V-d,C=P-w,nn=k-d;return Y*Y+b*b>C*C+nn*nn&&(q=P,V=k),{cx:q,cy:V,x01:-l,y01:-f,x11:q*(i/T-1),y11:V*(i/T-1)}}function wl(){var n=zs,t=Xs,e=N(0),r=null,i=Vs,o=Zs,u=Bs,a=null,c=bt(s);function s(){var l,f,h=+n.apply(this,arguments),g=+t.apply(this,arguments),m=i.apply(this,arguments)-Lt,_=o.apply(this,arguments)-Lt,w=ti(_-m),d=_>m;if(a||(a=l=c()),g<h&&(f=g,g=h,h=f),!(g>X))a.moveTo(0,0);else if(w>wn-X)a.moveTo(g*gn(m),g*j(m)),a.arc(0,0,g,m,_,!d),h>X&&(a.moveTo(h*gn(_),h*j(_)),a.arc(0,0,h,_,m,d));else{var S=m,M=_,v=m,T=_,x=w,D=w,q=u.apply(this,arguments)/2,V=q>X&&(r?+r.apply(this,arguments):R(h*h+g*g)),P=Wn(ti(g-h)/2,+e.apply(this,arguments)),k=P,Y=P,b,C;if(V>X){var nn=ei(V/h*j(q)),un=ei(V/g*j(q));(x-=nn*2)>X?(nn*=d?1:-1,v+=nn,T-=nn):(x=0,v=T=(m+_)/2),(D-=un*2)>X?(un*=d?1:-1,S+=un,M-=un):(D=0,S=M=(m+_)/2)}var Q=g*gn(S),G=g*j(S),W=h*gn(T),Yn=h*j(T);if(P>X){var Fn=g*gn(M),qn=g*j(M),et=h*gn(v),rt=h*j(v),an;if(w<bn)if(an=js(Q,G,et,rt,Fn,qn,W,Yn)){var it=Q-an[0],ot=G-an[1],ut=Fn-an[0],y=qn-an[1],$=1/j(Ws((it*ut+ot*y)/(R(it*it+ot*ot)*R(ut*ut+y*y)))/2),A=R(an[0]*an[0]+an[1]*an[1]);k=Wn(P,(h-A)/($-1)),Y=Wn(P,(g-A)/($+1))}else k=Y=0}D>X?Y>X?(b=Nt(et,rt,Q,G,g,Y,d),C=Nt(Fn,qn,W,Yn,g,Y,d),a.moveTo(b.cx+b.x01,b.cy+b.y01),Y<P?a.arc(b.cx,b.cy,Y,z(b.y01,b.x01),z(C.y01,C.x01),!d):(a.arc(b.cx,b.cy,Y,z(b.y01,b.x01),z(b.y11,b.x11),!d),a.arc(0,0,g,z(b.cy+b.y11,b.cx+b.x11),z(C.cy+C.y11,C.cx+C.x11),!d),a.arc(C.cx,C.cy,Y,z(C.y11,C.x11),z(C.y01,C.x01),!d))):(a.moveTo(Q,G),a.arc(0,0,g,S,M,!d)):a.moveTo(Q,G),!(h>X)||!(x>X)?a.lineTo(W,Yn):k>X?(b=Nt(W,Yn,Fn,qn,h,-k,d),C=Nt(Q,G,et,rt,h,-k,d),a.lineTo(b.cx+b.x01,b.cy+b.y01),k<P?a.arc(b.cx,b.cy,k,z(b.y01,b.x01),z(C.y01,C.x01),!d):(a.arc(b.cx,b.cy,k,z(b.y01,b.x01),z(b.y11,b.x11),!d),a.arc(0,0,h,z(b.cy+b.y11,b.cx+b.x11),z(C.cy+C.y11,C.cx+C.x11),d),a.arc(C.cx,C.cy,k,z(C.y11,C.x11),z(C.y01,C.x01),!d))):a.arc(0,0,h,T,v,d)}if(a.closePath(),l)return a=null,l+""||null}return s.centroid=function(){var l=(+n.apply(this,arguments)+ +t.apply(this,arguments))/2,f=(+i.apply(this,arguments)+ +o.apply(this,arguments))/2-bn/2;return[gn(f)*l,j(f)*l]},s.innerRadius=function(l){return arguments.length?(n=typeof l=="function"?l:N(+l),s):n},s.outerRadius=function(l){return arguments.length?(t=typeof l=="function"?l:N(+l),s):t},s.cornerRadius=function(l){return arguments.length?(e=typeof l=="function"?l:N(+l),s):e},s.padRadius=function(l){return arguments.length?(r=l==null?null:typeof l=="function"?l:N(+l),s):r},s.startAngle=function(l){return arguments.length?(i=typeof l=="function"?l:N(+l),s):i},s.endAngle=function(l){return arguments.length?(o=typeof l=="function"?l:N(+l),s):o},s.padAngle=function(l){return arguments.length?(u=typeof l=="function"?l:N(+l),s):u},s.context=function(l){return arguments.length?(a=l??null,s):a},s}var Qs=Array.prototype.slice;function te(n){return typeof n=="object"&&"length"in n?n:Array.from(n)}function uo(n){this._context=n}uo.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(n,t){switch(n=+n,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(n,t):this._context.moveTo(n,t);break;case 1:this._point=2;default:this._context.lineTo(n,t);break}}};function er(n){return new uo(n)}function rr(n){return n[0]}function ir(n){return n[1]}function ao(n,t){var e=N(!0),r=null,i=er,o=null,u=bt(a);n=typeof n=="function"?n:n===void 0?rr:N(n),t=typeof t=="function"?t:t===void 0?ir:N(t);function a(c){var s,l=(c=te(c)).length,f,h=!1,g;for(r==null&&(o=i(g=u())),s=0;s<=l;++s)!(s<l&&e(f=c[s],s,c))===h&&((h=!h)?o.lineStart():o.lineEnd()),h&&o.point(+n(f,s,c),+t(f,s,c));if(g)return o=null,g+""||null}return a.x=function(c){return arguments.length?(n=typeof c=="function"?c:N(+c),a):n},a.y=function(c){return arguments.length?(t=typeof c=="function"?c:N(+c),a):t},a.defined=function(c){return arguments.length?(e=typeof c=="function"?c:N(!!c),a):e},a.curve=function(c){return arguments.length?(i=c,r!=null&&(o=i(r)),a):i},a.context=function(c){return arguments.length?(c==null?r=o=null:o=i(r=c),a):r},a}function Gs(n,t,e){var r=null,i=N(!0),o=null,u=er,a=null,c=bt(s);n=typeof n=="function"?n:n===void 0?rr:N(+n),t=typeof t=="function"?t:N(t===void 0?0:+t),e=typeof e=="function"?e:e===void 0?ir:N(+e);function s(f){var h,g,m,_=(f=te(f)).length,w,d=!1,S,M=new Array(_),v=new Array(_);for(o==null&&(a=u(S=c())),h=0;h<=_;++h){if(!(h<_&&i(w=f[h],h,f))===d)if(d=!d)g=h,a.areaStart(),a.lineStart();else{for(a.lineEnd(),a.lineStart(),m=h-1;m>=g;--m)a.point(M[m],v[m]);a.lineEnd(),a.areaEnd()}d&&(M[h]=+n(w,h,f),v[h]=+t(w,h,f),a.point(r?+r(w,h,f):M[h],e?+e(w,h,f):v[h]))}if(S)return a=null,S+""||null}function l(){return ao().defined(i).curve(u).context(o)}return s.x=function(f){return arguments.length?(n=typeof f=="function"?f:N(+f),r=null,s):n},s.x0=function(f){return arguments.length?(n=typeof f=="function"?f:N(+f),s):n},s.x1=function(f){return arguments.length?(r=f==null?null:typeof f=="function"?f:N(+f),s):r},s.y=function(f){return arguments.length?(t=typeof f=="function"?f:N(+f),e=null,s):t},s.y0=function(f){return arguments.length?(t=typeof f=="function"?f:N(+f),s):t},s.y1=function(f){return arguments.length?(e=f==null?null:typeof f=="function"?f:N(+f),s):e},s.lineX0=s.lineY0=function(){return l().x(n).y(t)},s.lineY1=function(){return l().x(n).y(e)},s.lineX1=function(){return l().x(r).y(t)},s.defined=function(f){return arguments.length?(i=typeof f=="function"?f:N(!!f),s):i},s.curve=function(f){return arguments.length?(u=f,o!=null&&(a=u(o)),s):u},s.context=function(f){return arguments.length?(f==null?o=a=null:a=u(o=f),s):o},s}function Js(n,t){return t<n?-1:t>n?1:t>=n?0:NaN}function Ks(n){return n}function bl(){var n=Ks,t=Js,e=null,r=N(0),i=N(wn),o=N(0);function u(a){var c,s=(a=te(a)).length,l,f,h=0,g=new Array(s),m=new Array(s),_=+r.apply(this,arguments),w=Math.min(wn,Math.max(-wn,i.apply(this,arguments)-_)),d,S=Math.min(Math.abs(w)/s,o.apply(this,arguments)),M=S*(w<0?-1:1),v;for(c=0;c<s;++c)(v=m[g[c]=c]=+n(a[c],c,a))>0&&(h+=v);for(t!=null?g.sort(function(T,x){return t(m[T],m[x])}):e!=null&&g.sort(function(T,x){return e(a[T],a[x])}),c=0,f=h?(w-s*M)/h:0;c<s;++c,_=d)l=g[c],v=m[l],d=_+(v>0?v*f:0)+M,m[l]={data:a[l],index:c,value:v,startAngle:_,endAngle:d,padAngle:S};return m}return u.value=function(a){return arguments.length?(n=typeof a=="function"?a:N(+a),u):n},u.sortValues=function(a){return arguments.length?(t=a,e=null,u):t},u.sort=function(a){return arguments.length?(e=a,t=null,u):e},u.startAngle=function(a){return arguments.length?(r=typeof a=="function"?a:N(+a),u):r},u.endAngle=function(a){return arguments.length?(i=typeof a=="function"?a:N(+a),u):i},u.padAngle=function(a){return arguments.length?(o=typeof a=="function"?a:N(+a),u):o},u}var so=or(er);function co(n){this._curve=n}co.prototype={areaStart:function(){this._curve.areaStart()},areaEnd:function(){this._curve.areaEnd()},lineStart:function(){this._curve.lineStart()},lineEnd:function(){this._curve.lineEnd()},point:function(n,t){this._curve.point(t*Math.sin(n),t*-Math.cos(n))}};function or(n){function t(e){return new co(n(e))}return t._curve=n,t}function lt(n){var t=n.curve;return n.angle=n.x,delete n.x,n.radius=n.y,delete n.y,n.curve=function(e){return arguments.length?t(or(e)):t()._curve},n}function Ml(){return lt(ao().curve(so))}function vl(){var n=Gs().curve(so),t=n.curve,e=n.lineX0,r=n.lineX1,i=n.lineY0,o=n.lineY1;return n.angle=n.x,delete n.x,n.startAngle=n.x0,delete n.x0,n.endAngle=n.x1,delete n.x1,n.radius=n.y,delete n.y,n.innerRadius=n.y0,delete n.y0,n.outerRadius=n.y1,delete n.y1,n.lineStartAngle=function(){return lt(e())},delete n.lineX0,n.lineEndAngle=function(){return lt(r())},delete n.lineX1,n.lineInnerRadius=function(){return lt(i())},delete n.lineY0,n.lineOuterRadius=function(){return lt(o())},delete n.lineY1,n.curve=function(u){return arguments.length?t(or(u)):t()._curve},n}function St(n,t){return[(t=+t)*Math.cos(n-=Math.PI/2),t*Math.sin(n)]}class fo{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e);break}}this._x0=t,this._y0=e}}class nc{constructor(t){this._context=t}lineStart(){this._point=0}lineEnd(){}point(t,e){if(t=+t,e=+e,this._point===0)this._point=1;else{const r=St(this._x0,this._y0),i=St(this._x0,this._y0=(this._y0+e)/2),o=St(t,this._y0),u=St(t,e);this._context.moveTo(...r),this._context.bezierCurveTo(...i,...o,...u)}this._x0=t,this._y0=e}}function tc(n){return new fo(n,!0)}function ec(n){return new fo(n,!1)}function rc(n){return new nc(n)}function ic(n){return n.source}function oc(n){return n.target}function ur(n){let t=ic,e=oc,r=rr,i=ir,o=null,u=null,a=bt(c);function c(){let s;const l=Qs.call(arguments),f=t.apply(this,l),h=e.apply(this,l);if(o==null&&(u=n(s=a())),u.lineStart(),l[0]=f,u.point(+r.apply(this,l),+i.apply(this,l)),l[0]=h,u.point(+r.apply(this,l),+i.apply(this,l)),u.lineEnd(),s)return u=null,s+""||null}return c.source=function(s){return arguments.length?(t=s,c):t},c.target=function(s){return arguments.length?(e=s,c):e},c.x=function(s){return arguments.length?(r=typeof s=="function"?s:N(+s),c):r},c.y=function(s){return arguments.length?(i=typeof s=="function"?s:N(+s),c):i},c.context=function(s){return arguments.length?(s==null?o=u=null:u=n(o=s),c):o},c}function Tl(){return ur(tc)}function kl(){return ur(ec)}function Nl(){const n=ur(rc);return n.angle=n.x,delete n.x,n.radius=n.y,delete n.y,n}const uc=R(3),ac={draw(n,t){const e=R(t+Wn(t/28,.75))*.59436,r=e/2,i=r*uc;n.moveTo(0,e),n.lineTo(0,-e),n.moveTo(-i,-r),n.lineTo(i,r),n.moveTo(-i,r),n.lineTo(i,-r)}},ar={draw(n,t){const e=R(t/bn);n.moveTo(e,0),n.arc(0,0,e,0,wn)}},sc={draw(n,t){const e=R(t/5)/2;n.moveTo(-3*e,-e),n.lineTo(-e,-e),n.lineTo(-e,-3*e),n.lineTo(e,-3*e),n.lineTo(e,-e),n.lineTo(3*e,-e),n.lineTo(3*e,e),n.lineTo(e,e),n.lineTo(e,3*e),n.lineTo(-e,3*e),n.lineTo(-e,e),n.lineTo(-3*e,e),n.closePath()}},lo=R(1/3),cc=lo*2,fc={draw(n,t){const e=R(t/cc),r=e*lo;n.moveTo(0,-e),n.lineTo(r,0),n.lineTo(0,e),n.lineTo(-r,0),n.closePath()}},lc={draw(n,t){const e=R(t)*.62625;n.moveTo(0,-e),n.lineTo(e,0),n.lineTo(0,e),n.lineTo(-e,0),n.closePath()}},hc={draw(n,t){const e=R(t-Wn(t/7,2))*.87559;n.moveTo(-e,0),n.lineTo(e,0),n.moveTo(0,e),n.lineTo(0,-e)}},gc={draw(n,t){const e=R(t),r=-e/2;n.rect(r,r,e,e)}},mc={draw(n,t){const e=R(t)*.4431;n.moveTo(e,e),n.lineTo(e,-e),n.lineTo(-e,-e),n.lineTo(-e,e),n.closePath()}},_c=.8908130915292852,ho=j(bn/10)/j(7*bn/10),pc=j(wn/10)*ho,dc=-gn(wn/10)*ho,yc={draw(n,t){const e=R(t*_c),r=pc*e,i=dc*e;n.moveTo(0,-e),n.lineTo(r,i);for(let o=1;o<5;++o){const u=wn*o/5,a=gn(u),c=j(u);n.lineTo(c*e,-a*e),n.lineTo(a*r-c*i,c*r+a*i)}n.closePath()}},de=R(3),xc={draw(n,t){const e=-R(t/(de*3));n.moveTo(0,e*2),n.lineTo(-de*e,-e),n.lineTo(de*e,-e),n.closePath()}},wc=R(3),bc={draw(n,t){const e=R(t)*.6824,r=e/2,i=e*wc/2;n.moveTo(0,-e),n.lineTo(i,r),n.lineTo(-i,r),n.closePath()}},tn=-.5,en=R(3)/2,Ue=1/R(12),Mc=(Ue/2+1)*3,vc={draw(n,t){const e=R(t/Mc),r=e/2,i=e*Ue,o=r,u=e*Ue+e,a=-o,c=u;n.moveTo(r,i),n.lineTo(o,u),n.lineTo(a,c),n.lineTo(tn*r-en*i,en*r+tn*i),n.lineTo(tn*o-en*u,en*o+tn*u),n.lineTo(tn*a-en*c,en*a+tn*c),n.lineTo(tn*r+en*i,tn*i-en*r),n.lineTo(tn*o+en*u,tn*u-en*o),n.lineTo(tn*a+en*c,tn*c-en*a),n.closePath()}},Tc={draw(n,t){const e=R(t-Wn(t/6,1.7))*.6189;n.moveTo(-e,-e),n.lineTo(e,e),n.moveTo(-e,e),n.lineTo(e,-e)}},Sl=[ar,sc,fc,gc,yc,xc,vc],$l=[ar,hc,Tc,bc,ac,mc,lc];function Cl(n,t){let e=null,r=bt(i);n=typeof n=="function"?n:N(n||ar),t=typeof t=="function"?t:N(t===void 0?64:+t);function i(){let o;if(e||(e=o=r()),n.apply(this,arguments).draw(e,+t.apply(this,arguments)),o)return e=null,o+""||null}return i.type=function(o){return arguments.length?(n=typeof o=="function"?o:N(o),i):n},i.size=function(o){return arguments.length?(t=typeof o=="function"?o:N(+o),i):t},i.context=function(o){return arguments.length?(e=o??null,i):e},i}function Mn(){}function Ot(n,t,e){n._context.bezierCurveTo((2*n._x0+n._x1)/3,(2*n._y0+n._y1)/3,(n._x0+2*n._x1)/3,(n._y0+2*n._y1)/3,(n._x0+4*n._x1+t)/6,(n._y0+4*n._y1+e)/6)}function ee(n){this._context=n}ee.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Ot(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(n,t){switch(n=+n,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(n,t):this._context.moveTo(n,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Ot(this,n,t);break}this._x0=this._x1,this._x1=n,this._y0=this._y1,this._y1=t}};function Al(n){return new ee(n)}function go(n){this._context=n}go.prototype={areaStart:Mn,areaEnd:Mn,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(n,t){switch(n=+n,t=+t,this._point){case 0:this._point=1,this._x2=n,this._y2=t;break;case 1:this._point=2,this._x3=n,this._y3=t;break;case 2:this._point=3,this._x4=n,this._y4=t,this._context.moveTo((this._x0+4*this._x1+n)/6,(this._y0+4*this._y1+t)/6);break;default:Ot(this,n,t);break}this._x0=this._x1,this._x1=n,this._y0=this._y1,this._y1=t}};function Ul(n){return new go(n)}function mo(n){this._context=n}mo.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(n,t){switch(n=+n,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var e=(this._x0+4*this._x1+n)/6,r=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(e,r):this._context.moveTo(e,r);break;case 3:this._point=4;default:Ot(this,n,t);break}this._x0=this._x1,this._x1=n,this._y0=this._y1,this._y1=t}};function Dl(n){return new mo(n)}function _o(n,t){this._basis=new ee(n),this._beta=t}_o.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var n=this._x,t=this._y,e=n.length-1;if(e>0)for(var r=n[0],i=t[0],o=n[e]-r,u=t[e]-i,a=-1,c;++a<=e;)c=a/e,this._basis.point(this._beta*n[a]+(1-this._beta)*(r+c*o),this._beta*t[a]+(1-this._beta)*(i+c*u));this._x=this._y=null,this._basis.lineEnd()},point:function(n,t){this._x.push(+n),this._y.push(+t)}};const El=function n(t){function e(r){return t===1?new ee(r):new _o(r,t)}return e.beta=function(r){return n(+r)},e}(.85);function Wt(n,t,e){n._context.bezierCurveTo(n._x1+n._k*(n._x2-n._x0),n._y1+n._k*(n._y2-n._y0),n._x2+n._k*(n._x1-t),n._y2+n._k*(n._y1-e),n._x2,n._y2)}function sr(n,t){this._context=n,this._k=(1-t)/6}sr.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:Wt(this,this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(n,t){switch(n=+n,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(n,t):this._context.moveTo(n,t);break;case 1:this._point=2,this._x1=n,this._y1=t;break;case 2:this._point=3;default:Wt(this,n,t);break}this._x0=this._x1,this._x1=this._x2,this._x2=n,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const Rl=function n(t){function e(r){return new sr(r,t)}return e.tension=function(r){return n(+r)},e}(0);function cr(n,t){this._context=n,this._k=(1-t)/6}cr.prototype={areaStart:Mn,areaEnd:Mn,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x3,this._y3),this._context.closePath();break}case 2:{this._context.lineTo(this._x3,this._y3),this._context.closePath();break}case 3:{this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5);break}}},point:function(n,t){switch(n=+n,t=+t,this._point){case 0:this._point=1,this._x3=n,this._y3=t;break;case 1:this._point=2,this._context.moveTo(this._x4=n,this._y4=t);break;case 2:this._point=3,this._x5=n,this._y5=t;break;default:Wt(this,n,t);break}this._x0=this._x1,this._x1=this._x2,this._x2=n,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const Yl=function n(t){function e(r){return new cr(r,t)}return e.tension=function(r){return n(+r)},e}(0);function fr(n,t){this._context=n,this._k=(1-t)/6}fr.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(n,t){switch(n=+n,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:Wt(this,n,t);break}this._x0=this._x1,this._x1=this._x2,this._x2=n,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const Fl=function n(t){function e(r){return new fr(r,t)}return e.tension=function(r){return n(+r)},e}(0);function lr(n,t,e){var r=n._x1,i=n._y1,o=n._x2,u=n._y2;if(n._l01_a>X){var a=2*n._l01_2a+3*n._l01_a*n._l12_a+n._l12_2a,c=3*n._l01_a*(n._l01_a+n._l12_a);r=(r*a-n._x0*n._l12_2a+n._x2*n._l01_2a)/c,i=(i*a-n._y0*n._l12_2a+n._y2*n._l01_2a)/c}if(n._l23_a>X){var s=2*n._l23_2a+3*n._l23_a*n._l12_a+n._l12_2a,l=3*n._l23_a*(n._l23_a+n._l12_a);o=(o*s+n._x1*n._l23_2a-t*n._l12_2a)/l,u=(u*s+n._y1*n._l23_2a-e*n._l12_2a)/l}n._context.bezierCurveTo(r,i,o,u,n._x2,n._y2)}function po(n,t){this._context=n,this._alpha=t}po.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(n,t){if(n=+n,t=+t,this._point){var e=this._x2-n,r=this._y2-t;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(n,t):this._context.moveTo(n,t);break;case 1:this._point=2;break;case 2:this._point=3;default:lr(this,n,t);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=n,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const ql=function n(t){function e(r){return t?new po(r,t):new sr(r,0)}return e.alpha=function(r){return n(+r)},e}(.5);function yo(n,t){this._context=n,this._alpha=t}yo.prototype={areaStart:Mn,areaEnd:Mn,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x3,this._y3),this._context.closePath();break}case 2:{this._context.lineTo(this._x3,this._y3),this._context.closePath();break}case 3:{this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5);break}}},point:function(n,t){if(n=+n,t=+t,this._point){var e=this._x2-n,r=this._y2-t;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=n,this._y3=t;break;case 1:this._point=2,this._context.moveTo(this._x4=n,this._y4=t);break;case 2:this._point=3,this._x5=n,this._y5=t;break;default:lr(this,n,t);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=n,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const Il=function n(t){function e(r){return t?new yo(r,t):new cr(r,0)}return e.alpha=function(r){return n(+r)},e}(.5);function xo(n,t){this._context=n,this._alpha=t}xo.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(n,t){if(n=+n,t=+t,this._point){var e=this._x2-n,r=this._y2-t;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:lr(this,n,t);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=n,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const Pl=function n(t){function e(r){return t?new xo(r,t):new fr(r,0)}return e.alpha=function(r){return n(+r)},e}(.5);function wo(n){this._context=n}wo.prototype={areaStart:Mn,areaEnd:Mn,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(n,t){n=+n,t=+t,this._point?this._context.lineTo(n,t):(this._point=1,this._context.moveTo(n,t))}};function Hl(n){return new wo(n)}function ri(n){return n<0?-1:1}function ii(n,t,e){var r=n._x1-n._x0,i=t-n._x1,o=(n._y1-n._y0)/(r||i<0&&-0),u=(e-n._y1)/(i||r<0&&-0),a=(o*i+u*r)/(r+i);return(ri(o)+ri(u))*Math.min(Math.abs(o),Math.abs(u),.5*Math.abs(a))||0}function oi(n,t){var e=n._x1-n._x0;return e?(3*(n._y1-n._y0)/e-t)/2:t}function ye(n,t,e){var r=n._x0,i=n._y0,o=n._x1,u=n._y1,a=(o-r)/3;n._context.bezierCurveTo(r+a,i+a*t,o-a,u-a*e,o,u)}function zt(n){this._context=n}zt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:ye(this,this._t0,oi(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(n,t){var e=NaN;if(n=+n,t=+t,!(n===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(n,t):this._context.moveTo(n,t);break;case 1:this._point=2;break;case 2:this._point=3,ye(this,oi(this,e=ii(this,n,t)),e);break;default:ye(this,this._t0,e=ii(this,n,t));break}this._x0=this._x1,this._x1=n,this._y0=this._y1,this._y1=t,this._t0=e}}};function bo(n){this._context=new Mo(n)}(bo.prototype=Object.create(zt.prototype)).point=function(n,t){zt.prototype.point.call(this,t,n)};function Mo(n){this._context=n}Mo.prototype={moveTo:function(n,t){this._context.moveTo(t,n)},closePath:function(){this._context.closePath()},lineTo:function(n,t){this._context.lineTo(t,n)},bezierCurveTo:function(n,t,e,r,i,o){this._context.bezierCurveTo(t,n,r,e,o,i)}};function Ll(n){return new zt(n)}function Ol(n){return new bo(n)}function vo(n){this._context=n}vo.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var n=this._x,t=this._y,e=n.length;if(e)if(this._line?this._context.lineTo(n[0],t[0]):this._context.moveTo(n[0],t[0]),e===2)this._context.lineTo(n[1],t[1]);else for(var r=ui(n),i=ui(t),o=0,u=1;u<e;++o,++u)this._context.bezierCurveTo(r[0][o],i[0][o],r[1][o],i[1][o],n[u],t[u]);(this._line||this._line!==0&&e===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(n,t){this._x.push(+n),this._y.push(+t)}};function ui(n){var t,e=n.length-1,r,i=new Array(e),o=new Array(e),u=new Array(e);for(i[0]=0,o[0]=2,u[0]=n[0]+2*n[1],t=1;t<e-1;++t)i[t]=1,o[t]=4,u[t]=4*n[t]+2*n[t+1];for(i[e-1]=2,o[e-1]=7,u[e-1]=8*n[e-1]+n[e],t=1;t<e;++t)r=i[t]/o[t-1],o[t]-=r,u[t]-=r*u[t-1];for(i[e-1]=u[e-1]/o[e-1],t=e-2;t>=0;--t)i[t]=(u[t]-i[t+1])/o[t];for(o[e-1]=(n[e]+i[e-1])/2,t=0;t<e-1;++t)o[t]=2*n[t+1]-i[t+1];return[i,o]}function Wl(n){return new vo(n)}function re(n,t){this._context=n,this._t=t}re.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(n,t){switch(n=+n,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(n,t):this._context.moveTo(n,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(n,t);else{var e=this._x*(1-this._t)+n*this._t;this._context.lineTo(e,this._y),this._context.lineTo(e,t)}break}}this._x=n,this._y=t}};function zl(n){return new re(n,.5)}function Xl(n){return new re(n,0)}function Vl(n){return new re(n,1)}function pt(n,t){if((u=n.length)>1)for(var e=1,r,i,o=n[t[0]],u,a=o.length;e<u;++e)for(i=o,o=n[t[e]],r=0;r<a;++r)o[r][1]+=o[r][0]=isNaN(i[r][1])?i[r][0]:i[r][1]}function dt(n){for(var t=n.length,e=new Array(t);--t>=0;)e[t]=t;return e}function kc(n,t){return n[t]}function Nc(n){const t=[];return t.key=n,t}function Zl(){var n=N([]),t=dt,e=pt,r=kc;function i(o){var u=Array.from(n.apply(this,arguments),Nc),a,c=u.length,s=-1,l;for(const f of o)for(a=0,++s;a<c;++a)(u[a][s]=[0,+r(f,u[a].key,s,o)]).data=f;for(a=0,l=te(t(u));a<c;++a)u[l[a]].index=a;return e(u,l),u}return i.keys=function(o){return arguments.length?(n=typeof o=="function"?o:N(Array.from(o)),i):n},i.value=function(o){return arguments.length?(r=typeof o=="function"?o:N(+o),i):r},i.order=function(o){return arguments.length?(t=o==null?dt:typeof o=="function"?o:N(Array.from(o)),i):t},i.offset=function(o){return arguments.length?(e=o??pt,i):e},i}function Bl(n,t){if((r=n.length)>0){for(var e,r,i=0,o=n[0].length,u;i<o;++i){for(u=e=0;e<r;++e)u+=n[e][i][1]||0;if(u)for(e=0;e<r;++e)n[e][i][1]/=u}pt(n,t)}}function jl(n,t){if((c=n.length)>0)for(var e,r=0,i,o,u,a,c,s=n[t[0]].length;r<s;++r)for(u=a=0,e=0;e<c;++e)(o=(i=n[t[e]][r])[1]-i[0])>0?(i[0]=u,i[1]=u+=o):o<0?(i[1]=a,i[0]=a+=o):(i[0]=0,i[1]=o)}function Ql(n,t){if((i=n.length)>0){for(var e=0,r=n[t[0]],i,o=r.length;e<o;++e){for(var u=0,a=0;u<i;++u)a+=n[u][e][1]||0;r[e][1]+=r[e][0]=-a/2}pt(n,t)}}function Gl(n,t){if(!(!((u=n.length)>0)||!((o=(i=n[t[0]]).length)>0))){for(var e=0,r=1,i,o,u;r<o;++r){for(var a=0,c=0,s=0;a<u;++a){for(var l=n[t[a]],f=l[r][1]||0,h=l[r-1][1]||0,g=(f-h)/2,m=0;m<a;++m){var _=n[t[m]],w=_[r][1]||0,d=_[r-1][1]||0;g+=w-d}c+=f,s+=g*f}i[r-1][1]+=i[r-1][0]=e,c&&(e-=s/c)}i[r-1][1]+=i[r-1][0]=e,pt(n,t)}}function Sc(n){var t=n.map($c);return dt(n).sort(function(e,r){return t[e]-t[r]})}function $c(n){for(var t=-1,e=0,r=n.length,i,o=-1/0;++t<r;)(i=+n[t][1])>o&&(o=i,e=t);return e}function Cc(n){var t=n.map(To);return dt(n).sort(function(e,r){return t[e]-t[r]})}function To(n){for(var t=0,e=-1,r=n.length,i;++e<r;)(i=+n[e][1])&&(t+=i);return t}function Jl(n){return Cc(n).reverse()}function Kl(n){var t=n.length,e,r,i=n.map(To),o=Sc(n),u=0,a=0,c=[],s=[];for(e=0;e<t;++e)r=o[e],u<a?(u+=i[r],c.push(r)):(a+=i[r],s.push(r));return s.reverse().concat(c)}function nh(n){return dt(n).reverse()}export{Gc as $,fi as A,F as B,De as C,Rc as D,Fc as E,Re as F,qc as G,Ic as H,ko as I,Fo as J,Pc as K,Hc as L,qo as M,Lc as N,Oc as O,Io as P,Wc as Q,zc as R,Po as S,Ho as T,Xc as U,Vc as V,Zc as W,Bc as X,pi as Y,jc as Z,Qc as _,gt as a,Uf as a$,be as a0,di as a1,Kc as a2,nf as a3,Lo as a4,Dt as a5,Go as a6,Qo as a7,Fe as a8,tf as a9,Ct as aA,zn as aB,Me as aC,ve as aD,Te as aE,bf as aF,wf as aG,zu as aH,Oe as aI,Wi as aJ,Wu as aK,_t as aL,Le as aM,Xu as aN,Vu as aO,Zu as aP,Tf as aQ,xu as aR,wu as aS,vu as aT,kf as aU,Nf as aV,Ei as aW,Tu as aX,He as aY,vf as aZ,Af as a_,ef as aa,tu as ab,jo as ac,rf as ad,of as ae,uf as af,eu as ag,af as ah,Zn as ai,we as aj,ru as ak,Yo as al,sf as am,cf as an,ff as ao,lf as ap,hf as aq,gf as ar,mf as as,gr as at,_f as au,pf as av,df as aw,yf as ax,uu as ay,xf as az,Sr as b,Ul as b$,Df as b0,Ef as b1,Rf as b2,Yf as b3,Eu as b4,qf as b5,Bt as b6,If as b7,Xi as b8,Pf as b9,wl as bA,Gs as bB,ao as bC,bl as bD,vl as bE,Ml as bF,St as bG,ur as bH,Tl as bI,kl as bJ,Nl as bK,Cl as bL,$l as bM,Sl as bN,ac as bO,ar as bP,sc as bQ,fc as bR,lc as bS,hc as bT,gc as bU,mc as bV,yc as bW,xc as bX,bc as bY,vc as bZ,Tc as b_,na as ba,Ku as bb,ua as bc,aa as bd,zi as be,Yr as bf,Bi as bg,Hf as bh,la as bi,ha as bj,ga as bk,ma as bl,pl as bm,dl as bn,Ys as bo,Fs as bp,io as bq,yl as br,qs as bs,Is as bt,Ps as bu,Hs as bv,oo as bw,xl as bx,Ls as by,Ju as bz,Uc as c,el as c$,Dl as c0,Al as c1,tc as c2,ec as c3,El as c4,Yl as c5,Fl as c6,Rl as c7,Il as c8,Pl as c9,Be as cA,Wf as cB,je as cC,zf as cD,Qe as cE,Xf as cF,Ge as cG,Vf as cH,wt as cI,Zf as cJ,Qt as cK,Bf as cL,ji as cM,jf as cN,Gt as cO,Qf as cP,Pt as cQ,Gf as cR,_a as cS,Jf as cT,pa as cU,Kf as cV,Qn as cW,nl as cX,da as cY,tl as cZ,ya as c_,ql as ca,Hl as cb,er as cc,Ll as cd,Ol as ce,Wl as cf,zl as cg,Vl as ch,Xl as ci,Zl as cj,Bl as ck,jl as cl,pt as cm,Ql as cn,Gl as co,Sc as cp,Cc as cq,Jl as cr,Kl as cs,dt as ct,nh as cu,I as cv,It as cw,Lf as cx,Cn as cy,Of as cz,Su as d,Jt as d0,rl as d1,Ht as d2,il as d3,xa as d4,ol as d5,wa as d6,ul as d7,Gn as d8,al as d9,ba as da,sl as db,Ma as dc,cl as dd,Je as de,fl as df,Ke as dg,ll as dh,dn as di,hl as dj,yn as dk,gl as dl,va as dm,Ta as dn,ka as dp,Na as dq,$s as dr,to as ds,Ss as dt,nr as du,eo as dv,Sa as dw,ml as dx,_l as dy,$f as e,Sf as f,tt as g,ie as h,fn as i,Ut as j,Yc as k,Jc as l,mr as m,Zo as n,Mf as o,Fu as p,Ff as q,nu as r,ke as s,Bo as t,Et as u,Cf as v,Jn as w,$o as x,Dc as y,Ec as z};
//# sourceMappingURL=charts-150f5ced.js.map
