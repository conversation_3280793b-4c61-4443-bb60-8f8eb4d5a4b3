{"version": 3, "file": "charts-150f5ced.js", "sources": ["../../node_modules/clsx/dist/clsx.mjs", "../../node_modules/d3-array/src/ascending.js", "../../node_modules/d3-array/src/descending.js", "../../node_modules/d3-array/src/bisector.js", "../../node_modules/d3-array/src/number.js", "../../node_modules/d3-array/src/bisect.js", "../../node_modules/d3-array/src/blur.js", "../../node_modules/d3-array/src/count.js", "../../node_modules/d3-array/src/cross.js", "../../node_modules/d3-array/src/cumsum.js", "../../node_modules/d3-array/src/variance.js", "../../node_modules/d3-array/src/deviation.js", "../../node_modules/d3-array/src/extent.js", "../../node_modules/d3-array/src/fsum.js", "../../node_modules/internmap/src/index.js", "../../node_modules/d3-array/src/identity.js", "../../node_modules/d3-array/src/group.js", "../../node_modules/d3-array/src/permute.js", "../../node_modules/d3-array/src/sort.js", "../../node_modules/d3-array/src/groupSort.js", "../../node_modules/d3-array/src/array.js", "../../node_modules/d3-array/src/constant.js", "../../node_modules/d3-array/src/ticks.js", "../../node_modules/d3-array/src/nice.js", "../../node_modules/d3-array/src/threshold/sturges.js", "../../node_modules/d3-array/src/bin.js", "../../node_modules/d3-array/src/max.js", "../../node_modules/d3-array/src/maxIndex.js", "../../node_modules/d3-array/src/min.js", "../../node_modules/d3-array/src/minIndex.js", "../../node_modules/d3-array/src/quickselect.js", "../../node_modules/d3-array/src/greatest.js", "../../node_modules/d3-array/src/quantile.js", "../../node_modules/d3-array/src/threshold/freedmanDiaconis.js", "../../node_modules/d3-array/src/threshold/scott.js", "../../node_modules/d3-array/src/mean.js", "../../node_modules/d3-array/src/median.js", "../../node_modules/d3-array/src/merge.js", "../../node_modules/d3-array/src/mode.js", "../../node_modules/d3-array/src/pairs.js", "../../node_modules/d3-array/src/range.js", "../../node_modules/d3-array/src/rank.js", "../../node_modules/d3-array/src/least.js", "../../node_modules/d3-array/src/leastIndex.js", "../../node_modules/d3-array/src/greatestIndex.js", "../../node_modules/d3-array/src/scan.js", "../../node_modules/d3-array/src/shuffle.js", "../../node_modules/d3-array/src/sum.js", "../../node_modules/d3-array/src/transpose.js", "../../node_modules/d3-array/src/zip.js", "../../node_modules/d3-array/src/every.js", "../../node_modules/d3-array/src/some.js", "../../node_modules/d3-array/src/filter.js", "../../node_modules/d3-array/src/map.js", "../../node_modules/d3-array/src/reduce.js", "../../node_modules/d3-array/src/reverse.js", "../../node_modules/d3-array/src/difference.js", "../../node_modules/d3-array/src/disjoint.js", "../../node_modules/d3-array/src/intersection.js", "../../node_modules/d3-array/src/superset.js", "../../node_modules/d3-array/src/subset.js", "../../node_modules/d3-array/src/union.js", "../../node_modules/d3-color/src/define.js", "../../node_modules/d3-color/src/color.js", "../../node_modules/d3-color/src/math.js", "../../node_modules/d3-color/src/lab.js", "../../node_modules/d3-color/src/cubehelix.js", "../../node_modules/d3-interpolate/src/basis.js", "../../node_modules/d3-interpolate/src/basisClosed.js", "../../node_modules/d3-interpolate/src/constant.js", "../../node_modules/d3-interpolate/src/color.js", "../../node_modules/d3-interpolate/src/rgb.js", "../../node_modules/d3-interpolate/src/numberArray.js", "../../node_modules/d3-interpolate/src/array.js", "../../node_modules/d3-interpolate/src/date.js", "../../node_modules/d3-interpolate/src/number.js", "../../node_modules/d3-interpolate/src/object.js", "../../node_modules/d3-interpolate/src/string.js", "../../node_modules/d3-interpolate/src/value.js", "../../node_modules/d3-interpolate/src/discrete.js", "../../node_modules/d3-interpolate/src/hue.js", "../../node_modules/d3-interpolate/src/round.js", "../../node_modules/d3-interpolate/src/transform/decompose.js", "../../node_modules/d3-interpolate/src/transform/parse.js", "../../node_modules/d3-interpolate/src/transform/index.js", "../../node_modules/d3-interpolate/src/zoom.js", "../../node_modules/d3-interpolate/src/hsl.js", "../../node_modules/d3-interpolate/src/lab.js", "../../node_modules/d3-interpolate/src/hcl.js", "../../node_modules/d3-interpolate/src/cubehelix.js", "../../node_modules/d3-interpolate/src/piecewise.js", "../../node_modules/d3-interpolate/src/quantize.js", "../../node_modules/d3-path/src/path.js", "../../node_modules/d3-format/src/formatDecimal.js", "../../node_modules/d3-format/src/exponent.js", "../../node_modules/d3-format/src/formatGroup.js", "../../node_modules/d3-format/src/formatNumerals.js", "../../node_modules/d3-format/src/formatSpecifier.js", "../../node_modules/d3-format/src/formatTrim.js", "../../node_modules/d3-format/src/formatPrefixAuto.js", "../../node_modules/d3-format/src/formatRounded.js", "../../node_modules/d3-format/src/formatTypes.js", "../../node_modules/d3-format/src/identity.js", "../../node_modules/d3-format/src/locale.js", "../../node_modules/d3-format/src/defaultLocale.js", "../../node_modules/d3-format/src/precisionFixed.js", "../../node_modules/d3-format/src/precisionPrefix.js", "../../node_modules/d3-format/src/precisionRound.js", "../../node_modules/d3-scale/src/init.js", "../../node_modules/d3-scale/src/ordinal.js", "../../node_modules/d3-scale/src/band.js", "../../node_modules/d3-scale/src/constant.js", "../../node_modules/d3-scale/src/number.js", "../../node_modules/d3-scale/src/continuous.js", "../../node_modules/d3-scale/src/tickFormat.js", "../../node_modules/d3-scale/src/linear.js", "../../node_modules/d3-scale/src/identity.js", "../../node_modules/d3-scale/src/nice.js", "../../node_modules/d3-scale/src/log.js", "../../node_modules/d3-scale/src/symlog.js", "../../node_modules/d3-scale/src/pow.js", "../../node_modules/d3-scale/src/radial.js", "../../node_modules/d3-scale/src/quantile.js", "../../node_modules/d3-scale/src/quantize.js", "../../node_modules/d3-scale/src/threshold.js", "../../node_modules/d3-time/src/interval.js", "../../node_modules/d3-time/src/millisecond.js", "../../node_modules/d3-time/src/duration.js", "../../node_modules/d3-time/src/second.js", "../../node_modules/d3-time/src/minute.js", "../../node_modules/d3-time/src/hour.js", "../../node_modules/d3-time/src/day.js", "../../node_modules/d3-time/src/week.js", "../../node_modules/d3-time/src/month.js", "../../node_modules/d3-time/src/year.js", "../../node_modules/d3-time/src/ticks.js", "../../node_modules/d3-time-format/src/locale.js", "../../node_modules/d3-time-format/src/defaultLocale.js", "../../node_modules/d3-time-format/src/isoFormat.js", "../../node_modules/d3-time-format/src/isoParse.js", "../../node_modules/d3-scale/src/time.js", "../../node_modules/d3-scale/src/utcTime.js", "../../node_modules/d3-scale/src/sequential.js", "../../node_modules/d3-scale/src/sequentialQuantile.js", "../../node_modules/d3-scale/src/diverging.js", "../../node_modules/d3-shape/src/constant.js", "../../node_modules/d3-shape/src/math.js", "../../node_modules/d3-shape/src/path.js", "../../node_modules/d3-shape/src/arc.js", "../../node_modules/d3-shape/src/array.js", "../../node_modules/d3-shape/src/curve/linear.js", "../../node_modules/d3-shape/src/point.js", "../../node_modules/d3-shape/src/line.js", "../../node_modules/d3-shape/src/area.js", "../../node_modules/d3-shape/src/descending.js", "../../node_modules/d3-shape/src/identity.js", "../../node_modules/d3-shape/src/pie.js", "../../node_modules/d3-shape/src/curve/radial.js", "../../node_modules/d3-shape/src/lineRadial.js", "../../node_modules/d3-shape/src/areaRadial.js", "../../node_modules/d3-shape/src/pointRadial.js", "../../node_modules/d3-shape/src/curve/bump.js", "../../node_modules/d3-shape/src/link.js", "../../node_modules/d3-shape/src/symbol/asterisk.js", "../../node_modules/d3-shape/src/symbol/circle.js", "../../node_modules/d3-shape/src/symbol/cross.js", "../../node_modules/d3-shape/src/symbol/diamond.js", "../../node_modules/d3-shape/src/symbol/diamond2.js", "../../node_modules/d3-shape/src/symbol/plus.js", "../../node_modules/d3-shape/src/symbol/square.js", "../../node_modules/d3-shape/src/symbol/square2.js", "../../node_modules/d3-shape/src/symbol/star.js", "../../node_modules/d3-shape/src/symbol/triangle.js", "../../node_modules/d3-shape/src/symbol/triangle2.js", "../../node_modules/d3-shape/src/symbol/wye.js", "../../node_modules/d3-shape/src/symbol/times.js", "../../node_modules/d3-shape/src/symbol.js", "../../node_modules/d3-shape/src/noop.js", "../../node_modules/d3-shape/src/curve/basis.js", "../../node_modules/d3-shape/src/curve/basisClosed.js", "../../node_modules/d3-shape/src/curve/basisOpen.js", "../../node_modules/d3-shape/src/curve/bundle.js", "../../node_modules/d3-shape/src/curve/cardinal.js", "../../node_modules/d3-shape/src/curve/cardinalClosed.js", "../../node_modules/d3-shape/src/curve/cardinalOpen.js", "../../node_modules/d3-shape/src/curve/catmullRom.js", "../../node_modules/d3-shape/src/curve/catmullRomClosed.js", "../../node_modules/d3-shape/src/curve/catmullRomOpen.js", "../../node_modules/d3-shape/src/curve/linearClosed.js", "../../node_modules/d3-shape/src/curve/monotone.js", "../../node_modules/d3-shape/src/curve/natural.js", "../../node_modules/d3-shape/src/curve/step.js", "../../node_modules/d3-shape/src/offset/none.js", "../../node_modules/d3-shape/src/order/none.js", "../../node_modules/d3-shape/src/stack.js", "../../node_modules/d3-shape/src/offset/expand.js", "../../node_modules/d3-shape/src/offset/diverging.js", "../../node_modules/d3-shape/src/offset/silhouette.js", "../../node_modules/d3-shape/src/offset/wiggle.js", "../../node_modules/d3-shape/src/order/appearance.js", "../../node_modules/d3-shape/src/order/ascending.js", "../../node_modules/d3-shape/src/order/descending.js", "../../node_modules/d3-shape/src/order/insideOut.js", "../../node_modules/d3-shape/src/order/reverse.js"], "sourcesContent": ["function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;", "export default function ascending(a, b) {\n  return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n", "export default function descending(a, b) {\n  return a == null || b == null ? NaN\n    : b < a ? -1\n    : b > a ? 1\n    : b >= a ? 0\n    : NaN;\n}\n", "import ascending from \"./ascending.js\";\nimport descending from \"./descending.js\";\n\nexport default function bisector(f) {\n  let compare1, compare2, delta;\n\n  // If an accessor is specified, promote it to a comparator. In this case we\n  // can test whether the search value is (self-) comparable. We can’t do this\n  // for a comparator (except for specific, known comparators) because we can’t\n  // tell if the comparator is symmetric, and an asymmetric comparator can’t be\n  // used to test whether a single value is comparable.\n  if (f.length !== 2) {\n    compare1 = ascending;\n    compare2 = (d, x) => ascending(f(d), x);\n    delta = (d, x) => f(d) - x;\n  } else {\n    compare1 = f === ascending || f === descending ? f : zero;\n    compare2 = f;\n    delta = f;\n  }\n\n  function left(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) < 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function right(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) <= 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function center(a, x, lo = 0, hi = a.length) {\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n\n  return {left, center, right};\n}\n\nfunction zero() {\n  return 0;\n}\n", "export default function number(x) {\n  return x === null ? NaN : +x;\n}\n\nexport function* numbers(values, valueof) {\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  }\n}\n", "import ascending from \"./ascending.js\";\nimport bisector from \"./bisector.js\";\nimport number from \"./number.js\";\n\nconst ascendingBisect = bisector(ascending);\nexport const bisectRight = ascendingBisect.right;\nexport const bisectLeft = ascendingBisect.left;\nexport const bisectCenter = bisector(number).center;\nexport default bisectRight;\n", "export function blur(values, r) {\n  if (!((r = +r) >= 0)) throw new RangeError(\"invalid r\");\n  let length = values.length;\n  if (!((length = Math.floor(length)) >= 0)) throw new RangeError(\"invalid length\");\n  if (!length || !r) return values;\n  const blur = blurf(r);\n  const temp = values.slice();\n  blur(values, temp, 0, length, 1);\n  blur(temp, values, 0, length, 1);\n  blur(values, temp, 0, length, 1);\n  return values;\n}\n\nexport const blur2 = Blur2(blurf);\n\nexport const blurImage = Blur2(blurfImage);\n\nfunction Blur2(blur) {\n  return function(data, rx, ry = rx) {\n    if (!((rx = +rx) >= 0)) throw new RangeError(\"invalid rx\");\n    if (!((ry = +ry) >= 0)) throw new RangeError(\"invalid ry\");\n    let {data: values, width, height} = data;\n    if (!((width = Math.floor(width)) >= 0)) throw new RangeError(\"invalid width\");\n    if (!((height = Math.floor(height !== undefined ? height : values.length / width)) >= 0)) throw new RangeError(\"invalid height\");\n    if (!width || !height || (!rx && !ry)) return data;\n    const blurx = rx && blur(rx);\n    const blury = ry && blur(ry);\n    const temp = values.slice();\n    if (blurx && blury) {\n      blurh(blurx, temp, values, width, height);\n      blurh(blurx, values, temp, width, height);\n      blurh(blurx, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n      blurv(blury, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n    } else if (blurx) {\n      blurh(blurx, values, temp, width, height);\n      blurh(blurx, temp, values, width, height);\n      blurh(blurx, values, temp, width, height);\n    } else if (blury) {\n      blurv(blury, values, temp, width, height);\n      blurv(blury, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n    }\n    return data;\n  };\n}\n\nfunction blurh(blur, T, S, w, h) {\n  for (let y = 0, n = w * h; y < n;) {\n    blur(T, S, y, y += w, 1);\n  }\n}\n\nfunction blurv(blur, T, S, w, h) {\n  for (let x = 0, n = w * h; x < w; ++x) {\n    blur(T, S, x, x + n, w);\n  }\n}\n\nfunction blurfImage(radius) {\n  const blur = blurf(radius);\n  return (T, S, start, stop, step) => {\n    start <<= 2, stop <<= 2, step <<= 2;\n    blur(T, S, start + 0, stop + 0, step);\n    blur(T, S, start + 1, stop + 1, step);\n    blur(T, S, start + 2, stop + 2, step);\n    blur(T, S, start + 3, stop + 3, step);\n  };\n}\n\n// Given a target array T, a source array S, sets each value T[i] to the average\n// of {S[i - r], …, S[i], …, S[i + r]}, where r = ⌊radius⌋, start <= i < stop,\n// for each i, i + step, i + 2 * step, etc., and where S[j] is clamped between\n// S[start] (inclusive) and S[stop] (exclusive). If the given radius is not an\n// integer, S[i - r - 1] and S[i + r + 1] are added to the sum, each weighted\n// according to r - ⌊radius⌋.\nfunction blurf(radius) {\n  const radius0 = Math.floor(radius);\n  if (radius0 === radius) return bluri(radius);\n  const t = radius - radius0;\n  const w = 2 * radius + 1;\n  return (T, S, start, stop, step) => { // stop must be aligned!\n    if (!((stop -= step) >= start)) return; // inclusive stop\n    let sum = radius0 * S[start];\n    const s0 = step * radius0;\n    const s1 = s0 + step;\n    for (let i = start, j = start + s0; i < j; i += step) {\n      sum += S[Math.min(stop, i)];\n    }\n    for (let i = start, j = stop; i <= j; i += step) {\n      sum += S[Math.min(stop, i + s0)];\n      T[i] = (sum + t * (S[Math.max(start, i - s1)] + S[Math.min(stop, i + s1)])) / w;\n      sum -= S[Math.max(start, i - s0)];\n    }\n  };\n}\n\n// Like blurf, but optimized for integer radius.\nfunction bluri(radius) {\n  const w = 2 * radius + 1;\n  return (T, S, start, stop, step) => { // stop must be aligned!\n    if (!((stop -= step) >= start)) return; // inclusive stop\n    let sum = radius * S[start];\n    const s = step * radius;\n    for (let i = start, j = start + s; i < j; i += step) {\n      sum += S[Math.min(stop, i)];\n    }\n    for (let i = start, j = stop; i <= j; i += step) {\n      sum += S[Math.min(stop, i + s)];\n      T[i] = sum / w;\n      sum -= S[Math.max(start, i - s)];\n    }\n  };\n}\n", "export default function count(values, valueof) {\n  let count = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  }\n  return count;\n}\n", "function length(array) {\n  return array.length | 0;\n}\n\nfunction empty(length) {\n  return !(length > 0);\n}\n\nfunction arrayify(values) {\n  return typeof values !== \"object\" || \"length\" in values ? values : Array.from(values);\n}\n\nfunction reducer(reduce) {\n  return values => reduce(...values);\n}\n\nexport default function cross(...values) {\n  const reduce = typeof values[values.length - 1] === \"function\" && reducer(values.pop());\n  values = values.map(arrayify);\n  const lengths = values.map(length);\n  const j = values.length - 1;\n  const index = new Array(j + 1).fill(0);\n  const product = [];\n  if (j < 0 || lengths.some(empty)) return product;\n  while (true) {\n    product.push(index.map((j, i) => values[i][j]));\n    let i = j;\n    while (++index[i] === lengths[i]) {\n      if (i === 0) return reduce ? product.map(reduce) : product;\n      index[i--] = 0;\n    }\n  }\n}\n", "export default function cumsum(values, valueof) {\n  var sum = 0, index = 0;\n  return Float64Array.from(values, valueof === undefined\n    ? v => (sum += +v || 0)\n    : v => (sum += +valueof(v, index++, values) || 0));\n}", "export default function variance(values, valueof) {\n  let count = 0;\n  let delta;\n  let mean = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  }\n  if (count > 1) return sum / (count - 1);\n}\n", "import variance from \"./variance.js\";\n\nexport default function deviation(values, valueof) {\n  const v = variance(values, valueof);\n  return v ? Math.sqrt(v) : v;\n}\n", "export default function extent(values, valueof) {\n  let min;\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  }\n  return [min, max];\n}\n", "// https://github.com/python/cpython/blob/a74eea238f5baba15797e2e8b570d153bc8690a7/Modules/mathmodule.c#L1423\nexport class Adder {\n  constructor() {\n    this._partials = new Float64Array(32);\n    this._n = 0;\n  }\n  add(x) {\n    const p = this._partials;\n    let i = 0;\n    for (let j = 0; j < this._n && j < 32; j++) {\n      const y = p[j],\n        hi = x + y,\n        lo = Math.abs(x) < Math.abs(y) ? x - (hi - y) : y - (hi - x);\n      if (lo) p[i++] = lo;\n      x = hi;\n    }\n    p[i] = x;\n    this._n = i + 1;\n    return this;\n  }\n  valueOf() {\n    const p = this._partials;\n    let n = this._n, x, y, lo, hi = 0;\n    if (n > 0) {\n      hi = p[--n];\n      while (n > 0) {\n        x = hi;\n        y = p[--n];\n        hi = x + y;\n        lo = y - (hi - x);\n        if (lo) break;\n      }\n      if (n > 0 && ((lo < 0 && p[n - 1] < 0) || (lo > 0 && p[n - 1] > 0))) {\n        y = lo * 2;\n        x = hi + y;\n        if (y == x - hi) hi = x;\n      }\n    }\n    return hi;\n  }\n}\n\nexport function fsum(values, valueof) {\n  const adder = new Adder();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        adder.add(value);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        adder.add(value);\n      }\n    }\n  }\n  return +adder;\n}\n\nexport function fcumsum(values, valueof) {\n  const adder = new Adder();\n  let index = -1;\n  return Float64Array.from(values, valueof === undefined\n      ? v => adder.add(+v || 0)\n      : v => adder.add(+valueof(v, ++index, values) || 0)\n  );\n}\n", "export class InternMap extends Map {\n  constructor(entries, key = keyof) {\n    super();\n    Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});\n    if (entries != null) for (const [key, value] of entries) this.set(key, value);\n  }\n  get(key) {\n    return super.get(intern_get(this, key));\n  }\n  has(key) {\n    return super.has(intern_get(this, key));\n  }\n  set(key, value) {\n    return super.set(intern_set(this, key), value);\n  }\n  delete(key) {\n    return super.delete(intern_delete(this, key));\n  }\n}\n\nexport class InternSet extends Set {\n  constructor(values, key = keyof) {\n    super();\n    Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});\n    if (values != null) for (const value of values) this.add(value);\n  }\n  has(value) {\n    return super.has(intern_get(this, value));\n  }\n  add(value) {\n    return super.add(intern_set(this, value));\n  }\n  delete(value) {\n    return super.delete(intern_delete(this, value));\n  }\n}\n\nfunction intern_get({_intern, _key}, value) {\n  const key = _key(value);\n  return _intern.has(key) ? _intern.get(key) : value;\n}\n\nfunction intern_set({_intern, _key}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) return _intern.get(key);\n  _intern.set(key, value);\n  return value;\n}\n\nfunction intern_delete({_intern, _key}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) {\n    value = _intern.get(key);\n    _intern.delete(key);\n  }\n  return value;\n}\n\nfunction keyof(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}\n", "export default function identity(x) {\n  return x;\n}\n", "import {InternMap} from \"internmap\";\nimport identity from \"./identity.js\";\n\nexport default function group(values, ...keys) {\n  return nest(values, identity, identity, keys);\n}\n\nexport function groups(values, ...keys) {\n  return nest(values, Array.from, identity, keys);\n}\n\nfunction flatten(groups, keys) {\n  for (let i = 1, n = keys.length; i < n; ++i) {\n    groups = groups.flatMap(g => g.pop().map(([key, value]) => [...g, key, value]));\n  }\n  return groups;\n}\n\nexport function flatGroup(values, ...keys) {\n  return flatten(groups(values, ...keys), keys);\n}\n\nexport function flatRollup(values, reduce, ...keys) {\n  return flatten(rollups(values, reduce, ...keys), keys);\n}\n\nexport function rollup(values, reduce, ...keys) {\n  return nest(values, identity, reduce, keys);\n}\n\nexport function rollups(values, reduce, ...keys) {\n  return nest(values, Array.from, reduce, keys);\n}\n\nexport function index(values, ...keys) {\n  return nest(values, identity, unique, keys);\n}\n\nexport function indexes(values, ...keys) {\n  return nest(values, Array.from, unique, keys);\n}\n\nfunction unique(values) {\n  if (values.length !== 1) throw new Error(\"duplicate key\");\n  return values[0];\n}\n\nfunction nest(values, map, reduce, keys) {\n  return (function regroup(values, i) {\n    if (i >= keys.length) return reduce(values);\n    const groups = new InternMap();\n    const keyof = keys[i++];\n    let index = -1;\n    for (const value of values) {\n      const key = keyof(value, ++index, values);\n      const group = groups.get(key);\n      if (group) group.push(value);\n      else groups.set(key, [value]);\n    }\n    for (const [key, values] of groups) {\n      groups.set(key, regroup(values, i));\n    }\n    return map(groups);\n  })(values, 0);\n}\n", "export default function permute(source, keys) {\n  return Array.from(keys, key => source[key]);\n}\n", "import ascending from \"./ascending.js\";\nimport permute from \"./permute.js\";\n\nexport default function sort(values, ...F) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  values = Array.from(values);\n  let [f] = F;\n  if ((f && f.length !== 2) || F.length > 1) {\n    const index = Uint32Array.from(values, (d, i) => i);\n    if (F.length > 1) {\n      F = F.map(f => values.map(f));\n      index.sort((i, j) => {\n        for (const f of F) {\n          const c = ascendingDefined(f[i], f[j]);\n          if (c) return c;\n        }\n      });\n    } else {\n      f = values.map(f);\n      index.sort((i, j) => ascendingDefined(f[i], f[j]));\n    }\n    return permute(values, index);\n  }\n  return values.sort(compareDefined(f));\n}\n\nexport function compareDefined(compare = ascending) {\n  if (compare === ascending) return ascendingDefined;\n  if (typeof compare !== \"function\") throw new TypeError(\"compare is not a function\");\n  return (a, b) => {\n    const x = compare(a, b);\n    if (x || x === 0) return x;\n    return (compare(b, b) === 0) - (compare(a, a) === 0);\n  };\n}\n\nexport function ascendingDefined(a, b) {\n  return (a == null || !(a >= a)) - (b == null || !(b >= b)) || (a < b ? -1 : a > b ? 1 : 0);\n}\n", "import ascending from \"./ascending.js\";\nimport group, {rollup} from \"./group.js\";\nimport sort from \"./sort.js\";\n\nexport default function groupSort(values, reduce, key) {\n  return (reduce.length !== 2\n    ? sort(rollup(values, reduce, key), (([ak, av], [bk, bv]) => ascending(av, bv) || ascending(ak, bk)))\n    : sort(group(values, key), (([ak, av], [bk, bv]) => reduce(av, bv) || ascending(ak, bk))))\n    .map(([key]) => key);\n}\n", "var array = Array.prototype;\n\nexport var slice = array.slice;\nexport var map = array.map;\n", "export default function constant(x) {\n  return () => x;\n}\n", "const e10 = Math.sqrt(50),\n    e5 = Math.sqrt(10),\n    e2 = Math.sqrt(2);\n\nfunction tickSpec(start, stop, count) {\n  const step = (stop - start) / Math.max(0, count),\n      power = Math.floor(Math.log10(step)),\n      error = step / Math.pow(10, power),\n      factor = error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1;\n  let i1, i2, inc;\n  if (power < 0) {\n    inc = Math.pow(10, -power) / factor;\n    i1 = Math.round(start * inc);\n    i2 = Math.round(stop * inc);\n    if (i1 / inc < start) ++i1;\n    if (i2 / inc > stop) --i2;\n    inc = -inc;\n  } else {\n    inc = Math.pow(10, power) * factor;\n    i1 = Math.round(start / inc);\n    i2 = Math.round(stop / inc);\n    if (i1 * inc < start) ++i1;\n    if (i2 * inc > stop) --i2;\n  }\n  if (i2 < i1 && 0.5 <= count && count < 2) return tickSpec(start, stop, count * 2);\n  return [i1, i2, inc];\n}\n\nexport default function ticks(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  if (!(count > 0)) return [];\n  if (start === stop) return [start];\n  const reverse = stop < start, [i1, i2, inc] = reverse ? tickSpec(stop, start, count) : tickSpec(start, stop, count);\n  if (!(i2 >= i1)) return [];\n  const n = i2 - i1 + 1, ticks = new Array(n);\n  if (reverse) {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) / -inc;\n    else for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) * inc;\n  } else {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) / -inc;\n    else for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) * inc;\n  }\n  return ticks;\n}\n\nexport function tickIncrement(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  return tickSpec(start, stop, count)[2];\n}\n\nexport function tickStep(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  const reverse = stop < start, inc = reverse ? tickIncrement(stop, start, count) : tickIncrement(start, stop, count);\n  return (reverse ? -1 : 1) * (inc < 0 ? 1 / -inc : inc);\n}\n", "import {tickIncrement} from \"./ticks.js\";\n\nexport default function nice(start, stop, count) {\n  let prestep;\n  while (true) {\n    const step = tickIncrement(start, stop, count);\n    if (step === prestep || step === 0 || !isFinite(step)) {\n      return [start, stop];\n    } else if (step > 0) {\n      start = Math.floor(start / step) * step;\n      stop = Math.ceil(stop / step) * step;\n    } else if (step < 0) {\n      start = Math.ceil(start * step) / step;\n      stop = Math.floor(stop * step) / step;\n    }\n    prestep = step;\n  }\n}\n", "import count from \"../count.js\";\n\nexport default function thresholdSturges(values) {\n  return Math.max(1, Math.ceil(Math.log(count(values)) / Math.LN2) + 1);\n}\n", "import {slice} from \"./array.js\";\nimport bisect from \"./bisect.js\";\nimport constant from \"./constant.js\";\nimport extent from \"./extent.js\";\nimport identity from \"./identity.js\";\nimport nice from \"./nice.js\";\nimport ticks, {tickIncrement} from \"./ticks.js\";\nimport sturges from \"./threshold/sturges.js\";\n\nexport default function bin() {\n  var value = identity,\n      domain = extent,\n      threshold = sturges;\n\n  function histogram(data) {\n    if (!Array.isArray(data)) data = Array.from(data);\n\n    var i,\n        n = data.length,\n        x,\n        step,\n        values = new Array(n);\n\n    for (i = 0; i < n; ++i) {\n      values[i] = value(data[i], i, data);\n    }\n\n    var xz = domain(values),\n        x0 = xz[0],\n        x1 = xz[1],\n        tz = threshold(values, x0, x1);\n\n    // Convert number of thresholds into uniform thresholds, and nice the\n    // default domain accordingly.\n    if (!Array.isArray(tz)) {\n      const max = x1, tn = +tz;\n      if (domain === extent) [x0, x1] = nice(x0, x1, tn);\n      tz = ticks(x0, x1, tn);\n\n      // If the domain is aligned with the first tick (which it will by\n      // default), then we can use quantization rather than bisection to bin\n      // values, which is substantially faster.\n      if (tz[0] <= x0) step = tickIncrement(x0, x1, tn);\n\n      // If the last threshold is coincident with the domain’s upper bound, the\n      // last bin will be zero-width. If the default domain is used, and this\n      // last threshold is coincident with the maximum input value, we can\n      // extend the niced upper bound by one tick to ensure uniform bin widths;\n      // otherwise, we simply remove the last threshold. Note that we don’t\n      // coerce values or the domain to numbers, and thus must be careful to\n      // compare order (>=) rather than strict equality (===)!\n      if (tz[tz.length - 1] >= x1) {\n        if (max >= x1 && domain === extent) {\n          const step = tickIncrement(x0, x1, tn);\n          if (isFinite(step)) {\n            if (step > 0) {\n              x1 = (Math.floor(x1 / step) + 1) * step;\n            } else if (step < 0) {\n              x1 = (Math.ceil(x1 * -step) + 1) / -step;\n            }\n          }\n        } else {\n          tz.pop();\n        }\n      }\n    }\n\n    // Remove any thresholds outside the domain.\n    // Be careful not to mutate an array owned by the user!\n    var m = tz.length, a = 0, b = m;\n    while (tz[a] <= x0) ++a;\n    while (tz[b - 1] > x1) --b;\n    if (a || b < m) tz = tz.slice(a, b), m = b - a;\n\n    var bins = new Array(m + 1),\n        bin;\n\n    // Initialize bins.\n    for (i = 0; i <= m; ++i) {\n      bin = bins[i] = [];\n      bin.x0 = i > 0 ? tz[i - 1] : x0;\n      bin.x1 = i < m ? tz[i] : x1;\n    }\n\n    // Assign data to bins by value, ignoring any outside the domain.\n    if (isFinite(step)) {\n      if (step > 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            bins[Math.min(m, Math.floor((x - x0) / step))].push(data[i]);\n          }\n        }\n      } else if (step < 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            const j = Math.floor((x0 - x) * step);\n            bins[Math.min(m, j + (tz[j] <= x))].push(data[i]); // handle off-by-one due to rounding\n          }\n        }\n      }\n    } else {\n      for (i = 0; i < n; ++i) {\n        if ((x = values[i]) != null && x0 <= x && x <= x1) {\n          bins[bisect(tz, x, 0, m)].push(data[i]);\n        }\n      }\n    }\n\n    return bins;\n  }\n\n  histogram.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(_), histogram) : value;\n  };\n\n  histogram.domain = function(_) {\n    return arguments.length ? (domain = typeof _ === \"function\" ? _ : constant([_[0], _[1]]), histogram) : domain;\n  };\n\n  histogram.thresholds = function(_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : constant(Array.isArray(_) ? slice.call(_) : _), histogram) : threshold;\n  };\n\n  return histogram;\n}\n", "export default function max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}\n", "export default function maxIndex(values, valueof) {\n  let max;\n  let maxIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value, maxIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value, maxIndex = index;\n      }\n    }\n  }\n  return maxIndex;\n}\n", "export default function min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}\n", "export default function minIndex(values, valueof) {\n  let min;\n  let minIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value, minIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value, minIndex = index;\n      }\n    }\n  }\n  return minIndex;\n}\n", "import {ascendingDefined, compareDefined} from \"./sort.js\";\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nexport default function quickselect(array, k, left = 0, right = Infinity, compare) {\n  k = Math.floor(k);\n  left = Math.floor(Math.max(0, left));\n  right = Math.floor(Math.min(array.length - 1, right));\n\n  if (!(left <= k && k <= right)) return array;\n\n  compare = compare === undefined ? ascendingDefined : compareDefined(compare);\n\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n\n    const t = array[k];\n    let i = left;\n    let j = right;\n\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n      while (compare(array[i], t) < 0) ++i;\n      while (compare(array[j], t) > 0) --j;\n    }\n\n    if (compare(array[left], t) === 0) swap(array, left, j);\n    else ++j, swap(array, j, right);\n\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n\n  return array;\n}\n\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}\n", "import ascending from \"./ascending.js\";\n\nexport default function greatest(values, compare = ascending) {\n  let max;\n  let defined = false;\n  if (compare.length === 1) {\n    let maxValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? ascending(value, maxValue) > 0\n          : ascending(value, value) === 0) {\n        max = element;\n        maxValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, max) > 0\n          : compare(value, value) === 0) {\n        max = value;\n        defined = true;\n      }\n    }\n  }\n  return max;\n}\n", "import max from \"./max.js\";\nimport maxIndex from \"./maxIndex.js\";\nimport min from \"./min.js\";\nimport minIndex from \"./minIndex.js\";\nimport quickselect from \"./quickselect.js\";\nimport number, {numbers} from \"./number.js\";\nimport {ascendingDefined} from \"./sort.js\";\nimport greatest from \"./greatest.js\";\n\nexport default function quantile(values, p, valueof) {\n  values = Float64Array.from(numbers(values, valueof));\n  if (!(n = values.length) || isNaN(p = +p)) return;\n  if (p <= 0 || n < 2) return min(values);\n  if (p >= 1) return max(values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = max(quickselect(values, i0).subarray(0, i0 + 1)),\n      value1 = min(values.subarray(i0 + 1));\n  return value0 + (value1 - value0) * (i - i0);\n}\n\nexport function quantileSorted(values, p, valueof = number) {\n  if (!(n = values.length) || isNaN(p = +p)) return;\n  if (p <= 0 || n < 2) return +valueof(values[0], 0, values);\n  if (p >= 1) return +valueof(values[n - 1], n - 1, values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = +valueof(values[i0], i0, values),\n      value1 = +valueof(values[i0 + 1], i0 + 1, values);\n  return value0 + (value1 - value0) * (i - i0);\n}\n\nexport function quantileIndex(values, p, valueof = number) {\n  if (isNaN(p = +p)) return;\n  numbers = Float64Array.from(values, (_, i) => number(valueof(values[i], i, values)));\n  if (p <= 0) return minIndex(numbers);\n  if (p >= 1) return maxIndex(numbers);\n  var numbers,\n      index = Uint32Array.from(values, (_, i) => i),\n      j = numbers.length - 1,\n      i = Math.floor(j * p);\n  quickselect(index, i, 0, j, (i, j) => ascendingDefined(numbers[i], numbers[j]));\n  i = greatest(index.subarray(0, i + 1), (i) => numbers[i]);\n  return i >= 0 ? i : -1;\n}\n", "import count from \"../count.js\";\nimport quantile from \"../quantile.js\";\n\nexport default function thresholdFreedmanDiaconis(values, min, max) {\n  const c = count(values), d = quantile(values, 0.75) - quantile(values, 0.25);\n  return c && d ? Math.ceil((max - min) / (2 * d * Math.pow(c, -1 / 3))) : 1;\n}\n", "import count from \"../count.js\";\nimport deviation from \"../deviation.js\";\n\nexport default function thresholdScott(values, min, max) {\n  const c = count(values), d = deviation(values);\n  return c && d ? Math.ceil((max - min) * Math.cbrt(c) / (3.49 * d)) : 1;\n}\n", "export default function mean(values, valueof) {\n  let count = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  }\n  if (count) return sum / count;\n}\n", "import quantile, {quantileIndex} from \"./quantile.js\";\n\nexport default function median(values, valueof) {\n  return quantile(values, 0.5, valueof);\n}\n\nexport function medianIndex(values, valueof) {\n  return quantileIndex(values, 0.5, valueof);\n}\n", "function* flatten(arrays) {\n  for (const array of arrays) {\n    yield* array;\n  }\n}\n\nexport default function merge(arrays) {\n  return Array.from(flatten(arrays));\n}\n", "import {InternMap} from \"internmap\";\n\nexport default function mode(values, valueof) {\n  const counts = new InternMap();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && value >= value) {\n        counts.set(value, (counts.get(value) || 0) + 1);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && value >= value) {\n        counts.set(value, (counts.get(value) || 0) + 1);\n      }\n    }\n  }\n  let modeValue;\n  let modeCount = 0;\n  for (const [value, count] of counts) {\n    if (count > modeCount) {\n      modeCount = count;\n      modeValue = value;\n    }\n  }\n  return modeValue;\n}\n", "export default function pairs(values, pairof = pair) {\n  const pairs = [];\n  let previous;\n  let first = false;\n  for (const value of values) {\n    if (first) pairs.push(pairof(previous, value));\n    previous = value;\n    first = true;\n  }\n  return pairs;\n}\n\nexport function pair(a, b) {\n  return [a, b];\n}\n", "export default function range(start, stop, step) {\n  start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;\n\n  var i = -1,\n      n = Math.max(0, Math.ceil((stop - start) / step)) | 0,\n      range = new Array(n);\n\n  while (++i < n) {\n    range[i] = start + i * step;\n  }\n\n  return range;\n}\n", "import ascending from \"./ascending.js\";\nimport {ascendingDefined, compareDefined} from \"./sort.js\";\n\nexport default function rank(values, valueof = ascending) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  let V = Array.from(values);\n  const R = new Float64Array(V.length);\n  if (valueof.length !== 2) V = V.map(valueof), valueof = ascending;\n  const compareIndex = (i, j) => valueof(V[i], V[j]);\n  let k, r;\n  values = Uint32Array.from(V, (_, i) => i);\n  // Risky chaining due to Safari 14 https://github.com/d3/d3-array/issues/123\n  values.sort(valueof === ascending ? (i, j) => ascendingDefined(V[i], V[j]) : compareDefined(compareIndex));\n  values.forEach((j, i) => {\n      const c = compareIndex(j, k === undefined ? j : k);\n      if (c >= 0) {\n        if (k === undefined || c > 0) k = j, r = i;\n        R[j] = r;\n      } else {\n        R[j] = NaN;\n      }\n    });\n  return R;\n}\n", "import ascending from \"./ascending.js\";\n\nexport default function least(values, compare = ascending) {\n  let min;\n  let defined = false;\n  if (compare.length === 1) {\n    let minValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? ascending(value, minValue) < 0\n          : ascending(value, value) === 0) {\n        min = element;\n        minValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, min) < 0\n          : compare(value, value) === 0) {\n        min = value;\n        defined = true;\n      }\n    }\n  }\n  return min;\n}\n", "import ascending from \"./ascending.js\";\nimport minIndex from \"./minIndex.js\";\n\nexport default function leastIndex(values, compare = ascending) {\n  if (compare.length === 1) return minIndex(values, compare);\n  let minValue;\n  let min = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (min < 0\n        ? compare(value, value) === 0\n        : compare(value, minValue) < 0) {\n      minValue = value;\n      min = index;\n    }\n  }\n  return min;\n}\n", "import ascending from \"./ascending.js\";\nimport maxIndex from \"./maxIndex.js\";\n\nexport default function greatestIndex(values, compare = ascending) {\n  if (compare.length === 1) return maxIndex(values, compare);\n  let maxValue;\n  let max = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (max < 0\n        ? compare(value, value) === 0\n        : compare(value, maxValue) > 0) {\n      maxValue = value;\n      max = index;\n    }\n  }\n  return max;\n}\n", "import leastIndex from \"./leastIndex.js\";\n\nexport default function scan(values, compare) {\n  const index = leastIndex(values, compare);\n  return index < 0 ? undefined : index;\n}\n", "export default shuffler(Math.random);\n\nexport function shuffler(random) {\n  return function shuffle(array, i0 = 0, i1 = array.length) {\n    let m = i1 - (i0 = +i0);\n    while (m) {\n      const i = random() * m-- | 0, t = array[m + i0];\n      array[m + i0] = array[i + i0];\n      array[i + i0] = t;\n    }\n    return array;\n  };\n}\n", "export default function sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}\n", "import min from \"./min.js\";\n\nexport default function transpose(matrix) {\n  if (!(n = matrix.length)) return [];\n  for (var i = -1, m = min(matrix, length), transpose = new Array(m); ++i < m;) {\n    for (var j = -1, n, row = transpose[i] = new Array(n); ++j < n;) {\n      row[j] = matrix[j][i];\n    }\n  }\n  return transpose;\n}\n\nfunction length(d) {\n  return d.length;\n}\n", "import transpose from \"./transpose.js\";\n\nexport default function zip() {\n  return transpose(arguments);\n}\n", "export default function every(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (!test(value, ++index, values)) {\n      return false;\n    }\n  }\n  return true;\n}\n", "export default function some(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      return true;\n    }\n  }\n  return false;\n}\n", "export default function filter(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  const array = [];\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      array.push(value);\n    }\n  }\n  return array;\n}\n", "export default function map(values, mapper) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  if (typeof mapper !== \"function\") throw new TypeError(\"mapper is not a function\");\n  return Array.from(values, (value, index) => mapper(value, index, values));\n}\n", "export default function reduce(values, reducer, value) {\n  if (typeof reducer !== \"function\") throw new TypeError(\"reducer is not a function\");\n  const iterator = values[Symbol.iterator]();\n  let done, next, index = -1;\n  if (arguments.length < 3) {\n    ({done, value} = iterator.next());\n    if (done) return;\n    ++index;\n  }\n  while (({done, value: next} = iterator.next()), !done) {\n    value = reducer(value, next, ++index, values);\n  }\n  return value;\n}\n", "export default function reverse(values) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  return Array.from(values).reverse();\n}\n", "import {InternSet} from \"internmap\";\n\nexport default function difference(values, ...others) {\n  values = new InternSet(values);\n  for (const other of others) {\n    for (const value of other) {\n      values.delete(value);\n    }\n  }\n  return values;\n}\n", "import {InternSet} from \"internmap\";\n\nexport default function disjoint(values, other) {\n  const iterator = other[Symbol.iterator](), set = new InternSet();\n  for (const v of values) {\n    if (set.has(v)) return false;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) break;\n      if (Object.is(v, value)) return false;\n      set.add(value);\n    }\n  }\n  return true;\n}\n", "import {InternSet} from \"internmap\";\n\nexport default function intersection(values, ...others) {\n  values = new InternSet(values);\n  others = others.map(set);\n  out: for (const value of values) {\n    for (const other of others) {\n      if (!other.has(value)) {\n        values.delete(value);\n        continue out;\n      }\n    }\n  }\n  return values;\n}\n\nfunction set(values) {\n  return values instanceof InternSet ? values : new InternSet(values);\n}\n", "export default function superset(values, other) {\n  const iterator = values[Symbol.iterator](), set = new Set();\n  for (const o of other) {\n    const io = intern(o);\n    if (set.has(io)) continue;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) return false;\n      const ivalue = intern(value);\n      set.add(ivalue);\n      if (Object.is(io, ivalue)) break;\n    }\n  }\n  return true;\n}\n\nfunction intern(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}\n", "import superset from \"./superset.js\";\n\nexport default function subset(values, other) {\n  return superset(other, values);\n}\n", "import {InternSet} from \"internmap\";\n\nexport default function union(...others) {\n  const set = new InternSet();\n  for (const other of others) {\n    for (const o of other) {\n      set.add(o);\n    }\n  }\n  return set;\n}\n", "export default function(constructor, factory, prototype) {\n  constructor.prototype = factory.prototype = prototype;\n  prototype.constructor = constructor;\n}\n\nexport function extend(parent, definition) {\n  var prototype = Object.create(parent.prototype);\n  for (var key in definition) prototype[key] = definition[key];\n  return prototype;\n}\n", "import define, {extend} from \"./define.js\";\n\nexport function Color() {}\n\nexport var darker = 0.7;\nexport var brighter = 1 / darker;\n\nvar reI = \"\\\\s*([+-]?\\\\d+)\\\\s*\",\n    reN = \"\\\\s*([+-]?(?:\\\\d*\\\\.)?\\\\d+(?:[eE][+-]?\\\\d+)?)\\\\s*\",\n    reP = \"\\\\s*([+-]?(?:\\\\d*\\\\.)?\\\\d+(?:[eE][+-]?\\\\d+)?)%\\\\s*\",\n    reHex = /^#([0-9a-f]{3,8})$/,\n    reRgbInteger = new RegExp(`^rgb\\\\(${reI},${reI},${reI}\\\\)$`),\n    reRgbPercent = new RegExp(`^rgb\\\\(${reP},${reP},${reP}\\\\)$`),\n    reRgbaInteger = new RegExp(`^rgba\\\\(${reI},${reI},${reI},${reN}\\\\)$`),\n    reRgbaPercent = new RegExp(`^rgba\\\\(${reP},${reP},${reP},${reN}\\\\)$`),\n    reHslPercent = new RegExp(`^hsl\\\\(${reN},${reP},${reP}\\\\)$`),\n    reHslaPercent = new RegExp(`^hsla\\\\(${reN},${reP},${reP},${reN}\\\\)$`);\n\nvar named = {\n  aliceblue: 0xf0f8ff,\n  antiquewhite: 0xfaebd7,\n  aqua: 0x00ffff,\n  aquamarine: 0x7fffd4,\n  azure: 0xf0ffff,\n  beige: 0xf5f5dc,\n  bisque: 0xffe4c4,\n  black: 0x000000,\n  blanchedalmond: 0xffebcd,\n  blue: 0x0000ff,\n  blueviolet: 0x8a2be2,\n  brown: 0xa52a2a,\n  burlywood: 0xdeb887,\n  cadetblue: 0x5f9ea0,\n  chartreuse: 0x7fff00,\n  chocolate: 0xd2691e,\n  coral: 0xff7f50,\n  cornflowerblue: 0x6495ed,\n  cornsilk: 0xfff8dc,\n  crimson: 0xdc143c,\n  cyan: 0x00ffff,\n  darkblue: 0x00008b,\n  darkcyan: 0x008b8b,\n  darkgoldenrod: 0xb8860b,\n  darkgray: 0xa9a9a9,\n  darkgreen: 0x006400,\n  darkgrey: 0xa9a9a9,\n  darkkhaki: 0xbdb76b,\n  darkmagenta: 0x8b008b,\n  darkolivegreen: 0x556b2f,\n  darkorange: 0xff8c00,\n  darkorchid: 0x9932cc,\n  darkred: 0x8b0000,\n  darksalmon: 0xe9967a,\n  darkseagreen: 0x8fbc8f,\n  darkslateblue: 0x483d8b,\n  darkslategray: 0x2f4f4f,\n  darkslategrey: 0x2f4f4f,\n  darkturquoise: 0x00ced1,\n  darkviolet: 0x9400d3,\n  deeppink: 0xff1493,\n  deepskyblue: 0x00bfff,\n  dimgray: 0x696969,\n  dimgrey: 0x696969,\n  dodgerblue: 0x1e90ff,\n  firebrick: 0xb22222,\n  floralwhite: 0xfffaf0,\n  forestgreen: 0x228b22,\n  fuchsia: 0xff00ff,\n  gainsboro: 0xdcdcdc,\n  ghostwhite: 0xf8f8ff,\n  gold: 0xffd700,\n  goldenrod: 0xdaa520,\n  gray: 0x808080,\n  green: 0x008000,\n  greenyellow: 0xadff2f,\n  grey: 0x808080,\n  honeydew: 0xf0fff0,\n  hotpink: 0xff69b4,\n  indianred: 0xcd5c5c,\n  indigo: 0x4b0082,\n  ivory: 0xfffff0,\n  khaki: 0xf0e68c,\n  lavender: 0xe6e6fa,\n  lavenderblush: 0xfff0f5,\n  lawngreen: 0x7cfc00,\n  lemonchiffon: 0xfffacd,\n  lightblue: 0xadd8e6,\n  lightcoral: 0xf08080,\n  lightcyan: 0xe0ffff,\n  lightgoldenrodyellow: 0xfafad2,\n  lightgray: 0xd3d3d3,\n  lightgreen: 0x90ee90,\n  lightgrey: 0xd3d3d3,\n  lightpink: 0xffb6c1,\n  lightsalmon: 0xffa07a,\n  lightseagreen: 0x20b2aa,\n  lightskyblue: 0x87cefa,\n  lightslategray: 0x778899,\n  lightslategrey: 0x778899,\n  lightsteelblue: 0xb0c4de,\n  lightyellow: 0xffffe0,\n  lime: 0x00ff00,\n  limegreen: 0x32cd32,\n  linen: 0xfaf0e6,\n  magenta: 0xff00ff,\n  maroon: 0x800000,\n  mediumaquamarine: 0x66cdaa,\n  mediumblue: 0x0000cd,\n  mediumorchid: 0xba55d3,\n  mediumpurple: 0x9370db,\n  mediumseagreen: 0x3cb371,\n  mediumslateblue: 0x7b68ee,\n  mediumspringgreen: 0x00fa9a,\n  mediumturquoise: 0x48d1cc,\n  mediumvioletred: 0xc71585,\n  midnightblue: 0x191970,\n  mintcream: 0xf5fffa,\n  mistyrose: 0xffe4e1,\n  moccasin: 0xffe4b5,\n  navajowhite: 0xffdead,\n  navy: 0x000080,\n  oldlace: 0xfdf5e6,\n  olive: 0x808000,\n  olivedrab: 0x6b8e23,\n  orange: 0xffa500,\n  orangered: 0xff4500,\n  orchid: 0xda70d6,\n  palegoldenrod: 0xeee8aa,\n  palegreen: 0x98fb98,\n  paleturquoise: 0xafeeee,\n  palevioletred: 0xdb7093,\n  papayawhip: 0xffefd5,\n  peachpuff: 0xffdab9,\n  peru: 0xcd853f,\n  pink: 0xffc0cb,\n  plum: 0xdda0dd,\n  powderblue: 0xb0e0e6,\n  purple: 0x800080,\n  rebeccapurple: 0x663399,\n  red: 0xff0000,\n  rosybrown: 0xbc8f8f,\n  royalblue: 0x4169e1,\n  saddlebrown: 0x8b4513,\n  salmon: 0xfa8072,\n  sandybrown: 0xf4a460,\n  seagreen: 0x2e8b57,\n  seashell: 0xfff5ee,\n  sienna: 0xa0522d,\n  silver: 0xc0c0c0,\n  skyblue: 0x87ceeb,\n  slateblue: 0x6a5acd,\n  slategray: 0x708090,\n  slategrey: 0x708090,\n  snow: 0xfffafa,\n  springgreen: 0x00ff7f,\n  steelblue: 0x4682b4,\n  tan: 0xd2b48c,\n  teal: 0x008080,\n  thistle: 0xd8bfd8,\n  tomato: 0xff6347,\n  turquoise: 0x40e0d0,\n  violet: 0xee82ee,\n  wheat: 0xf5deb3,\n  white: 0xffffff,\n  whitesmoke: 0xf5f5f5,\n  yellow: 0xffff00,\n  yellowgreen: 0x9acd32\n};\n\ndefine(Color, color, {\n  copy(channels) {\n    return Object.assign(new this.constructor, this, channels);\n  },\n  displayable() {\n    return this.rgb().displayable();\n  },\n  hex: color_formatHex, // Deprecated! Use color.formatHex.\n  formatHex: color_formatHex,\n  formatHex8: color_formatHex8,\n  formatHsl: color_formatHsl,\n  formatRgb: color_formatRgb,\n  toString: color_formatRgb\n});\n\nfunction color_formatHex() {\n  return this.rgb().formatHex();\n}\n\nfunction color_formatHex8() {\n  return this.rgb().formatHex8();\n}\n\nfunction color_formatHsl() {\n  return hslConvert(this).formatHsl();\n}\n\nfunction color_formatRgb() {\n  return this.rgb().formatRgb();\n}\n\nexport default function color(format) {\n  var m, l;\n  format = (format + \"\").trim().toLowerCase();\n  return (m = reHex.exec(format)) ? (l = m[1].length, m = parseInt(m[1], 16), l === 6 ? rgbn(m) // #ff0000\n      : l === 3 ? new Rgb((m >> 8 & 0xf) | (m >> 4 & 0xf0), (m >> 4 & 0xf) | (m & 0xf0), ((m & 0xf) << 4) | (m & 0xf), 1) // #f00\n      : l === 8 ? rgba(m >> 24 & 0xff, m >> 16 & 0xff, m >> 8 & 0xff, (m & 0xff) / 0xff) // #ff000000\n      : l === 4 ? rgba((m >> 12 & 0xf) | (m >> 8 & 0xf0), (m >> 8 & 0xf) | (m >> 4 & 0xf0), (m >> 4 & 0xf) | (m & 0xf0), (((m & 0xf) << 4) | (m & 0xf)) / 0xff) // #f000\n      : null) // invalid hex\n      : (m = reRgbInteger.exec(format)) ? new Rgb(m[1], m[2], m[3], 1) // rgb(255, 0, 0)\n      : (m = reRgbPercent.exec(format)) ? new Rgb(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, 1) // rgb(100%, 0%, 0%)\n      : (m = reRgbaInteger.exec(format)) ? rgba(m[1], m[2], m[3], m[4]) // rgba(255, 0, 0, 1)\n      : (m = reRgbaPercent.exec(format)) ? rgba(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, m[4]) // rgb(100%, 0%, 0%, 1)\n      : (m = reHslPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, 1) // hsl(120, 50%, 50%)\n      : (m = reHslaPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, m[4]) // hsla(120, 50%, 50%, 1)\n      : named.hasOwnProperty(format) ? rgbn(named[format]) // eslint-disable-line no-prototype-builtins\n      : format === \"transparent\" ? new Rgb(NaN, NaN, NaN, 0)\n      : null;\n}\n\nfunction rgbn(n) {\n  return new Rgb(n >> 16 & 0xff, n >> 8 & 0xff, n & 0xff, 1);\n}\n\nfunction rgba(r, g, b, a) {\n  if (a <= 0) r = g = b = NaN;\n  return new Rgb(r, g, b, a);\n}\n\nexport function rgbConvert(o) {\n  if (!(o instanceof Color)) o = color(o);\n  if (!o) return new Rgb;\n  o = o.rgb();\n  return new Rgb(o.r, o.g, o.b, o.opacity);\n}\n\nexport function rgb(r, g, b, opacity) {\n  return arguments.length === 1 ? rgbConvert(r) : new Rgb(r, g, b, opacity == null ? 1 : opacity);\n}\n\nexport function Rgb(r, g, b, opacity) {\n  this.r = +r;\n  this.g = +g;\n  this.b = +b;\n  this.opacity = +opacity;\n}\n\ndefine(Rgb, rgb, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);\n  },\n  rgb() {\n    return this;\n  },\n  clamp() {\n    return new Rgb(clampi(this.r), clampi(this.g), clampi(this.b), clampa(this.opacity));\n  },\n  displayable() {\n    return (-0.5 <= this.r && this.r < 255.5)\n        && (-0.5 <= this.g && this.g < 255.5)\n        && (-0.5 <= this.b && this.b < 255.5)\n        && (0 <= this.opacity && this.opacity <= 1);\n  },\n  hex: rgb_formatHex, // Deprecated! Use color.formatHex.\n  formatHex: rgb_formatHex,\n  formatHex8: rgb_formatHex8,\n  formatRgb: rgb_formatRgb,\n  toString: rgb_formatRgb\n}));\n\nfunction rgb_formatHex() {\n  return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}`;\n}\n\nfunction rgb_formatHex8() {\n  return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}${hex((isNaN(this.opacity) ? 1 : this.opacity) * 255)}`;\n}\n\nfunction rgb_formatRgb() {\n  const a = clampa(this.opacity);\n  return `${a === 1 ? \"rgb(\" : \"rgba(\"}${clampi(this.r)}, ${clampi(this.g)}, ${clampi(this.b)}${a === 1 ? \")\" : `, ${a})`}`;\n}\n\nfunction clampa(opacity) {\n  return isNaN(opacity) ? 1 : Math.max(0, Math.min(1, opacity));\n}\n\nfunction clampi(value) {\n  return Math.max(0, Math.min(255, Math.round(value) || 0));\n}\n\nfunction hex(value) {\n  value = clampi(value);\n  return (value < 16 ? \"0\" : \"\") + value.toString(16);\n}\n\nfunction hsla(h, s, l, a) {\n  if (a <= 0) h = s = l = NaN;\n  else if (l <= 0 || l >= 1) h = s = NaN;\n  else if (s <= 0) h = NaN;\n  return new Hsl(h, s, l, a);\n}\n\nexport function hslConvert(o) {\n  if (o instanceof Hsl) return new Hsl(o.h, o.s, o.l, o.opacity);\n  if (!(o instanceof Color)) o = color(o);\n  if (!o) return new Hsl;\n  if (o instanceof Hsl) return o;\n  o = o.rgb();\n  var r = o.r / 255,\n      g = o.g / 255,\n      b = o.b / 255,\n      min = Math.min(r, g, b),\n      max = Math.max(r, g, b),\n      h = NaN,\n      s = max - min,\n      l = (max + min) / 2;\n  if (s) {\n    if (r === max) h = (g - b) / s + (g < b) * 6;\n    else if (g === max) h = (b - r) / s + 2;\n    else h = (r - g) / s + 4;\n    s /= l < 0.5 ? max + min : 2 - max - min;\n    h *= 60;\n  } else {\n    s = l > 0 && l < 1 ? 0 : h;\n  }\n  return new Hsl(h, s, l, o.opacity);\n}\n\nexport function hsl(h, s, l, opacity) {\n  return arguments.length === 1 ? hslConvert(h) : new Hsl(h, s, l, opacity == null ? 1 : opacity);\n}\n\nfunction Hsl(h, s, l, opacity) {\n  this.h = +h;\n  this.s = +s;\n  this.l = +l;\n  this.opacity = +opacity;\n}\n\ndefine(Hsl, hsl, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Hsl(this.h, this.s, this.l * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Hsl(this.h, this.s, this.l * k, this.opacity);\n  },\n  rgb() {\n    var h = this.h % 360 + (this.h < 0) * 360,\n        s = isNaN(h) || isNaN(this.s) ? 0 : this.s,\n        l = this.l,\n        m2 = l + (l < 0.5 ? l : 1 - l) * s,\n        m1 = 2 * l - m2;\n    return new Rgb(\n      hsl2rgb(h >= 240 ? h - 240 : h + 120, m1, m2),\n      hsl2rgb(h, m1, m2),\n      hsl2rgb(h < 120 ? h + 240 : h - 120, m1, m2),\n      this.opacity\n    );\n  },\n  clamp() {\n    return new Hsl(clamph(this.h), clampt(this.s), clampt(this.l), clampa(this.opacity));\n  },\n  displayable() {\n    return (0 <= this.s && this.s <= 1 || isNaN(this.s))\n        && (0 <= this.l && this.l <= 1)\n        && (0 <= this.opacity && this.opacity <= 1);\n  },\n  formatHsl() {\n    const a = clampa(this.opacity);\n    return `${a === 1 ? \"hsl(\" : \"hsla(\"}${clamph(this.h)}, ${clampt(this.s) * 100}%, ${clampt(this.l) * 100}%${a === 1 ? \")\" : `, ${a})`}`;\n  }\n}));\n\nfunction clamph(value) {\n  value = (value || 0) % 360;\n  return value < 0 ? value + 360 : value;\n}\n\nfunction clampt(value) {\n  return Math.max(0, Math.min(1, value || 0));\n}\n\n/* From FvD 13.37, CSS Color Module Level 3 */\nfunction hsl2rgb(h, m1, m2) {\n  return (h < 60 ? m1 + (m2 - m1) * h / 60\n      : h < 180 ? m2\n      : h < 240 ? m1 + (m2 - m1) * (240 - h) / 60\n      : m1) * 255;\n}\n", "export const radians = Math.PI / 180;\nexport const degrees = 180 / Math.PI;\n", "import define, {extend} from \"./define.js\";\nimport {Color, rgbConvert, Rgb} from \"./color.js\";\nimport {degrees, radians} from \"./math.js\";\n\n// https://observablehq.com/@mbostock/lab-and-rgb\nconst K = 18,\n    Xn = 0.96422,\n    Yn = 1,\n    Zn = 0.82521,\n    t0 = 4 / 29,\n    t1 = 6 / 29,\n    t2 = 3 * t1 * t1,\n    t3 = t1 * t1 * t1;\n\nfunction labConvert(o) {\n  if (o instanceof Lab) return new Lab(o.l, o.a, o.b, o.opacity);\n  if (o instanceof Hcl) return hcl2lab(o);\n  if (!(o instanceof Rgb)) o = rgbConvert(o);\n  var r = rgb2lrgb(o.r),\n      g = rgb2lrgb(o.g),\n      b = rgb2lrgb(o.b),\n      y = xyz2lab((0.2225045 * r + 0.7168786 * g + 0.0606169 * b) / Yn), x, z;\n  if (r === g && g === b) x = z = y; else {\n    x = xyz2lab((0.4360747 * r + 0.3850649 * g + 0.1430804 * b) / Xn);\n    z = xyz2lab((0.0139322 * r + 0.0971045 * g + 0.7141733 * b) / Zn);\n  }\n  return new Lab(116 * y - 16, 500 * (x - y), 200 * (y - z), o.opacity);\n}\n\nexport function gray(l, opacity) {\n  return new Lab(l, 0, 0, opacity == null ? 1 : opacity);\n}\n\nexport default function lab(l, a, b, opacity) {\n  return arguments.length === 1 ? labConvert(l) : new Lab(l, a, b, opacity == null ? 1 : opacity);\n}\n\nexport function Lab(l, a, b, opacity) {\n  this.l = +l;\n  this.a = +a;\n  this.b = +b;\n  this.opacity = +opacity;\n}\n\ndefine(Lab, lab, extend(Color, {\n  brighter(k) {\n    return new Lab(this.l + K * (k == null ? 1 : k), this.a, this.b, this.opacity);\n  },\n  darker(k) {\n    return new Lab(this.l - K * (k == null ? 1 : k), this.a, this.b, this.opacity);\n  },\n  rgb() {\n    var y = (this.l + 16) / 116,\n        x = isNaN(this.a) ? y : y + this.a / 500,\n        z = isNaN(this.b) ? y : y - this.b / 200;\n    x = Xn * lab2xyz(x);\n    y = Yn * lab2xyz(y);\n    z = Zn * lab2xyz(z);\n    return new Rgb(\n      lrgb2rgb( 3.1338561 * x - 1.6168667 * y - 0.4906146 * z),\n      lrgb2rgb(-0.9787684 * x + 1.9161415 * y + 0.0334540 * z),\n      lrgb2rgb( 0.0719453 * x - 0.2289914 * y + 1.4052427 * z),\n      this.opacity\n    );\n  }\n}));\n\nfunction xyz2lab(t) {\n  return t > t3 ? Math.pow(t, 1 / 3) : t / t2 + t0;\n}\n\nfunction lab2xyz(t) {\n  return t > t1 ? t * t * t : t2 * (t - t0);\n}\n\nfunction lrgb2rgb(x) {\n  return 255 * (x <= 0.0031308 ? 12.92 * x : 1.055 * Math.pow(x, 1 / 2.4) - 0.055);\n}\n\nfunction rgb2lrgb(x) {\n  return (x /= 255) <= 0.04045 ? x / 12.92 : Math.pow((x + 0.055) / 1.055, 2.4);\n}\n\nfunction hclConvert(o) {\n  if (o instanceof Hcl) return new Hcl(o.h, o.c, o.l, o.opacity);\n  if (!(o instanceof Lab)) o = labConvert(o);\n  if (o.a === 0 && o.b === 0) return new Hcl(NaN, 0 < o.l && o.l < 100 ? 0 : NaN, o.l, o.opacity);\n  var h = Math.atan2(o.b, o.a) * degrees;\n  return new Hcl(h < 0 ? h + 360 : h, Math.sqrt(o.a * o.a + o.b * o.b), o.l, o.opacity);\n}\n\nexport function lch(l, c, h, opacity) {\n  return arguments.length === 1 ? hclConvert(l) : new Hcl(h, c, l, opacity == null ? 1 : opacity);\n}\n\nexport function hcl(h, c, l, opacity) {\n  return arguments.length === 1 ? hclConvert(h) : new Hcl(h, c, l, opacity == null ? 1 : opacity);\n}\n\nexport function Hcl(h, c, l, opacity) {\n  this.h = +h;\n  this.c = +c;\n  this.l = +l;\n  this.opacity = +opacity;\n}\n\nfunction hcl2lab(o) {\n  if (isNaN(o.h)) return new Lab(o.l, 0, 0, o.opacity);\n  var h = o.h * radians;\n  return new Lab(o.l, Math.cos(h) * o.c, Math.sin(h) * o.c, o.opacity);\n}\n\ndefine(Hcl, hcl, extend(Color, {\n  brighter(k) {\n    return new Hcl(this.h, this.c, this.l + K * (k == null ? 1 : k), this.opacity);\n  },\n  darker(k) {\n    return new Hcl(this.h, this.c, this.l - K * (k == null ? 1 : k), this.opacity);\n  },\n  rgb() {\n    return hcl2lab(this).rgb();\n  }\n}));\n", "import define, {extend} from \"./define.js\";\nimport {Color, rgbConvert, Rgb, darker, brighter} from \"./color.js\";\nimport {degrees, radians} from \"./math.js\";\n\nvar A = -0.14861,\n    B = +1.78277,\n    C = -0.29227,\n    D = -0.90649,\n    E = +1.97294,\n    ED = E * D,\n    EB = E * B,\n    BC_DA = B * C - D * A;\n\nfunction cubehelixConvert(o) {\n  if (o instanceof Cubehelix) return new Cubehelix(o.h, o.s, o.l, o.opacity);\n  if (!(o instanceof Rgb)) o = rgbConvert(o);\n  var r = o.r / 255,\n      g = o.g / 255,\n      b = o.b / 255,\n      l = (BC_DA * b + ED * r - EB * g) / (BC_DA + ED - EB),\n      bl = b - l,\n      k = (E * (g - l) - C * bl) / D,\n      s = Math.sqrt(k * k + bl * bl) / (E * l * (1 - l)), // NaN if l=0 or l=1\n      h = s ? Math.atan2(k, bl) * degrees - 120 : NaN;\n  return new Cubehelix(h < 0 ? h + 360 : h, s, l, o.opacity);\n}\n\nexport default function cubehelix(h, s, l, opacity) {\n  return arguments.length === 1 ? cubehelixConvert(h) : new Cubehelix(h, s, l, opacity == null ? 1 : opacity);\n}\n\nexport function Cubehelix(h, s, l, opacity) {\n  this.h = +h;\n  this.s = +s;\n  this.l = +l;\n  this.opacity = +opacity;\n}\n\ndefine(Cubehelix, cubehelix, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Cubehelix(this.h, this.s, this.l * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Cubehelix(this.h, this.s, this.l * k, this.opacity);\n  },\n  rgb() {\n    var h = isNaN(this.h) ? 0 : (this.h + 120) * radians,\n        l = +this.l,\n        a = isNaN(this.s) ? 0 : this.s * l * (1 - l),\n        cosh = Math.cos(h),\n        sinh = Math.sin(h);\n    return new Rgb(\n      255 * (l + a * (A * cosh + B * sinh)),\n      255 * (l + a * (C * cosh + D * sinh)),\n      255 * (l + a * (E * cosh)),\n      this.opacity\n    );\n  }\n}));\n", "export function basis(t1, v0, v1, v2, v3) {\n  var t2 = t1 * t1, t3 = t2 * t1;\n  return ((1 - 3 * t1 + 3 * t2 - t3) * v0\n      + (4 - 6 * t2 + 3 * t3) * v1\n      + (1 + 3 * t1 + 3 * t2 - 3 * t3) * v2\n      + t3 * v3) / 6;\n}\n\nexport default function(values) {\n  var n = values.length - 1;\n  return function(t) {\n    var i = t <= 0 ? (t = 0) : t >= 1 ? (t = 1, n - 1) : Math.floor(t * n),\n        v1 = values[i],\n        v2 = values[i + 1],\n        v0 = i > 0 ? values[i - 1] : 2 * v1 - v2,\n        v3 = i < n - 1 ? values[i + 2] : 2 * v2 - v1;\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}\n", "import {basis} from \"./basis.js\";\n\nexport default function(values) {\n  var n = values.length;\n  return function(t) {\n    var i = Math.floor(((t %= 1) < 0 ? ++t : t) * n),\n        v0 = values[(i + n - 1) % n],\n        v1 = values[i % n],\n        v2 = values[(i + 1) % n],\n        v3 = values[(i + 2) % n];\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}\n", "export default x => () => x;\n", "import constant from \"./constant.js\";\n\nfunction linear(a, d) {\n  return function(t) {\n    return a + t * d;\n  };\n}\n\nfunction exponential(a, b, y) {\n  return a = Math.pow(a, y), b = Math.pow(b, y) - a, y = 1 / y, function(t) {\n    return Math.pow(a + t * b, y);\n  };\n}\n\nexport function hue(a, b) {\n  var d = b - a;\n  return d ? linear(a, d > 180 || d < -180 ? d - 360 * Math.round(d / 360) : d) : constant(isNaN(a) ? b : a);\n}\n\nexport function gamma(y) {\n  return (y = +y) === 1 ? nogamma : function(a, b) {\n    return b - a ? exponential(a, b, y) : constant(isNaN(a) ? b : a);\n  };\n}\n\nexport default function nogamma(a, b) {\n  var d = b - a;\n  return d ? linear(a, d) : constant(isNaN(a) ? b : a);\n}\n", "import {rgb as colorRgb} from \"d3-color\";\nimport basis from \"./basis.js\";\nimport basisClosed from \"./basisClosed.js\";\nimport nogamma, {gamma} from \"./color.js\";\n\nexport default (function rgbGamma(y) {\n  var color = gamma(y);\n\n  function rgb(start, end) {\n    var r = color((start = colorRgb(start)).r, (end = colorRgb(end)).r),\n        g = color(start.g, end.g),\n        b = color(start.b, end.b),\n        opacity = nogamma(start.opacity, end.opacity);\n    return function(t) {\n      start.r = r(t);\n      start.g = g(t);\n      start.b = b(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n\n  rgb.gamma = rgbGamma;\n\n  return rgb;\n})(1);\n\nfunction rgbSpline(spline) {\n  return function(colors) {\n    var n = colors.length,\n        r = new Array(n),\n        g = new Array(n),\n        b = new Array(n),\n        i, color;\n    for (i = 0; i < n; ++i) {\n      color = colorRgb(colors[i]);\n      r[i] = color.r || 0;\n      g[i] = color.g || 0;\n      b[i] = color.b || 0;\n    }\n    r = spline(r);\n    g = spline(g);\n    b = spline(b);\n    color.opacity = 1;\n    return function(t) {\n      color.r = r(t);\n      color.g = g(t);\n      color.b = b(t);\n      return color + \"\";\n    };\n  };\n}\n\nexport var rgbBasis = rgbSpline(basis);\nexport var rgbBasisClosed = rgbSpline(basisClosed);\n", "export default function(a, b) {\n  if (!b) b = [];\n  var n = a ? Math.min(b.length, a.length) : 0,\n      c = b.slice(),\n      i;\n  return function(t) {\n    for (i = 0; i < n; ++i) c[i] = a[i] * (1 - t) + b[i] * t;\n    return c;\n  };\n}\n\nexport function isNumberArray(x) {\n  return ArrayBuffer.isView(x) && !(x instanceof DataView);\n}\n", "import value from \"./value.js\";\nimport numberArray, {isNumberArray} from \"./numberArray.js\";\n\nexport default function(a, b) {\n  return (isNumberArray(b) ? numberArray : genericArray)(a, b);\n}\n\nexport function genericArray(a, b) {\n  var nb = b ? b.length : 0,\n      na = a ? Math.min(nb, a.length) : 0,\n      x = new Array(na),\n      c = new Array(nb),\n      i;\n\n  for (i = 0; i < na; ++i) x[i] = value(a[i], b[i]);\n  for (; i < nb; ++i) c[i] = b[i];\n\n  return function(t) {\n    for (i = 0; i < na; ++i) c[i] = x[i](t);\n    return c;\n  };\n}\n", "export default function(a, b) {\n  var d = new Date;\n  return a = +a, b = +b, function(t) {\n    return d.setTime(a * (1 - t) + b * t), d;\n  };\n}\n", "export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return a * (1 - t) + b * t;\n  };\n}\n", "import value from \"./value.js\";\n\nexport default function(a, b) {\n  var i = {},\n      c = {},\n      k;\n\n  if (a === null || typeof a !== \"object\") a = {};\n  if (b === null || typeof b !== \"object\") b = {};\n\n  for (k in b) {\n    if (k in a) {\n      i[k] = value(a[k], b[k]);\n    } else {\n      c[k] = b[k];\n    }\n  }\n\n  return function(t) {\n    for (k in i) c[k] = i[k](t);\n    return c;\n  };\n}\n", "import number from \"./number.js\";\n\nvar reA = /[-+]?(?:\\d+\\.?\\d*|\\.?\\d+)(?:[eE][-+]?\\d+)?/g,\n    reB = new RegExp(reA.source, \"g\");\n\nfunction zero(b) {\n  return function() {\n    return b;\n  };\n}\n\nfunction one(b) {\n  return function(t) {\n    return b(t) + \"\";\n  };\n}\n\nexport default function(a, b) {\n  var bi = reA.lastIndex = reB.lastIndex = 0, // scan index for next number in b\n      am, // current match in a\n      bm, // current match in b\n      bs, // string preceding current number in b, if any\n      i = -1, // index in s\n      s = [], // string constants and placeholders\n      q = []; // number interpolators\n\n  // Coerce inputs to strings.\n  a = a + \"\", b = b + \"\";\n\n  // Interpolate pairs of numbers in a & b.\n  while ((am = reA.exec(a))\n      && (bm = reB.exec(b))) {\n    if ((bs = bm.index) > bi) { // a string precedes the next number in b\n      bs = b.slice(bi, bs);\n      if (s[i]) s[i] += bs; // coalesce with previous string\n      else s[++i] = bs;\n    }\n    if ((am = am[0]) === (bm = bm[0])) { // numbers in a & b match\n      if (s[i]) s[i] += bm; // coalesce with previous string\n      else s[++i] = bm;\n    } else { // interpolate non-matching numbers\n      s[++i] = null;\n      q.push({i: i, x: number(am, bm)});\n    }\n    bi = reB.lastIndex;\n  }\n\n  // Add remains of b.\n  if (bi < b.length) {\n    bs = b.slice(bi);\n    if (s[i]) s[i] += bs; // coalesce with previous string\n    else s[++i] = bs;\n  }\n\n  // Special optimization for only a single match.\n  // Otherwise, interpolate each of the numbers and rejoin the string.\n  return s.length < 2 ? (q[0]\n      ? one(q[0].x)\n      : zero(b))\n      : (b = q.length, function(t) {\n          for (var i = 0, o; i < b; ++i) s[(o = q[i]).i] = o.x(t);\n          return s.join(\"\");\n        });\n}\n", "import {color} from \"d3-color\";\nimport rgb from \"./rgb.js\";\nimport {genericArray} from \"./array.js\";\nimport date from \"./date.js\";\nimport number from \"./number.js\";\nimport object from \"./object.js\";\nimport string from \"./string.js\";\nimport constant from \"./constant.js\";\nimport numberArray, {isNumberArray} from \"./numberArray.js\";\n\nexport default function(a, b) {\n  var t = typeof b, c;\n  return b == null || t === \"boolean\" ? constant(b)\n      : (t === \"number\" ? number\n      : t === \"string\" ? ((c = color(b)) ? (b = c, rgb) : string)\n      : b instanceof color ? rgb\n      : b instanceof Date ? date\n      : isNumberArray(b) ? numberArray\n      : Array.isArray(b) ? genericArray\n      : typeof b.valueOf !== \"function\" && typeof b.toString !== \"function\" || isNaN(b) ? object\n      : number)(a, b);\n}\n", "export default function(range) {\n  var n = range.length;\n  return function(t) {\n    return range[Math.max(0, Math.min(n - 1, Math.floor(t * n)))];\n  };\n}\n", "import {hue} from \"./color.js\";\n\nexport default function(a, b) {\n  var i = hue(+a, +b);\n  return function(t) {\n    var x = i(t);\n    return x - 360 * Math.floor(x / 360);\n  };\n}\n", "export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return Math.round(a * (1 - t) + b * t);\n  };\n}\n", "var degrees = 180 / Math.PI;\n\nexport var identity = {\n  translateX: 0,\n  translateY: 0,\n  rotate: 0,\n  skewX: 0,\n  scaleX: 1,\n  scaleY: 1\n};\n\nexport default function(a, b, c, d, e, f) {\n  var scaleX, scaleY, skewX;\n  if (scaleX = Math.sqrt(a * a + b * b)) a /= scaleX, b /= scaleX;\n  if (skewX = a * c + b * d) c -= a * skewX, d -= b * skewX;\n  if (scaleY = Math.sqrt(c * c + d * d)) c /= scaleY, d /= scaleY, skewX /= scaleY;\n  if (a * d < b * c) a = -a, b = -b, skewX = -skewX, scaleX = -scaleX;\n  return {\n    translateX: e,\n    translateY: f,\n    rotate: Math.atan2(b, a) * degrees,\n    skewX: Math.atan(skewX) * degrees,\n    scaleX: scaleX,\n    scaleY: scaleY\n  };\n}\n", "import decompose, {identity} from \"./decompose.js\";\n\nvar svgNode;\n\n/* eslint-disable no-undef */\nexport function parseCss(value) {\n  const m = new (typeof DOMMatrix === \"function\" ? DOMMatrix : WebKitCSSMatrix)(value + \"\");\n  return m.isIdentity ? identity : decompose(m.a, m.b, m.c, m.d, m.e, m.f);\n}\n\nexport function parseSvg(value) {\n  if (value == null) return identity;\n  if (!svgNode) svgNode = document.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n  svgNode.setAttribute(\"transform\", value);\n  if (!(value = svgNode.transform.baseVal.consolidate())) return identity;\n  value = value.matrix;\n  return decompose(value.a, value.b, value.c, value.d, value.e, value.f);\n}\n", "import number from \"../number.js\";\nimport {parseCss, parseSvg} from \"./parse.js\";\n\nfunction interpolateTransform(parse, pxComma, pxParen, degParen) {\n\n  function pop(s) {\n    return s.length ? s.pop() + \" \" : \"\";\n  }\n\n  function translate(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(\"translate(\", null, pxComma, null, pxParen);\n      q.push({i: i - 4, x: number(xa, xb)}, {i: i - 2, x: number(ya, yb)});\n    } else if (xb || yb) {\n      s.push(\"translate(\" + xb + pxComma + yb + pxParen);\n    }\n  }\n\n  function rotate(a, b, s, q) {\n    if (a !== b) {\n      if (a - b > 180) b += 360; else if (b - a > 180) a += 360; // shortest path\n      q.push({i: s.push(pop(s) + \"rotate(\", null, degParen) - 2, x: number(a, b)});\n    } else if (b) {\n      s.push(pop(s) + \"rotate(\" + b + degParen);\n    }\n  }\n\n  function skewX(a, b, s, q) {\n    if (a !== b) {\n      q.push({i: s.push(pop(s) + \"skewX(\", null, degParen) - 2, x: number(a, b)});\n    } else if (b) {\n      s.push(pop(s) + \"skewX(\" + b + degParen);\n    }\n  }\n\n  function scale(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(pop(s) + \"scale(\", null, \",\", null, \")\");\n      q.push({i: i - 4, x: number(xa, xb)}, {i: i - 2, x: number(ya, yb)});\n    } else if (xb !== 1 || yb !== 1) {\n      s.push(pop(s) + \"scale(\" + xb + \",\" + yb + \")\");\n    }\n  }\n\n  return function(a, b) {\n    var s = [], // string constants and placeholders\n        q = []; // number interpolators\n    a = parse(a), b = parse(b);\n    translate(a.translateX, a.translateY, b.translateX, b.translateY, s, q);\n    rotate(a.rotate, b.rotate, s, q);\n    skewX(a.skewX, b.skewX, s, q);\n    scale(a.scaleX, a.scaleY, b.scaleX, b.scaleY, s, q);\n    a = b = null; // gc\n    return function(t) {\n      var i = -1, n = q.length, o;\n      while (++i < n) s[(o = q[i]).i] = o.x(t);\n      return s.join(\"\");\n    };\n  };\n}\n\nexport var interpolateTransformCss = interpolateTransform(parseCss, \"px, \", \"px)\", \"deg)\");\nexport var interpolateTransformSvg = interpolateTransform(parseSvg, \", \", \")\", \")\");\n", "var epsilon2 = 1e-12;\n\nfunction cosh(x) {\n  return ((x = Math.exp(x)) + 1 / x) / 2;\n}\n\nfunction sinh(x) {\n  return ((x = Math.exp(x)) - 1 / x) / 2;\n}\n\nfunction tanh(x) {\n  return ((x = Math.exp(2 * x)) - 1) / (x + 1);\n}\n\nexport default (function zoomRho(rho, rho2, rho4) {\n\n  // p0 = [ux0, uy0, w0]\n  // p1 = [ux1, uy1, w1]\n  function zoom(p0, p1) {\n    var ux0 = p0[0], uy0 = p0[1], w0 = p0[2],\n        ux1 = p1[0], uy1 = p1[1], w1 = p1[2],\n        dx = ux1 - ux0,\n        dy = uy1 - uy0,\n        d2 = dx * dx + dy * dy,\n        i,\n        S;\n\n    // Special case for u0 ≅ u1.\n    if (d2 < epsilon2) {\n      S = Math.log(w1 / w0) / rho;\n      i = function(t) {\n        return [\n          ux0 + t * dx,\n          uy0 + t * dy,\n          w0 * Math.exp(rho * t * S)\n        ];\n      }\n    }\n\n    // General case.\n    else {\n      var d1 = Math.sqrt(d2),\n          b0 = (w1 * w1 - w0 * w0 + rho4 * d2) / (2 * w0 * rho2 * d1),\n          b1 = (w1 * w1 - w0 * w0 - rho4 * d2) / (2 * w1 * rho2 * d1),\n          r0 = Math.log(Math.sqrt(b0 * b0 + 1) - b0),\n          r1 = Math.log(Math.sqrt(b1 * b1 + 1) - b1);\n      S = (r1 - r0) / rho;\n      i = function(t) {\n        var s = t * S,\n            coshr0 = cosh(r0),\n            u = w0 / (rho2 * d1) * (coshr0 * tanh(rho * s + r0) - sinh(r0));\n        return [\n          ux0 + u * dx,\n          uy0 + u * dy,\n          w0 * coshr0 / cosh(rho * s + r0)\n        ];\n      }\n    }\n\n    i.duration = S * 1000 * rho / Math.SQRT2;\n\n    return i;\n  }\n\n  zoom.rho = function(_) {\n    var _1 = Math.max(1e-3, +_), _2 = _1 * _1, _4 = _2 * _2;\n    return zoomRho(_1, _2, _4);\n  };\n\n  return zoom;\n})(Math.SQRT2, 2, 4);\n", "import {hsl as colorHsl} from \"d3-color\";\nimport color, {hue} from \"./color.js\";\n\nfunction hsl(hue) {\n  return function(start, end) {\n    var h = hue((start = colorHsl(start)).h, (end = colorHsl(end)).h),\n        s = color(start.s, end.s),\n        l = color(start.l, end.l),\n        opacity = color(start.opacity, end.opacity);\n    return function(t) {\n      start.h = h(t);\n      start.s = s(t);\n      start.l = l(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n}\n\nexport default hsl(hue);\nexport var hslLong = hsl(color);\n", "import {lab as colorLab} from \"d3-color\";\nimport color from \"./color.js\";\n\nexport default function lab(start, end) {\n  var l = color((start = colorLab(start)).l, (end = colorLab(end)).l),\n      a = color(start.a, end.a),\n      b = color(start.b, end.b),\n      opacity = color(start.opacity, end.opacity);\n  return function(t) {\n    start.l = l(t);\n    start.a = a(t);\n    start.b = b(t);\n    start.opacity = opacity(t);\n    return start + \"\";\n  };\n}\n", "import {hcl as colorHcl} from \"d3-color\";\nimport color, {hue} from \"./color.js\";\n\nfunction hcl(hue) {\n  return function(start, end) {\n    var h = hue((start = colorHcl(start)).h, (end = colorHcl(end)).h),\n        c = color(start.c, end.c),\n        l = color(start.l, end.l),\n        opacity = color(start.opacity, end.opacity);\n    return function(t) {\n      start.h = h(t);\n      start.c = c(t);\n      start.l = l(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n}\n\nexport default hcl(hue);\nexport var hclLong = hcl(color);\n", "import {cubehelix as colorCubehelix} from \"d3-color\";\nimport color, {hue} from \"./color.js\";\n\nfunction cubehelix(hue) {\n  return (function cubehelixGamma(y) {\n    y = +y;\n\n    function cubehelix(start, end) {\n      var h = hue((start = colorCubehelix(start)).h, (end = colorCubehelix(end)).h),\n          s = color(start.s, end.s),\n          l = color(start.l, end.l),\n          opacity = color(start.opacity, end.opacity);\n      return function(t) {\n        start.h = h(t);\n        start.s = s(t);\n        start.l = l(Math.pow(t, y));\n        start.opacity = opacity(t);\n        return start + \"\";\n      };\n    }\n\n    cubehelix.gamma = cubehelixGamma;\n\n    return cubehelix;\n  })(1);\n}\n\nexport default cubehelix(hue);\nexport var cubehelixLong = cubehelix(color);\n", "import {default as value} from \"./value.js\";\n\nexport default function piecewise(interpolate, values) {\n  if (values === undefined) values = interpolate, interpolate = value;\n  var i = 0, n = values.length - 1, v = values[0], I = new Array(n < 0 ? 0 : n);\n  while (i < n) I[i] = interpolate(v, v = values[++i]);\n  return function(t) {\n    var i = Math.max(0, Math.min(n - 1, Math.floor(t *= n)));\n    return I[i](t - i);\n  };\n}\n", "export default function(interpolator, n) {\n  var samples = new Array(n);\n  for (var i = 0; i < n; ++i) samples[i] = interpolator(i / (n - 1));\n  return samples;\n}\n", "const pi = Math.PI,\n    tau = 2 * pi,\n    epsilon = 1e-6,\n    tauEpsilon = tau - epsilon;\n\nfunction append(strings) {\n  this._ += strings[0];\n  for (let i = 1, n = strings.length; i < n; ++i) {\n    this._ += arguments[i] + strings[i];\n  }\n}\n\nfunction appendRound(digits) {\n  let d = Math.floor(digits);\n  if (!(d >= 0)) throw new Error(`invalid digits: ${digits}`);\n  if (d > 15) return append;\n  const k = 10 ** d;\n  return function(strings) {\n    this._ += strings[0];\n    for (let i = 1, n = strings.length; i < n; ++i) {\n      this._ += Math.round(arguments[i] * k) / k + strings[i];\n    }\n  };\n}\n\nexport class Path {\n  constructor(digits) {\n    this._x0 = this._y0 = // start of current subpath\n    this._x1 = this._y1 = null; // end of current subpath\n    this._ = \"\";\n    this._append = digits == null ? append : appendRound(digits);\n  }\n  moveTo(x, y) {\n    this._append`M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}`;\n  }\n  closePath() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._append`Z`;\n    }\n  }\n  lineTo(x, y) {\n    this._append`L${this._x1 = +x},${this._y1 = +y}`;\n  }\n  quadraticCurveTo(x1, y1, x, y) {\n    this._append`Q${+x1},${+y1},${this._x1 = +x},${this._y1 = +y}`;\n  }\n  bezierCurveTo(x1, y1, x2, y2, x, y) {\n    this._append`C${+x1},${+y1},${+x2},${+y2},${this._x1 = +x},${this._y1 = +y}`;\n  }\n  arcTo(x1, y1, x2, y2, r) {\n    x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(`negative radius: ${r}`);\n\n    let x0 = this._x1,\n        y0 = this._y1,\n        x21 = x2 - x1,\n        y21 = y2 - y1,\n        x01 = x0 - x1,\n        y01 = y0 - y1,\n        l01_2 = x01 * x01 + y01 * y01;\n\n    // Is this path empty? Move to (x1,y1).\n    if (this._x1 === null) {\n      this._append`M${this._x1 = x1},${this._y1 = y1}`;\n    }\n\n    // Or, is (x1,y1) coincident with (x0,y0)? Do nothing.\n    else if (!(l01_2 > epsilon));\n\n    // Or, are (x0,y0), (x1,y1) and (x2,y2) collinear?\n    // Equivalently, is (x1,y1) coincident with (x2,y2)?\n    // Or, is the radius zero? Line to (x1,y1).\n    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {\n      this._append`L${this._x1 = x1},${this._y1 = y1}`;\n    }\n\n    // Otherwise, draw an arc!\n    else {\n      let x20 = x2 - x0,\n          y20 = y2 - y0,\n          l21_2 = x21 * x21 + y21 * y21,\n          l20_2 = x20 * x20 + y20 * y20,\n          l21 = Math.sqrt(l21_2),\n          l01 = Math.sqrt(l01_2),\n          l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2),\n          t01 = l / l01,\n          t21 = l / l21;\n\n      // If the start tangent is not coincident with (x0,y0), line to.\n      if (Math.abs(t01 - 1) > epsilon) {\n        this._append`L${x1 + t01 * x01},${y1 + t01 * y01}`;\n      }\n\n      this._append`A${r},${r},0,0,${+(y01 * x20 > x01 * y20)},${this._x1 = x1 + t21 * x21},${this._y1 = y1 + t21 * y21}`;\n    }\n  }\n  arc(x, y, r, a0, a1, ccw) {\n    x = +x, y = +y, r = +r, ccw = !!ccw;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(`negative radius: ${r}`);\n\n    let dx = r * Math.cos(a0),\n        dy = r * Math.sin(a0),\n        x0 = x + dx,\n        y0 = y + dy,\n        cw = 1 ^ ccw,\n        da = ccw ? a0 - a1 : a1 - a0;\n\n    // Is this path empty? Move to (x0,y0).\n    if (this._x1 === null) {\n      this._append`M${x0},${y0}`;\n    }\n\n    // Or, is (x0,y0) not coincident with the previous point? Line to (x0,y0).\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {\n      this._append`L${x0},${y0}`;\n    }\n\n    // Is this arc empty? We’re done.\n    if (!r) return;\n\n    // Does the angle go the wrong way? Flip the direction.\n    if (da < 0) da = da % tau + tau;\n\n    // Is this a complete circle? Draw two arcs to complete the circle.\n    if (da > tauEpsilon) {\n      this._append`A${r},${r},0,1,${cw},${x - dx},${y - dy}A${r},${r},0,1,${cw},${this._x1 = x0},${this._y1 = y0}`;\n    }\n\n    // Is this arc non-empty? Draw an arc!\n    else if (da > epsilon) {\n      this._append`A${r},${r},0,${+(da >= pi)},${cw},${this._x1 = x + r * Math.cos(a1)},${this._y1 = y + r * Math.sin(a1)}`;\n    }\n  }\n  rect(x, y, w, h) {\n    this._append`M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}h${w = +w}v${+h}h${-w}Z`;\n  }\n  toString() {\n    return this._;\n  }\n}\n\nexport function path() {\n  return new Path;\n}\n\n// Allow instanceof d3.path\npath.prototype = Path.prototype;\n\nexport function pathRound(digits = 3) {\n  return new Path(+digits);\n}\n", "export default function(x) {\n  return Math.abs(x = Math.round(x)) >= 1e21\n      ? x.toLocaleString(\"en\").replace(/,/g, \"\")\n      : x.toString(10);\n}\n\n// Computes the decimal coefficient and exponent of the specified number x with\n// significant digits p, where x is positive and p is in [1, 21] or undefined.\n// For example, formatDecimalParts(1.23) returns [\"123\", 0].\nexport function formatDecimalParts(x, p) {\n  if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf(\"e\")) < 0) return null; // NaN, ±Infinity\n  var i, coefficient = x.slice(0, i);\n\n  // The string returned by toExponential either has the form \\d\\.\\d+e[-+]\\d+\n  // (e.g., 1.2e+3) or the form \\de[-+]\\d+ (e.g., 1e+3).\n  return [\n    coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient,\n    +x.slice(i + 1)\n  ];\n}\n", "import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport default function(x) {\n  return x = formatDecimalParts(Math.abs(x)), x ? x[1] : NaN;\n}\n", "export default function(grouping, thousands) {\n  return function(value, width) {\n    var i = value.length,\n        t = [],\n        j = 0,\n        g = grouping[0],\n        length = 0;\n\n    while (i > 0 && g > 0) {\n      if (length + g + 1 > width) g = Math.max(1, width - length);\n      t.push(value.substring(i -= g, i + g));\n      if ((length += g + 1) > width) break;\n      g = grouping[j = (j + 1) % grouping.length];\n    }\n\n    return t.reverse().join(thousands);\n  };\n}\n", "export default function(numerals) {\n  return function(value) {\n    return value.replace(/[0-9]/g, function(i) {\n      return numerals[+i];\n    });\n  };\n}\n", "// [[fill]align][sign][symbol][0][width][,][.precision][~][type]\nvar re = /^(?:(.)?([<>=^]))?([+\\-( ])?([$#])?(0)?(\\d+)?(,)?(\\.\\d+)?(~)?([a-z%])?$/i;\n\nexport default function formatSpecifier(specifier) {\n  if (!(match = re.exec(specifier))) throw new Error(\"invalid format: \" + specifier);\n  var match;\n  return new FormatSpecifier({\n    fill: match[1],\n    align: match[2],\n    sign: match[3],\n    symbol: match[4],\n    zero: match[5],\n    width: match[6],\n    comma: match[7],\n    precision: match[8] && match[8].slice(1),\n    trim: match[9],\n    type: match[10]\n  });\n}\n\nformatSpecifier.prototype = FormatSpecifier.prototype; // instanceof\n\nexport function FormatSpecifier(specifier) {\n  this.fill = specifier.fill === undefined ? \" \" : specifier.fill + \"\";\n  this.align = specifier.align === undefined ? \">\" : specifier.align + \"\";\n  this.sign = specifier.sign === undefined ? \"-\" : specifier.sign + \"\";\n  this.symbol = specifier.symbol === undefined ? \"\" : specifier.symbol + \"\";\n  this.zero = !!specifier.zero;\n  this.width = specifier.width === undefined ? undefined : +specifier.width;\n  this.comma = !!specifier.comma;\n  this.precision = specifier.precision === undefined ? undefined : +specifier.precision;\n  this.trim = !!specifier.trim;\n  this.type = specifier.type === undefined ? \"\" : specifier.type + \"\";\n}\n\nFormatSpecifier.prototype.toString = function() {\n  return this.fill\n      + this.align\n      + this.sign\n      + this.symbol\n      + (this.zero ? \"0\" : \"\")\n      + (this.width === undefined ? \"\" : Math.max(1, this.width | 0))\n      + (this.comma ? \",\" : \"\")\n      + (this.precision === undefined ? \"\" : \".\" + Math.max(0, this.precision | 0))\n      + (this.trim ? \"~\" : \"\")\n      + this.type;\n};\n", "// Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.\nexport default function(s) {\n  out: for (var n = s.length, i = 1, i0 = -1, i1; i < n; ++i) {\n    switch (s[i]) {\n      case \".\": i0 = i1 = i; break;\n      case \"0\": if (i0 === 0) i0 = i; i1 = i; break;\n      default: if (!+s[i]) break out; if (i0 > 0) i0 = 0; break;\n    }\n  }\n  return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;\n}\n", "import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport var prefixExponent;\n\nexport default function(x, p) {\n  var d = formatDecimalParts(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n      exponent = d[1],\n      i = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1,\n      n = coefficient.length;\n  return i === n ? coefficient\n      : i > n ? coefficient + new Array(i - n + 1).join(\"0\")\n      : i > 0 ? coefficient.slice(0, i) + \".\" + coefficient.slice(i)\n      : \"0.\" + new Array(1 - i).join(\"0\") + formatDecimalParts(x, Math.max(0, p + i - 1))[0]; // less than 1y!\n}\n", "import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport default function(x, p) {\n  var d = formatDecimalParts(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n      exponent = d[1];\n  return exponent < 0 ? \"0.\" + new Array(-exponent).join(\"0\") + coefficient\n      : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + \".\" + coefficient.slice(exponent + 1)\n      : coefficient + new Array(exponent - coefficient.length + 2).join(\"0\");\n}\n", "import formatDecimal from \"./formatDecimal.js\";\nimport formatPrefixAuto from \"./formatPrefixAuto.js\";\nimport formatRounded from \"./formatRounded.js\";\n\nexport default {\n  \"%\": (x, p) => (x * 100).toFixed(p),\n  \"b\": (x) => Math.round(x).toString(2),\n  \"c\": (x) => x + \"\",\n  \"d\": formatDecimal,\n  \"e\": (x, p) => x.toExponential(p),\n  \"f\": (x, p) => x.toFixed(p),\n  \"g\": (x, p) => x.toPrecision(p),\n  \"o\": (x) => Math.round(x).toString(8),\n  \"p\": (x, p) => formatRounded(x * 100, p),\n  \"r\": formatRounded,\n  \"s\": formatPrefixAuto,\n  \"X\": (x) => Math.round(x).toString(16).toUpperCase(),\n  \"x\": (x) => Math.round(x).toString(16)\n};\n", "export default function(x) {\n  return x;\n}\n", "import exponent from \"./exponent.js\";\nimport formatGroup from \"./formatGroup.js\";\nimport formatNumerals from \"./formatNumerals.js\";\nimport formatSpecifier from \"./formatSpecifier.js\";\nimport formatTrim from \"./formatTrim.js\";\nimport formatTypes from \"./formatTypes.js\";\nimport {prefixExponent} from \"./formatPrefixAuto.js\";\nimport identity from \"./identity.js\";\n\nvar map = Array.prototype.map,\n    prefixes = [\"y\",\"z\",\"a\",\"f\",\"p\",\"n\",\"µ\",\"m\",\"\",\"k\",\"M\",\"G\",\"T\",\"P\",\"E\",\"Z\",\"Y\"];\n\nexport default function(locale) {\n  var group = locale.grouping === undefined || locale.thousands === undefined ? identity : formatGroup(map.call(locale.grouping, Number), locale.thousands + \"\"),\n      currencyPrefix = locale.currency === undefined ? \"\" : locale.currency[0] + \"\",\n      currencySuffix = locale.currency === undefined ? \"\" : locale.currency[1] + \"\",\n      decimal = locale.decimal === undefined ? \".\" : locale.decimal + \"\",\n      numerals = locale.numerals === undefined ? identity : formatNumerals(map.call(locale.numerals, String)),\n      percent = locale.percent === undefined ? \"%\" : locale.percent + \"\",\n      minus = locale.minus === undefined ? \"−\" : locale.minus + \"\",\n      nan = locale.nan === undefined ? \"NaN\" : locale.nan + \"\";\n\n  function newFormat(specifier) {\n    specifier = formatSpecifier(specifier);\n\n    var fill = specifier.fill,\n        align = specifier.align,\n        sign = specifier.sign,\n        symbol = specifier.symbol,\n        zero = specifier.zero,\n        width = specifier.width,\n        comma = specifier.comma,\n        precision = specifier.precision,\n        trim = specifier.trim,\n        type = specifier.type;\n\n    // The \"n\" type is an alias for \",g\".\n    if (type === \"n\") comma = true, type = \"g\";\n\n    // The \"\" type, and any invalid type, is an alias for \".12~g\".\n    else if (!formatTypes[type]) precision === undefined && (precision = 12), trim = true, type = \"g\";\n\n    // If zero fill is specified, padding goes after sign and before digits.\n    if (zero || (fill === \"0\" && align === \"=\")) zero = true, fill = \"0\", align = \"=\";\n\n    // Compute the prefix and suffix.\n    // For SI-prefix, the suffix is lazily computed.\n    var prefix = symbol === \"$\" ? currencyPrefix : symbol === \"#\" && /[boxX]/.test(type) ? \"0\" + type.toLowerCase() : \"\",\n        suffix = symbol === \"$\" ? currencySuffix : /[%p]/.test(type) ? percent : \"\";\n\n    // What format function should we use?\n    // Is this an integer type?\n    // Can this type generate exponential notation?\n    var formatType = formatTypes[type],\n        maybeSuffix = /[defgprs%]/.test(type);\n\n    // Set the default precision if not specified,\n    // or clamp the specified precision to the supported range.\n    // For significant precision, it must be in [1, 21].\n    // For fixed precision, it must be in [0, 20].\n    precision = precision === undefined ? 6\n        : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision))\n        : Math.max(0, Math.min(20, precision));\n\n    function format(value) {\n      var valuePrefix = prefix,\n          valueSuffix = suffix,\n          i, n, c;\n\n      if (type === \"c\") {\n        valueSuffix = formatType(value) + valueSuffix;\n        value = \"\";\n      } else {\n        value = +value;\n\n        // Determine the sign. -0 is not less than 0, but 1 / -0 is!\n        var valueNegative = value < 0 || 1 / value < 0;\n\n        // Perform the initial formatting.\n        value = isNaN(value) ? nan : formatType(Math.abs(value), precision);\n\n        // Trim insignificant zeros.\n        if (trim) value = formatTrim(value);\n\n        // If a negative value rounds to zero after formatting, and no explicit positive sign is requested, hide the sign.\n        if (valueNegative && +value === 0 && sign !== \"+\") valueNegative = false;\n\n        // Compute the prefix and suffix.\n        valuePrefix = (valueNegative ? (sign === \"(\" ? sign : minus) : sign === \"-\" || sign === \"(\" ? \"\" : sign) + valuePrefix;\n        valueSuffix = (type === \"s\" ? prefixes[8 + prefixExponent / 3] : \"\") + valueSuffix + (valueNegative && sign === \"(\" ? \")\" : \"\");\n\n        // Break the formatted value into the integer “value” part that can be\n        // grouped, and fractional or exponential “suffix” part that is not.\n        if (maybeSuffix) {\n          i = -1, n = value.length;\n          while (++i < n) {\n            if (c = value.charCodeAt(i), 48 > c || c > 57) {\n              valueSuffix = (c === 46 ? decimal + value.slice(i + 1) : value.slice(i)) + valueSuffix;\n              value = value.slice(0, i);\n              break;\n            }\n          }\n        }\n      }\n\n      // If the fill character is not \"0\", grouping is applied before padding.\n      if (comma && !zero) value = group(value, Infinity);\n\n      // Compute the padding.\n      var length = valuePrefix.length + value.length + valueSuffix.length,\n          padding = length < width ? new Array(width - length + 1).join(fill) : \"\";\n\n      // If the fill character is \"0\", grouping is applied after padding.\n      if (comma && zero) value = group(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = \"\";\n\n      // Reconstruct the final output based on the desired alignment.\n      switch (align) {\n        case \"<\": value = valuePrefix + value + valueSuffix + padding; break;\n        case \"=\": value = valuePrefix + padding + value + valueSuffix; break;\n        case \"^\": value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length); break;\n        default: value = padding + valuePrefix + value + valueSuffix; break;\n      }\n\n      return numerals(value);\n    }\n\n    format.toString = function() {\n      return specifier + \"\";\n    };\n\n    return format;\n  }\n\n  function formatPrefix(specifier, value) {\n    var f = newFormat((specifier = formatSpecifier(specifier), specifier.type = \"f\", specifier)),\n        e = Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3,\n        k = Math.pow(10, -e),\n        prefix = prefixes[8 + e / 3];\n    return function(value) {\n      return f(k * value) + prefix;\n    };\n  }\n\n  return {\n    format: newFormat,\n    formatPrefix: formatPrefix\n  };\n}\n", "import formatLocale from \"./locale.js\";\n\nvar locale;\nexport var format;\nexport var formatPrefix;\n\ndefaultLocale({\n  thousands: \",\",\n  grouping: [3],\n  currency: [\"$\", \"\"]\n});\n\nexport default function defaultLocale(definition) {\n  locale = formatLocale(definition);\n  format = locale.format;\n  formatPrefix = locale.formatPrefix;\n  return locale;\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step) {\n  return Math.max(0, -exponent(Math.abs(step)));\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step, value) {\n  return Math.max(0, Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3 - exponent(Math.abs(step)));\n}\n", "import exponent from \"./exponent.js\";\n\nexport default function(step, max) {\n  step = Math.abs(step), max = Math.abs(max) - step;\n  return Math.max(0, exponent(max) - exponent(step)) + 1;\n}\n", "export function initRange(domain, range) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: this.range(domain); break;\n    default: this.range(range).domain(domain); break;\n  }\n  return this;\n}\n\nexport function initInterpolator(domain, interpolator) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: {\n      if (typeof domain === \"function\") this.interpolator(domain);\n      else this.range(domain);\n      break;\n    }\n    default: {\n      this.domain(domain);\n      if (typeof interpolator === \"function\") this.interpolator(interpolator);\n      else this.range(interpolator);\n      break;\n    }\n  }\n  return this;\n}\n", "import {InternMap} from \"d3-array\";\nimport {initRange} from \"./init.js\";\n\nexport const implicit = Symbol(\"implicit\");\n\nexport default function ordinal() {\n  var index = new InternMap(),\n      domain = [],\n      range = [],\n      unknown = implicit;\n\n  function scale(d) {\n    let i = index.get(d);\n    if (i === undefined) {\n      if (unknown !== implicit) return unknown;\n      index.set(d, i = domain.push(d) - 1);\n    }\n    return range[i % range.length];\n  }\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [], index = new InternMap();\n    for (const value of _) {\n      if (index.has(value)) continue;\n      index.set(value, domain.push(value) - 1);\n    }\n    return scale;\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), scale) : range.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return ordinal(domain, range).unknown(unknown);\n  };\n\n  initRange.apply(scale, arguments);\n\n  return scale;\n}\n", "import {range as sequence} from \"d3-array\";\nimport {initRange} from \"./init.js\";\nimport ordinal from \"./ordinal.js\";\n\nexport default function band() {\n  var scale = ordinal().unknown(undefined),\n      domain = scale.domain,\n      ordinalRange = scale.range,\n      r0 = 0,\n      r1 = 1,\n      step,\n      bandwidth,\n      round = false,\n      paddingInner = 0,\n      paddingOuter = 0,\n      align = 0.5;\n\n  delete scale.unknown;\n\n  function rescale() {\n    var n = domain().length,\n        reverse = r1 < r0,\n        start = reverse ? r1 : r0,\n        stop = reverse ? r0 : r1;\n    step = (stop - start) / Math.max(1, n - paddingInner + paddingOuter * 2);\n    if (round) step = Math.floor(step);\n    start += (stop - start - step * (n - paddingInner)) * align;\n    bandwidth = step * (1 - paddingInner);\n    if (round) start = Math.round(start), bandwidth = Math.round(bandwidth);\n    var values = sequence(n).map(function(i) { return start + step * i; });\n    return ordinalRange(reverse ? values.reverse() : values);\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? ([r0, r1] = _, r0 = +r0, r1 = +r1, rescale()) : [r0, r1];\n  };\n\n  scale.rangeRound = function(_) {\n    return [r0, r1] = _, r0 = +r0, r1 = +r1, round = true, rescale();\n  };\n\n  scale.bandwidth = function() {\n    return bandwidth;\n  };\n\n  scale.step = function() {\n    return step;\n  };\n\n  scale.round = function(_) {\n    return arguments.length ? (round = !!_, rescale()) : round;\n  };\n\n  scale.padding = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, paddingOuter = +_), rescale()) : paddingInner;\n  };\n\n  scale.paddingInner = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, _), rescale()) : paddingInner;\n  };\n\n  scale.paddingOuter = function(_) {\n    return arguments.length ? (paddingOuter = +_, rescale()) : paddingOuter;\n  };\n\n  scale.align = function(_) {\n    return arguments.length ? (align = Math.max(0, Math.min(1, _)), rescale()) : align;\n  };\n\n  scale.copy = function() {\n    return band(domain(), [r0, r1])\n        .round(round)\n        .paddingInner(paddingInner)\n        .paddingOuter(paddingOuter)\n        .align(align);\n  };\n\n  return initRange.apply(rescale(), arguments);\n}\n\nfunction pointish(scale) {\n  var copy = scale.copy;\n\n  scale.padding = scale.paddingOuter;\n  delete scale.paddingInner;\n  delete scale.paddingOuter;\n\n  scale.copy = function() {\n    return pointish(copy());\n  };\n\n  return scale;\n}\n\nexport function point() {\n  return pointish(band.apply(null, arguments).paddingInner(1));\n}\n", "export default function constants(x) {\n  return function() {\n    return x;\n  };\n}\n", "export default function number(x) {\n  return +x;\n}\n", "import {bisect} from \"d3-array\";\nimport {interpolate as interpolateValue, interpolateNumber, interpolateRound} from \"d3-interpolate\";\nimport constant from \"./constant.js\";\nimport number from \"./number.js\";\n\nvar unit = [0, 1];\n\nexport function identity(x) {\n  return x;\n}\n\nfunction normalize(a, b) {\n  return (b -= (a = +a))\n      ? function(x) { return (x - a) / b; }\n      : constant(isNaN(b) ? NaN : 0.5);\n}\n\nfunction clamper(a, b) {\n  var t;\n  if (a > b) t = a, a = b, b = t;\n  return function(x) { return Math.max(a, Math.min(b, x)); };\n}\n\n// normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].\n// interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].\nfunction bimap(domain, range, interpolate) {\n  var d0 = domain[0], d1 = domain[1], r0 = range[0], r1 = range[1];\n  if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate(r1, r0);\n  else d0 = normalize(d0, d1), r0 = interpolate(r0, r1);\n  return function(x) { return r0(d0(x)); };\n}\n\nfunction polymap(domain, range, interpolate) {\n  var j = Math.min(domain.length, range.length) - 1,\n      d = new Array(j),\n      r = new Array(j),\n      i = -1;\n\n  // Reverse descending domains.\n  if (domain[j] < domain[0]) {\n    domain = domain.slice().reverse();\n    range = range.slice().reverse();\n  }\n\n  while (++i < j) {\n    d[i] = normalize(domain[i], domain[i + 1]);\n    r[i] = interpolate(range[i], range[i + 1]);\n  }\n\n  return function(x) {\n    var i = bisect(domain, x, 1, j) - 1;\n    return r[i](d[i](x));\n  };\n}\n\nexport function copy(source, target) {\n  return target\n      .domain(source.domain())\n      .range(source.range())\n      .interpolate(source.interpolate())\n      .clamp(source.clamp())\n      .unknown(source.unknown());\n}\n\nexport function transformer() {\n  var domain = unit,\n      range = unit,\n      interpolate = interpolateValue,\n      transform,\n      untransform,\n      unknown,\n      clamp = identity,\n      piecewise,\n      output,\n      input;\n\n  function rescale() {\n    var n = Math.min(domain.length, range.length);\n    if (clamp !== identity) clamp = clamper(domain[0], domain[n - 1]);\n    piecewise = n > 2 ? polymap : bimap;\n    output = input = null;\n    return scale;\n  }\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : (output || (output = piecewise(domain.map(transform), range, interpolate)))(transform(clamp(x)));\n  }\n\n  scale.invert = function(y) {\n    return clamp(untransform((input || (input = piecewise(range, domain.map(transform), interpolateNumber)))(y)));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain = Array.from(_, number), rescale()) : domain.slice();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n\n  scale.rangeRound = function(_) {\n    return range = Array.from(_), interpolate = interpolateRound, rescale();\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = _ ? true : identity, rescale()) : clamp !== identity;\n  };\n\n  scale.interpolate = function(_) {\n    return arguments.length ? (interpolate = _, rescale()) : interpolate;\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t, u) {\n    transform = t, untransform = u;\n    return rescale();\n  };\n}\n\nexport default function continuous() {\n  return transformer()(identity, identity);\n}\n", "import {tickStep} from \"d3-array\";\nimport {format, formatPrefix, formatSpecifier, precisionFixed, precisionPrefix, precisionRound} from \"d3-format\";\n\nexport default function tickFormat(start, stop, count, specifier) {\n  var step = tickStep(start, stop, count),\n      precision;\n  specifier = formatSpecifier(specifier == null ? \",f\" : specifier);\n  switch (specifier.type) {\n    case \"s\": {\n      var value = Math.max(Math.abs(start), Math.abs(stop));\n      if (specifier.precision == null && !isNaN(precision = precisionPrefix(step, value))) specifier.precision = precision;\n      return formatPrefix(specifier, value);\n    }\n    case \"\":\n    case \"e\":\n    case \"g\":\n    case \"p\":\n    case \"r\": {\n      if (specifier.precision == null && !isNaN(precision = precisionRound(step, Math.max(Math.abs(start), Math.abs(stop))))) specifier.precision = precision - (specifier.type === \"e\");\n      break;\n    }\n    case \"f\":\n    case \"%\": {\n      if (specifier.precision == null && !isNaN(precision = precisionFixed(step))) specifier.precision = precision - (specifier.type === \"%\") * 2;\n      break;\n    }\n  }\n  return format(specifier);\n}\n", "import {ticks, tickIncrement} from \"d3-array\";\nimport continuous, {copy} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\nimport tickFormat from \"./tickFormat.js\";\n\nexport function linearish(scale) {\n  var domain = scale.domain;\n\n  scale.ticks = function(count) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], count == null ? 10 : count);\n  };\n\n  scale.tickFormat = function(count, specifier) {\n    var d = domain();\n    return tickFormat(d[0], d[d.length - 1], count == null ? 10 : count, specifier);\n  };\n\n  scale.nice = function(count) {\n    if (count == null) count = 10;\n\n    var d = domain();\n    var i0 = 0;\n    var i1 = d.length - 1;\n    var start = d[i0];\n    var stop = d[i1];\n    var prestep;\n    var step;\n    var maxIter = 10;\n\n    if (stop < start) {\n      step = start, start = stop, stop = step;\n      step = i0, i0 = i1, i1 = step;\n    }\n    \n    while (maxIter-- > 0) {\n      step = tickIncrement(start, stop, count);\n      if (step === prestep) {\n        d[i0] = start\n        d[i1] = stop\n        return domain(d);\n      } else if (step > 0) {\n        start = Math.floor(start / step) * step;\n        stop = Math.ceil(stop / step) * step;\n      } else if (step < 0) {\n        start = Math.ceil(start * step) / step;\n        stop = Math.floor(stop * step) / step;\n      } else {\n        break;\n      }\n      prestep = step;\n    }\n\n    return scale;\n  };\n\n  return scale;\n}\n\nexport default function linear() {\n  var scale = continuous();\n\n  scale.copy = function() {\n    return copy(scale, linear());\n  };\n\n  initRange.apply(scale, arguments);\n\n  return linearish(scale);\n}\n", "import {linearish} from \"./linear.js\";\nimport number from \"./number.js\";\n\nexport default function identity(domain) {\n  var unknown;\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : x;\n  }\n\n  scale.invert = scale;\n\n  scale.domain = scale.range = function(_) {\n    return arguments.length ? (domain = Array.from(_, number), scale) : domain.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return identity(domain).unknown(unknown);\n  };\n\n  domain = arguments.length ? Array.from(domain, number) : [0, 1];\n\n  return linearish(scale);\n}\n", "export default function nice(domain, interval) {\n  domain = domain.slice();\n\n  var i0 = 0,\n      i1 = domain.length - 1,\n      x0 = domain[i0],\n      x1 = domain[i1],\n      t;\n\n  if (x1 < x0) {\n    t = i0, i0 = i1, i1 = t;\n    t = x0, x0 = x1, x1 = t;\n  }\n\n  domain[i0] = interval.floor(x0);\n  domain[i1] = interval.ceil(x1);\n  return domain;\n}\n", "import {ticks} from \"d3-array\";\nimport {format, formatSpecifier} from \"d3-format\";\nimport nice from \"./nice.js\";\nimport {copy, transformer} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\n\nfunction transformLog(x) {\n  return Math.log(x);\n}\n\nfunction transformExp(x) {\n  return Math.exp(x);\n}\n\nfunction transformLogn(x) {\n  return -Math.log(-x);\n}\n\nfunction transformExpn(x) {\n  return -Math.exp(-x);\n}\n\nfunction pow10(x) {\n  return isFinite(x) ? +(\"1e\" + x) : x < 0 ? 0 : x;\n}\n\nfunction powp(base) {\n  return base === 10 ? pow10\n      : base === Math.E ? Math.exp\n      : x => Math.pow(base, x);\n}\n\nfunction logp(base) {\n  return base === Math.E ? Math.log\n      : base === 10 && Math.log10\n      || base === 2 && Math.log2\n      || (base = Math.log(base), x => Math.log(x) / base);\n}\n\nfunction reflect(f) {\n  return (x, k) => -f(-x, k);\n}\n\nexport function loggish(transform) {\n  const scale = transform(transformLog, transformExp);\n  const domain = scale.domain;\n  let base = 10;\n  let logs;\n  let pows;\n\n  function rescale() {\n    logs = logp(base), pows = powp(base);\n    if (domain()[0] < 0) {\n      logs = reflect(logs), pows = reflect(pows);\n      transform(transformLogn, transformExpn);\n    } else {\n      transform(transformLog, transformExp);\n    }\n    return scale;\n  }\n\n  scale.base = function(_) {\n    return arguments.length ? (base = +_, rescale()) : base;\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n\n  scale.ticks = count => {\n    const d = domain();\n    let u = d[0];\n    let v = d[d.length - 1];\n    const r = v < u;\n\n    if (r) ([u, v] = [v, u]);\n\n    let i = logs(u);\n    let j = logs(v);\n    let k;\n    let t;\n    const n = count == null ? 10 : +count;\n    let z = [];\n\n    if (!(base % 1) && j - i < n) {\n      i = Math.floor(i), j = Math.ceil(j);\n      if (u > 0) for (; i <= j; ++i) {\n        for (k = 1; k < base; ++k) {\n          t = i < 0 ? k / pows(-i) : k * pows(i);\n          if (t < u) continue;\n          if (t > v) break;\n          z.push(t);\n        }\n      } else for (; i <= j; ++i) {\n        for (k = base - 1; k >= 1; --k) {\n          t = i > 0 ? k / pows(-i) : k * pows(i);\n          if (t < u) continue;\n          if (t > v) break;\n          z.push(t);\n        }\n      }\n      if (z.length * 2 < n) z = ticks(u, v, n);\n    } else {\n      z = ticks(i, j, Math.min(j - i, n)).map(pows);\n    }\n    return r ? z.reverse() : z;\n  };\n\n  scale.tickFormat = (count, specifier) => {\n    if (count == null) count = 10;\n    if (specifier == null) specifier = base === 10 ? \"s\" : \",\";\n    if (typeof specifier !== \"function\") {\n      if (!(base % 1) && (specifier = formatSpecifier(specifier)).precision == null) specifier.trim = true;\n      specifier = format(specifier);\n    }\n    if (count === Infinity) return specifier;\n    const k = Math.max(1, base * count / scale.ticks().length); // TODO fast estimate?\n    return d => {\n      let i = d / pows(Math.round(logs(d)));\n      if (i * base < base - 0.5) i *= base;\n      return i <= k ? specifier(d) : \"\";\n    };\n  };\n\n  scale.nice = () => {\n    return domain(nice(domain(), {\n      floor: x => pows(Math.floor(logs(x))),\n      ceil: x => pows(Math.ceil(logs(x)))\n    }));\n  };\n\n  return scale;\n}\n\nexport default function log() {\n  const scale = loggish(transformer()).domain([1, 10]);\n  scale.copy = () => copy(scale, log()).base(scale.base());\n  initRange.apply(scale, arguments);\n  return scale;\n}\n", "import {linearish} from \"./linear.js\";\nimport {copy, transformer} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\n\nfunction transformSymlog(c) {\n  return function(x) {\n    return Math.sign(x) * Math.log1p(Math.abs(x / c));\n  };\n}\n\nfunction transformSymexp(c) {\n  return function(x) {\n    return Math.sign(x) * Math.expm1(Math.abs(x)) * c;\n  };\n}\n\nexport function symlogish(transform) {\n  var c = 1, scale = transform(transformSymlog(c), transformSymexp(c));\n\n  scale.constant = function(_) {\n    return arguments.length ? transform(transformSymlog(c = +_), transformSymexp(c)) : c;\n  };\n\n  return linearish(scale);\n}\n\nexport default function symlog() {\n  var scale = symlogish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, symlog()).constant(scale.constant());\n  };\n\n  return initRange.apply(scale, arguments);\n}\n", "import {linearish} from \"./linear.js\";\nimport {copy, identity, transformer} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\n\nfunction transformPow(exponent) {\n  return function(x) {\n    return x < 0 ? -Math.pow(-x, exponent) : Math.pow(x, exponent);\n  };\n}\n\nfunction transformSqrt(x) {\n  return x < 0 ? -Math.sqrt(-x) : Math.sqrt(x);\n}\n\nfunction transformSquare(x) {\n  return x < 0 ? -x * x : x * x;\n}\n\nexport function powish(transform) {\n  var scale = transform(identity, identity),\n      exponent = 1;\n\n  function rescale() {\n    return exponent === 1 ? transform(identity, identity)\n        : exponent === 0.5 ? transform(transformSqrt, transformSquare)\n        : transform(transformPow(exponent), transformPow(1 / exponent));\n  }\n\n  scale.exponent = function(_) {\n    return arguments.length ? (exponent = +_, rescale()) : exponent;\n  };\n\n  return linearish(scale);\n}\n\nexport default function pow() {\n  var scale = powish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, pow()).exponent(scale.exponent());\n  };\n\n  initRange.apply(scale, arguments);\n\n  return scale;\n}\n\nexport function sqrt() {\n  return pow.apply(null, arguments).exponent(0.5);\n}\n", "import continuous from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\nimport {linearish} from \"./linear.js\";\nimport number from \"./number.js\";\n\nfunction square(x) {\n  return Math.sign(x) * x * x;\n}\n\nfunction unsquare(x) {\n  return Math.sign(x) * Math.sqrt(Math.abs(x));\n}\n\nexport default function radial() {\n  var squared = continuous(),\n      range = [0, 1],\n      round = false,\n      unknown;\n\n  function scale(x) {\n    var y = unsquare(squared(x));\n    return isNaN(y) ? unknown : round ? Math.round(y) : y;\n  }\n\n  scale.invert = function(y) {\n    return squared.invert(square(y));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (squared.domain(_), scale) : squared.domain();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (squared.range((range = Array.from(_, number)).map(square)), scale) : range.slice();\n  };\n\n  scale.rangeRound = function(_) {\n    return scale.range(_).round(true);\n  };\n\n  scale.round = function(_) {\n    return arguments.length ? (round = !!_, scale) : round;\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (squared.clamp(_), scale) : squared.clamp();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return radial(squared.domain(), range)\n        .round(round)\n        .clamp(squared.clamp())\n        .unknown(unknown);\n  };\n\n  initRange.apply(scale, arguments);\n\n  return linearish(scale);\n}\n", "import {ascending, bisect, quantileSorted as threshold} from \"d3-array\";\nimport {initRange} from \"./init.js\";\n\nexport default function quantile() {\n  var domain = [],\n      range = [],\n      thresholds = [],\n      unknown;\n\n  function rescale() {\n    var i = 0, n = Math.max(1, range.length);\n    thresholds = new Array(n - 1);\n    while (++i < n) thresholds[i - 1] = threshold(domain, i / n);\n    return scale;\n  }\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : range[bisect(thresholds, x)];\n  }\n\n  scale.invertExtent = function(y) {\n    var i = range.indexOf(y);\n    return i < 0 ? [NaN, NaN] : [\n      i > 0 ? thresholds[i - 1] : domain[0],\n      i < thresholds.length ? thresholds[i] : domain[domain.length - 1]\n    ];\n  };\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [];\n    for (let d of _) if (d != null && !isNaN(d = +d)) domain.push(d);\n    domain.sort(ascending);\n    return rescale();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.quantiles = function() {\n    return thresholds.slice();\n  };\n\n  scale.copy = function() {\n    return quantile()\n        .domain(domain)\n        .range(range)\n        .unknown(unknown);\n  };\n\n  return initRange.apply(scale, arguments);\n}\n", "import {bisect} from \"d3-array\";\nimport {linearish} from \"./linear.js\";\nimport {initRange} from \"./init.js\";\n\nexport default function quantize() {\n  var x0 = 0,\n      x1 = 1,\n      n = 1,\n      domain = [0.5],\n      range = [0, 1],\n      unknown;\n\n  function scale(x) {\n    return x != null && x <= x ? range[bisect(domain, x, 0, n)] : unknown;\n  }\n\n  function rescale() {\n    var i = -1;\n    domain = new Array(n);\n    while (++i < n) domain[i] = ((i + 1) * x1 - (i - n) * x0) / (n + 1);\n    return scale;\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? ([x0, x1] = _, x0 = +x0, x1 = +x1, rescale()) : [x0, x1];\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (n = (range = Array.from(_)).length - 1, rescale()) : range.slice();\n  };\n\n  scale.invertExtent = function(y) {\n    var i = range.indexOf(y);\n    return i < 0 ? [NaN, NaN]\n        : i < 1 ? [x0, domain[0]]\n        : i >= n ? [domain[n - 1], x1]\n        : [domain[i - 1], domain[i]];\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : scale;\n  };\n\n  scale.thresholds = function() {\n    return domain.slice();\n  };\n\n  scale.copy = function() {\n    return quantize()\n        .domain([x0, x1])\n        .range(range)\n        .unknown(unknown);\n  };\n\n  return initRange.apply(linearish(scale), arguments);\n}\n", "import {bisect} from \"d3-array\";\nimport {initRange} from \"./init.js\";\n\nexport default function threshold() {\n  var domain = [0.5],\n      range = [0, 1],\n      unknown,\n      n = 1;\n\n  function scale(x) {\n    return x != null && x <= x ? range[bisect(domain, x, 0, n)] : unknown;\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain = Array.from(_), n = Math.min(domain.length, range.length - 1), scale) : domain.slice();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), n = Math.min(domain.length, range.length - 1), scale) : range.slice();\n  };\n\n  scale.invertExtent = function(y) {\n    var i = range.indexOf(y);\n    return [domain[i - 1], domain[i]];\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return threshold()\n        .domain(domain)\n        .range(range)\n        .unknown(unknown);\n  };\n\n  return initRange.apply(scale, arguments);\n}\n", "const t0 = new Date, t1 = new Date;\n\nexport function timeInterval(floori, offseti, count, field) {\n\n  function interval(date) {\n    return floori(date = arguments.length === 0 ? new Date : new Date(+date)), date;\n  }\n\n  interval.floor = (date) => {\n    return floori(date = new Date(+date)), date;\n  };\n\n  interval.ceil = (date) => {\n    return floori(date = new Date(date - 1)), offseti(date, 1), floori(date), date;\n  };\n\n  interval.round = (date) => {\n    const d0 = interval(date), d1 = interval.ceil(date);\n    return date - d0 < d1 - date ? d0 : d1;\n  };\n\n  interval.offset = (date, step) => {\n    return offseti(date = new Date(+date), step == null ? 1 : Math.floor(step)), date;\n  };\n\n  interval.range = (start, stop, step) => {\n    const range = [];\n    start = interval.ceil(start);\n    step = step == null ? 1 : Math.floor(step);\n    if (!(start < stop) || !(step > 0)) return range; // also handles Invalid Date\n    let previous;\n    do range.push(previous = new Date(+start)), offseti(start, step), floori(start);\n    while (previous < start && start < stop);\n    return range;\n  };\n\n  interval.filter = (test) => {\n    return timeInterval((date) => {\n      if (date >= date) while (floori(date), !test(date)) date.setTime(date - 1);\n    }, (date, step) => {\n      if (date >= date) {\n        if (step < 0) while (++step <= 0) {\n          while (offseti(date, -1), !test(date)) {} // eslint-disable-line no-empty\n        } else while (--step >= 0) {\n          while (offseti(date, +1), !test(date)) {} // eslint-disable-line no-empty\n        }\n      }\n    });\n  };\n\n  if (count) {\n    interval.count = (start, end) => {\n      t0.setTime(+start), t1.setTime(+end);\n      floori(t0), floori(t1);\n      return Math.floor(count(t0, t1));\n    };\n\n    interval.every = (step) => {\n      step = Math.floor(step);\n      return !isFinite(step) || !(step > 0) ? null\n          : !(step > 1) ? interval\n          : interval.filter(field\n              ? (d) => field(d) % step === 0\n              : (d) => interval.count(0, d) % step === 0);\n    };\n  }\n\n  return interval;\n}\n", "import {timeInterval} from \"./interval.js\";\n\nexport const millisecond = timeInterval(() => {\n  // noop\n}, (date, step) => {\n  date.setTime(+date + step);\n}, (start, end) => {\n  return end - start;\n});\n\n// An optimized implementation for this simple case.\nmillisecond.every = (k) => {\n  k = Math.floor(k);\n  if (!isFinite(k) || !(k > 0)) return null;\n  if (!(k > 1)) return millisecond;\n  return timeInterval((date) => {\n    date.setTime(Math.floor(date / k) * k);\n  }, (date, step) => {\n    date.setTime(+date + step * k);\n  }, (start, end) => {\n    return (end - start) / k;\n  });\n};\n\nexport const milliseconds = millisecond.range;\n", "export const durationSecond = 1000;\nexport const durationMinute = durationSecond * 60;\nexport const durationHour = durationMinute * 60;\nexport const durationDay = durationHour * 24;\nexport const durationWeek = durationDay * 7;\nexport const durationMonth = durationDay * 30;\nexport const durationYear = durationDay * 365;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationSecond} from \"./duration.js\";\n\nexport const second = timeInterval((date) => {\n  date.setTime(date - date.getMilliseconds());\n}, (date, step) => {\n  date.setTime(+date + step * durationSecond);\n}, (start, end) => {\n  return (end - start) / durationSecond;\n}, (date) => {\n  return date.getUTCSeconds();\n});\n\nexport const seconds = second.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationMinute, durationSecond} from \"./duration.js\";\n\nexport const timeMinute = timeInterval((date) => {\n  date.setTime(date - date.getMilliseconds() - date.getSeconds() * durationSecond);\n}, (date, step) => {\n  date.setTime(+date + step * durationMinute);\n}, (start, end) => {\n  return (end - start) / durationMinute;\n}, (date) => {\n  return date.getMinutes();\n});\n\nexport const timeMinutes = timeMinute.range;\n\nexport const utcMinute = timeInterval((date) => {\n  date.setUTCSeconds(0, 0);\n}, (date, step) => {\n  date.setTime(+date + step * durationMinute);\n}, (start, end) => {\n  return (end - start) / durationMinute;\n}, (date) => {\n  return date.getUTCMinutes();\n});\n\nexport const utcMinutes = utcMinute.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationHour, durationMinute, durationSecond} from \"./duration.js\";\n\nexport const timeHour = timeInterval((date) => {\n  date.setTime(date - date.getMilliseconds() - date.getSeconds() * durationSecond - date.getMinutes() * durationMinute);\n}, (date, step) => {\n  date.setTime(+date + step * durationHour);\n}, (start, end) => {\n  return (end - start) / durationHour;\n}, (date) => {\n  return date.getHours();\n});\n\nexport const timeHours = timeHour.range;\n\nexport const utcHour = timeInterval((date) => {\n  date.setUTCMinutes(0, 0, 0);\n}, (date, step) => {\n  date.setTime(+date + step * durationHour);\n}, (start, end) => {\n  return (end - start) / durationHour;\n}, (date) => {\n  return date.getUTCHours();\n});\n\nexport const utcHours = utcHour.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationDay, durationMinute} from \"./duration.js\";\n\nexport const timeDay = timeInterval(\n  date => date.setHours(0, 0, 0, 0),\n  (date, step) => date.setDate(date.getDate() + step),\n  (start, end) => (end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * durationMinute) / durationDay,\n  date => date.getDate() - 1\n);\n\nexport const timeDays = timeDay.range;\n\nexport const utcDay = timeInterval((date) => {\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCDate(date.getUTCDate() + step);\n}, (start, end) => {\n  return (end - start) / durationDay;\n}, (date) => {\n  return date.getUTCDate() - 1;\n});\n\nexport const utcDays = utcDay.range;\n\nexport const unixDay = timeInterval((date) => {\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCDate(date.getUTCDate() + step);\n}, (start, end) => {\n  return (end - start) / durationDay;\n}, (date) => {\n  return Math.floor(date / durationDay);\n});\n\nexport const unixDays = unixDay.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationMinute, durationWeek} from \"./duration.js\";\n\nfunction timeWeekday(i) {\n  return timeInterval((date) => {\n    date.setDate(date.getDate() - (date.getDay() + 7 - i) % 7);\n    date.setHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setDate(date.getDate() + step * 7);\n  }, (start, end) => {\n    return (end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * durationMinute) / durationWeek;\n  });\n}\n\nexport const timeSunday = timeWeekday(0);\nexport const timeMonday = timeWeekday(1);\nexport const timeTuesday = timeWeekday(2);\nexport const timeWednesday = timeWeekday(3);\nexport const timeThursday = timeWeekday(4);\nexport const timeFriday = timeWeekday(5);\nexport const timeSaturday = timeWeekday(6);\n\nexport const timeSundays = timeSunday.range;\nexport const timeMondays = timeMonday.range;\nexport const timeTuesdays = timeTuesday.range;\nexport const timeWednesdays = timeWednesday.range;\nexport const timeThursdays = timeThursday.range;\nexport const timeFridays = timeFriday.range;\nexport const timeSaturdays = timeSaturday.range;\n\nfunction utcWeekday(i) {\n  return timeInterval((date) => {\n    date.setUTCDate(date.getUTCDate() - (date.getUTCDay() + 7 - i) % 7);\n    date.setUTCHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setUTCDate(date.getUTCDate() + step * 7);\n  }, (start, end) => {\n    return (end - start) / durationWeek;\n  });\n}\n\nexport const utcSunday = utcWeekday(0);\nexport const utcMonday = utcWeekday(1);\nexport const utcTuesday = utcWeekday(2);\nexport const utcWednesday = utcWeekday(3);\nexport const utcThursday = utcWeekday(4);\nexport const utcFriday = utcWeekday(5);\nexport const utcSaturday = utcWeekday(6);\n\nexport const utcSundays = utcSunday.range;\nexport const utcMondays = utcMonday.range;\nexport const utcTuesdays = utcTuesday.range;\nexport const utcWednesdays = utcWednesday.range;\nexport const utcThursdays = utcThursday.range;\nexport const utcFridays = utcFriday.range;\nexport const utcSaturdays = utcSaturday.range;\n", "import {timeInterval} from \"./interval.js\";\n\nexport const timeMonth = timeInterval((date) => {\n  date.setDate(1);\n  date.setHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setMonth(date.getMonth() + step);\n}, (start, end) => {\n  return end.getMonth() - start.getMonth() + (end.getFullYear() - start.getFullYear()) * 12;\n}, (date) => {\n  return date.getMonth();\n});\n\nexport const timeMonths = timeMonth.range;\n\nexport const utcMonth = timeInterval((date) => {\n  date.setUTCDate(1);\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCMonth(date.getUTCMonth() + step);\n}, (start, end) => {\n  return end.getUTCMonth() - start.getUTCMonth() + (end.getUTCFullYear() - start.getUTCFullYear()) * 12;\n}, (date) => {\n  return date.getUTCMonth();\n});\n\nexport const utcMonths = utcMonth.range;\n", "import {timeInterval} from \"./interval.js\";\n\nexport const timeYear = timeInterval((date) => {\n  date.setMonth(0, 1);\n  date.setHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setFullYear(date.getFullYear() + step);\n}, (start, end) => {\n  return end.getFullYear() - start.getFullYear();\n}, (date) => {\n  return date.getFullYear();\n});\n\n// An optimized implementation for this simple case.\ntimeYear.every = (k) => {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : timeInterval((date) => {\n    date.setFullYear(Math.floor(date.getFullYear() / k) * k);\n    date.setMonth(0, 1);\n    date.setHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setFullYear(date.getFullYear() + step * k);\n  });\n};\n\nexport const timeYears = timeYear.range;\n\nexport const utcYear = timeInterval((date) => {\n  date.setUTCMonth(0, 1);\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCFullYear(date.getUTCFullYear() + step);\n}, (start, end) => {\n  return end.getUTCFullYear() - start.getUTCFullYear();\n}, (date) => {\n  return date.getUTCFullYear();\n});\n\n// An optimized implementation for this simple case.\nutcYear.every = (k) => {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : timeInterval((date) => {\n    date.setUTCFullYear(Math.floor(date.getUTCFullYear() / k) * k);\n    date.setUTCMonth(0, 1);\n    date.setUTCHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setUTCFullYear(date.getUTCFullYear() + step * k);\n  });\n};\n\nexport const utcYears = utcYear.range;\n", "import {bisector, tickStep} from \"d3-array\";\nimport {durationDay, durationHour, durationMinute, durationMonth, durationSecond, durationWeek, durationYear} from \"./duration.js\";\nimport {millisecond} from \"./millisecond.js\";\nimport {second} from \"./second.js\";\nimport {timeMinute, utcMinute} from \"./minute.js\";\nimport {timeHour, utcHour} from \"./hour.js\";\nimport {timeDay, unixDay} from \"./day.js\";\nimport {timeSunday, utcSunday} from \"./week.js\";\nimport {timeMonth, utcMonth} from \"./month.js\";\nimport {timeYear, utcYear} from \"./year.js\";\n\nfunction ticker(year, month, week, day, hour, minute) {\n\n  const tickIntervals = [\n    [second,  1,      durationSecond],\n    [second,  5,  5 * durationSecond],\n    [second, 15, 15 * durationSecond],\n    [second, 30, 30 * durationSecond],\n    [minute,  1,      durationMinute],\n    [minute,  5,  5 * durationMinute],\n    [minute, 15, 15 * durationMinute],\n    [minute, 30, 30 * durationMinute],\n    [  hour,  1,      durationHour  ],\n    [  hour,  3,  3 * durationHour  ],\n    [  hour,  6,  6 * durationHour  ],\n    [  hour, 12, 12 * durationHour  ],\n    [   day,  1,      durationDay   ],\n    [   day,  2,  2 * durationDay   ],\n    [  week,  1,      durationWeek  ],\n    [ month,  1,      durationMonth ],\n    [ month,  3,  3 * durationMonth ],\n    [  year,  1,      durationYear  ]\n  ];\n\n  function ticks(start, stop, count) {\n    const reverse = stop < start;\n    if (reverse) [start, stop] = [stop, start];\n    const interval = count && typeof count.range === \"function\" ? count : tickInterval(start, stop, count);\n    const ticks = interval ? interval.range(start, +stop + 1) : []; // inclusive stop\n    return reverse ? ticks.reverse() : ticks;\n  }\n\n  function tickInterval(start, stop, count) {\n    const target = Math.abs(stop - start) / count;\n    const i = bisector(([,, step]) => step).right(tickIntervals, target);\n    if (i === tickIntervals.length) return year.every(tickStep(start / durationYear, stop / durationYear, count));\n    if (i === 0) return millisecond.every(Math.max(tickStep(start, stop, count), 1));\n    const [t, step] = tickIntervals[target / tickIntervals[i - 1][2] < tickIntervals[i][2] / target ? i - 1 : i];\n    return t.every(step);\n  }\n\n  return [ticks, tickInterval];\n}\n\nconst [utcTicks, utcTickInterval] = ticker(utcYear, utcMonth, utcSunday, unixDay, utcHour, utcMinute);\nconst [timeTicks, timeTickInterval] = ticker(timeYear, timeMonth, timeSunday, timeDay, timeHour, timeMinute);\n\nexport {utcTicks, utcTickInterval, timeTicks, timeTickInterval};\n", "import {\n  timeDay,\n  timeSunday,\n  timeMonday,\n  timeThursday,\n  timeYear,\n  utcDay,\n  utcSunday,\n  utcMonday,\n  utcThursday,\n  utcYear\n} from \"d3-time\";\n\nfunction localDate(d) {\n  if (0 <= d.y && d.y < 100) {\n    var date = new Date(-1, d.m, d.d, d.H, d.M, d.S, d.L);\n    date.setFullYear(d.y);\n    return date;\n  }\n  return new Date(d.y, d.m, d.d, d.H, d.M, d.S, d.L);\n}\n\nfunction utcDate(d) {\n  if (0 <= d.y && d.y < 100) {\n    var date = new Date(Date.UTC(-1, d.m, d.d, d.H, d.M, d.S, d.L));\n    date.setUTCFullYear(d.y);\n    return date;\n  }\n  return new Date(Date.UTC(d.y, d.m, d.d, d.H, d.M, d.S, d.L));\n}\n\nfunction newDate(y, m, d) {\n  return {y: y, m: m, d: d, H: 0, M: 0, S: 0, L: 0};\n}\n\nexport default function formatLocale(locale) {\n  var locale_dateTime = locale.dateTime,\n      locale_date = locale.date,\n      locale_time = locale.time,\n      locale_periods = locale.periods,\n      locale_weekdays = locale.days,\n      locale_shortWeekdays = locale.shortDays,\n      locale_months = locale.months,\n      locale_shortMonths = locale.shortMonths;\n\n  var periodRe = formatRe(locale_periods),\n      periodLookup = formatLookup(locale_periods),\n      weekdayRe = formatRe(locale_weekdays),\n      weekdayLookup = formatLookup(locale_weekdays),\n      shortWeekdayRe = formatRe(locale_shortWeekdays),\n      shortWeekdayLookup = formatLookup(locale_shortWeekdays),\n      monthRe = formatRe(locale_months),\n      monthLookup = formatLookup(locale_months),\n      shortMonthRe = formatRe(locale_shortMonths),\n      shortMonthLookup = formatLookup(locale_shortMonths);\n\n  var formats = {\n    \"a\": formatShortWeekday,\n    \"A\": formatWeekday,\n    \"b\": formatShortMonth,\n    \"B\": formatMonth,\n    \"c\": null,\n    \"d\": formatDayOfMonth,\n    \"e\": formatDayOfMonth,\n    \"f\": formatMicroseconds,\n    \"g\": formatYearISO,\n    \"G\": formatFullYearISO,\n    \"H\": formatHour24,\n    \"I\": formatHour12,\n    \"j\": formatDayOfYear,\n    \"L\": formatMilliseconds,\n    \"m\": formatMonthNumber,\n    \"M\": formatMinutes,\n    \"p\": formatPeriod,\n    \"q\": formatQuarter,\n    \"Q\": formatUnixTimestamp,\n    \"s\": formatUnixTimestampSeconds,\n    \"S\": formatSeconds,\n    \"u\": formatWeekdayNumberMonday,\n    \"U\": formatWeekNumberSunday,\n    \"V\": formatWeekNumberISO,\n    \"w\": formatWeekdayNumberSunday,\n    \"W\": formatWeekNumberMonday,\n    \"x\": null,\n    \"X\": null,\n    \"y\": formatYear,\n    \"Y\": formatFullYear,\n    \"Z\": formatZone,\n    \"%\": formatLiteralPercent\n  };\n\n  var utcFormats = {\n    \"a\": formatUTCShortWeekday,\n    \"A\": formatUTCWeekday,\n    \"b\": formatUTCShortMonth,\n    \"B\": formatUTCMonth,\n    \"c\": null,\n    \"d\": formatUTCDayOfMonth,\n    \"e\": formatUTCDayOfMonth,\n    \"f\": formatUTCMicroseconds,\n    \"g\": formatUTCYearISO,\n    \"G\": formatUTCFullYearISO,\n    \"H\": formatUTCHour24,\n    \"I\": formatUTCHour12,\n    \"j\": formatUTCDayOfYear,\n    \"L\": formatUTCMilliseconds,\n    \"m\": formatUTCMonthNumber,\n    \"M\": formatUTCMinutes,\n    \"p\": formatUTCPeriod,\n    \"q\": formatUTCQuarter,\n    \"Q\": formatUnixTimestamp,\n    \"s\": formatUnixTimestampSeconds,\n    \"S\": formatUTCSeconds,\n    \"u\": formatUTCWeekdayNumberMonday,\n    \"U\": formatUTCWeekNumberSunday,\n    \"V\": formatUTCWeekNumberISO,\n    \"w\": formatUTCWeekdayNumberSunday,\n    \"W\": formatUTCWeekNumberMonday,\n    \"x\": null,\n    \"X\": null,\n    \"y\": formatUTCYear,\n    \"Y\": formatUTCFullYear,\n    \"Z\": formatUTCZone,\n    \"%\": formatLiteralPercent\n  };\n\n  var parses = {\n    \"a\": parseShortWeekday,\n    \"A\": parseWeekday,\n    \"b\": parseShortMonth,\n    \"B\": parseMonth,\n    \"c\": parseLocaleDateTime,\n    \"d\": parseDayOfMonth,\n    \"e\": parseDayOfMonth,\n    \"f\": parseMicroseconds,\n    \"g\": parseYear,\n    \"G\": parseFullYear,\n    \"H\": parseHour24,\n    \"I\": parseHour24,\n    \"j\": parseDayOfYear,\n    \"L\": parseMilliseconds,\n    \"m\": parseMonthNumber,\n    \"M\": parseMinutes,\n    \"p\": parsePeriod,\n    \"q\": parseQuarter,\n    \"Q\": parseUnixTimestamp,\n    \"s\": parseUnixTimestampSeconds,\n    \"S\": parseSeconds,\n    \"u\": parseWeekdayNumberMonday,\n    \"U\": parseWeekNumberSunday,\n    \"V\": parseWeekNumberISO,\n    \"w\": parseWeekdayNumberSunday,\n    \"W\": parseWeekNumberMonday,\n    \"x\": parseLocaleDate,\n    \"X\": parseLocaleTime,\n    \"y\": parseYear,\n    \"Y\": parseFullYear,\n    \"Z\": parseZone,\n    \"%\": parseLiteralPercent\n  };\n\n  // These recursive directive definitions must be deferred.\n  formats.x = newFormat(locale_date, formats);\n  formats.X = newFormat(locale_time, formats);\n  formats.c = newFormat(locale_dateTime, formats);\n  utcFormats.x = newFormat(locale_date, utcFormats);\n  utcFormats.X = newFormat(locale_time, utcFormats);\n  utcFormats.c = newFormat(locale_dateTime, utcFormats);\n\n  function newFormat(specifier, formats) {\n    return function(date) {\n      var string = [],\n          i = -1,\n          j = 0,\n          n = specifier.length,\n          c,\n          pad,\n          format;\n\n      if (!(date instanceof Date)) date = new Date(+date);\n\n      while (++i < n) {\n        if (specifier.charCodeAt(i) === 37) {\n          string.push(specifier.slice(j, i));\n          if ((pad = pads[c = specifier.charAt(++i)]) != null) c = specifier.charAt(++i);\n          else pad = c === \"e\" ? \" \" : \"0\";\n          if (format = formats[c]) c = format(date, pad);\n          string.push(c);\n          j = i + 1;\n        }\n      }\n\n      string.push(specifier.slice(j, i));\n      return string.join(\"\");\n    };\n  }\n\n  function newParse(specifier, Z) {\n    return function(string) {\n      var d = newDate(1900, undefined, 1),\n          i = parseSpecifier(d, specifier, string += \"\", 0),\n          week, day;\n      if (i != string.length) return null;\n\n      // If a UNIX timestamp is specified, return it.\n      if (\"Q\" in d) return new Date(d.Q);\n      if (\"s\" in d) return new Date(d.s * 1000 + (\"L\" in d ? d.L : 0));\n\n      // If this is utcParse, never use the local timezone.\n      if (Z && !(\"Z\" in d)) d.Z = 0;\n\n      // The am-pm flag is 0 for AM, and 1 for PM.\n      if (\"p\" in d) d.H = d.H % 12 + d.p * 12;\n\n      // If the month was not specified, inherit from the quarter.\n      if (d.m === undefined) d.m = \"q\" in d ? d.q : 0;\n\n      // Convert day-of-week and week-of-year to day-of-year.\n      if (\"V\" in d) {\n        if (d.V < 1 || d.V > 53) return null;\n        if (!(\"w\" in d)) d.w = 1;\n        if (\"Z\" in d) {\n          week = utcDate(newDate(d.y, 0, 1)), day = week.getUTCDay();\n          week = day > 4 || day === 0 ? utcMonday.ceil(week) : utcMonday(week);\n          week = utcDay.offset(week, (d.V - 1) * 7);\n          d.y = week.getUTCFullYear();\n          d.m = week.getUTCMonth();\n          d.d = week.getUTCDate() + (d.w + 6) % 7;\n        } else {\n          week = localDate(newDate(d.y, 0, 1)), day = week.getDay();\n          week = day > 4 || day === 0 ? timeMonday.ceil(week) : timeMonday(week);\n          week = timeDay.offset(week, (d.V - 1) * 7);\n          d.y = week.getFullYear();\n          d.m = week.getMonth();\n          d.d = week.getDate() + (d.w + 6) % 7;\n        }\n      } else if (\"W\" in d || \"U\" in d) {\n        if (!(\"w\" in d)) d.w = \"u\" in d ? d.u % 7 : \"W\" in d ? 1 : 0;\n        day = \"Z\" in d ? utcDate(newDate(d.y, 0, 1)).getUTCDay() : localDate(newDate(d.y, 0, 1)).getDay();\n        d.m = 0;\n        d.d = \"W\" in d ? (d.w + 6) % 7 + d.W * 7 - (day + 5) % 7 : d.w + d.U * 7 - (day + 6) % 7;\n      }\n\n      // If a time zone is specified, all fields are interpreted as UTC and then\n      // offset according to the specified time zone.\n      if (\"Z\" in d) {\n        d.H += d.Z / 100 | 0;\n        d.M += d.Z % 100;\n        return utcDate(d);\n      }\n\n      // Otherwise, all fields are in local time.\n      return localDate(d);\n    };\n  }\n\n  function parseSpecifier(d, specifier, string, j) {\n    var i = 0,\n        n = specifier.length,\n        m = string.length,\n        c,\n        parse;\n\n    while (i < n) {\n      if (j >= m) return -1;\n      c = specifier.charCodeAt(i++);\n      if (c === 37) {\n        c = specifier.charAt(i++);\n        parse = parses[c in pads ? specifier.charAt(i++) : c];\n        if (!parse || ((j = parse(d, string, j)) < 0)) return -1;\n      } else if (c != string.charCodeAt(j++)) {\n        return -1;\n      }\n    }\n\n    return j;\n  }\n\n  function parsePeriod(d, string, i) {\n    var n = periodRe.exec(string.slice(i));\n    return n ? (d.p = periodLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseShortWeekday(d, string, i) {\n    var n = shortWeekdayRe.exec(string.slice(i));\n    return n ? (d.w = shortWeekdayLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseWeekday(d, string, i) {\n    var n = weekdayRe.exec(string.slice(i));\n    return n ? (d.w = weekdayLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseShortMonth(d, string, i) {\n    var n = shortMonthRe.exec(string.slice(i));\n    return n ? (d.m = shortMonthLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseMonth(d, string, i) {\n    var n = monthRe.exec(string.slice(i));\n    return n ? (d.m = monthLookup.get(n[0].toLowerCase()), i + n[0].length) : -1;\n  }\n\n  function parseLocaleDateTime(d, string, i) {\n    return parseSpecifier(d, locale_dateTime, string, i);\n  }\n\n  function parseLocaleDate(d, string, i) {\n    return parseSpecifier(d, locale_date, string, i);\n  }\n\n  function parseLocaleTime(d, string, i) {\n    return parseSpecifier(d, locale_time, string, i);\n  }\n\n  function formatShortWeekday(d) {\n    return locale_shortWeekdays[d.getDay()];\n  }\n\n  function formatWeekday(d) {\n    return locale_weekdays[d.getDay()];\n  }\n\n  function formatShortMonth(d) {\n    return locale_shortMonths[d.getMonth()];\n  }\n\n  function formatMonth(d) {\n    return locale_months[d.getMonth()];\n  }\n\n  function formatPeriod(d) {\n    return locale_periods[+(d.getHours() >= 12)];\n  }\n\n  function formatQuarter(d) {\n    return 1 + ~~(d.getMonth() / 3);\n  }\n\n  function formatUTCShortWeekday(d) {\n    return locale_shortWeekdays[d.getUTCDay()];\n  }\n\n  function formatUTCWeekday(d) {\n    return locale_weekdays[d.getUTCDay()];\n  }\n\n  function formatUTCShortMonth(d) {\n    return locale_shortMonths[d.getUTCMonth()];\n  }\n\n  function formatUTCMonth(d) {\n    return locale_months[d.getUTCMonth()];\n  }\n\n  function formatUTCPeriod(d) {\n    return locale_periods[+(d.getUTCHours() >= 12)];\n  }\n\n  function formatUTCQuarter(d) {\n    return 1 + ~~(d.getUTCMonth() / 3);\n  }\n\n  return {\n    format: function(specifier) {\n      var f = newFormat(specifier += \"\", formats);\n      f.toString = function() { return specifier; };\n      return f;\n    },\n    parse: function(specifier) {\n      var p = newParse(specifier += \"\", false);\n      p.toString = function() { return specifier; };\n      return p;\n    },\n    utcFormat: function(specifier) {\n      var f = newFormat(specifier += \"\", utcFormats);\n      f.toString = function() { return specifier; };\n      return f;\n    },\n    utcParse: function(specifier) {\n      var p = newParse(specifier += \"\", true);\n      p.toString = function() { return specifier; };\n      return p;\n    }\n  };\n}\n\nvar pads = {\"-\": \"\", \"_\": \" \", \"0\": \"0\"},\n    numberRe = /^\\s*\\d+/, // note: ignores next directive\n    percentRe = /^%/,\n    requoteRe = /[\\\\^$*+?|[\\]().{}]/g;\n\nfunction pad(value, fill, width) {\n  var sign = value < 0 ? \"-\" : \"\",\n      string = (sign ? -value : value) + \"\",\n      length = string.length;\n  return sign + (length < width ? new Array(width - length + 1).join(fill) + string : string);\n}\n\nfunction requote(s) {\n  return s.replace(requoteRe, \"\\\\$&\");\n}\n\nfunction formatRe(names) {\n  return new RegExp(\"^(?:\" + names.map(requote).join(\"|\") + \")\", \"i\");\n}\n\nfunction formatLookup(names) {\n  return new Map(names.map((name, i) => [name.toLowerCase(), i]));\n}\n\nfunction parseWeekdayNumberSunday(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 1));\n  return n ? (d.w = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseWeekdayNumberMonday(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 1));\n  return n ? (d.u = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseWeekNumberSunday(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.U = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseWeekNumberISO(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.V = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseWeekNumberMonday(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.W = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseFullYear(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 4));\n  return n ? (d.y = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseYear(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.y = +n[0] + (+n[0] > 68 ? 1900 : 2000), i + n[0].length) : -1;\n}\n\nfunction parseZone(d, string, i) {\n  var n = /^(Z)|([+-]\\d\\d)(?::?(\\d\\d))?/.exec(string.slice(i, i + 6));\n  return n ? (d.Z = n[1] ? 0 : -(n[2] + (n[3] || \"00\")), i + n[0].length) : -1;\n}\n\nfunction parseQuarter(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 1));\n  return n ? (d.q = n[0] * 3 - 3, i + n[0].length) : -1;\n}\n\nfunction parseMonthNumber(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.m = n[0] - 1, i + n[0].length) : -1;\n}\n\nfunction parseDayOfMonth(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.d = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseDayOfYear(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 3));\n  return n ? (d.m = 0, d.d = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseHour24(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.H = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseMinutes(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.M = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseSeconds(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 2));\n  return n ? (d.S = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseMilliseconds(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 3));\n  return n ? (d.L = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseMicroseconds(d, string, i) {\n  var n = numberRe.exec(string.slice(i, i + 6));\n  return n ? (d.L = Math.floor(n[0] / 1000), i + n[0].length) : -1;\n}\n\nfunction parseLiteralPercent(d, string, i) {\n  var n = percentRe.exec(string.slice(i, i + 1));\n  return n ? i + n[0].length : -1;\n}\n\nfunction parseUnixTimestamp(d, string, i) {\n  var n = numberRe.exec(string.slice(i));\n  return n ? (d.Q = +n[0], i + n[0].length) : -1;\n}\n\nfunction parseUnixTimestampSeconds(d, string, i) {\n  var n = numberRe.exec(string.slice(i));\n  return n ? (d.s = +n[0], i + n[0].length) : -1;\n}\n\nfunction formatDayOfMonth(d, p) {\n  return pad(d.getDate(), p, 2);\n}\n\nfunction formatHour24(d, p) {\n  return pad(d.getHours(), p, 2);\n}\n\nfunction formatHour12(d, p) {\n  return pad(d.getHours() % 12 || 12, p, 2);\n}\n\nfunction formatDayOfYear(d, p) {\n  return pad(1 + timeDay.count(timeYear(d), d), p, 3);\n}\n\nfunction formatMilliseconds(d, p) {\n  return pad(d.getMilliseconds(), p, 3);\n}\n\nfunction formatMicroseconds(d, p) {\n  return formatMilliseconds(d, p) + \"000\";\n}\n\nfunction formatMonthNumber(d, p) {\n  return pad(d.getMonth() + 1, p, 2);\n}\n\nfunction formatMinutes(d, p) {\n  return pad(d.getMinutes(), p, 2);\n}\n\nfunction formatSeconds(d, p) {\n  return pad(d.getSeconds(), p, 2);\n}\n\nfunction formatWeekdayNumberMonday(d) {\n  var day = d.getDay();\n  return day === 0 ? 7 : day;\n}\n\nfunction formatWeekNumberSunday(d, p) {\n  return pad(timeSunday.count(timeYear(d) - 1, d), p, 2);\n}\n\nfunction dISO(d) {\n  var day = d.getDay();\n  return (day >= 4 || day === 0) ? timeThursday(d) : timeThursday.ceil(d);\n}\n\nfunction formatWeekNumberISO(d, p) {\n  d = dISO(d);\n  return pad(timeThursday.count(timeYear(d), d) + (timeYear(d).getDay() === 4), p, 2);\n}\n\nfunction formatWeekdayNumberSunday(d) {\n  return d.getDay();\n}\n\nfunction formatWeekNumberMonday(d, p) {\n  return pad(timeMonday.count(timeYear(d) - 1, d), p, 2);\n}\n\nfunction formatYear(d, p) {\n  return pad(d.getFullYear() % 100, p, 2);\n}\n\nfunction formatYearISO(d, p) {\n  d = dISO(d);\n  return pad(d.getFullYear() % 100, p, 2);\n}\n\nfunction formatFullYear(d, p) {\n  return pad(d.getFullYear() % 10000, p, 4);\n}\n\nfunction formatFullYearISO(d, p) {\n  var day = d.getDay();\n  d = (day >= 4 || day === 0) ? timeThursday(d) : timeThursday.ceil(d);\n  return pad(d.getFullYear() % 10000, p, 4);\n}\n\nfunction formatZone(d) {\n  var z = d.getTimezoneOffset();\n  return (z > 0 ? \"-\" : (z *= -1, \"+\"))\n      + pad(z / 60 | 0, \"0\", 2)\n      + pad(z % 60, \"0\", 2);\n}\n\nfunction formatUTCDayOfMonth(d, p) {\n  return pad(d.getUTCDate(), p, 2);\n}\n\nfunction formatUTCHour24(d, p) {\n  return pad(d.getUTCHours(), p, 2);\n}\n\nfunction formatUTCHour12(d, p) {\n  return pad(d.getUTCHours() % 12 || 12, p, 2);\n}\n\nfunction formatUTCDayOfYear(d, p) {\n  return pad(1 + utcDay.count(utcYear(d), d), p, 3);\n}\n\nfunction formatUTCMilliseconds(d, p) {\n  return pad(d.getUTCMilliseconds(), p, 3);\n}\n\nfunction formatUTCMicroseconds(d, p) {\n  return formatUTCMilliseconds(d, p) + \"000\";\n}\n\nfunction formatUTCMonthNumber(d, p) {\n  return pad(d.getUTCMonth() + 1, p, 2);\n}\n\nfunction formatUTCMinutes(d, p) {\n  return pad(d.getUTCMinutes(), p, 2);\n}\n\nfunction formatUTCSeconds(d, p) {\n  return pad(d.getUTCSeconds(), p, 2);\n}\n\nfunction formatUTCWeekdayNumberMonday(d) {\n  var dow = d.getUTCDay();\n  return dow === 0 ? 7 : dow;\n}\n\nfunction formatUTCWeekNumberSunday(d, p) {\n  return pad(utcSunday.count(utcYear(d) - 1, d), p, 2);\n}\n\nfunction UTCdISO(d) {\n  var day = d.getUTCDay();\n  return (day >= 4 || day === 0) ? utcThursday(d) : utcThursday.ceil(d);\n}\n\nfunction formatUTCWeekNumberISO(d, p) {\n  d = UTCdISO(d);\n  return pad(utcThursday.count(utcYear(d), d) + (utcYear(d).getUTCDay() === 4), p, 2);\n}\n\nfunction formatUTCWeekdayNumberSunday(d) {\n  return d.getUTCDay();\n}\n\nfunction formatUTCWeekNumberMonday(d, p) {\n  return pad(utcMonday.count(utcYear(d) - 1, d), p, 2);\n}\n\nfunction formatUTCYear(d, p) {\n  return pad(d.getUTCFullYear() % 100, p, 2);\n}\n\nfunction formatUTCYearISO(d, p) {\n  d = UTCdISO(d);\n  return pad(d.getUTCFullYear() % 100, p, 2);\n}\n\nfunction formatUTCFullYear(d, p) {\n  return pad(d.getUTCFullYear() % 10000, p, 4);\n}\n\nfunction formatUTCFullYearISO(d, p) {\n  var day = d.getUTCDay();\n  d = (day >= 4 || day === 0) ? utcThursday(d) : utcThursday.ceil(d);\n  return pad(d.getUTCFullYear() % 10000, p, 4);\n}\n\nfunction formatUTCZone() {\n  return \"+0000\";\n}\n\nfunction formatLiteralPercent() {\n  return \"%\";\n}\n\nfunction formatUnixTimestamp(d) {\n  return +d;\n}\n\nfunction formatUnixTimestampSeconds(d) {\n  return Math.floor(+d / 1000);\n}\n", "import formatLocale from \"./locale.js\";\n\nvar locale;\nexport var timeFormat;\nexport var timeParse;\nexport var utcFormat;\nexport var utcParse;\n\ndefaultLocale({\n  dateTime: \"%x, %X\",\n  date: \"%-m/%-d/%Y\",\n  time: \"%-I:%M:%S %p\",\n  periods: [\"AM\", \"PM\"],\n  days: [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"],\n  shortDays: [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"],\n  months: [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"],\n  shortMonths: [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"]\n});\n\nexport default function defaultLocale(definition) {\n  locale = formatLocale(definition);\n  timeFormat = locale.format;\n  timeParse = locale.parse;\n  utcFormat = locale.utcFormat;\n  utcParse = locale.utcParse;\n  return locale;\n}\n", "import {utcFormat} from \"./defaultLocale.js\";\n\nexport var isoSpecifier = \"%Y-%m-%dT%H:%M:%S.%LZ\";\n\nfunction formatIsoNative(date) {\n  return date.toISOString();\n}\n\nvar formatIso = Date.prototype.toISOString\n    ? formatIsoNative\n    : utcFormat(isoSpecifier);\n\nexport default formatIso;\n", "import {isoSpecifier} from \"./isoFormat.js\";\nimport {utcParse} from \"./defaultLocale.js\";\n\nfunction parseIsoNative(string) {\n  var date = new Date(string);\n  return isNaN(date) ? null : date;\n}\n\nvar parseIso = +new Date(\"2000-01-01T00:00:00.000Z\")\n    ? parseIsoNative\n    : utcParse(isoSpecifier);\n\nexport default parseIso;\n", "import {timeYear, timeMonth, timeWeek, timeDay, timeHour, timeMinute, timeSecond, timeTicks, timeTickInterval} from \"d3-time\";\nimport {timeFormat} from \"d3-time-format\";\nimport continuous, {copy} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\nimport nice from \"./nice.js\";\n\nfunction date(t) {\n  return new Date(t);\n}\n\nfunction number(t) {\n  return t instanceof Date ? +t : +new Date(+t);\n}\n\nexport function calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format) {\n  var scale = continuous(),\n      invert = scale.invert,\n      domain = scale.domain;\n\n  var formatMillisecond = format(\".%L\"),\n      formatSecond = format(\":%S\"),\n      formatMinute = format(\"%I:%M\"),\n      formatHour = format(\"%I %p\"),\n      formatDay = format(\"%a %d\"),\n      formatWeek = format(\"%b %d\"),\n      formatMonth = format(\"%B\"),\n      formatYear = format(\"%Y\");\n\n  function tickFormat(date) {\n    return (second(date) < date ? formatMillisecond\n        : minute(date) < date ? formatSecond\n        : hour(date) < date ? formatMinute\n        : day(date) < date ? formatHour\n        : month(date) < date ? (week(date) < date ? formatDay : formatWeek)\n        : year(date) < date ? formatMonth\n        : formatYear)(date);\n  }\n\n  scale.invert = function(y) {\n    return new Date(invert(y));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? domain(Array.from(_, number)) : domain().map(date);\n  };\n\n  scale.ticks = function(interval) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], interval == null ? 10 : interval);\n  };\n\n  scale.tickFormat = function(count, specifier) {\n    return specifier == null ? tickFormat : format(specifier);\n  };\n\n  scale.nice = function(interval) {\n    var d = domain();\n    if (!interval || typeof interval.range !== \"function\") interval = tickInterval(d[0], d[d.length - 1], interval == null ? 10 : interval);\n    return interval ? domain(nice(d, interval)) : scale;\n  };\n\n  scale.copy = function() {\n    return copy(scale, calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format));\n  };\n\n  return scale;\n}\n\nexport default function time() {\n  return initRange.apply(calendar(timeTicks, timeTickInterval, timeYear, timeMonth, timeWeek, timeDay, timeHour, timeMinute, timeSecond, timeFormat).domain([new Date(2000, 0, 1), new Date(2000, 0, 2)]), arguments);\n}\n", "import {utcYear, utcMonth, utcWeek, utcDay, utcHour, utcMinute, utcSecond, utcTicks, utcTickInterval} from \"d3-time\";\nimport {utcFormat} from \"d3-time-format\";\nimport {calendar} from \"./time.js\";\nimport {initRange} from \"./init.js\";\n\nexport default function utcTime() {\n  return initRange.apply(calendar(utcTicks, utcTickInterval, utcYear, utcMonth, utcWeek, utcDay, utcHour, utcMinute, utcSecond, utcFormat).domain([Date.UTC(2000, 0, 1), Date.UTC(2000, 0, 2)]), arguments);\n}\n", "import {interpolate, interpolateRound} from \"d3-interpolate\";\nimport {identity} from \"./continuous.js\";\nimport {initInterpolator} from \"./init.js\";\nimport {linearish} from \"./linear.js\";\nimport {loggish} from \"./log.js\";\nimport {symlogish} from \"./symlog.js\";\nimport {powish} from \"./pow.js\";\n\nfunction transformer() {\n  var x0 = 0,\n      x1 = 1,\n      t0,\n      t1,\n      k10,\n      transform,\n      interpolator = identity,\n      clamp = false,\n      unknown;\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : interpolator(k10 === 0 ? 0.5 : (x = (transform(x) - t0) * k10, clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? ([x0, x1] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0), scale) : [x0, x1];\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  function range(interpolate) {\n    return function(_) {\n      var r0, r1;\n      return arguments.length ? ([r0, r1] = _, interpolator = interpolate(r0, r1), scale) : [interpolator(0), interpolator(1)];\n    };\n  }\n\n  scale.range = range(interpolate);\n\n  scale.rangeRound = range(interpolateRound);\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t) {\n    transform = t, t0 = t(x0), t1 = t(x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0);\n    return scale;\n  };\n}\n\nexport function copy(source, target) {\n  return target\n      .domain(source.domain())\n      .interpolator(source.interpolator())\n      .clamp(source.clamp())\n      .unknown(source.unknown());\n}\n\nexport default function sequential() {\n  var scale = linearish(transformer()(identity));\n\n  scale.copy = function() {\n    return copy(scale, sequential());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialLog() {\n  var scale = loggish(transformer()).domain([1, 10]);\n\n  scale.copy = function() {\n    return copy(scale, sequentialLog()).base(scale.base());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialSymlog() {\n  var scale = symlogish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, sequentialSymlog()).constant(scale.constant());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialPow() {\n  var scale = powish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, sequentialPow()).exponent(scale.exponent());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function sequentialSqrt() {\n  return sequentialPow.apply(null, arguments).exponent(0.5);\n}\n", "import {ascending, bisect, quantile} from \"d3-array\";\nimport {identity} from \"./continuous.js\";\nimport {initInterpolator} from \"./init.js\";\n\nexport default function sequentialQuantile() {\n  var domain = [],\n      interpolator = identity;\n\n  function scale(x) {\n    if (x != null && !isNaN(x = +x)) return interpolator((bisect(domain, x, 1) - 1) / (domain.length - 1));\n  }\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [];\n    for (let d of _) if (d != null && !isNaN(d = +d)) domain.push(d);\n    domain.sort(ascending);\n    return scale;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  scale.range = function() {\n    return domain.map((d, i) => interpolator(i / (domain.length - 1)));\n  };\n\n  scale.quantiles = function(n) {\n    return Array.from({length: n + 1}, (_, i) => quantile(domain, i / n));\n  };\n\n  scale.copy = function() {\n    return sequentialQuantile(interpolator).domain(domain);\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n", "import {interpolate, interpolateRound, piecewise} from \"d3-interpolate\";\nimport {identity} from \"./continuous.js\";\nimport {initInterpolator} from \"./init.js\";\nimport {linearish} from \"./linear.js\";\nimport {loggish} from \"./log.js\";\nimport {copy} from \"./sequential.js\";\nimport {symlogish} from \"./symlog.js\";\nimport {powish} from \"./pow.js\";\n\nfunction transformer() {\n  var x0 = 0,\n      x1 = 0.5,\n      x2 = 1,\n      s = 1,\n      t0,\n      t1,\n      t2,\n      k10,\n      k21,\n      interpolator = identity,\n      transform,\n      clamp = false,\n      unknown;\n\n  function scale(x) {\n    return isNaN(x = +x) ? unknown : (x = 0.5 + ((x = +transform(x)) - t1) * (s * x < s * t1 ? k10 : k21), interpolator(clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? ([x0, x1, x2] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), t2 = transform(x2 = +x2), k10 = t0 === t1 ? 0 : 0.5 / (t1 - t0), k21 = t1 === t2 ? 0 : 0.5 / (t2 - t1), s = t1 < t0 ? -1 : 1, scale) : [x0, x1, x2];\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  function range(interpolate) {\n    return function(_) {\n      var r0, r1, r2;\n      return arguments.length ? ([r0, r1, r2] = _, interpolator = piecewise(interpolate, [r0, r1, r2]), scale) : [interpolator(0), interpolator(0.5), interpolator(1)];\n    };\n  }\n\n  scale.range = range(interpolate);\n\n  scale.rangeRound = range(interpolateRound);\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t) {\n    transform = t, t0 = t(x0), t1 = t(x1), t2 = t(x2), k10 = t0 === t1 ? 0 : 0.5 / (t1 - t0), k21 = t1 === t2 ? 0 : 0.5 / (t2 - t1), s = t1 < t0 ? -1 : 1;\n    return scale;\n  };\n}\n\nexport default function diverging() {\n  var scale = linearish(transformer()(identity));\n\n  scale.copy = function() {\n    return copy(scale, diverging());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function divergingLog() {\n  var scale = loggish(transformer()).domain([0.1, 1, 10]);\n\n  scale.copy = function() {\n    return copy(scale, divergingLog()).base(scale.base());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function divergingSymlog() {\n  var scale = symlogish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, divergingSymlog()).constant(scale.constant());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function divergingPow() {\n  var scale = powish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, divergingPow()).exponent(scale.exponent());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function divergingSqrt() {\n  return divergingPow.apply(null, arguments).exponent(0.5);\n}\n", "export default function(x) {\n  return function constant() {\n    return x;\n  };\n}\n", "export const abs = Math.abs;\nexport const atan2 = Math.atan2;\nexport const cos = Math.cos;\nexport const max = Math.max;\nexport const min = Math.min;\nexport const sin = Math.sin;\nexport const sqrt = Math.sqrt;\n\nexport const epsilon = 1e-12;\nexport const pi = Math.PI;\nexport const halfPi = pi / 2;\nexport const tau = 2 * pi;\n\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\n\nexport function asin(x) {\n  return x >= 1 ? halfPi : x <= -1 ? -halfPi : Math.asin(x);\n}\n", "import {Path} from \"d3-path\";\n\nexport function withPath(shape) {\n  let digits = 3;\n\n  shape.digits = function(_) {\n    if (!arguments.length) return digits;\n    if (_ == null) {\n      digits = null;\n    } else {\n      const d = Math.floor(_);\n      if (!(d >= 0)) throw new RangeError(`invalid digits: ${_}`);\n      digits = d;\n    }\n    return shape;\n  };\n\n  return () => new Path(digits);\n}\n", "import constant from \"./constant.js\";\nimport {abs, acos, asin, atan2, cos, epsilon, halfPi, max, min, pi, sin, sqrt, tau} from \"./math.js\";\nimport {withPath} from \"./path.js\";\n\nfunction arcInnerRadius(d) {\n  return d.innerRadius;\n}\n\nfunction arcOuterRadius(d) {\n  return d.outerRadius;\n}\n\nfunction arcStartAngle(d) {\n  return d.startAngle;\n}\n\nfunction arcEndAngle(d) {\n  return d.endAngle;\n}\n\nfunction arcPadAngle(d) {\n  return d && d.padAngle; // Note: optional!\n}\n\nfunction intersect(x0, y0, x1, y1, x2, y2, x3, y3) {\n  var x10 = x1 - x0, y10 = y1 - y0,\n      x32 = x3 - x2, y32 = y3 - y2,\n      t = y32 * x10 - x32 * y10;\n  if (t * t < epsilon) return;\n  t = (x32 * (y0 - y2) - y32 * (x0 - x2)) / t;\n  return [x0 + t * x10, y0 + t * y10];\n}\n\n// Compute perpendicular offset line of length rc.\n// http://mathworld.wolfram.com/Circle-LineIntersection.html\nfunction cornerTangents(x0, y0, x1, y1, r1, rc, cw) {\n  var x01 = x0 - x1,\n      y01 = y0 - y1,\n      lo = (cw ? rc : -rc) / sqrt(x01 * x01 + y01 * y01),\n      ox = lo * y01,\n      oy = -lo * x01,\n      x11 = x0 + ox,\n      y11 = y0 + oy,\n      x10 = x1 + ox,\n      y10 = y1 + oy,\n      x00 = (x11 + x10) / 2,\n      y00 = (y11 + y10) / 2,\n      dx = x10 - x11,\n      dy = y10 - y11,\n      d2 = dx * dx + dy * dy,\n      r = r1 - rc,\n      D = x11 * y10 - x10 * y11,\n      d = (dy < 0 ? -1 : 1) * sqrt(max(0, r * r * d2 - D * D)),\n      cx0 = (D * dy - dx * d) / d2,\n      cy0 = (-D * dx - dy * d) / d2,\n      cx1 = (D * dy + dx * d) / d2,\n      cy1 = (-D * dx + dy * d) / d2,\n      dx0 = cx0 - x00,\n      dy0 = cy0 - y00,\n      dx1 = cx1 - x00,\n      dy1 = cy1 - y00;\n\n  // Pick the closer of the two intersection points.\n  // TODO Is there a faster way to determine which intersection to use?\n  if (dx0 * dx0 + dy0 * dy0 > dx1 * dx1 + dy1 * dy1) cx0 = cx1, cy0 = cy1;\n\n  return {\n    cx: cx0,\n    cy: cy0,\n    x01: -ox,\n    y01: -oy,\n    x11: cx0 * (r1 / r - 1),\n    y11: cy0 * (r1 / r - 1)\n  };\n}\n\nexport default function() {\n  var innerRadius = arcInnerRadius,\n      outerRadius = arcOuterRadius,\n      cornerRadius = constant(0),\n      padRadius = null,\n      startAngle = arcStartAngle,\n      endAngle = arcEndAngle,\n      padAngle = arcPadAngle,\n      context = null,\n      path = withPath(arc);\n\n  function arc() {\n    var buffer,\n        r,\n        r0 = +innerRadius.apply(this, arguments),\n        r1 = +outerRadius.apply(this, arguments),\n        a0 = startAngle.apply(this, arguments) - halfPi,\n        a1 = endAngle.apply(this, arguments) - halfPi,\n        da = abs(a1 - a0),\n        cw = a1 > a0;\n\n    if (!context) context = buffer = path();\n\n    // Ensure that the outer radius is always larger than the inner radius.\n    if (r1 < r0) r = r1, r1 = r0, r0 = r;\n\n    // Is it a point?\n    if (!(r1 > epsilon)) context.moveTo(0, 0);\n\n    // Or is it a circle or annulus?\n    else if (da > tau - epsilon) {\n      context.moveTo(r1 * cos(a0), r1 * sin(a0));\n      context.arc(0, 0, r1, a0, a1, !cw);\n      if (r0 > epsilon) {\n        context.moveTo(r0 * cos(a1), r0 * sin(a1));\n        context.arc(0, 0, r0, a1, a0, cw);\n      }\n    }\n\n    // Or is it a circular or annular sector?\n    else {\n      var a01 = a0,\n          a11 = a1,\n          a00 = a0,\n          a10 = a1,\n          da0 = da,\n          da1 = da,\n          ap = padAngle.apply(this, arguments) / 2,\n          rp = (ap > epsilon) && (padRadius ? +padRadius.apply(this, arguments) : sqrt(r0 * r0 + r1 * r1)),\n          rc = min(abs(r1 - r0) / 2, +cornerRadius.apply(this, arguments)),\n          rc0 = rc,\n          rc1 = rc,\n          t0,\n          t1;\n\n      // Apply padding? Note that since r1 ≥ r0, da1 ≥ da0.\n      if (rp > epsilon) {\n        var p0 = asin(rp / r0 * sin(ap)),\n            p1 = asin(rp / r1 * sin(ap));\n        if ((da0 -= p0 * 2) > epsilon) p0 *= (cw ? 1 : -1), a00 += p0, a10 -= p0;\n        else da0 = 0, a00 = a10 = (a0 + a1) / 2;\n        if ((da1 -= p1 * 2) > epsilon) p1 *= (cw ? 1 : -1), a01 += p1, a11 -= p1;\n        else da1 = 0, a01 = a11 = (a0 + a1) / 2;\n      }\n\n      var x01 = r1 * cos(a01),\n          y01 = r1 * sin(a01),\n          x10 = r0 * cos(a10),\n          y10 = r0 * sin(a10);\n\n      // Apply rounded corners?\n      if (rc > epsilon) {\n        var x11 = r1 * cos(a11),\n            y11 = r1 * sin(a11),\n            x00 = r0 * cos(a00),\n            y00 = r0 * sin(a00),\n            oc;\n\n        // Restrict the corner radius according to the sector angle. If this\n        // intersection fails, it’s probably because the arc is too small, so\n        // disable the corner radius entirely.\n        if (da < pi) {\n          if (oc = intersect(x01, y01, x00, y00, x11, y11, x10, y10)) {\n            var ax = x01 - oc[0],\n                ay = y01 - oc[1],\n                bx = x11 - oc[0],\n                by = y11 - oc[1],\n                kc = 1 / sin(acos((ax * bx + ay * by) / (sqrt(ax * ax + ay * ay) * sqrt(bx * bx + by * by))) / 2),\n                lc = sqrt(oc[0] * oc[0] + oc[1] * oc[1]);\n            rc0 = min(rc, (r0 - lc) / (kc - 1));\n            rc1 = min(rc, (r1 - lc) / (kc + 1));\n          } else {\n            rc0 = rc1 = 0;\n          }\n        }\n      }\n\n      // Is the sector collapsed to a line?\n      if (!(da1 > epsilon)) context.moveTo(x01, y01);\n\n      // Does the sector’s outer ring have rounded corners?\n      else if (rc1 > epsilon) {\n        t0 = cornerTangents(x00, y00, x01, y01, r1, rc1, cw);\n        t1 = cornerTangents(x11, y11, x10, y10, r1, rc1, cw);\n\n        context.moveTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc1 < rc) context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r1, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), !cw);\n          context.arc(t1.cx, t1.cy, rc1, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the outer ring just a circular arc?\n      else context.moveTo(x01, y01), context.arc(0, 0, r1, a01, a11, !cw);\n\n      // Is there no inner ring, and it’s a circular sector?\n      // Or perhaps it’s an annular sector collapsed due to padding?\n      if (!(r0 > epsilon) || !(da0 > epsilon)) context.lineTo(x10, y10);\n\n      // Does the sector’s inner ring (or point) have rounded corners?\n      else if (rc0 > epsilon) {\n        t0 = cornerTangents(x10, y10, x11, y11, r0, -rc0, cw);\n        t1 = cornerTangents(x01, y01, x00, y00, r0, -rc0, cw);\n\n        context.lineTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc0 < rc) context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r0, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), cw);\n          context.arc(t1.cx, t1.cy, rc0, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the inner ring just a circular arc?\n      else context.arc(0, 0, r0, a10, a00, cw);\n    }\n\n    context.closePath();\n\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  arc.centroid = function() {\n    var r = (+innerRadius.apply(this, arguments) + +outerRadius.apply(this, arguments)) / 2,\n        a = (+startAngle.apply(this, arguments) + +endAngle.apply(this, arguments)) / 2 - pi / 2;\n    return [cos(a) * r, sin(a) * r];\n  };\n\n  arc.innerRadius = function(_) {\n    return arguments.length ? (innerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : innerRadius;\n  };\n\n  arc.outerRadius = function(_) {\n    return arguments.length ? (outerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : outerRadius;\n  };\n\n  arc.cornerRadius = function(_) {\n    return arguments.length ? (cornerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : cornerRadius;\n  };\n\n  arc.padRadius = function(_) {\n    return arguments.length ? (padRadius = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), arc) : padRadius;\n  };\n\n  arc.startAngle = function(_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : startAngle;\n  };\n\n  arc.endAngle = function(_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : endAngle;\n  };\n\n  arc.padAngle = function(_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : padAngle;\n  };\n\n  arc.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), arc) : context;\n  };\n\n  return arc;\n}\n", "export var slice = Array.prototype.slice;\n\nexport default function(x) {\n  return typeof x === \"object\" && \"length\" in x\n    ? x // Array, TypedArray, NodeList, array-like\n    : Array.from(x); // Map, Set, iterable, string, or anything else\n}\n", "function Linear(context) {\n  this._context = context;\n}\n\nLinear.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; // falls through\n      default: this._context.lineTo(x, y); break;\n    }\n  }\n};\n\nexport default function(context) {\n  return new Linear(context);\n}\n", "export function x(p) {\n  return p[0];\n}\n\nexport function y(p) {\n  return p[1];\n}\n", "import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport curveLinear from \"./curve/linear.js\";\nimport {withPath} from \"./path.js\";\nimport {x as pointX, y as pointY} from \"./point.js\";\n\nexport default function(x, y) {\n  var defined = constant(true),\n      context = null,\n      curve = curveLinear,\n      output = null,\n      path = withPath(line);\n\n  x = typeof x === \"function\" ? x : (x === undefined) ? pointX : constant(x);\n  y = typeof y === \"function\" ? y : (y === undefined) ? pointY : constant(y);\n\n  function line(data) {\n    var i,\n        n = (data = array(data)).length,\n        d,\n        defined0 = false,\n        buffer;\n\n    if (context == null) output = curve(buffer = path());\n\n    for (i = 0; i <= n; ++i) {\n      if (!(i < n && defined(d = data[i], i, data)) === defined0) {\n        if (defined0 = !defined0) output.lineStart();\n        else output.lineEnd();\n      }\n      if (defined0) output.point(+x(d, i, data), +y(d, i, data));\n    }\n\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n\n  line.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), line) : x;\n  };\n\n  line.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), line) : y;\n  };\n\n  line.defined = function(_) {\n    return arguments.length ? (defined = typeof _ === \"function\" ? _ : constant(!!_), line) : defined;\n  };\n\n  line.curve = function(_) {\n    return arguments.length ? (curve = _, context != null && (output = curve(context)), line) : curve;\n  };\n\n  line.context = function(_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), line) : context;\n  };\n\n  return line;\n}\n", "import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport curveLinear from \"./curve/linear.js\";\nimport line from \"./line.js\";\nimport {withPath} from \"./path.js\";\nimport {x as pointX, y as pointY} from \"./point.js\";\n\nexport default function(x0, y0, y1) {\n  var x1 = null,\n      defined = constant(true),\n      context = null,\n      curve = curveLinear,\n      output = null,\n      path = withPath(area);\n\n  x0 = typeof x0 === \"function\" ? x0 : (x0 === undefined) ? pointX : constant(+x0);\n  y0 = typeof y0 === \"function\" ? y0 : (y0 === undefined) ? constant(0) : constant(+y0);\n  y1 = typeof y1 === \"function\" ? y1 : (y1 === undefined) ? pointY : constant(+y1);\n\n  function area(data) {\n    var i,\n        j,\n        k,\n        n = (data = array(data)).length,\n        d,\n        defined0 = false,\n        buffer,\n        x0z = new Array(n),\n        y0z = new Array(n);\n\n    if (context == null) output = curve(buffer = path());\n\n    for (i = 0; i <= n; ++i) {\n      if (!(i < n && defined(d = data[i], i, data)) === defined0) {\n        if (defined0 = !defined0) {\n          j = i;\n          output.areaStart();\n          output.lineStart();\n        } else {\n          output.lineEnd();\n          output.lineStart();\n          for (k = i - 1; k >= j; --k) {\n            output.point(x0z[k], y0z[k]);\n          }\n          output.lineEnd();\n          output.areaEnd();\n        }\n      }\n      if (defined0) {\n        x0z[i] = +x0(d, i, data), y0z[i] = +y0(d, i, data);\n        output.point(x1 ? +x1(d, i, data) : x0z[i], y1 ? +y1(d, i, data) : y0z[i]);\n      }\n    }\n\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n\n  function arealine() {\n    return line().defined(defined).curve(curve).context(context);\n  }\n\n  area.x = function(_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : constant(+_), x1 = null, area) : x0;\n  };\n\n  area.x0 = function(_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : constant(+_), area) : x0;\n  };\n\n  area.x1 = function(_) {\n    return arguments.length ? (x1 = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), area) : x1;\n  };\n\n  area.y = function(_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : constant(+_), y1 = null, area) : y0;\n  };\n\n  area.y0 = function(_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : constant(+_), area) : y0;\n  };\n\n  area.y1 = function(_) {\n    return arguments.length ? (y1 = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), area) : y1;\n  };\n\n  area.lineX0 =\n  area.lineY0 = function() {\n    return arealine().x(x0).y(y0);\n  };\n\n  area.lineY1 = function() {\n    return arealine().x(x0).y(y1);\n  };\n\n  area.lineX1 = function() {\n    return arealine().x(x1).y(y0);\n  };\n\n  area.defined = function(_) {\n    return arguments.length ? (defined = typeof _ === \"function\" ? _ : constant(!!_), area) : defined;\n  };\n\n  area.curve = function(_) {\n    return arguments.length ? (curve = _, context != null && (output = curve(context)), area) : curve;\n  };\n\n  area.context = function(_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), area) : context;\n  };\n\n  return area;\n}\n", "export default function(a, b) {\n  return b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}\n", "export default function(d) {\n  return d;\n}\n", "import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport descending from \"./descending.js\";\nimport identity from \"./identity.js\";\nimport {tau} from \"./math.js\";\n\nexport default function() {\n  var value = identity,\n      sortValues = descending,\n      sort = null,\n      startAngle = constant(0),\n      endAngle = constant(tau),\n      padAngle = constant(0);\n\n  function pie(data) {\n    var i,\n        n = (data = array(data)).length,\n        j,\n        k,\n        sum = 0,\n        index = new Array(n),\n        arcs = new Array(n),\n        a0 = +startAngle.apply(this, arguments),\n        da = Math.min(tau, Math.max(-tau, endAngle.apply(this, arguments) - a0)),\n        a1,\n        p = Math.min(Math.abs(da) / n, padAngle.apply(this, arguments)),\n        pa = p * (da < 0 ? -1 : 1),\n        v;\n\n    for (i = 0; i < n; ++i) {\n      if ((v = arcs[index[i] = i] = +value(data[i], i, data)) > 0) {\n        sum += v;\n      }\n    }\n\n    // Optionally sort the arcs by previously-computed values or by data.\n    if (sortValues != null) index.sort(function(i, j) { return sortValues(arcs[i], arcs[j]); });\n    else if (sort != null) index.sort(function(i, j) { return sort(data[i], data[j]); });\n\n    // Compute the arcs! They are stored in the original data's order.\n    for (i = 0, k = sum ? (da - n * pa) / sum : 0; i < n; ++i, a0 = a1) {\n      j = index[i], v = arcs[j], a1 = a0 + (v > 0 ? v * k : 0) + pa, arcs[j] = {\n        data: data[j],\n        index: i,\n        value: v,\n        startAngle: a0,\n        endAngle: a1,\n        padAngle: p\n      };\n    }\n\n    return arcs;\n  }\n\n  pie.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(+_), pie) : value;\n  };\n\n  pie.sortValues = function(_) {\n    return arguments.length ? (sortValues = _, sort = null, pie) : sortValues;\n  };\n\n  pie.sort = function(_) {\n    return arguments.length ? (sort = _, sortValues = null, pie) : sort;\n  };\n\n  pie.startAngle = function(_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : startAngle;\n  };\n\n  pie.endAngle = function(_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : endAngle;\n  };\n\n  pie.padAngle = function(_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : padAngle;\n  };\n\n  return pie;\n}\n", "import curveLinear from \"./linear.js\";\n\nexport var curveRadialLinear = curveRadial(curveLinear);\n\nfunction Radial(curve) {\n  this._curve = curve;\n}\n\nRadial.prototype = {\n  areaStart: function() {\n    this._curve.areaStart();\n  },\n  areaEnd: function() {\n    this._curve.areaEnd();\n  },\n  lineStart: function() {\n    this._curve.lineStart();\n  },\n  lineEnd: function() {\n    this._curve.lineEnd();\n  },\n  point: function(a, r) {\n    this._curve.point(r * Math.sin(a), r * -Math.cos(a));\n  }\n};\n\nexport default function curveRadial(curve) {\n\n  function radial(context) {\n    return new Radial(curve(context));\n  }\n\n  radial._curve = curve;\n\n  return radial;\n}\n", "import curveRadial, {curveRadialLinear} from \"./curve/radial.js\";\nimport line from \"./line.js\";\n\nexport function lineRadial(l) {\n  var c = l.curve;\n\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n\n  l.curve = function(_) {\n    return arguments.length ? c(curveRadial(_)) : c()._curve;\n  };\n\n  return l;\n}\n\nexport default function() {\n  return lineRadial(line().curve(curveRadialLinear));\n}\n", "import curveRadial, {curveRadialLinear} from \"./curve/radial.js\";\nimport area from \"./area.js\";\nimport {lineRadial} from \"./lineRadial.js\";\n\nexport default function() {\n  var a = area().curve(curveRadialLinear),\n      c = a.curve,\n      x0 = a.lineX0,\n      x1 = a.lineX1,\n      y0 = a.lineY0,\n      y1 = a.lineY1;\n\n  a.angle = a.x, delete a.x;\n  a.startAngle = a.x0, delete a.x0;\n  a.endAngle = a.x1, delete a.x1;\n  a.radius = a.y, delete a.y;\n  a.innerRadius = a.y0, delete a.y0;\n  a.outerRadius = a.y1, delete a.y1;\n  a.lineStartAngle = function() { return lineRadial(x0()); }, delete a.lineX0;\n  a.lineEndAngle = function() { return lineRadial(x1()); }, delete a.lineX1;\n  a.lineInnerRadius = function() { return lineRadial(y0()); }, delete a.lineY0;\n  a.lineOuterRadius = function() { return lineRadial(y1()); }, delete a.lineY1;\n\n  a.curve = function(_) {\n    return arguments.length ? c(curveRadial(_)) : c()._curve;\n  };\n\n  return a;\n}\n", "export default function(x, y) {\n  return [(y = +y) * Math.cos(x -= Math.PI / 2), y * Math.sin(x)];\n}\n", "import pointRadial from \"../pointRadial.js\";\n\nclass Bump {\n  constructor(context, x) {\n    this._context = context;\n    this._x = x;\n  }\n  areaStart() {\n    this._line = 0;\n  }\n  areaEnd() {\n    this._line = NaN;\n  }\n  lineStart() {\n    this._point = 0;\n  }\n  lineEnd() {\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  }\n  point(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: {\n        this._point = 1;\n        if (this._line) this._context.lineTo(x, y);\n        else this._context.moveTo(x, y);\n        break;\n      }\n      case 1: this._point = 2; // falls through\n      default: {\n        if (this._x) this._context.bezierCurveTo(this._x0 = (this._x0 + x) / 2, this._y0, this._x0, y, x, y);\n        else this._context.bezierCurveTo(this._x0, this._y0 = (this._y0 + y) / 2, x, this._y0, x, y);\n        break;\n      }\n    }\n    this._x0 = x, this._y0 = y;\n  }\n}\n\nclass BumpRadial {\n  constructor(context) {\n    this._context = context;\n  }\n  lineStart() {\n    this._point = 0;\n  }\n  lineEnd() {}\n  point(x, y) {\n    x = +x, y = +y;\n    if (this._point === 0) {\n      this._point = 1;\n    } else {\n      const p0 = pointRadial(this._x0, this._y0);\n      const p1 = pointRadial(this._x0, this._y0 = (this._y0 + y) / 2);\n      const p2 = pointRadial(x, this._y0);\n      const p3 = pointRadial(x, y);\n      this._context.moveTo(...p0);\n      this._context.bezierCurveTo(...p1, ...p2, ...p3);\n    }\n    this._x0 = x, this._y0 = y;\n  }\n}\n\nexport function bumpX(context) {\n  return new Bump(context, true);\n}\n\nexport function bumpY(context) {\n  return new Bump(context, false);\n}\n\nexport function bumpRadial(context) {\n  return new BumpRadial(context);\n}\n", "import {slice} from \"./array.js\";\nimport constant from \"./constant.js\";\nimport {bumpX, bumpY, bumpRadial} from \"./curve/bump.js\";\nimport {withPath} from \"./path.js\";\nimport {x as pointX, y as pointY} from \"./point.js\";\n\nfunction linkSource(d) {\n  return d.source;\n}\n\nfunction linkTarget(d) {\n  return d.target;\n}\n\nexport function link(curve) {\n  let source = linkSource,\n      target = linkTarget,\n      x = pointX,\n      y = pointY,\n      context = null,\n      output = null,\n      path = withPath(link);\n\n  function link() {\n    let buffer;\n    const argv = slice.call(arguments);\n    const s = source.apply(this, argv);\n    const t = target.apply(this, argv);\n    if (context == null) output = curve(buffer = path());\n    output.lineStart();\n    argv[0] = s, output.point(+x.apply(this, argv), +y.apply(this, argv));\n    argv[0] = t, output.point(+x.apply(this, argv), +y.apply(this, argv));\n    output.lineEnd();\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n\n  link.source = function(_) {\n    return arguments.length ? (source = _, link) : source;\n  };\n\n  link.target = function(_) {\n    return arguments.length ? (target = _, link) : target;\n  };\n\n  link.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), link) : x;\n  };\n\n  link.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), link) : y;\n  };\n\n  link.context = function(_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), link) : context;\n  };\n\n  return link;\n}\n\nexport function linkHorizontal() {\n  return link(bumpX);\n}\n\nexport function linkVertical() {\n  return link(bumpY);\n}\n\nexport function linkRadial() {\n  const l = link(bumpRadial);\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n  return l;\n}\n", "import {min, sqrt} from \"../math.js\";\n\nconst sqrt3 = sqrt(3);\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size + min(size / 28, 0.75)) * 0.59436;\n    const t = r / 2;\n    const u = t * sqrt3;\n    context.moveTo(0, r);\n    context.lineTo(0, -r);\n    context.moveTo(-u, -t);\n    context.lineTo(u, t);\n    context.moveTo(-u, t);\n    context.lineTo(u, -t);\n  }\n};\n", "import {pi, sqrt, tau} from \"../math.js\";\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size / pi);\n    context.moveTo(r, 0);\n    context.arc(0, 0, r, 0, tau);\n  }\n};\n", "import {sqrt} from \"../math.js\";\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size / 5) / 2;\n    context.moveTo(-3 * r, -r);\n    context.lineTo(-r, -r);\n    context.lineTo(-r, -3 * r);\n    context.lineTo(r, -3 * r);\n    context.lineTo(r, -r);\n    context.lineTo(3 * r, -r);\n    context.lineTo(3 * r, r);\n    context.lineTo(r, r);\n    context.lineTo(r, 3 * r);\n    context.lineTo(-r, 3 * r);\n    context.lineTo(-r, r);\n    context.lineTo(-3 * r, r);\n    context.closePath();\n  }\n};\n", "import {sqrt} from \"../math.js\";\n\nconst tan30 = sqrt(1 / 3);\nconst tan30_2 = tan30 * 2;\n\nexport default {\n  draw(context, size) {\n    const y = sqrt(size / tan30_2);\n    const x = y * tan30;\n    context.moveTo(0, -y);\n    context.lineTo(x, 0);\n    context.lineTo(0, y);\n    context.lineTo(-x, 0);\n    context.closePath();\n  }\n};\n", "import {sqrt} from \"../math.js\";\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size) * 0.62625;\n    context.moveTo(0, -r);\n    context.lineTo(r, 0);\n    context.lineTo(0, r);\n    context.lineTo(-r, 0);\n    context.closePath();\n  }\n};\n", "import {min, sqrt} from \"../math.js\";\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size - min(size / 7, 2)) * 0.87559;\n    context.moveTo(-r, 0);\n    context.lineTo(r, 0);\n    context.moveTo(0, r);\n    context.lineTo(0, -r);\n  }\n};\n", "import {sqrt} from \"../math.js\";\n\nexport default {\n  draw(context, size) {\n    const w = sqrt(size);\n    const x = -w / 2;\n    context.rect(x, x, w, w);\n  }\n};\n", "import {sqrt} from \"../math.js\";\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size) * 0.4431;\n    context.moveTo(r, r);\n    context.lineTo(r, -r);\n    context.lineTo(-r, -r);\n    context.lineTo(-r, r);\n    context.closePath();\n  }\n};\n", "import {sin, cos, sqrt, pi, tau} from \"../math.js\";\n\nconst ka = 0.89081309152928522810;\nconst kr = sin(pi / 10) / sin(7 * pi / 10);\nconst kx = sin(tau / 10) * kr;\nconst ky = -cos(tau / 10) * kr;\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size * ka);\n    const x = kx * r;\n    const y = ky * r;\n    context.moveTo(0, -r);\n    context.lineTo(x, y);\n    for (let i = 1; i < 5; ++i) {\n      const a = tau * i / 5;\n      const c = cos(a);\n      const s = sin(a);\n      context.lineTo(s * r, -c * r);\n      context.lineTo(c * x - s * y, s * x + c * y);\n    }\n    context.closePath();\n  }\n};\n", "import {sqrt} from \"../math.js\";\n\nconst sqrt3 = sqrt(3);\n\nexport default {\n  draw(context, size) {\n    const y = -sqrt(size / (sqrt3 * 3));\n    context.moveTo(0, y * 2);\n    context.lineTo(-sqrt3 * y, -y);\n    context.lineTo(sqrt3 * y, -y);\n    context.closePath();\n  }\n};\n", "import {sqrt} from \"../math.js\";\n\nconst sqrt3 = sqrt(3);\n\nexport default {\n  draw(context, size) {\n    const s = sqrt(size) * 0.6824;\n    const t = s  / 2;\n    const u = (s * sqrt3) / 2; // cos(Math.PI / 6)\n    context.moveTo(0, -s);\n    context.lineTo(u, t);\n    context.lineTo(-u, t);\n    context.closePath();\n  }\n};\n", "import {sqrt} from \"../math.js\";\n\nconst c = -0.5;\nconst s = sqrt(3) / 2;\nconst k = 1 / sqrt(12);\nconst a = (k / 2 + 1) * 3;\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size / a);\n    const x0 = r / 2, y0 = r * k;\n    const x1 = x0, y1 = r * k + r;\n    const x2 = -x1, y2 = y1;\n    context.moveTo(x0, y0);\n    context.lineTo(x1, y1);\n    context.lineTo(x2, y2);\n    context.lineTo(c * x0 - s * y0, s * x0 + c * y0);\n    context.lineTo(c * x1 - s * y1, s * x1 + c * y1);\n    context.lineTo(c * x2 - s * y2, s * x2 + c * y2);\n    context.lineTo(c * x0 + s * y0, c * y0 - s * x0);\n    context.lineTo(c * x1 + s * y1, c * y1 - s * x1);\n    context.lineTo(c * x2 + s * y2, c * y2 - s * x2);\n    context.closePath();\n  }\n};\n", "import {min, sqrt} from \"../math.js\";\n\nexport default {\n  draw(context, size) {\n    const r = sqrt(size - min(size / 6, 1.7)) * 0.6189;\n    context.moveTo(-r, -r);\n    context.lineTo(r, r);\n    context.moveTo(-r, r);\n    context.lineTo(r, -r);\n  }\n};\n", "import constant from \"./constant.js\";\nimport {withPath} from \"./path.js\";\nimport asterisk from \"./symbol/asterisk.js\";\nimport circle from \"./symbol/circle.js\";\nimport cross from \"./symbol/cross.js\";\nimport diamond from \"./symbol/diamond.js\";\nimport diamond2 from \"./symbol/diamond2.js\";\nimport plus from \"./symbol/plus.js\";\nimport square from \"./symbol/square.js\";\nimport square2 from \"./symbol/square2.js\";\nimport star from \"./symbol/star.js\";\nimport triangle from \"./symbol/triangle.js\";\nimport triangle2 from \"./symbol/triangle2.js\";\nimport wye from \"./symbol/wye.js\";\nimport times from \"./symbol/times.js\";\n\n// These symbols are designed to be filled.\nexport const symbolsFill = [\n  circle,\n  cross,\n  diamond,\n  square,\n  star,\n  triangle,\n  wye\n];\n\n// These symbols are designed to be stroked (with a width of 1.5px and round caps).\nexport const symbolsStroke = [\n  circle,\n  plus,\n  times,\n  triangle2,\n  asterisk,\n  square2,\n  diamond2\n];\n\nexport default function Symbol(type, size) {\n  let context = null,\n      path = withPath(symbol);\n\n  type = typeof type === \"function\" ? type : constant(type || circle);\n  size = typeof size === \"function\" ? size : constant(size === undefined ? 64 : +size);\n\n  function symbol() {\n    let buffer;\n    if (!context) context = buffer = path();\n    type.apply(this, arguments).draw(context, +size.apply(this, arguments));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  symbol.type = function(_) {\n    return arguments.length ? (type = typeof _ === \"function\" ? _ : constant(_), symbol) : type;\n  };\n\n  symbol.size = function(_) {\n    return arguments.length ? (size = typeof _ === \"function\" ? _ : constant(+_), symbol) : size;\n  };\n\n  symbol.context = function(_) {\n    return arguments.length ? (context = _ == null ? null : _, symbol) : context;\n  };\n\n  return symbol;\n}\n", "export default function() {}\n", "export function point(that, x, y) {\n  that._context.bezierCurveTo(\n    (2 * that._x0 + that._x1) / 3,\n    (2 * that._y0 + that._y1) / 3,\n    (that._x0 + 2 * that._x1) / 3,\n    (that._y0 + 2 * that._y1) / 3,\n    (that._x0 + 4 * that._x1 + x) / 6,\n    (that._y0 + 4 * that._y1 + y) / 6\n  );\n}\n\nexport function Basis(context) {\n  this._context = context;\n}\n\nBasis.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 =\n    this._y0 = this._y1 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 3: point(this, this._x1, this._y1); // falls through\n      case 2: this._context.lineTo(this._x1, this._y1); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; this._context.lineTo((5 * this._x0 + this._x1) / 6, (5 * this._y0 + this._y1) / 6); // falls through\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\n\nexport default function(context) {\n  return new Basis(context);\n}\n", "import noop from \"../noop.js\";\nimport {point} from \"./basis.js\";\n\nfunction BasisClosed(context) {\n  this._context = context;\n}\n\nBasisClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 =\n    this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 1: {\n        this._context.moveTo(this._x2, this._y2);\n        this._context.closePath();\n        break;\n      }\n      case 2: {\n        this._context.moveTo((this._x2 + 2 * this._x3) / 3, (this._y2 + 2 * this._y3) / 3);\n        this._context.lineTo((this._x3 + 2 * this._x2) / 3, (this._y3 + 2 * this._y2) / 3);\n        this._context.closePath();\n        break;\n      }\n      case 3: {\n        this.point(this._x2, this._y2);\n        this.point(this._x3, this._y3);\n        this.point(this._x4, this._y4);\n        break;\n      }\n    }\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._x2 = x, this._y2 = y; break;\n      case 1: this._point = 2; this._x3 = x, this._y3 = y; break;\n      case 2: this._point = 3; this._x4 = x, this._y4 = y; this._context.moveTo((this._x0 + 4 * this._x1 + x) / 6, (this._y0 + 4 * this._y1 + y) / 6); break;\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\n\nexport default function(context) {\n  return new BasisClosed(context);\n}\n", "import {point} from \"./basis.js\";\n\nfunction BasisOpen(context) {\n  this._context = context;\n}\n\nBasisOpen.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 =\n    this._y0 = this._y1 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 3)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; var x0 = (this._x0 + 4 * this._x1 + x) / 6, y0 = (this._y0 + 4 * this._y1 + y) / 6; this._line ? this._context.lineTo(x0, y0) : this._context.moveTo(x0, y0); break;\n      case 3: this._point = 4; // falls through\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\n\nexport default function(context) {\n  return new BasisOpen(context);\n}\n", "import {Basis} from \"./basis.js\";\n\nfunction Bundle(context, beta) {\n  this._basis = new Basis(context);\n  this._beta = beta;\n}\n\nBundle.prototype = {\n  lineStart: function() {\n    this._x = [];\n    this._y = [];\n    this._basis.lineStart();\n  },\n  lineEnd: function() {\n    var x = this._x,\n        y = this._y,\n        j = x.length - 1;\n\n    if (j > 0) {\n      var x0 = x[0],\n          y0 = y[0],\n          dx = x[j] - x0,\n          dy = y[j] - y0,\n          i = -1,\n          t;\n\n      while (++i <= j) {\n        t = i / j;\n        this._basis.point(\n          this._beta * x[i] + (1 - this._beta) * (x0 + t * dx),\n          this._beta * y[i] + (1 - this._beta) * (y0 + t * dy)\n        );\n      }\n    }\n\n    this._x = this._y = null;\n    this._basis.lineEnd();\n  },\n  point: function(x, y) {\n    this._x.push(+x);\n    this._y.push(+y);\n  }\n};\n\nexport default (function custom(beta) {\n\n  function bundle(context) {\n    return beta === 1 ? new Basis(context) : new Bundle(context, beta);\n  }\n\n  bundle.beta = function(beta) {\n    return custom(+beta);\n  };\n\n  return bundle;\n})(0.85);\n", "export function point(that, x, y) {\n  that._context.bezierCurveTo(\n    that._x1 + that._k * (that._x2 - that._x0),\n    that._y1 + that._k * (that._y2 - that._y0),\n    that._x2 + that._k * (that._x1 - x),\n    that._y2 + that._k * (that._y1 - y),\n    that._x2,\n    that._y2\n  );\n}\n\nexport function Cardinal(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\n\nCardinal.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 2: this._context.lineTo(this._x2, this._y2); break;\n      case 3: point(this, this._x1, this._y1); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; this._x1 = x, this._y1 = y; break;\n      case 2: this._point = 3; // falls through\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(tension) {\n\n  function cardinal(context) {\n    return new Cardinal(context, tension);\n  }\n\n  cardinal.tension = function(tension) {\n    return custom(+tension);\n  };\n\n  return cardinal;\n})(0);\n", "import noop from \"../noop.js\";\nimport {point} from \"./cardinal.js\";\n\nexport function CardinalClosed(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\n\nCardinalClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 =\n    this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 1: {\n        this._context.moveTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 2: {\n        this._context.lineTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 3: {\n        this.point(this._x3, this._y3);\n        this.point(this._x4, this._y4);\n        this.point(this._x5, this._y5);\n        break;\n      }\n    }\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._x3 = x, this._y3 = y; break;\n      case 1: this._point = 2; this._context.moveTo(this._x4 = x, this._y4 = y); break;\n      case 2: this._point = 3; this._x5 = x, this._y5 = y; break;\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(tension) {\n\n  function cardinal(context) {\n    return new CardinalClosed(context, tension);\n  }\n\n  cardinal.tension = function(tension) {\n    return custom(+tension);\n  };\n\n  return cardinal;\n})(0);\n", "import {point} from \"./cardinal.js\";\n\nexport function CardinalOpen(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\n\nCardinalOpen.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 3)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2); break;\n      case 3: this._point = 4; // falls through\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(tension) {\n\n  function cardinal(context) {\n    return new CardinalOpen(context, tension);\n  }\n\n  cardinal.tension = function(tension) {\n    return custom(+tension);\n  };\n\n  return cardinal;\n})(0);\n", "import {epsilon} from \"../math.js\";\nimport {<PERSON>} from \"./cardinal.js\";\n\nexport function point(that, x, y) {\n  var x1 = that._x1,\n      y1 = that._y1,\n      x2 = that._x2,\n      y2 = that._y2;\n\n  if (that._l01_a > epsilon) {\n    var a = 2 * that._l01_2a + 3 * that._l01_a * that._l12_a + that._l12_2a,\n        n = 3 * that._l01_a * (that._l01_a + that._l12_a);\n    x1 = (x1 * a - that._x0 * that._l12_2a + that._x2 * that._l01_2a) / n;\n    y1 = (y1 * a - that._y0 * that._l12_2a + that._y2 * that._l01_2a) / n;\n  }\n\n  if (that._l23_a > epsilon) {\n    var b = 2 * that._l23_2a + 3 * that._l23_a * that._l12_a + that._l12_2a,\n        m = 3 * that._l23_a * (that._l23_a + that._l12_a);\n    x2 = (x2 * b + that._x1 * that._l23_2a - x * that._l12_2a) / m;\n    y2 = (y2 * b + that._y1 * that._l23_2a - y * that._l12_2a) / m;\n  }\n\n  that._context.bezierCurveTo(x1, y1, x2, y2, that._x2, that._y2);\n}\n\nfunction CatmullRom(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\n\nCatmullRom.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._l01_a = this._l12_a = this._l23_a =\n    this._l01_2a = this._l12_2a = this._l23_2a =\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 2: this._context.lineTo(this._x2, this._y2); break;\n      case 3: this.point(this._x2, this._y2); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n\n    if (this._point) {\n      var x23 = this._x2 - x,\n          y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; // falls through\n      default: point(this, x, y); break;\n    }\n\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(alpha) {\n\n  function catmullRom(context) {\n    return alpha ? new CatmullRom(context, alpha) : new Cardinal(context, 0);\n  }\n\n  catmullRom.alpha = function(alpha) {\n    return custom(+alpha);\n  };\n\n  return catmullRom;\n})(0.5);\n", "import {CardinalClosed} from \"./cardinalClosed.js\";\nimport noop from \"../noop.js\";\nimport {point} from \"./catmullRom.js\";\n\nfunction CatmullRomClosed(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\n\nCatmullRomClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 =\n    this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;\n    this._l01_a = this._l12_a = this._l23_a =\n    this._l01_2a = this._l12_2a = this._l23_2a =\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 1: {\n        this._context.moveTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 2: {\n        this._context.lineTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 3: {\n        this.point(this._x3, this._y3);\n        this.point(this._x4, this._y4);\n        this.point(this._x5, this._y5);\n        break;\n      }\n    }\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n\n    if (this._point) {\n      var x23 = this._x2 - x,\n          y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n\n    switch (this._point) {\n      case 0: this._point = 1; this._x3 = x, this._y3 = y; break;\n      case 1: this._point = 2; this._context.moveTo(this._x4 = x, this._y4 = y); break;\n      case 2: this._point = 3; this._x5 = x, this._y5 = y; break;\n      default: point(this, x, y); break;\n    }\n\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(alpha) {\n\n  function catmullRom(context) {\n    return alpha ? new CatmullRomClosed(context, alpha) : new CardinalClosed(context, 0);\n  }\n\n  catmullRom.alpha = function(alpha) {\n    return custom(+alpha);\n  };\n\n  return catmullRom;\n})(0.5);\n", "import {<PERSON><PERSON><PERSON>} from \"./cardinalOpen.js\";\nimport {point} from \"./catmullRom.js\";\n\nfunction CatmullRomOpen(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\n\nCatmullRomOpen.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._l01_a = this._l12_a = this._l23_a =\n    this._l01_2a = this._l12_2a = this._l23_2a =\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 3)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n\n    if (this._point) {\n      var x23 = this._x2 - x,\n          y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n\n    switch (this._point) {\n      case 0: this._point = 1; break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2); break;\n      case 3: this._point = 4; // falls through\n      default: point(this, x, y); break;\n    }\n\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(alpha) {\n\n  function catmullRom(context) {\n    return alpha ? new CatmullRomOpen(context, alpha) : new CardinalOpen(context, 0);\n  }\n\n  catmullRom.alpha = function(alpha) {\n    return custom(+alpha);\n  };\n\n  return catmullRom;\n})(0.5);\n", "import noop from \"../noop.js\";\n\nfunction LinearClosed(context) {\n  this._context = context;\n}\n\nLinearClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._point) this._context.closePath();\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    if (this._point) this._context.lineTo(x, y);\n    else this._point = 1, this._context.moveTo(x, y);\n  }\n};\n\nexport default function(context) {\n  return new LinearClosed(context);\n}\n", "function sign(x) {\n  return x < 0 ? -1 : 1;\n}\n\n// Calculate the slopes of the tangents (Hermite-type interpolation) based on\n// the following paper: <PERSON>effen, M. 1990. A Simple Method for Monotonic\n// Interpolation in One Dimension. Astronomy and Astrophysics, Vol. 239, NO.\n// NOV(II), P. 443, 1990.\nfunction slope3(that, x2, y2) {\n  var h0 = that._x1 - that._x0,\n      h1 = x2 - that._x1,\n      s0 = (that._y1 - that._y0) / (h0 || h1 < 0 && -0),\n      s1 = (y2 - that._y1) / (h1 || h0 < 0 && -0),\n      p = (s0 * h1 + s1 * h0) / (h0 + h1);\n  return (sign(s0) + sign(s1)) * Math.min(Math.abs(s0), Math.abs(s1), 0.5 * Math.abs(p)) || 0;\n}\n\n// Calculate a one-sided slope.\nfunction slope2(that, t) {\n  var h = that._x1 - that._x0;\n  return h ? (3 * (that._y1 - that._y0) / h - t) / 2 : t;\n}\n\n// According to https://en.wikipedia.org/wiki/Cubic_Hermite_spline#Representations\n// \"you can express cubic Hermite interpolation in terms of cubic Bézier curves\n// with respect to the four values p0, p0 + m0 / 3, p1 - m1 / 3, p1\".\nfunction point(that, t0, t1) {\n  var x0 = that._x0,\n      y0 = that._y0,\n      x1 = that._x1,\n      y1 = that._y1,\n      dx = (x1 - x0) / 3;\n  that._context.bezierCurveTo(x0 + dx, y0 + dx * t0, x1 - dx, y1 - dx * t1, x1, y1);\n}\n\nfunction MonotoneX(context) {\n  this._context = context;\n}\n\nMonotoneX.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 =\n    this._y0 = this._y1 =\n    this._t0 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 2: this._context.lineTo(this._x1, this._y1); break;\n      case 3: point(this, this._t0, slope2(this, this._t0)); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    var t1 = NaN;\n\n    x = +x, y = +y;\n    if (x === this._x1 && y === this._y1) return; // Ignore coincident points.\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; point(this, slope2(this, t1 = slope3(this, x, y)), t1); break;\n      default: point(this, this._t0, t1 = slope3(this, x, y)); break;\n    }\n\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n    this._t0 = t1;\n  }\n}\n\nfunction MonotoneY(context) {\n  this._context = new ReflectContext(context);\n}\n\n(MonotoneY.prototype = Object.create(MonotoneX.prototype)).point = function(x, y) {\n  MonotoneX.prototype.point.call(this, y, x);\n};\n\nfunction ReflectContext(context) {\n  this._context = context;\n}\n\nReflectContext.prototype = {\n  moveTo: function(x, y) { this._context.moveTo(y, x); },\n  closePath: function() { this._context.closePath(); },\n  lineTo: function(x, y) { this._context.lineTo(y, x); },\n  bezierCurveTo: function(x1, y1, x2, y2, x, y) { this._context.bezierCurveTo(y1, x1, y2, x2, y, x); }\n};\n\nexport function monotoneX(context) {\n  return new MonotoneX(context);\n}\n\nexport function monotoneY(context) {\n  return new MonotoneY(context);\n}\n", "function Natural(context) {\n  this._context = context;\n}\n\nNatural.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x = [];\n    this._y = [];\n  },\n  lineEnd: function() {\n    var x = this._x,\n        y = this._y,\n        n = x.length;\n\n    if (n) {\n      this._line ? this._context.lineTo(x[0], y[0]) : this._context.moveTo(x[0], y[0]);\n      if (n === 2) {\n        this._context.lineTo(x[1], y[1]);\n      } else {\n        var px = controlPoints(x),\n            py = controlPoints(y);\n        for (var i0 = 0, i1 = 1; i1 < n; ++i0, ++i1) {\n          this._context.bezierCurveTo(px[0][i0], py[0][i0], px[1][i0], py[1][i0], x[i1], y[i1]);\n        }\n      }\n    }\n\n    if (this._line || (this._line !== 0 && n === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n    this._x = this._y = null;\n  },\n  point: function(x, y) {\n    this._x.push(+x);\n    this._y.push(+y);\n  }\n};\n\n// See https://www.particleincell.com/2012/bezier-splines/ for derivation.\nfunction controlPoints(x) {\n  var i,\n      n = x.length - 1,\n      m,\n      a = new Array(n),\n      b = new Array(n),\n      r = new Array(n);\n  a[0] = 0, b[0] = 2, r[0] = x[0] + 2 * x[1];\n  for (i = 1; i < n - 1; ++i) a[i] = 1, b[i] = 4, r[i] = 4 * x[i] + 2 * x[i + 1];\n  a[n - 1] = 2, b[n - 1] = 7, r[n - 1] = 8 * x[n - 1] + x[n];\n  for (i = 1; i < n; ++i) m = a[i] / b[i - 1], b[i] -= m, r[i] -= m * r[i - 1];\n  a[n - 1] = r[n - 1] / b[n - 1];\n  for (i = n - 2; i >= 0; --i) a[i] = (r[i] - a[i + 1]) / b[i];\n  b[n - 1] = (x[n] + a[n - 1]) / 2;\n  for (i = 0; i < n - 1; ++i) b[i] = 2 * x[i + 1] - a[i + 1];\n  return [a, b];\n}\n\nexport default function(context) {\n  return new Natural(context);\n}\n", "function Step(context, t) {\n  this._context = context;\n  this._t = t;\n}\n\nStep.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x = this._y = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (0 < this._t && this._t < 1 && this._point === 2) this._context.lineTo(this._x, this._y);\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    if (this._line >= 0) this._t = 1 - this._t, this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; // falls through\n      default: {\n        if (this._t <= 0) {\n          this._context.lineTo(this._x, y);\n          this._context.lineTo(x, y);\n        } else {\n          var x1 = this._x * (1 - this._t) + x * this._t;\n          this._context.lineTo(x1, this._y);\n          this._context.lineTo(x1, y);\n        }\n        break;\n      }\n    }\n    this._x = x, this._y = y;\n  }\n};\n\nexport default function(context) {\n  return new Step(context, 0.5);\n}\n\nexport function stepBefore(context) {\n  return new Step(context, 0);\n}\n\nexport function stepAfter(context) {\n  return new Step(context, 1);\n}\n", "export default function(series, order) {\n  if (!((n = series.length) > 1)) return;\n  for (var i = 1, j, s0, s1 = series[order[0]], n, m = s1.length; i < n; ++i) {\n    s0 = s1, s1 = series[order[i]];\n    for (j = 0; j < m; ++j) {\n      s1[j][1] += s1[j][0] = isNaN(s0[j][1]) ? s0[j][0] : s0[j][1];\n    }\n  }\n}\n", "export default function(series) {\n  var n = series.length, o = new Array(n);\n  while (--n >= 0) o[n] = n;\n  return o;\n}\n", "import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport offsetNone from \"./offset/none.js\";\nimport orderNone from \"./order/none.js\";\n\nfunction stackValue(d, key) {\n  return d[key];\n}\n\nfunction stackSeries(key) {\n  const series = [];\n  series.key = key;\n  return series;\n}\n\nexport default function() {\n  var keys = constant([]),\n      order = orderNone,\n      offset = offsetNone,\n      value = stackValue;\n\n  function stack(data) {\n    var sz = Array.from(keys.apply(this, arguments), stackSeries),\n        i, n = sz.length, j = -1,\n        oz;\n\n    for (const d of data) {\n      for (i = 0, ++j; i < n; ++i) {\n        (sz[i][j] = [0, +value(d, sz[i].key, j, data)]).data = d;\n      }\n    }\n\n    for (i = 0, oz = array(order(sz)); i < n; ++i) {\n      sz[oz[i]].index = i;\n    }\n\n    offset(sz, oz);\n    return sz;\n  }\n\n  stack.keys = function(_) {\n    return arguments.length ? (keys = typeof _ === \"function\" ? _ : constant(Array.from(_)), stack) : keys;\n  };\n\n  stack.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(+_), stack) : value;\n  };\n\n  stack.order = function(_) {\n    return arguments.length ? (order = _ == null ? orderNone : typeof _ === \"function\" ? _ : constant(Array.from(_)), stack) : order;\n  };\n\n  stack.offset = function(_) {\n    return arguments.length ? (offset = _ == null ? offsetNone : _, stack) : offset;\n  };\n\n  return stack;\n}\n", "import none from \"./none.js\";\n\nexport default function(series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var i, n, j = 0, m = series[0].length, y; j < m; ++j) {\n    for (y = i = 0; i < n; ++i) y += series[i][j][1] || 0;\n    if (y) for (i = 0; i < n; ++i) series[i][j][1] /= y;\n  }\n  none(series, order);\n}\n", "export default function(series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var i, j = 0, d, dy, yp, yn, n, m = series[order[0]].length; j < m; ++j) {\n    for (yp = yn = 0, i = 0; i < n; ++i) {\n      if ((dy = (d = series[order[i]][j])[1] - d[0]) > 0) {\n        d[0] = yp, d[1] = yp += dy;\n      } else if (dy < 0) {\n        d[1] = yn, d[0] = yn += dy;\n      } else {\n        d[0] = 0, d[1] = dy;\n      }\n    }\n  }\n}\n", "import none from \"./none.js\";\n\nexport default function(series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var j = 0, s0 = series[order[0]], n, m = s0.length; j < m; ++j) {\n    for (var i = 0, y = 0; i < n; ++i) y += series[i][j][1] || 0;\n    s0[j][1] += s0[j][0] = -y / 2;\n  }\n  none(series, order);\n}\n", "import none from \"./none.js\";\n\nexport default function(series, order) {\n  if (!((n = series.length) > 0) || !((m = (s0 = series[order[0]]).length) > 0)) return;\n  for (var y = 0, j = 1, s0, m, n; j < m; ++j) {\n    for (var i = 0, s1 = 0, s2 = 0; i < n; ++i) {\n      var si = series[order[i]],\n          sij0 = si[j][1] || 0,\n          sij1 = si[j - 1][1] || 0,\n          s3 = (sij0 - sij1) / 2;\n      for (var k = 0; k < i; ++k) {\n        var sk = series[order[k]],\n            skj0 = sk[j][1] || 0,\n            skj1 = sk[j - 1][1] || 0;\n        s3 += skj0 - skj1;\n      }\n      s1 += sij0, s2 += s3 * sij0;\n    }\n    s0[j - 1][1] += s0[j - 1][0] = y;\n    if (s1) y -= s2 / s1;\n  }\n  s0[j - 1][1] += s0[j - 1][0] = y;\n  none(series, order);\n}\n", "import none from \"./none.js\";\n\nexport default function(series) {\n  var peaks = series.map(peak);\n  return none(series).sort(function(a, b) { return peaks[a] - peaks[b]; });\n}\n\nfunction peak(series) {\n  var i = -1, j = 0, n = series.length, vi, vj = -Infinity;\n  while (++i < n) if ((vi = +series[i][1]) > vj) vj = vi, j = i;\n  return j;\n}\n", "import none from \"./none.js\";\n\nexport default function(series) {\n  var sums = series.map(sum);\n  return none(series).sort(function(a, b) { return sums[a] - sums[b]; });\n}\n\nexport function sum(series) {\n  var s = 0, i = -1, n = series.length, v;\n  while (++i < n) if (v = +series[i][1]) s += v;\n  return s;\n}\n", "import ascending from \"./ascending.js\";\n\nexport default function(series) {\n  return ascending(series).reverse();\n}\n", "import appearance from \"./appearance.js\";\nimport {sum} from \"./ascending.js\";\n\nexport default function(series) {\n  var n = series.length,\n      i,\n      j,\n      sums = series.map(sum),\n      order = appearance(series),\n      top = 0,\n      bottom = 0,\n      tops = [],\n      bottoms = [];\n\n  for (i = 0; i < n; ++i) {\n    j = order[i];\n    if (top < bottom) {\n      top += sums[j];\n      tops.push(j);\n    } else {\n      bottom += sums[j];\n      bottoms.push(j);\n    }\n  }\n\n  return bottoms.reverse().concat(tops);\n}\n", "import none from \"./none.js\";\n\nexport default function(series) {\n  return none(series).reverse();\n}\n"], "names": ["r", "e", "f", "n", "o", "clsx", "ascending", "a", "b", "descending", "bisector", "compare1", "compare2", "delta", "d", "x", "zero", "left", "lo", "hi", "mid", "right", "center", "i", "number", "numbers", "values", "valueof", "value", "index", "ascendingBisect", "bisectRight", "bisectLeft", "bisectCenter", "bisect", "blur", "length", "blurf", "temp", "blur2", "Blur2", "blurImage", "blurfImage", "data", "rx", "ry", "width", "height", "blurx", "blury", "blurh", "blurv", "T", "S", "w", "h", "y", "radius", "start", "stop", "step", "radius0", "bluri", "t", "sum", "s0", "s1", "j", "s", "count", "array", "empty", "arrayify", "reducer", "reduce", "cross", "lengths", "product", "cumsum", "v", "variance", "mean", "deviation", "extent", "min", "max", "<PERSON><PERSON>", "p", "fsum", "adder", "fcumsum", "InternMap", "entries", "key", "keyof", "intern_get", "intern_set", "intern_delete", "InternSet", "_intern", "_key", "identity", "group", "keys", "nest", "groups", "flatten", "g", "flatGroup", "flatRollup", "rollups", "rollup", "unique", "indexes", "map", "regroup", "permute", "source", "sort", "F", "c", "ascendingDefined", "compareDefined", "compare", "groupSort", "ak", "av", "bk", "bv", "slice", "constant", "e10", "e5", "e2", "tickSpec", "power", "error", "factor", "i1", "i2", "inc", "ticks", "reverse", "tickIncrement", "tickStep", "nice", "prestep", "thresholdSturges", "bin", "domain", "threshold", "sturges", "histogram", "xz", "x0", "x1", "tz", "tn", "bins", "_", "maxIndex", "minIndex", "quickselect", "k", "m", "z", "sd", "newLeft", "newRight", "swap", "greatest", "defined", "maxValue", "element", "quantile", "i0", "value0", "value1", "quantileSorted", "quantileIndex", "thresholdFreedman<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "median", "medianIndex", "arrays", "merge", "mode", "counts", "modeValue", "modeCount", "pairs", "pairof", "pair", "previous", "first", "range", "rank", "V", "R", "compareIndex", "least", "minValue", "leastIndex", "greatestIndex", "scan", "shuffle", "shuffler", "random", "transpose", "matrix", "row", "zip", "every", "test", "some", "filter", "mapper", "iterator", "done", "next", "difference", "others", "other", "disjoint", "set", "intersection", "out", "superset", "io", "intern", "ivalue", "subset", "union", "define", "constructor", "factory", "prototype", "extend", "parent", "definition", "Color", "darker", "brighter", "reI", "reN", "reP", "reHex", "reRgbInteger", "reRgbPercent", "reRgbaInteger", "reRgbaPercent", "reHslPercent", "reHslaPercent", "named", "color", "channels", "color_formatHex", "color_formatHex8", "color_formatHsl", "color_formatRgb", "hslConvert", "format", "l", "rgbn", "Rgb", "rgba", "hsla", "rgbConvert", "rgb", "opacity", "clampi", "clampa", "rgb_formatHex", "rgb_formatHex8", "rgb_formatRgb", "hex", "Hsl", "hsl", "m2", "m1", "hsl2rgb", "clamph", "clampt", "radians", "degrees", "K", "Xn", "Yn", "Zn", "t0", "t1", "t2", "t3", "labConvert", "Lab", "Hcl", "hcl2lab", "rgb2lrgb", "xyz2lab", "gray", "lab", "lab2xyz", "lrgb2rgb", "hclConvert", "lch", "hcl", "A", "B", "C", "D", "E", "ED", "EB", "BC_DA", "cubehelixConvert", "Cubehel<PERSON>", "bl", "cubehelix", "cosh", "sinh", "basis", "v0", "v1", "v2", "v3", "basis$2", "basisClosed$1", "constant$1", "linear", "exponential", "hue", "gamma", "nogamma", "interpolateRgb", "rgbGamma", "end", "colorRgb", "rgbSpline", "spline", "colors", "rgbBasis", "rgbBasisClosed", "basisClosed", "numberArray", "isNumberArray", "array$1", "genericArray", "nb", "na", "date$1", "interpolateNumber", "object", "reA", "reB", "one", "interpolateString", "bi", "am", "bm", "bs", "q", "interpolate", "string", "date", "discrete", "interpolateRound", "decompose", "scaleX", "scaleY", "skewX", "svgNode", "parseCss", "parseSvg", "interpolateTransform", "parse", "pxComma", "pxParen", "degParen", "pop", "translate", "xa", "ya", "xb", "yb", "rotate", "scale", "interpolateTransformCss", "interpolateTransformSvg", "epsilon2", "tanh", "interpolateZoom", "zoomRho", "rho", "rho2", "rho4", "zoom", "p0", "p1", "ux0", "uy0", "w0", "ux1", "uy1", "w1", "dx", "dy", "d2", "d1", "b0", "b1", "r0", "r1", "coshr0", "u", "_1", "_2", "_4", "colorHsl", "hsl$1", "hslLong", "colorLab", "colorHcl", "hcl$1", "hclLong", "cubehelix<PERSON>ma", "colorCubehelix", "cubehelix$1", "cubehelixLong", "piecewise", "I", "quantize$1", "interpolator", "samples", "pi", "tau", "epsilon", "tauEpsilon", "append", "strings", "appendRound", "digits", "Path", "y1", "x2", "y2", "y0", "x21", "y21", "x01", "y01", "l01_2", "x20", "y20", "l21_2", "l20_2", "l21", "l01", "t01", "t21", "a0", "a1", "ccw", "cw", "da", "path", "pathRound", "formatDecimal", "formatDecimalParts", "coefficient", "exponent", "formatGroup", "grouping", "thousands", "formatNumerals", "numerals", "re", "formatSpecifier", "specifier", "match", "FormatSpecifier", "formatTrim", "prefixExponent", "formatPrefixAuto", "formatRounded", "formatTypes", "identity$3", "prefixes", "formatLocale$1", "locale", "currencyPrefix", "currencySuffix", "decimal", "percent", "minus", "nan", "newFormat", "fill", "align", "sign", "symbol", "comma", "precision", "trim", "type", "prefix", "suffix", "formatType", "maybeSuffix", "valuePrefix", "valueSuffix", "valueNegative", "padding", "formatPrefix", "defaultLocale", "formatLocale", "precisionFixed", "precisionPrefix", "precisionRound", "initRange", "initInterpolator", "implicit", "ordinal", "unknown", "band", "ordinalRange", "bandwidth", "round", "paddingInner", "paddingOuter", "rescale", "sequence", "pointish", "copy", "point", "constants", "unit", "normalize", "clamper", "bimap", "d0", "polymap", "target", "transformer", "interpolateV<PERSON>ue", "transform", "untransform", "clamp", "output", "input", "continuous", "tickFormat", "linearish", "maxIter", "interval", "transformLog", "transformExp", "transformLogn", "transformExpn", "pow10", "powp", "base", "logp", "reflect", "loggish", "logs", "pows", "log", "transformSymlog", "transformSymexp", "symlogish", "symlog", "transformPow", "transformSqrt", "transformSquare", "powish", "pow", "sqrt", "square", "unsquare", "radial", "squared", "thresholds", "quantize", "timeInterval", "floori", "offseti", "field", "millisecond", "milliseconds", "durationSecond", "durationMinute", "durationHour", "durationDay", "durationWeek", "durationMonth", "durationYear", "second", "seconds", "timeMinute", "timeMinutes", "utcMinute", "utcMinutes", "timeHour", "timeHours", "utcHour", "utcHours", "timeDay", "timeDays", "utcDay", "utcDays", "unixDay", "unixDays", "timeWeekday", "timeSunday", "timeMonday", "timeTuesday", "timeWednesday", "timeThursday", "timeFriday", "timeSaturday", "timeSundays", "timeMondays", "timeTuesdays", "timeWednesdays", "timeThursdays", "timeFridays", "timeSaturdays", "utcWeekday", "utcSunday", "utcMonday", "utcTuesday", "utcWednesday", "utcThursday", "utcFriday", "utcSaturday", "utcSundays", "utcMondays", "utcTuesdays", "utcWednesdays", "utcThursdays", "utcFridays", "utcSaturdays", "timeMonth", "timeMonths", "utcMonth", "utcMonths", "timeYear", "timeYears", "utcYear", "utcYears", "ticker", "year", "month", "week", "day", "hour", "minute", "tickIntervals", "tickInterval", "utcTicks", "utcTickInterval", "timeTicks", "timeTickInterval", "localDate", "utcDate", "newDate", "locale_dateTime", "locale_date", "locale_time", "locale_periods", "locale_weekdays", "locale_shortWeekdays", "locale_months", "locale_shortMonths", "periodRe", "formatRe", "periodLookup", "formatLookup", "weekdayRe", "weekdayLookup", "shortWeekdayRe", "shortWeekdayLookup", "monthRe", "monthLookup", "shortMonthRe", "shortMonthLookup", "formats", "formatShortWeekday", "formatWeekday", "formatShortMonth", "formatMonth", "formatDayOfMonth", "formatMicroseconds", "formatYearISO", "formatFullYearISO", "formatHour24", "formatHour12", "formatDayOfYear", "formatMilliseconds", "formatMonthNumber", "formatMinutes", "formatPeriod", "formatQuarter", "formatUnixTimestamp", "formatUnixTimestampSeconds", "formatSeconds", "formatWeekdayNumberMonday", "formatWeekNumberSunday", "formatWeekNumberISO", "formatWeekdayNumberSunday", "formatWeekNumberMonday", "formatYear", "formatFullYear", "formatZone", "formatLiteralPercent", "utcFormats", "formatUTCShortWeekday", "formatUTCWeekday", "formatUTCShortMonth", "formatUTCMonth", "formatUTCDayOfMonth", "formatUTCMicroseconds", "formatUTCYearISO", "formatUTCFullYearISO", "formatUTCHour24", "formatUTCHour12", "formatUTCDayOfYear", "formatUTCMilliseconds", "formatUTCMonthNumber", "formatUTCMinutes", "formatUTCPeriod", "formatUTCQuarter", "formatUTCSeconds", "formatUTCWeekdayNumberMonday", "formatUTCWeekNumberSunday", "formatUTCWeekNumberISO", "formatUTCWeekdayNumberSunday", "formatUTCWeekNumberMonday", "formatUTCYear", "formatUTCFullYear", "formatUTCZone", "parses", "parseShortWeekday", "parseWeekday", "parseShortMonth", "parseMonth", "parseLocaleDateTime", "parseDayOfMonth", "parseMicroseconds", "parseYear", "parseFullYear", "parseHour24", "parseDayOfYear", "parseMilliseconds", "parseMonthNumber", "parseMinutes", "parsePeriod", "parseQuarter", "parseUnixTimestamp", "parseUnixTimestampSeconds", "parseSeconds", "parseWeekdayNumberMonday", "parseWeekNumberSunday", "parseWeekNumberISO", "parseWeekdayNumberSunday", "parseWeekNumberMonday", "parseLocaleDate", "parseLocaleTime", "parseZone", "parseLiteralPercent", "pad", "pads", "newParse", "Z", "parseSpecifier", "numberRe", "percentRe", "requoteRe", "requote", "names", "name", "dISO", "dow", "UTCdISO", "timeFormat", "timeParse", "utcFormat", "utcParse", "isoSpecifier", "formatIsoNative", "formatIso", "formatIso$1", "parseIsoNative", "parseIso", "parseIso$1", "calendar", "invert", "formatMillisecond", "formatSecond", "formatMinute", "formatHour", "formatDay", "formatWeek", "time", "timeWeek", "timeSecond", "utcTime", "utcWeek", "utcSecond", "k10", "sequential", "sequentialLog", "sequentialSymlog", "sequentialPow", "sequentialSqrt", "sequentialQuantile", "k21", "r2", "diverging", "divergingLog", "divergingSymlog", "divergingPow", "divergingSqrt", "abs", "atan2", "cos", "sin", "halfPi", "acos", "asin", "with<PERSON><PERSON>", "shape", "arcInnerRadius", "arcOuterRadius", "arcStartAngle", "arcEndAngle", "arcPadAngle", "intersect", "x3", "y3", "x10", "y10", "x32", "y32", "cornerTangents", "rc", "ox", "oy", "x11", "y11", "x00", "y00", "cx0", "cy0", "cx1", "cy1", "dx0", "dy0", "dx1", "dy1", "arc", "innerRadius", "outerRadius", "cornerRadius", "padRadius", "startAngle", "endAngle", "padAngle", "context", "buffer", "a01", "a11", "a00", "a10", "da0", "da1", "ap", "rp", "rc0", "rc1", "oc", "ax", "ay", "bx", "by", "kc", "lc", "Linear", "curveLinear", "line", "curve", "pointX", "pointY", "defined0", "area", "x0z", "y0z", "arealine", "descending$1", "pie", "sortValues", "arcs", "pa", "curveRadialLinear", "curveRadial", "Radial", "lineRadial", "lineRadial$1", "areaRadial", "pointRadial", "Bump", "BumpRadial", "p2", "p3", "bumpX", "bumpY", "bumpRadial", "linkSource", "linkTarget", "link", "argv", "linkHorizontal", "linkVertical", "linkRadial", "sqrt3", "asterisk", "size", "circle", "tan30", "tan30_2", "diamond", "diamond2", "plus", "square2", "ka", "kr", "kx", "ky", "star", "triangle", "triangle2", "wye", "times", "symbolsFill", "symbolsStroke", "Symbol", "noop", "that", "<PERSON><PERSON>", "BasisClosed", "BasisOpen", "basisOpen", "Bundle", "beta", "bundle", "custom", "<PERSON>", "tension", "cardinal", "CardinalClosed", "cardinalClosed", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "CatmullRom", "alpha", "x23", "y23", "catmullRom", "CatmullRomClosed", "catmullRomClosed", "CatmullRomOpen", "catmullRomOpen", "LinearClosed", "linearClosed", "slope3", "h0", "h1", "slope2", "MonotoneX", "MonotoneY", "ReflectContext", "monotoneX", "monotoneY", "Natural", "px", "controlPoints", "py", "natural", "Step", "stepBefore", "stepAfter", "none$1", "series", "order", "none", "stackValue", "stackSeries", "stack", "orderNone", "offset", "offsetNone", "sz", "oz", "expand", "yp", "yn", "silhouette", "wiggle", "s2", "si", "sij0", "sij1", "s3", "sk", "skj0", "skj1", "appearance", "peaks", "peak", "vi", "vj", "sums", "insideOut", "top", "bottom", "tops", "bottoms"], "mappings": "6BAAA,SAASA,GAAEC,EAAE,CAAC,IAAI,EAAEC,EAAEC,EAAE,GAAG,GAAa,OAAOF,GAAjB,UAA8B,OAAOA,GAAjB,SAAmBE,GAAGF,UAAoB,OAAOA,GAAjB,SAAmB,GAAG,MAAM,QAAQA,CAAC,EAAE,CAAC,IAAIG,EAAEH,EAAE,OAAO,IAAI,EAAE,EAAE,EAAEG,EAAE,IAAIH,EAAE,CAAC,IAAIC,EAAEF,GAAEC,EAAE,CAAC,CAAC,KAAKE,IAAIA,GAAG,KAAKA,GAAGD,EAAE,KAAM,KAAIA,KAAKD,EAAEA,EAAEC,CAAC,IAAIC,IAAIA,GAAG,KAAKA,GAAGD,GAAG,OAAOC,CAAC,CAAQ,SAASE,IAAM,CAAC,QAAQJ,EAAE,EAAEC,EAAE,EAAEC,EAAE,GAAGC,EAAE,UAAU,OAAOF,EAAEE,EAAEF,KAAKD,EAAE,UAAUC,CAAC,KAAK,EAAEF,GAAEC,CAAC,KAAKE,IAAIA,GAAG,KAAKA,GAAG,GAAG,OAAOA,CAAC,CCAhW,SAASG,EAAUC,EAAGC,EAAG,CACtC,OAAOD,GAAK,MAAQC,GAAK,KAAO,IAAMD,EAAIC,EAAI,GAAKD,EAAIC,EAAI,EAAID,GAAKC,EAAI,EAAI,GAC9E,CCFe,SAASC,GAAWF,EAAGC,EAAG,CACvC,OAAOD,GAAK,MAAQC,GAAK,KAAO,IAC5BA,EAAID,EAAI,GACRC,EAAID,EAAI,EACRC,GAAKD,EAAI,EACT,GACN,CCHe,SAASG,GAASR,EAAG,CAClC,IAAIS,EAAUC,EAAUC,EAOpBX,EAAE,SAAW,GACfS,EAAWL,EACXM,EAAW,CAACE,EAAGC,IAAMT,EAAUJ,EAAEY,CAAC,EAAGC,CAAC,EACtCF,EAAQ,CAACC,EAAGC,IAAMb,EAAEY,CAAC,EAAIC,IAEzBJ,EAAWT,IAAMI,GAAaJ,IAAMO,GAAaP,EAAIc,GACrDJ,EAAWV,EACXW,EAAQX,GAGV,SAASe,EAAK,EAAGF,EAAGG,EAAK,EAAGC,EAAK,EAAE,OAAQ,CACzC,GAAID,EAAKC,EAAI,CACX,GAAIR,EAASI,EAAGA,CAAC,IAAM,EAAG,OAAOI,EACjC,EAAG,CACD,MAAMC,EAAOF,EAAKC,IAAQ,EACtBP,EAAS,EAAEQ,CAAG,EAAGL,CAAC,EAAI,EAAGG,EAAKE,EAAM,EACnCD,EAAKC,CAClB,OAAeF,EAAKC,EACf,CACD,OAAOD,CACR,CAED,SAASG,EAAM,EAAGN,EAAGG,EAAK,EAAGC,EAAK,EAAE,OAAQ,CAC1C,GAAID,EAAKC,EAAI,CACX,GAAIR,EAASI,EAAGA,CAAC,IAAM,EAAG,OAAOI,EACjC,EAAG,CACD,MAAMC,EAAOF,EAAKC,IAAQ,EACtBP,EAAS,EAAEQ,CAAG,EAAGL,CAAC,GAAK,EAAGG,EAAKE,EAAM,EACpCD,EAAKC,CAClB,OAAeF,EAAKC,EACf,CACD,OAAOD,CACR,CAED,SAASI,EAAO,EAAGP,EAAGG,EAAK,EAAGC,EAAK,EAAE,OAAQ,CAC3C,MAAMI,EAAIN,EAAK,EAAGF,EAAGG,EAAIC,EAAK,CAAC,EAC/B,OAAOI,EAAIL,GAAML,EAAM,EAAEU,EAAI,CAAC,EAAGR,CAAC,EAAI,CAACF,EAAM,EAAEU,CAAC,EAAGR,CAAC,EAAIQ,EAAI,EAAIA,CACjE,CAED,MAAO,CAAC,KAAAN,EAAM,OAAAK,EAAQ,MAAAD,CAAK,CAC7B,CAEA,SAASL,IAAO,CACd,MAAO,EACT,CCvDe,SAASQ,GAAOT,EAAG,CAChC,OAAOA,IAAM,KAAO,IAAM,CAACA,CAC7B,CAEO,SAAUU,GAAQC,EAAQC,EAAS,CACxC,GAAIA,IAAY,OACd,QAASC,KAASF,EACZE,GAAS,OAASA,EAAQ,CAACA,IAAUA,IACvC,MAAMA,OAGL,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,OAASE,EAAQ,CAACA,IAAUA,IAC3E,MAAMA,EAGX,CACH,CCfA,MAAME,GAAkBpB,GAASJ,CAAS,EAC7ByB,GAAcD,GAAgB,MAC9BE,GAAaF,GAAgB,KAC7BG,GAAevB,GAASc,EAAM,EAAE,OAC7CU,GAAeH,GCRR,SAASI,GAAKT,EAAQ1B,EAAG,CAC9B,GAAI,GAAGA,EAAI,CAACA,IAAM,GAAI,MAAM,IAAI,WAAW,WAAW,EACtD,IAAIoC,EAASV,EAAO,OACpB,GAAI,GAAGU,EAAS,KAAK,MAAMA,CAAM,IAAM,GAAI,MAAM,IAAI,WAAW,gBAAgB,EAChF,GAAI,CAACA,GAAU,CAACpC,EAAG,OAAO0B,EAC1B,MAAMS,EAAOE,GAAMrC,CAAC,EACdsC,EAAOZ,EAAO,QACpB,OAAAS,EAAKT,EAAQY,EAAM,EAAGF,EAAQ,CAAC,EAC/BD,EAAKG,EAAMZ,EAAQ,EAAGU,EAAQ,CAAC,EAC/BD,EAAKT,EAAQY,EAAM,EAAGF,EAAQ,CAAC,EACxBV,CACT,CAEY,MAACa,GAAQC,GAAMH,EAAK,EAEnBI,GAAYD,GAAME,EAAU,EAEzC,SAASF,GAAML,EAAM,CACnB,OAAO,SAASQ,EAAMC,EAAIC,EAAKD,EAAI,CACjC,GAAI,GAAGA,EAAK,CAACA,IAAO,GAAI,MAAM,IAAI,WAAW,YAAY,EACzD,GAAI,GAAGC,EAAK,CAACA,IAAO,GAAI,MAAM,IAAI,WAAW,YAAY,EACzD,GAAI,CAAC,KAAMnB,EAAQ,MAAAoB,EAAO,OAAAC,CAAM,EAAIJ,EACpC,GAAI,GAAGG,EAAQ,KAAK,MAAMA,CAAK,IAAM,GAAI,MAAM,IAAI,WAAW,eAAe,EAC7E,GAAI,GAAGC,EAAS,KAAK,MAAMA,IAAW,OAAYA,EAASrB,EAAO,OAASoB,CAAK,IAAM,GAAI,MAAM,IAAI,WAAW,gBAAgB,EAC/H,GAAI,CAACA,GAAS,CAACC,GAAW,CAACH,GAAM,CAACC,EAAK,OAAOF,EAC9C,MAAMK,EAAQJ,GAAMT,EAAKS,CAAE,EACrBK,EAAQJ,GAAMV,EAAKU,CAAE,EACrBP,EAAOZ,EAAO,QACpB,OAAIsB,GAASC,GACXC,GAAMF,EAAOV,EAAMZ,EAAQoB,EAAOC,CAAM,EACxCG,GAAMF,EAAOtB,EAAQY,EAAMQ,EAAOC,CAAM,EACxCG,GAAMF,EAAOV,EAAMZ,EAAQoB,EAAOC,CAAM,EACxCI,GAAMF,EAAOvB,EAAQY,EAAMQ,EAAOC,CAAM,EACxCI,GAAMF,EAAOX,EAAMZ,EAAQoB,EAAOC,CAAM,EACxCI,GAAMF,EAAOvB,EAAQY,EAAMQ,EAAOC,CAAM,GAC/BC,GACTE,GAAMF,EAAOtB,EAAQY,EAAMQ,EAAOC,CAAM,EACxCG,GAAMF,EAAOV,EAAMZ,EAAQoB,EAAOC,CAAM,EACxCG,GAAMF,EAAOtB,EAAQY,EAAMQ,EAAOC,CAAM,GAC/BE,IACTE,GAAMF,EAAOvB,EAAQY,EAAMQ,EAAOC,CAAM,EACxCI,GAAMF,EAAOX,EAAMZ,EAAQoB,EAAOC,CAAM,EACxCI,GAAMF,EAAOvB,EAAQY,EAAMQ,EAAOC,CAAM,GAEnCJ,CACX,CACA,CAEA,SAASO,GAAMf,EAAMiB,EAAGC,EAAGC,EAAGC,EAAG,CAC/B,QAASC,EAAI,EAAGrD,EAAImD,EAAIC,EAAGC,EAAIrD,GAC7BgC,EAAKiB,EAAGC,EAAGG,EAAGA,GAAKF,EAAG,CAAC,CAE3B,CAEA,SAASH,GAAMhB,EAAMiB,EAAGC,EAAGC,EAAGC,EAAG,CAC/B,QAASxC,EAAI,EAAGZ,EAAImD,EAAIC,EAAGxC,EAAIuC,EAAG,EAAEvC,EAClCoB,EAAKiB,EAAGC,EAAGtC,EAAGA,EAAIZ,EAAGmD,CAAC,CAE1B,CAEA,SAASZ,GAAWe,EAAQ,CAC1B,MAAMtB,EAAOE,GAAMoB,CAAM,EACzB,MAAO,CAACL,EAAGC,EAAGK,EAAOC,EAAMC,IAAS,CAClCF,IAAU,EAAGC,IAAS,EAAGC,IAAS,EAClCzB,EAAKiB,EAAGC,EAAGK,EAAQ,EAAGC,EAAO,EAAGC,CAAI,EACpCzB,EAAKiB,EAAGC,EAAGK,EAAQ,EAAGC,EAAO,EAAGC,CAAI,EACpCzB,EAAKiB,EAAGC,EAAGK,EAAQ,EAAGC,EAAO,EAAGC,CAAI,EACpCzB,EAAKiB,EAAGC,EAAGK,EAAQ,EAAGC,EAAO,EAAGC,CAAI,CACxC,CACA,CAQA,SAASvB,GAAMoB,EAAQ,CACrB,MAAMI,EAAU,KAAK,MAAMJ,CAAM,EACjC,GAAII,IAAYJ,EAAQ,OAAOK,GAAML,CAAM,EAC3C,MAAMM,EAAIN,EAASI,EACbP,EAAI,EAAIG,EAAS,EACvB,MAAO,CAACL,EAAGC,EAAGK,EAAOC,EAAMC,IAAS,CAClC,GAAI,GAAGD,GAAQC,IAASF,GAAQ,OAChC,IAAIM,EAAMH,EAAUR,EAAEK,CAAK,EAC3B,MAAMO,EAAKL,EAAOC,EACZK,EAAKD,EAAKL,EAChB,QAASrC,EAAImC,EAAOS,EAAIT,EAAQO,EAAI1C,EAAI4C,EAAG5C,GAAKqC,EAC9CI,GAAOX,EAAE,KAAK,IAAIM,EAAMpC,CAAC,CAAC,EAE5B,QAASA,EAAImC,EAAOS,EAAIR,EAAMpC,GAAK4C,EAAG5C,GAAKqC,EACzCI,GAAOX,EAAE,KAAK,IAAIM,EAAMpC,EAAI0C,CAAE,CAAC,EAC/Bb,EAAE7B,CAAC,GAAKyC,EAAMD,GAAKV,EAAE,KAAK,IAAIK,EAAOnC,EAAI2C,CAAE,CAAC,EAAIb,EAAE,KAAK,IAAIM,EAAMpC,EAAI2C,CAAE,CAAC,IAAMZ,EAC9EU,GAAOX,EAAE,KAAK,IAAIK,EAAOnC,EAAI0C,CAAE,CAAC,CAEtC,CACA,CAGA,SAASH,GAAML,EAAQ,CACrB,MAAMH,EAAI,EAAIG,EAAS,EACvB,MAAO,CAACL,EAAGC,EAAGK,EAAOC,EAAMC,IAAS,CAClC,GAAI,GAAGD,GAAQC,IAASF,GAAQ,OAChC,IAAIM,EAAMP,EAASJ,EAAEK,CAAK,EAC1B,MAAMU,EAAIR,EAAOH,EACjB,QAASlC,EAAImC,EAAOS,EAAIT,EAAQU,EAAG7C,EAAI4C,EAAG5C,GAAKqC,EAC7CI,GAAOX,EAAE,KAAK,IAAIM,EAAMpC,CAAC,CAAC,EAE5B,QAASA,EAAImC,EAAOS,EAAIR,EAAMpC,GAAK4C,EAAG5C,GAAKqC,EACzCI,GAAOX,EAAE,KAAK,IAAIM,EAAMpC,EAAI6C,CAAC,CAAC,EAC9BhB,EAAE7B,CAAC,EAAIyC,EAAMV,EACbU,GAAOX,EAAE,KAAK,IAAIK,EAAOnC,EAAI6C,CAAC,CAAC,CAErC,CACA,CClHe,SAASC,GAAM3C,EAAQC,EAAS,CAC7C,IAAI0C,EAAQ,EACZ,GAAI1C,IAAY,OACd,QAASC,KAASF,EACZE,GAAS,OAASA,EAAQ,CAACA,IAAUA,GACvC,EAAEyC,MAGD,CACL,IAAIxC,EAAQ,GACZ,QAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,OAASE,EAAQ,CAACA,IAAUA,GAC3E,EAAEyC,CAGP,CACD,OAAOA,CACT,CCjBA,SAASjC,GAAOkC,EAAO,CACrB,OAAOA,EAAM,OAAS,CACxB,CAEA,SAASC,GAAMnC,EAAQ,CACrB,MAAO,EAAEA,EAAS,EACpB,CAEA,SAASoC,GAAS9C,EAAQ,CACxB,OAAO,OAAOA,GAAW,UAAY,WAAYA,EAASA,EAAS,MAAM,KAAKA,CAAM,CACtF,CAEA,SAAS+C,GAAQC,EAAQ,CACvB,OAAOhD,GAAUgD,EAAO,GAAGhD,CAAM,CACnC,CAEe,SAASiD,MAASjD,EAAQ,CACvC,MAAMgD,EAAS,OAAOhD,EAAOA,EAAO,OAAS,CAAC,GAAM,YAAc+C,GAAQ/C,EAAO,IAAK,CAAA,EACtFA,EAASA,EAAO,IAAI8C,EAAQ,EAC5B,MAAMI,EAAUlD,EAAO,IAAIU,EAAM,EAC3B+B,EAAIzC,EAAO,OAAS,EACpBG,EAAQ,IAAI,MAAMsC,EAAI,CAAC,EAAE,KAAK,CAAC,EAC/BU,EAAU,CAAA,EAChB,GAAIV,EAAI,GAAKS,EAAQ,KAAKL,EAAK,EAAG,OAAOM,EACzC,OAAa,CACXA,EAAQ,KAAKhD,EAAM,IAAI,CAACsC,EAAG5C,IAAMG,EAAOH,CAAC,EAAE4C,CAAC,CAAC,CAAC,EAC9C,IAAI5C,EAAI4C,EACR,KAAO,EAAEtC,EAAMN,CAAC,IAAMqD,EAAQrD,CAAC,GAAG,CAChC,GAAIA,IAAM,EAAG,OAAOmD,EAASG,EAAQ,IAAIH,CAAM,EAAIG,EACnDhD,EAAMN,GAAG,EAAI,CACd,CACF,CACH,CChCe,SAASuD,GAAOpD,EAAQC,EAAS,CAC9C,IAAIqC,EAAM,EAAGnC,EAAQ,EACrB,OAAO,aAAa,KAAKH,EAAQC,IAAY,OACzCoD,GAAMf,GAAO,CAACe,GAAK,EACnBA,GAAMf,GAAO,CAACrC,EAAQoD,EAAGlD,IAASH,CAAM,GAAK,CAAE,CACrD,CCLe,SAASsD,GAAStD,EAAQC,EAAS,CAChD,IAAI0C,EAAQ,EACRxD,EACAoE,EAAO,EACPjB,EAAM,EACV,GAAIrC,IAAY,OACd,QAASC,KAASF,EACZE,GAAS,OAASA,EAAQ,CAACA,IAAUA,IACvCf,EAAQe,EAAQqD,EAChBA,GAAQpE,EAAQ,EAAEwD,EAClBL,GAAOnD,GAASe,EAAQqD,QAGvB,CACL,IAAIpD,EAAQ,GACZ,QAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,OAASE,EAAQ,CAACA,IAAUA,IAC3Ef,EAAQe,EAAQqD,EAChBA,GAAQpE,EAAQ,EAAEwD,EAClBL,GAAOnD,GAASe,EAAQqD,GAG7B,CACD,GAAIZ,EAAQ,EAAG,OAAOL,GAAOK,EAAQ,EACvC,CCtBe,SAASa,GAAUxD,EAAQC,EAAS,CACjD,MAAMoD,EAAIC,GAAStD,EAAQC,CAAO,EAClC,OAAOoD,GAAI,KAAK,KAAKA,CAAC,CACxB,CCLe,SAASI,GAAOzD,EAAQC,EAAS,CAC9C,IAAIyD,EACAC,EACJ,GAAI1D,IAAY,OACd,UAAWC,KAASF,EACdE,GAAS,OACPwD,IAAQ,OACNxD,GAASA,IAAOwD,EAAMC,EAAMzD,IAE5BwD,EAAMxD,IAAOwD,EAAMxD,GACnByD,EAAMzD,IAAOyD,EAAMzD,SAIxB,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,OAC3C0D,IAAQ,OACNxD,GAASA,IAAOwD,EAAMC,EAAMzD,IAE5BwD,EAAMxD,IAAOwD,EAAMxD,GACnByD,EAAMzD,IAAOyD,EAAMzD,IAI9B,CACD,MAAO,CAACwD,EAAKC,CAAG,CAClB,CC3BO,MAAMC,EAAM,CACjB,aAAc,CACZ,KAAK,UAAY,IAAI,aAAa,EAAE,EACpC,KAAK,GAAK,CACX,CACD,IAAIvE,EAAG,CACL,MAAMwE,EAAI,KAAK,UACf,IAAIhE,EAAI,EACR,QAAS4C,EAAI,EAAGA,EAAI,KAAK,IAAMA,EAAI,GAAIA,IAAK,CAC1C,MAAMX,EAAI+B,EAAEpB,CAAC,EACXhD,EAAKJ,EAAIyC,EACTtC,EAAK,KAAK,IAAIH,CAAC,EAAI,KAAK,IAAIyC,CAAC,EAAIzC,GAAKI,EAAKqC,GAAKA,GAAKrC,EAAKJ,GACxDG,IAAIqE,EAAEhE,GAAG,EAAIL,GACjBH,EAAII,CACL,CACD,OAAAoE,EAAEhE,CAAC,EAAIR,EACP,KAAK,GAAKQ,EAAI,EACP,IACR,CACD,SAAU,CACR,MAAMgE,EAAI,KAAK,UACf,IAAIpF,EAAI,KAAK,GAAIY,EAAGyC,EAAGtC,EAAIC,EAAK,EAChC,GAAIhB,EAAI,EAAG,CAET,IADAgB,EAAKoE,EAAE,EAAEpF,CAAC,EACHA,EAAI,IACTY,EAAII,EACJqC,EAAI+B,EAAE,EAAEpF,CAAC,EACTgB,EAAKJ,EAAIyC,EACTtC,EAAKsC,GAAKrC,EAAKJ,GACX,CAAAG,IAAJ,CAEEf,EAAI,IAAOe,EAAK,GAAKqE,EAAEpF,EAAI,CAAC,EAAI,GAAOe,EAAK,GAAKqE,EAAEpF,EAAI,CAAC,EAAI,KAC9DqD,EAAItC,EAAK,EACTH,EAAII,EAAKqC,EACLA,GAAKzC,EAAII,IAAIA,EAAKJ,GAEzB,CACD,OAAOI,CACR,CACH,CAEO,SAASqE,GAAK9D,EAAQC,EAAS,CACpC,MAAM8D,EAAQ,IAAIH,GAClB,GAAI3D,IAAY,OACd,QAASC,KAASF,GACZE,EAAQ,CAACA,IACX6D,EAAM,IAAI7D,CAAK,MAGd,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACZE,EAAQ,CAACD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IACzC+D,EAAM,IAAI7D,CAAK,CAGpB,CACD,MAAO,CAAC6D,CACV,CAEO,SAASC,GAAQhE,EAAQC,EAAS,CACvC,MAAM8D,EAAQ,IAAIH,GAClB,IAAIzD,EAAQ,GACZ,OAAO,aAAa,KAAKH,EAAQC,IAAY,OACvCoD,GAAKU,EAAM,IAAI,CAACV,GAAK,CAAC,EACtBA,GAAKU,EAAM,IAAI,CAAC9D,EAAQoD,EAAG,EAAElD,EAAOH,CAAM,GAAK,CAAC,CACxD,CACA,CCpEO,MAAMiE,WAAkB,GAAI,CACjC,YAAYC,EAASC,EAAMC,GAAO,CAGhC,GAFA,QACA,OAAO,iBAAiB,KAAM,CAAC,QAAS,CAAC,MAAO,IAAI,GAAK,EAAG,KAAM,CAAC,MAAOD,CAAG,CAAC,CAAC,EAC3ED,GAAW,KAAM,SAAW,CAACC,EAAKjE,CAAK,IAAKgE,EAAS,KAAK,IAAIC,EAAKjE,CAAK,CAC7E,CACD,IAAIiE,EAAK,CACP,OAAO,MAAM,IAAIE,GAAW,KAAMF,CAAG,CAAC,CACvC,CACD,IAAIA,EAAK,CACP,OAAO,MAAM,IAAIE,GAAW,KAAMF,CAAG,CAAC,CACvC,CACD,IAAIA,EAAKjE,EAAO,CACd,OAAO,MAAM,IAAIoE,GAAW,KAAMH,CAAG,EAAGjE,CAAK,CAC9C,CACD,OAAOiE,EAAK,CACV,OAAO,MAAM,OAAOI,GAAc,KAAMJ,CAAG,CAAC,CAC7C,CACH,CAEO,MAAMK,WAAkB,GAAI,CACjC,YAAYxE,EAAQmE,EAAMC,GAAO,CAG/B,GAFA,QACA,OAAO,iBAAiB,KAAM,CAAC,QAAS,CAAC,MAAO,IAAI,GAAK,EAAG,KAAM,CAAC,MAAOD,CAAG,CAAC,CAAC,EAC3EnE,GAAU,KAAM,UAAWE,KAASF,EAAQ,KAAK,IAAIE,CAAK,CAC/D,CACD,IAAIA,EAAO,CACT,OAAO,MAAM,IAAImE,GAAW,KAAMnE,CAAK,CAAC,CACzC,CACD,IAAIA,EAAO,CACT,OAAO,MAAM,IAAIoE,GAAW,KAAMpE,CAAK,CAAC,CACzC,CACD,OAAOA,EAAO,CACZ,OAAO,MAAM,OAAOqE,GAAc,KAAMrE,CAAK,CAAC,CAC/C,CACH,CAEA,SAASmE,GAAW,CAAC,QAAAI,EAAS,KAAAC,CAAI,EAAGxE,EAAO,CAC1C,MAAMiE,EAAMO,EAAKxE,CAAK,EACtB,OAAOuE,EAAQ,IAAIN,CAAG,EAAIM,EAAQ,IAAIN,CAAG,EAAIjE,CAC/C,CAEA,SAASoE,GAAW,CAAC,QAAAG,EAAS,KAAAC,CAAI,EAAGxE,EAAO,CAC1C,MAAMiE,EAAMO,EAAKxE,CAAK,EACtB,OAAIuE,EAAQ,IAAIN,CAAG,EAAUM,EAAQ,IAAIN,CAAG,GAC5CM,EAAQ,IAAIN,EAAKjE,CAAK,EACfA,EACT,CAEA,SAASqE,GAAc,CAAC,QAAAE,EAAS,KAAAC,CAAI,EAAGxE,EAAO,CAC7C,MAAMiE,EAAMO,EAAKxE,CAAK,EACtB,OAAIuE,EAAQ,IAAIN,CAAG,IACjBjE,EAAQuE,EAAQ,IAAIN,CAAG,EACvBM,EAAQ,OAAON,CAAG,GAEbjE,CACT,CAEA,SAASkE,GAAMlE,EAAO,CACpB,OAAOA,IAAU,MAAQ,OAAOA,GAAU,SAAWA,EAAM,QAAS,EAAGA,CACzE,CC5De,SAASyE,GAAStF,EAAG,CAClC,OAAOA,CACT,CCCe,SAASuF,GAAM5E,KAAW6E,EAAM,CAC7C,OAAOC,GAAK9E,EAAQ2E,GAAUA,GAAUE,CAAI,CAC9C,CAEO,SAASE,GAAO/E,KAAW6E,EAAM,CACtC,OAAOC,GAAK9E,EAAQ,MAAM,KAAM2E,GAAUE,CAAI,CAChD,CAEA,SAASG,GAAQD,EAAQF,EAAM,CAC7B,QAAShF,EAAI,EAAGpB,EAAIoG,EAAK,OAAQhF,EAAIpB,EAAG,EAAEoB,EACxCkF,EAASA,EAAO,QAAQE,GAAKA,EAAE,IAAK,EAAC,IAAI,CAAC,CAACd,EAAKjE,CAAK,IAAM,CAAC,GAAG+E,EAAGd,EAAKjE,CAAK,CAAC,CAAC,EAEhF,OAAO6E,CACT,CAEO,SAASG,GAAUlF,KAAW6E,EAAM,CACzC,OAAOG,GAAQD,GAAO/E,EAAQ,GAAG6E,CAAI,EAAGA,CAAI,CAC9C,CAEO,SAASM,GAAWnF,EAAQgD,KAAW6B,EAAM,CAClD,OAAOG,GAAQI,GAAQpF,EAAQgD,EAAQ,GAAG6B,CAAI,EAAGA,CAAI,CACvD,CAEO,SAASQ,GAAOrF,EAAQgD,KAAW6B,EAAM,CAC9C,OAAOC,GAAK9E,EAAQ2E,GAAU3B,EAAQ6B,CAAI,CAC5C,CAEO,SAASO,GAAQpF,EAAQgD,KAAW6B,EAAM,CAC/C,OAAOC,GAAK9E,EAAQ,MAAM,KAAMgD,EAAQ6B,CAAI,CAC9C,CAEO,SAAS1E,GAAMH,KAAW6E,EAAM,CACrC,OAAOC,GAAK9E,EAAQ2E,GAAUW,GAAQT,CAAI,CAC5C,CAEO,SAASU,GAAQvF,KAAW6E,EAAM,CACvC,OAAOC,GAAK9E,EAAQ,MAAM,KAAMsF,GAAQT,CAAI,CAC9C,CAEA,SAASS,GAAOtF,EAAQ,CACtB,GAAIA,EAAO,SAAW,EAAG,MAAM,IAAI,MAAM,eAAe,EACxD,OAAOA,EAAO,CAAC,CACjB,CAEA,SAAS8E,GAAK9E,EAAQwF,EAAKxC,EAAQ6B,EAAM,CACvC,OAAQ,SAASY,EAAQzF,EAAQH,EAAG,CAClC,GAAIA,GAAKgF,EAAK,OAAQ,OAAO7B,EAAOhD,CAAM,EAC1C,MAAM+E,EAAS,IAAId,GACbG,EAAQS,EAAKhF,GAAG,EACtB,IAAIM,EAAQ,GACZ,UAAWD,KAASF,EAAQ,CAC1B,MAAMmE,EAAMC,EAAMlE,EAAO,EAAEC,EAAOH,CAAM,EAClC4E,EAAQG,EAAO,IAAIZ,CAAG,EACxBS,EAAOA,EAAM,KAAK1E,CAAK,EACtB6E,EAAO,IAAIZ,EAAK,CAACjE,CAAK,CAAC,CAC7B,CACD,SAAW,CAACiE,EAAKnE,CAAM,IAAK+E,EAC1BA,EAAO,IAAIZ,EAAKsB,EAAQzF,EAAQH,CAAC,CAAC,EAEpC,OAAO2F,EAAIT,CAAM,CACrB,EAAK/E,EAAQ,CAAC,CACd,CChEe,SAAS0F,GAAQC,EAAQd,EAAM,CAC5C,OAAO,MAAM,KAAKA,EAAMV,GAAOwB,EAAOxB,CAAG,CAAC,CAC5C,CCCe,SAASyB,GAAK5F,KAAW6F,EAAG,CACzC,GAAI,OAAO7F,EAAO,OAAO,QAAQ,GAAM,WAAY,MAAM,IAAI,UAAU,wBAAwB,EAC/FA,EAAS,MAAM,KAAKA,CAAM,EAC1B,GAAI,CAACxB,CAAC,EAAIqH,EACV,GAAKrH,GAAKA,EAAE,SAAW,GAAMqH,EAAE,OAAS,EAAG,CACzC,MAAM1F,EAAQ,YAAY,KAAKH,EAAQ,CAACZ,EAAGS,IAAMA,CAAC,EAClD,OAAIgG,EAAE,OAAS,GACbA,EAAIA,EAAE,IAAIrH,GAAKwB,EAAO,IAAIxB,CAAC,CAAC,EAC5B2B,EAAM,KAAK,CAAC,EAAGsC,IAAM,CACnB,UAAWjE,KAAKqH,EAAG,CACjB,MAAMC,EAAIC,GAAiBvH,EAAE,CAAC,EAAGA,EAAEiE,CAAC,CAAC,EACrC,GAAIqD,EAAG,OAAOA,CACf,CACT,CAAO,IAEDtH,EAAIwB,EAAO,IAAIxB,CAAC,EAChB2B,EAAM,KAAK,CAAC,EAAGsC,IAAMsD,GAAiBvH,EAAE,CAAC,EAAGA,EAAEiE,CAAC,CAAC,CAAC,GAE5CiD,GAAQ1F,EAAQG,CAAK,CAC7B,CACD,OAAOH,EAAO,KAAKgG,GAAexH,CAAC,CAAC,CACtC,CAEO,SAASwH,GAAeC,EAAUrH,EAAW,CAClD,GAAIqH,IAAYrH,EAAW,OAAOmH,GAClC,GAAI,OAAOE,GAAY,WAAY,MAAM,IAAI,UAAU,2BAA2B,EAClF,MAAO,CAACpH,EAAGC,IAAM,CACf,MAAMO,EAAI4G,EAAQpH,EAAGC,CAAC,EACtB,OAAIO,GAAKA,IAAM,EAAUA,GACjB4G,EAAQnH,EAAGA,CAAC,IAAM,IAAMmH,EAAQpH,EAAGA,CAAC,IAAM,EACtD,CACA,CAEO,SAASkH,GAAiBlH,EAAGC,EAAG,CACrC,OAAQD,GAAK,MAAQ,EAAEA,GAAKA,KAAOC,GAAK,MAAQ,EAAEA,GAAKA,MAAQD,EAAIC,EAAI,GAAKD,EAAIC,EAAI,EAAI,EAC1F,CClCe,SAASoH,GAAUlG,EAAQgD,EAAQmB,EAAK,CACrD,OAAQnB,EAAO,SAAW,EACtB4C,GAAKP,GAAOrF,EAAQgD,EAAQmB,CAAG,EAAI,CAAC,CAACgC,EAAIC,CAAE,EAAG,CAACC,EAAIC,CAAE,IAAM1H,EAAUwH,EAAIE,CAAE,GAAK1H,EAAUuH,EAAIE,CAAE,CAAG,EACnGT,GAAKhB,GAAM5E,EAAQmE,CAAG,EAAI,CAAC,CAACgC,EAAIC,CAAE,EAAG,CAACC,EAAIC,CAAE,IAAMtD,EAAOoD,EAAIE,CAAE,GAAK1H,EAAUuH,EAAIE,CAAE,CAAG,GACxF,IAAI,CAAC,CAAClC,CAAG,IAAMA,CAAG,CACvB,CCTA,IAAIvB,GAAQ,MAAM,UAEP2D,GAAQ3D,GAAM,MCFV,SAAS4D,GAASnH,EAAG,CAClC,MAAO,IAAMA,CACf,CCFA,MAAMoH,GAAM,KAAK,KAAK,EAAE,EACpBC,GAAK,KAAK,KAAK,EAAE,EACjBC,GAAK,KAAK,KAAK,CAAC,EAEpB,SAASC,GAAS5E,EAAOC,EAAMU,EAAO,CACpC,MAAMT,GAAQD,EAAOD,GAAS,KAAK,IAAI,EAAGW,CAAK,EAC3CkE,EAAQ,KAAK,MAAM,KAAK,MAAM3E,CAAI,CAAC,EACnC4E,EAAQ5E,EAAO,KAAK,IAAI,GAAI2E,CAAK,EACjCE,EAASD,GAASL,GAAM,GAAKK,GAASJ,GAAK,EAAII,GAASH,GAAK,EAAI,EACrE,IAAIK,EAAIC,EAAIC,EAeZ,OAdIL,EAAQ,GACVK,EAAM,KAAK,IAAI,GAAI,CAACL,CAAK,EAAIE,EAC7BC,EAAK,KAAK,MAAMhF,EAAQkF,CAAG,EAC3BD,EAAK,KAAK,MAAMhF,EAAOiF,CAAG,EACtBF,EAAKE,EAAMlF,GAAO,EAAEgF,EACpBC,EAAKC,EAAMjF,GAAM,EAAEgF,EACvBC,EAAM,CAACA,IAEPA,EAAM,KAAK,IAAI,GAAIL,CAAK,EAAIE,EAC5BC,EAAK,KAAK,MAAMhF,EAAQkF,CAAG,EAC3BD,EAAK,KAAK,MAAMhF,EAAOiF,CAAG,EACtBF,EAAKE,EAAMlF,GAAO,EAAEgF,EACpBC,EAAKC,EAAMjF,GAAM,EAAEgF,GAErBA,EAAKD,GAAM,IAAOrE,GAASA,EAAQ,EAAUiE,GAAS5E,EAAOC,EAAMU,EAAQ,CAAC,EACzE,CAACqE,EAAIC,EAAIC,CAAG,CACrB,CAEe,SAASC,GAAMnF,EAAOC,EAAMU,EAAO,CAEhD,GADAV,EAAO,CAACA,EAAMD,EAAQ,CAACA,EAAOW,EAAQ,CAACA,EACnC,EAAEA,EAAQ,GAAI,MAAO,CAAA,EACzB,GAAIX,IAAUC,EAAM,MAAO,CAACD,CAAK,EACjC,MAAMoF,EAAUnF,EAAOD,EAAO,CAACgF,EAAIC,EAAIC,CAAG,EAAIE,EAAUR,GAAS3E,EAAMD,EAAOW,CAAK,EAAIiE,GAAS5E,EAAOC,EAAMU,CAAK,EAClH,GAAI,EAAEsE,GAAMD,GAAK,MAAO,CAAA,EACxB,MAAMvI,EAAIwI,EAAKD,EAAK,EAAGG,EAAQ,IAAI,MAAM1I,CAAC,EAC1C,GAAI2I,EACF,GAAIF,EAAM,EAAG,QAASrH,EAAI,EAAGA,EAAIpB,EAAG,EAAEoB,EAAGsH,EAAMtH,CAAC,GAAKoH,EAAKpH,GAAK,CAACqH,MAC3D,SAASrH,EAAI,EAAGA,EAAIpB,EAAG,EAAEoB,EAAGsH,EAAMtH,CAAC,GAAKoH,EAAKpH,GAAKqH,UAEnDA,EAAM,EAAG,QAASrH,EAAI,EAAGA,EAAIpB,EAAG,EAAEoB,EAAGsH,EAAMtH,CAAC,GAAKmH,EAAKnH,GAAK,CAACqH,MAC3D,SAASrH,EAAI,EAAGA,EAAIpB,EAAG,EAAEoB,EAAGsH,EAAMtH,CAAC,GAAKmH,EAAKnH,GAAKqH,EAEzD,OAAOC,CACT,CAEO,SAASE,GAAcrF,EAAOC,EAAMU,EAAO,CAChD,OAAAV,EAAO,CAACA,EAAMD,EAAQ,CAACA,EAAOW,EAAQ,CAACA,EAChCiE,GAAS5E,EAAOC,EAAMU,CAAK,EAAE,CAAC,CACvC,CAEO,SAAS2E,GAAStF,EAAOC,EAAMU,EAAO,CAC3CV,EAAO,CAACA,EAAMD,EAAQ,CAACA,EAAOW,EAAQ,CAACA,EACvC,MAAMyE,EAAUnF,EAAOD,EAAOkF,EAAME,EAAUC,GAAcpF,EAAMD,EAAOW,CAAK,EAAI0E,GAAcrF,EAAOC,EAAMU,CAAK,EAClH,OAAQyE,EAAU,GAAK,IAAMF,EAAM,EAAI,EAAI,CAACA,EAAMA,EACpD,CCpDe,SAASK,GAAKvF,EAAOC,EAAMU,EAAO,CAC/C,IAAI6E,EACJ,OAAa,CACX,MAAMtF,EAAOmF,GAAcrF,EAAOC,EAAMU,CAAK,EAC7C,GAAIT,IAASsF,GAAWtF,IAAS,GAAK,CAAC,SAASA,CAAI,EAClD,MAAO,CAACF,EAAOC,CAAI,EACVC,EAAO,GAChBF,EAAQ,KAAK,MAAMA,EAAQE,CAAI,EAAIA,EACnCD,EAAO,KAAK,KAAKA,EAAOC,CAAI,EAAIA,GACvBA,EAAO,IAChBF,EAAQ,KAAK,KAAKA,EAAQE,CAAI,EAAIA,EAClCD,EAAO,KAAK,MAAMA,EAAOC,CAAI,EAAIA,GAEnCsF,EAAUtF,CACX,CACH,CCfe,SAASuF,GAAiBzH,EAAQ,CAC/C,OAAO,KAAK,IAAI,EAAG,KAAK,KAAK,KAAK,IAAI2C,GAAM3C,CAAM,CAAC,EAAI,KAAK,GAAG,EAAI,CAAC,CACtE,CCKe,SAAS0H,IAAM,CAC5B,IAAIxH,EAAQyE,GACRgD,EAASlE,GACTmE,EAAYC,GAEhB,SAASC,EAAU7G,EAAM,CAClB,MAAM,QAAQA,CAAI,IAAGA,EAAO,MAAM,KAAKA,CAAI,GAEhD,IAAIpB,EACApB,EAAIwC,EAAK,OACT5B,EACA6C,EACAlC,EAAS,IAAI,MAAMvB,CAAC,EAExB,IAAKoB,EAAI,EAAGA,EAAIpB,EAAG,EAAEoB,EACnBG,EAAOH,CAAC,EAAIK,EAAMe,EAAKpB,CAAC,EAAGA,EAAGoB,CAAI,EAGpC,IAAI8G,EAAKJ,EAAO3H,CAAM,EAClBgI,EAAKD,EAAG,CAAC,EACTE,EAAKF,EAAG,CAAC,EACTG,EAAKN,EAAU5H,EAAQgI,EAAIC,CAAE,EAIjC,GAAI,CAAC,MAAM,QAAQC,CAAE,EAAG,CACtB,MAAMvE,EAAMsE,EAAIE,EAAK,CAACD,EAgBtB,GAfIP,IAAWlE,KAAQ,CAACuE,EAAIC,CAAE,EAAIV,GAAKS,EAAIC,EAAIE,CAAE,GACjDD,EAAKf,GAAMa,EAAIC,EAAIE,CAAE,EAKjBD,EAAG,CAAC,GAAKF,IAAI9F,EAAOmF,GAAcW,EAAIC,EAAIE,CAAE,GAS5CD,EAAGA,EAAG,OAAS,CAAC,GAAKD,EACvB,GAAItE,GAAOsE,GAAMN,IAAWlE,GAAQ,CAClC,MAAMvB,EAAOmF,GAAcW,EAAIC,EAAIE,CAAE,EACjC,SAASjG,CAAI,IACXA,EAAO,EACT+F,GAAM,KAAK,MAAMA,EAAK/F,CAAI,EAAI,GAAKA,EAC1BA,EAAO,IAChB+F,GAAM,KAAK,KAAKA,EAAK,CAAC/F,CAAI,EAAI,GAAK,CAACA,GAGlD,MACUgG,EAAG,IAAG,CAGX,CAKD,QADI,EAAIA,EAAG,OAAQrJ,EAAI,EAAGC,EAAI,EACvBoJ,EAAGrJ,CAAC,GAAKmJ,GAAI,EAAEnJ,EACtB,KAAOqJ,EAAGpJ,EAAI,CAAC,EAAImJ,GAAI,EAAEnJ,GACrBD,GAAKC,EAAI,KAAGoJ,EAAKA,EAAG,MAAMrJ,EAAGC,CAAC,EAAG,EAAIA,EAAID,GAE7C,IAAIuJ,EAAO,IAAI,MAAM,EAAI,CAAC,EACtBV,EAGJ,IAAK7H,EAAI,EAAGA,GAAK,EAAG,EAAEA,EACpB6H,EAAMU,EAAKvI,CAAC,EAAI,GAChB6H,EAAI,GAAK7H,EAAI,EAAIqI,EAAGrI,EAAI,CAAC,EAAImI,EAC7BN,EAAI,GAAK7H,EAAI,EAAIqI,EAAGrI,CAAC,EAAIoI,EAI3B,GAAI,SAAS/F,CAAI,GACf,GAAIA,EAAO,EACT,IAAKrC,EAAI,EAAGA,EAAIpB,EAAG,EAAEoB,GACdR,EAAIW,EAAOH,CAAC,IAAM,MAAQmI,GAAM3I,GAAKA,GAAK4I,GAC7CG,EAAK,KAAK,IAAI,EAAG,KAAK,OAAO/I,EAAI2I,GAAM9F,CAAI,CAAC,CAAC,EAAE,KAAKjB,EAAKpB,CAAC,CAAC,UAGtDqC,EAAO,GAChB,IAAKrC,EAAI,EAAGA,EAAIpB,EAAG,EAAEoB,EACnB,IAAKR,EAAIW,EAAOH,CAAC,IAAM,MAAQmI,GAAM3I,GAAKA,GAAK4I,EAAI,CACjD,MAAMxF,EAAI,KAAK,OAAOuF,EAAK3I,GAAK6C,CAAI,EACpCkG,EAAK,KAAK,IAAI,EAAG3F,GAAKyF,EAAGzF,CAAC,GAAKpD,EAAE,CAAC,EAAE,KAAK4B,EAAKpB,CAAC,CAAC,CACjD,OAIL,KAAKA,EAAI,EAAGA,EAAIpB,EAAG,EAAEoB,GACdR,EAAIW,EAAOH,CAAC,IAAM,MAAQmI,GAAM3I,GAAKA,GAAK4I,GAC7CG,EAAK5H,GAAO0H,EAAI7I,EAAG,EAAG,CAAC,CAAC,EAAE,KAAK4B,EAAKpB,CAAC,CAAC,EAK5C,OAAOuI,CACR,CAED,OAAAN,EAAU,MAAQ,SAASO,EAAG,CAC5B,OAAO,UAAU,QAAUnI,EAAQ,OAAOmI,GAAM,WAAaA,EAAI7B,GAAS6B,CAAC,EAAGP,GAAa5H,CAC/F,EAEE4H,EAAU,OAAS,SAASO,EAAG,CAC7B,OAAO,UAAU,QAAUV,EAAS,OAAOU,GAAM,WAAaA,EAAI7B,GAAS,CAAC6B,EAAE,CAAC,EAAGA,EAAE,CAAC,CAAC,CAAC,EAAGP,GAAaH,CAC3G,EAEEG,EAAU,WAAa,SAASO,EAAG,CACjC,OAAO,UAAU,QAAUT,EAAY,OAAOS,GAAM,WAAaA,EAAI7B,GAAS,MAAM,QAAQ6B,CAAC,EAAI9B,GAAM,KAAK8B,CAAC,EAAIA,CAAC,EAAGP,GAAaF,CACtI,EAESE,CACT,CC5He,SAASnE,GAAI3D,EAAQC,EAAS,CAC3C,IAAI0D,EACJ,GAAI1D,IAAY,OACd,UAAWC,KAASF,EACdE,GAAS,OACLyD,EAAMzD,GAAUyD,IAAQ,QAAazD,GAASA,KACpDyD,EAAMzD,OAGL,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,OACzC2D,EAAMzD,GAAUyD,IAAQ,QAAazD,GAASA,KACpDyD,EAAMzD,EAGX,CACD,OAAOyD,CACT,CCnBe,SAAS2E,GAAStI,EAAQC,EAAS,CAChD,IAAI0D,EACA2E,EAAW,GACXnI,EAAQ,GACZ,GAAIF,IAAY,OACd,UAAWC,KAASF,EAClB,EAAEG,EACED,GAAS,OACLyD,EAAMzD,GAAUyD,IAAQ,QAAazD,GAASA,KACpDyD,EAAMzD,EAAOoI,EAAWnI,OAI5B,SAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,OACzC2D,EAAMzD,GAAUyD,IAAQ,QAAazD,GAASA,KACpDyD,EAAMzD,EAAOoI,EAAWnI,GAI9B,OAAOmI,CACT,CCrBe,SAAS5E,GAAI1D,EAAQC,EAAS,CAC3C,IAAIyD,EACJ,GAAIzD,IAAY,OACd,UAAWC,KAASF,EACdE,GAAS,OACLwD,EAAMxD,GAAUwD,IAAQ,QAAaxD,GAASA,KACpDwD,EAAMxD,OAGL,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,OACzC0D,EAAMxD,GAAUwD,IAAQ,QAAaxD,GAASA,KACpDwD,EAAMxD,EAGX,CACD,OAAOwD,CACT,CCnBe,SAAS6E,GAASvI,EAAQC,EAAS,CAChD,IAAIyD,EACA6E,EAAW,GACXpI,EAAQ,GACZ,GAAIF,IAAY,OACd,UAAWC,KAASF,EAClB,EAAEG,EACED,GAAS,OACLwD,EAAMxD,GAAUwD,IAAQ,QAAaxD,GAASA,KACpDwD,EAAMxD,EAAOqI,EAAWpI,OAI5B,SAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,OACzC0D,EAAMxD,GAAUwD,IAAQ,QAAaxD,GAASA,KACpDwD,EAAMxD,EAAOqI,EAAWpI,GAI9B,OAAOoI,CACT,CCjBe,SAASC,GAAY5F,EAAO6F,EAAGlJ,EAAO,EAAGI,EAAQ,IAAUsG,EAAS,CAKjF,GAJAwC,EAAI,KAAK,MAAMA,CAAC,EAChBlJ,EAAO,KAAK,MAAM,KAAK,IAAI,EAAGA,CAAI,CAAC,EACnCI,EAAQ,KAAK,MAAM,KAAK,IAAIiD,EAAM,OAAS,EAAGjD,CAAK,CAAC,EAEhD,EAAEJ,GAAQkJ,GAAKA,GAAK9I,GAAQ,OAAOiD,EAIvC,IAFAqD,EAAUA,IAAY,OAAYF,GAAmBC,GAAeC,CAAO,EAEpEtG,EAAQJ,GAAM,CACnB,GAAII,EAAQJ,EAAO,IAAK,CACtB,MAAMd,EAAIkB,EAAQJ,EAAO,EACnBmJ,EAAID,EAAIlJ,EAAO,EACfoJ,EAAI,KAAK,IAAIlK,CAAC,EACdiE,EAAI,GAAM,KAAK,IAAI,EAAIiG,EAAI,CAAC,EAC5BC,EAAK,GAAM,KAAK,KAAKD,EAAIjG,GAAKjE,EAAIiE,GAAKjE,CAAC,GAAKiK,EAAIjK,EAAI,EAAI,EAAI,GAAK,GAClEoK,EAAU,KAAK,IAAItJ,EAAM,KAAK,MAAMkJ,EAAIC,EAAIhG,EAAIjE,EAAImK,CAAE,CAAC,EACvDE,EAAW,KAAK,IAAInJ,EAAO,KAAK,MAAM8I,GAAKhK,EAAIiK,GAAKhG,EAAIjE,EAAImK,CAAE,CAAC,EACrEJ,GAAY5F,EAAO6F,EAAGI,EAASC,EAAU7C,CAAO,CACjD,CAED,MAAM5D,EAAIO,EAAM6F,CAAC,EACjB,IAAI5I,EAAIN,EACJkD,EAAI9C,EAKR,IAHAoJ,GAAKnG,EAAOrD,EAAMkJ,CAAC,EACfxC,EAAQrD,EAAMjD,CAAK,EAAG0C,CAAC,EAAI,GAAG0G,GAAKnG,EAAOrD,EAAMI,CAAK,EAElDE,EAAI4C,GAAG,CAEZ,IADAsG,GAAKnG,EAAO/C,EAAG4C,CAAC,EAAG,EAAE5C,EAAG,EAAE4C,EACnBwD,EAAQrD,EAAM/C,CAAC,EAAGwC,CAAC,EAAI,GAAG,EAAExC,EACnC,KAAOoG,EAAQrD,EAAMH,CAAC,EAAGJ,CAAC,EAAI,GAAG,EAAEI,CACpC,CAEGwD,EAAQrD,EAAMrD,CAAI,EAAG8C,CAAC,IAAM,EAAG0G,GAAKnG,EAAOrD,EAAMkD,CAAC,GACjD,EAAEA,EAAGsG,GAAKnG,EAAOH,EAAG9C,CAAK,GAE1B8C,GAAKgG,IAAGlJ,EAAOkD,EAAI,GACnBgG,GAAKhG,IAAG9C,EAAQ8C,EAAI,EACzB,CAED,OAAOG,CACT,CAEA,SAASmG,GAAKnG,EAAO/C,EAAG4C,EAAG,CACzB,MAAMJ,EAAIO,EAAM/C,CAAC,EACjB+C,EAAM/C,CAAC,EAAI+C,EAAMH,CAAC,EAClBG,EAAMH,CAAC,EAAIJ,CACb,CClDe,SAAS2G,GAAShJ,EAAQiG,EAAUrH,EAAW,CAC5D,IAAI+E,EACAsF,EAAU,GACd,GAAIhD,EAAQ,SAAW,EAAG,CACxB,IAAIiD,EACJ,UAAWC,KAAWnJ,EAAQ,CAC5B,MAAME,EAAQ+F,EAAQkD,CAAO,GACzBF,EACErK,EAAUsB,EAAOgJ,CAAQ,EAAI,EAC7BtK,EAAUsB,EAAOA,CAAK,IAAM,KAChCyD,EAAMwF,EACND,EAAWhJ,EACX+I,EAAU,GAEb,CACL,KACI,WAAW/I,KAASF,GACdiJ,EACEhD,EAAQ/F,EAAOyD,CAAG,EAAI,EACtBsC,EAAQ/F,EAAOA,CAAK,IAAM,KAC9ByD,EAAMzD,EACN+I,EAAU,IAIhB,OAAOtF,CACT,CCnBe,SAASyF,GAASpJ,EAAQ6D,EAAG5D,EAAS,CAEnD,GADAD,EAAS,aAAa,KAAKD,GAAQC,EAAQC,CAAO,CAAC,EAC/C,IAAExB,EAAIuB,EAAO,SAAW,MAAM6D,EAAI,CAACA,CAAC,GACxC,IAAIA,GAAK,GAAKpF,EAAI,EAAG,OAAOiF,GAAI1D,CAAM,EACtC,GAAI6D,GAAK,EAAG,OAAOF,GAAI3D,CAAM,EAC7B,IAAIvB,EACA,GAAKA,EAAI,GAAKoF,EACdwF,EAAK,KAAK,MAAM,CAAC,EACjBC,EAAS3F,GAAI6E,GAAYxI,EAAQqJ,CAAE,EAAE,SAAS,EAAGA,EAAK,CAAC,CAAC,EACxDE,EAAS7F,GAAI1D,EAAO,SAASqJ,EAAK,CAAC,CAAC,EACxC,OAAOC,GAAUC,EAASD,IAAW,EAAID,GAC3C,CAEO,SAASG,GAAexJ,EAAQ6D,EAAG5D,EAAUH,GAAQ,CAC1D,GAAI,IAAErB,EAAIuB,EAAO,SAAW,MAAM6D,EAAI,CAACA,CAAC,GACxC,IAAIA,GAAK,GAAKpF,EAAI,EAAG,MAAO,CAACwB,EAAQD,EAAO,CAAC,EAAG,EAAGA,CAAM,EACzD,GAAI6D,GAAK,EAAG,MAAO,CAAC5D,EAAQD,EAAOvB,EAAI,CAAC,EAAGA,EAAI,EAAGuB,CAAM,EACxD,IAAIvB,EACA,GAAKA,EAAI,GAAKoF,EACdwF,EAAK,KAAK,MAAM,CAAC,EACjBC,EAAS,CAACrJ,EAAQD,EAAOqJ,CAAE,EAAGA,EAAIrJ,CAAM,EACxCuJ,EAAS,CAACtJ,EAAQD,EAAOqJ,EAAK,CAAC,EAAGA,EAAK,EAAGrJ,CAAM,EACpD,OAAOsJ,GAAUC,EAASD,IAAW,EAAID,GAC3C,CAEO,SAASI,GAAczJ,EAAQ6D,EAAG5D,EAAUH,GAAQ,CACzD,GAAI,OAAM+D,EAAI,CAACA,CAAC,EAEhB,IADA9D,EAAU,aAAa,KAAKC,EAAQ,CAACqI,EAAGxI,IAAMC,GAAOG,EAAQD,EAAOH,CAAC,EAAGA,EAAGG,CAAM,CAAC,CAAC,EAC/E6D,GAAK,EAAG,OAAO0E,GAASxI,CAAO,EACnC,GAAI8D,GAAK,EAAG,OAAOyE,GAASvI,CAAO,EACnC,IAAIA,EACAI,EAAQ,YAAY,KAAKH,EAAQ,CAACqI,EAAGxI,IAAMA,CAAC,EAC5C4C,EAAI1C,EAAQ,OAAS,EACrBF,EAAI,KAAK,MAAM4C,EAAIoB,CAAC,EACxB,OAAA2E,GAAYrI,EAAON,EAAG,EAAG4C,EAAG,CAAC5C,EAAG4C,IAAMsD,GAAiBhG,EAAQF,CAAC,EAAGE,EAAQ0C,CAAC,CAAC,CAAC,EAC9E5C,EAAImJ,GAAS7I,EAAM,SAAS,EAAGN,EAAI,CAAC,EAAIA,GAAME,EAAQF,CAAC,CAAC,EACjDA,GAAK,EAAIA,EAAI,GACtB,CC3Ce,SAAS6J,GAA0B1J,EAAQ0D,EAAKC,EAAK,CAClE,MAAMmC,EAAInD,GAAM3C,CAAM,EAAGZ,EAAIgK,GAASpJ,EAAQ,GAAI,EAAIoJ,GAASpJ,EAAQ,GAAI,EAC3E,OAAO8F,GAAK1G,EAAI,KAAK,MAAMuE,EAAMD,IAAQ,EAAItE,EAAI,KAAK,IAAI0G,EAAG,GAAK,CAAC,EAAE,EAAI,CAC3E,CCHe,SAAS6D,GAAe3J,EAAQ0D,EAAKC,EAAK,CACvD,MAAMmC,EAAInD,GAAM3C,CAAM,EAAGZ,EAAIoE,GAAUxD,CAAM,EAC7C,OAAO8F,GAAK1G,EAAI,KAAK,MAAMuE,EAAMD,GAAO,KAAK,KAAKoC,CAAC,GAAK,KAAO1G,EAAE,EAAI,CACvE,CCNe,SAASmE,GAAKvD,EAAQC,EAAS,CAC5C,IAAI0C,EAAQ,EACRL,EAAM,EACV,GAAIrC,IAAY,OACd,QAASC,KAASF,EACZE,GAAS,OAASA,EAAQ,CAACA,IAAUA,IACvC,EAAEyC,EAAOL,GAAOpC,OAGf,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,OAASE,EAAQ,CAACA,IAAUA,IAC3E,EAAEyC,EAAOL,GAAOpC,EAGrB,CACD,GAAIyC,EAAO,OAAOL,EAAMK,CAC1B,CChBe,SAASiH,GAAO5J,EAAQC,EAAS,CAC9C,OAAOmJ,GAASpJ,EAAQ,GAAKC,CAAO,CACtC,CAEO,SAAS4J,GAAY7J,EAAQC,EAAS,CAC3C,OAAOwJ,GAAczJ,EAAQ,GAAKC,CAAO,CAC3C,CCRA,SAAU+E,GAAQ8E,EAAQ,CACxB,UAAWlH,KAASkH,EAClB,MAAOlH,CAEX,CAEe,SAASmH,GAAMD,EAAQ,CACpC,OAAO,MAAM,KAAK9E,GAAQ8E,CAAM,CAAC,CACnC,CCNe,SAASE,GAAKhK,EAAQC,EAAS,CAC5C,MAAMgK,EAAS,IAAIhG,GACnB,GAAIhE,IAAY,OACd,QAASC,KAASF,EACZE,GAAS,MAAQA,GAASA,GAC5B+J,EAAO,IAAI/J,GAAQ+J,EAAO,IAAI/J,CAAK,GAAK,GAAK,CAAC,MAG7C,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,MAAQE,GAASA,GAChE+J,EAAO,IAAI/J,GAAQ+J,EAAO,IAAI/J,CAAK,GAAK,GAAK,CAAC,CAGnD,CACD,IAAIgK,EACAC,EAAY,EAChB,SAAW,CAACjK,EAAOyC,CAAK,IAAKsH,EACvBtH,EAAQwH,IACVA,EAAYxH,EACZuH,EAAYhK,GAGhB,OAAOgK,CACT,CC3Be,SAASE,GAAMpK,EAAQqK,EAASC,GAAM,CACnD,MAAMF,EAAQ,CAAA,EACd,IAAIG,EACAC,EAAQ,GACZ,UAAWtK,KAASF,EACdwK,GAAOJ,EAAM,KAAKC,EAAOE,EAAUrK,CAAK,CAAC,EAC7CqK,EAAWrK,EACXsK,EAAQ,GAEV,OAAOJ,CACT,CAEO,SAASE,GAAKzL,EAAGC,EAAG,CACzB,MAAO,CAACD,EAAGC,CAAC,CACd,CCde,SAAS2L,GAAMzI,EAAOC,EAAMC,EAAM,CAC/CF,EAAQ,CAACA,EAAOC,EAAO,CAACA,EAAMC,GAAQzD,EAAI,UAAU,QAAU,GAAKwD,EAAOD,EAAOA,EAAQ,EAAG,GAAKvD,EAAI,EAAI,EAAI,CAACyD,EAM9G,QAJIrC,EAAI,GACJpB,EAAI,KAAK,IAAI,EAAG,KAAK,MAAMwD,EAAOD,GAASE,CAAI,CAAC,EAAI,EACpDuI,EAAQ,IAAI,MAAMhM,CAAC,EAEhB,EAAEoB,EAAIpB,GACXgM,EAAM5K,CAAC,EAAImC,EAAQnC,EAAIqC,EAGzB,OAAOuI,CACT,CCTe,SAASC,GAAK1K,EAAQC,EAAUrB,EAAW,CACxD,GAAI,OAAOoB,EAAO,OAAO,QAAQ,GAAM,WAAY,MAAM,IAAI,UAAU,wBAAwB,EAC/F,IAAI2K,EAAI,MAAM,KAAK3K,CAAM,EACzB,MAAM4K,EAAI,IAAI,aAAaD,EAAE,MAAM,EAC/B1K,EAAQ,SAAW,IAAG0K,EAAIA,EAAE,IAAI1K,CAAO,EAAGA,EAAUrB,GACxD,MAAMiM,EAAe,CAAChL,EAAG4C,IAAMxC,EAAQ0K,EAAE9K,CAAC,EAAG8K,EAAElI,CAAC,CAAC,EACjD,IAAIgG,EAAGnK,EACP,OAAA0B,EAAS,YAAY,KAAK2K,EAAG,CAACtC,EAAGxI,IAAMA,CAAC,EAExCG,EAAO,KAAKC,IAAYrB,EAAY,CAACiB,EAAG4C,IAAMsD,GAAiB4E,EAAE9K,CAAC,EAAG8K,EAAElI,CAAC,CAAC,EAAIuD,GAAe6E,CAAY,CAAC,EACzG7K,EAAO,QAAQ,CAACyC,EAAG5C,IAAM,CACrB,MAAMiG,EAAI+E,EAAapI,EAAGgG,IAAM,OAAYhG,EAAIgG,CAAC,EAC7C3C,GAAK,IACH2C,IAAM,QAAa3C,EAAI,KAAG2C,EAAIhG,EAAGnE,EAAIuB,GACzC+K,EAAEnI,CAAC,EAAInE,GAEPsM,EAAEnI,CAAC,EAAI,GAEf,CAAK,EACImI,CACT,CCrBe,SAASE,GAAM9K,EAAQiG,EAAUrH,EAAW,CACzD,IAAI8E,EACAuF,EAAU,GACd,GAAIhD,EAAQ,SAAW,EAAG,CACxB,IAAI8E,EACJ,UAAW5B,KAAWnJ,EAAQ,CAC5B,MAAME,EAAQ+F,EAAQkD,CAAO,GACzBF,EACErK,EAAUsB,EAAO6K,CAAQ,EAAI,EAC7BnM,EAAUsB,EAAOA,CAAK,IAAM,KAChCwD,EAAMyF,EACN4B,EAAW7K,EACX+I,EAAU,GAEb,CACL,KACI,WAAW/I,KAASF,GACdiJ,EACEhD,EAAQ/F,EAAOwD,CAAG,EAAI,EACtBuC,EAAQ/F,EAAOA,CAAK,IAAM,KAC9BwD,EAAMxD,EACN+I,EAAU,IAIhB,OAAOvF,CACT,CCzBe,SAASsH,GAAWhL,EAAQiG,EAAUrH,EAAW,CAC9D,GAAIqH,EAAQ,SAAW,EAAG,OAAOsC,GAASvI,EAAQiG,CAAO,EACzD,IAAI8E,EACArH,EAAM,GACNvD,EAAQ,GACZ,UAAWD,KAASF,EAClB,EAAEG,GACEuD,EAAM,EACJuC,EAAQ/F,EAAOA,CAAK,IAAM,EAC1B+F,EAAQ/F,EAAO6K,CAAQ,EAAI,KAC/BA,EAAW7K,EACXwD,EAAMvD,GAGV,OAAOuD,CACT,CCfe,SAASuH,GAAcjL,EAAQiG,EAAUrH,EAAW,CACjE,GAAIqH,EAAQ,SAAW,EAAG,OAAOqC,GAAStI,EAAQiG,CAAO,EACzD,IAAIiD,EACAvF,EAAM,GACNxD,EAAQ,GACZ,UAAWD,KAASF,EAClB,EAAEG,GACEwD,EAAM,EACJsC,EAAQ/F,EAAOA,CAAK,IAAM,EAC1B+F,EAAQ/F,EAAOgJ,CAAQ,EAAI,KAC/BA,EAAWhJ,EACXyD,EAAMxD,GAGV,OAAOwD,CACT,CChBe,SAASuH,GAAKlL,EAAQiG,EAAS,CAC5C,MAAM9F,EAAQ6K,GAAWhL,EAAQiG,CAAO,EACxC,OAAO9F,EAAQ,EAAI,OAAYA,CACjC,CCLA,MAAAgL,GAAeC,GAAS,KAAK,MAAM,EAE5B,SAASA,GAASC,EAAQ,CAC/B,OAAO,SAAiBzI,EAAOyG,EAAK,EAAGrC,EAAKpE,EAAM,OAAQ,CACxD,IAAI8F,EAAI1B,GAAMqC,EAAK,CAACA,GACpB,KAAOX,GAAG,CACR,MAAM7I,EAAIwL,EAAQ,EAAG3C,IAAM,EAAGrG,EAAIO,EAAM8F,EAAIW,CAAE,EAC9CzG,EAAM8F,EAAIW,CAAE,EAAIzG,EAAM/C,EAAIwJ,CAAE,EAC5BzG,EAAM/C,EAAIwJ,CAAE,EAAIhH,CACjB,CACD,OAAOO,CACX,CACA,CCZe,SAASN,GAAItC,EAAQC,EAAS,CAC3C,IAAIqC,EAAM,EACV,GAAIrC,IAAY,OACd,QAASC,KAASF,GACZE,EAAQ,CAACA,KACXoC,GAAOpC,OAGN,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACZE,EAAQ,CAACD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,KACzCsC,GAAOpC,EAGZ,CACD,OAAOoC,CACT,CCfe,SAASgJ,GAAUC,EAAQ,CACxC,GAAI,EAAE9M,EAAI8M,EAAO,QAAS,MAAO,CAAA,EACjC,QAAS1L,EAAI,GAAI6I,EAAIhF,GAAI6H,EAAQ7K,EAAM,EAAG4K,EAAY,IAAI,MAAM5C,CAAC,EAAG,EAAE7I,EAAI6I,GACxE,QAASjG,EAAI,GAAIhE,EAAG+M,EAAMF,EAAUzL,CAAC,EAAI,IAAI,MAAMpB,CAAC,EAAG,EAAEgE,EAAIhE,GAC3D+M,EAAI/I,CAAC,EAAI8I,EAAO9I,CAAC,EAAE5C,CAAC,EAGxB,OAAOyL,CACT,CAEA,SAAS5K,GAAOtB,EAAG,CACjB,OAAOA,EAAE,MACX,CCZe,SAASqM,IAAM,CAC5B,OAAOH,GAAU,SAAS,CAC5B,CCJe,SAASI,GAAM1L,EAAQ2L,EAAM,CAC1C,GAAI,OAAOA,GAAS,WAAY,MAAM,IAAI,UAAU,wBAAwB,EAC5E,IAAIxL,EAAQ,GACZ,UAAWD,KAASF,EAClB,GAAI,CAAC2L,EAAKzL,EAAO,EAAEC,EAAOH,CAAM,EAC9B,MAAO,GAGX,MAAO,EACT,CCTe,SAAS4L,GAAK5L,EAAQ2L,EAAM,CACzC,GAAI,OAAOA,GAAS,WAAY,MAAM,IAAI,UAAU,wBAAwB,EAC5E,IAAIxL,EAAQ,GACZ,UAAWD,KAASF,EAClB,GAAI2L,EAAKzL,EAAO,EAAEC,EAAOH,CAAM,EAC7B,MAAO,GAGX,MAAO,EACT,CCTe,SAAS6L,GAAO7L,EAAQ2L,EAAM,CAC3C,GAAI,OAAOA,GAAS,WAAY,MAAM,IAAI,UAAU,wBAAwB,EAC5E,MAAM/I,EAAQ,CAAA,EACd,IAAIzC,EAAQ,GACZ,UAAWD,KAASF,EACd2L,EAAKzL,EAAO,EAAEC,EAAOH,CAAM,GAC7B4C,EAAM,KAAK1C,CAAK,EAGpB,OAAO0C,CACT,CCVe,SAAS4C,GAAIxF,EAAQ8L,EAAQ,CAC1C,GAAI,OAAO9L,EAAO,OAAO,QAAQ,GAAM,WAAY,MAAM,IAAI,UAAU,wBAAwB,EAC/F,GAAI,OAAO8L,GAAW,WAAY,MAAM,IAAI,UAAU,0BAA0B,EAChF,OAAO,MAAM,KAAK9L,EAAQ,CAACE,EAAOC,IAAU2L,EAAO5L,EAAOC,EAAOH,CAAM,CAAC,CAC1E,CCJe,SAASgD,GAAOhD,EAAQ+C,EAAS7C,EAAO,CACrD,GAAI,OAAO6C,GAAY,WAAY,MAAM,IAAI,UAAU,2BAA2B,EAClF,MAAMgJ,EAAW/L,EAAO,OAAO,QAAQ,EAAC,EACxC,IAAIgM,EAAMC,EAAM9L,EAAQ,GACxB,GAAI,UAAU,OAAS,EAAG,CAExB,GADC,CAAC,KAAA6L,EAAM,MAAA9L,CAAK,EAAI6L,EAAS,KAAI,EAC1BC,EAAM,OACV,EAAE7L,CACH,CACD,KAAQ,CAAC,KAAA6L,EAAM,MAAOC,CAAI,EAAIF,EAAS,OAAS,CAACC,GAC/C9L,EAAQ6C,EAAQ7C,EAAO+L,EAAM,EAAE9L,EAAOH,CAAM,EAE9C,OAAOE,CACT,CCbe,SAASkH,GAAQpH,EAAQ,CACtC,GAAI,OAAOA,EAAO,OAAO,QAAQ,GAAM,WAAY,MAAM,IAAI,UAAU,wBAAwB,EAC/F,OAAO,MAAM,KAAKA,CAAM,EAAE,QAAO,CACnC,CCDe,SAASkM,GAAWlM,KAAWmM,EAAQ,CACpDnM,EAAS,IAAIwE,GAAUxE,CAAM,EAC7B,UAAWoM,KAASD,EAClB,UAAWjM,KAASkM,EAClBpM,EAAO,OAAOE,CAAK,EAGvB,OAAOF,CACT,CCRe,SAASqM,GAASrM,EAAQoM,EAAO,CAC9C,MAAML,EAAWK,EAAM,OAAO,QAAQ,EAAC,EAAIE,EAAM,IAAI9H,GACrD,UAAWnB,KAAKrD,EAAQ,CACtB,GAAIsM,EAAI,IAAIjJ,CAAC,EAAG,MAAO,GACvB,IAAInD,EAAO8L,EACX,MAAQ,CAAC,MAAA9L,EAAO,KAAA8L,CAAI,EAAID,EAAS,KAAI,IAC/B,CAAAC,GADoC,CAExC,GAAI,OAAO,GAAG3I,EAAGnD,CAAK,EAAG,MAAO,GAChCoM,EAAI,IAAIpM,CAAK,CACd,CACF,CACD,MAAO,EACT,CCZe,SAASqM,GAAavM,KAAWmM,EAAQ,CACtDnM,EAAS,IAAIwE,GAAUxE,CAAM,EAC7BmM,EAASA,EAAO,IAAIG,EAAG,EACvBE,EAAK,UAAWtM,KAASF,EACvB,UAAWoM,KAASD,EAClB,GAAI,CAACC,EAAM,IAAIlM,CAAK,EAAG,CACrBF,EAAO,OAAOE,CAAK,EACnB,SAASsM,CACV,CAGL,OAAOxM,CACT,CAEA,SAASsM,GAAItM,EAAQ,CACnB,OAAOA,aAAkBwE,GAAYxE,EAAS,IAAIwE,GAAUxE,CAAM,CACpE,CClBe,SAASyM,GAASzM,EAAQoM,EAAO,CAC9C,MAAML,EAAW/L,EAAO,OAAO,QAAQ,EAAC,EAAIsM,EAAM,IAAI,IACtD,UAAW5N,KAAK0N,EAAO,CACrB,MAAMM,EAAKC,GAAOjO,CAAC,EACnB,GAAI4N,EAAI,IAAII,CAAE,EAAG,SACjB,IAAIxM,EAAO8L,EACX,KAAQ,CAAC,MAAA9L,EAAO,KAAA8L,CAAI,EAAID,EAAS,KAAI,GAAK,CACxC,GAAIC,EAAM,MAAO,GACjB,MAAMY,EAASD,GAAOzM,CAAK,EAE3B,GADAoM,EAAI,IAAIM,CAAM,EACV,OAAO,GAAGF,EAAIE,CAAM,EAAG,KAC5B,CACF,CACD,MAAO,EACT,CAEA,SAASD,GAAOzM,EAAO,CACrB,OAAOA,IAAU,MAAQ,OAAOA,GAAU,SAAWA,EAAM,QAAS,EAAGA,CACzE,CChBe,SAAS2M,GAAO7M,EAAQoM,EAAO,CAC5C,OAAOK,GAASL,EAAOpM,CAAM,CAC/B,CCFe,SAAS8M,MAASX,EAAQ,CACvC,MAAMG,EAAM,IAAI9H,GAChB,UAAW4H,KAASD,EAClB,UAAWzN,KAAK0N,EACdE,EAAI,IAAI5N,CAAC,EAGb,OAAO4N,CACT,CCVe,SAAAS,GAASC,EAAaC,EAASC,EAAW,CACvDF,EAAY,UAAYC,EAAQ,UAAYC,EAC5CA,EAAU,YAAcF,CAC1B,CAEO,SAASG,GAAOC,EAAQC,EAAY,CACzC,IAAIH,EAAY,OAAO,OAAOE,EAAO,SAAS,EAC9C,QAASjJ,KAAOkJ,EAAYH,EAAU/I,CAAG,EAAIkJ,EAAWlJ,CAAG,EAC3D,OAAO+I,CACT,CCPO,SAASI,IAAQ,CAAE,CAEnB,IAAIC,GAAS,GACTC,GAAW,EAAID,GAEtBE,GAAM,sBACNC,GAAM,oDACNC,GAAM,qDACNC,GAAQ,qBACRC,GAAe,IAAI,OAAO,UAAUJ,EAAG,IAAIA,EAAG,IAAIA,EAAG,MAAM,EAC3DK,GAAe,IAAI,OAAO,UAAUH,EAAG,IAAIA,EAAG,IAAIA,EAAG,MAAM,EAC3DI,GAAgB,IAAI,OAAO,WAAWN,EAAG,IAAIA,EAAG,IAAIA,EAAG,IAAIC,EAAG,MAAM,EACpEM,GAAgB,IAAI,OAAO,WAAWL,EAAG,IAAIA,EAAG,IAAIA,EAAG,IAAID,EAAG,MAAM,EACpEO,GAAe,IAAI,OAAO,UAAUP,EAAG,IAAIC,EAAG,IAAIA,EAAG,MAAM,EAC3DO,GAAgB,IAAI,OAAO,WAAWR,EAAG,IAAIC,EAAG,IAAIA,EAAG,IAAID,EAAG,MAAM,EAEpES,GAAQ,CACV,UAAW,SACX,aAAc,SACd,KAAM,MACN,WAAY,QACZ,MAAO,SACP,MAAO,SACP,OAAQ,SACR,MAAO,EACP,eAAgB,SAChB,KAAM,IACN,WAAY,QACZ,MAAO,SACP,UAAW,SACX,UAAW,QACX,WAAY,QACZ,UAAW,SACX,MAAO,SACP,eAAgB,QAChB,SAAU,SACV,QAAS,SACT,KAAM,MACN,SAAU,IACV,SAAU,MACV,cAAe,SACf,SAAU,SACV,UAAW,MACX,SAAU,SACV,UAAW,SACX,YAAa,QACb,eAAgB,QAChB,WAAY,SACZ,WAAY,SACZ,QAAS,QACT,WAAY,SACZ,aAAc,QACd,cAAe,QACf,cAAe,QACf,cAAe,QACf,cAAe,MACf,WAAY,QACZ,SAAU,SACV,YAAa,MACb,QAAS,QACT,QAAS,QACT,WAAY,QACZ,UAAW,SACX,YAAa,SACb,YAAa,QACb,QAAS,SACT,UAAW,SACX,WAAY,SACZ,KAAM,SACN,UAAW,SACX,KAAM,QACN,MAAO,MACP,YAAa,SACb,KAAM,QACN,SAAU,SACV,QAAS,SACT,UAAW,SACX,OAAQ,QACR,MAAO,SACP,MAAO,SACP,SAAU,SACV,cAAe,SACf,UAAW,QACX,aAAc,SACd,UAAW,SACX,WAAY,SACZ,UAAW,SACX,qBAAsB,SACtB,UAAW,SACX,WAAY,QACZ,UAAW,SACX,UAAW,SACX,YAAa,SACb,cAAe,QACf,aAAc,QACd,eAAgB,QAChB,eAAgB,QAChB,eAAgB,SAChB,YAAa,SACb,KAAM,MACN,UAAW,QACX,MAAO,SACP,QAAS,SACT,OAAQ,QACR,iBAAkB,QAClB,WAAY,IACZ,aAAc,SACd,aAAc,QACd,eAAgB,QAChB,gBAAiB,QACjB,kBAAmB,MACnB,gBAAiB,QACjB,gBAAiB,SACjB,aAAc,QACd,UAAW,SACX,UAAW,SACX,SAAU,SACV,YAAa,SACb,KAAM,IACN,QAAS,SACT,MAAO,QACP,UAAW,QACX,OAAQ,SACR,UAAW,SACX,OAAQ,SACR,cAAe,SACf,UAAW,SACX,cAAe,SACf,cAAe,SACf,WAAY,SACZ,UAAW,SACX,KAAM,SACN,KAAM,SACN,KAAM,SACN,WAAY,SACZ,OAAQ,QACR,cAAe,QACf,IAAK,SACL,UAAW,SACX,UAAW,QACX,YAAa,QACb,OAAQ,SACR,WAAY,SACZ,SAAU,QACV,SAAU,SACV,OAAQ,SACR,OAAQ,SACR,QAAS,QACT,UAAW,QACX,UAAW,QACX,UAAW,QACX,KAAM,SACN,YAAa,MACb,UAAW,QACX,IAAK,SACL,KAAM,MACN,QAAS,SACT,OAAQ,SACR,UAAW,QACX,OAAQ,SACR,MAAO,SACP,MAAO,SACP,WAAY,SACZ,OAAQ,SACR,YAAa,QACf,EAEApB,GAAOO,GAAOc,GAAO,CACnB,KAAKC,EAAU,CACb,OAAO,OAAO,OAAO,IAAI,KAAK,YAAa,KAAMA,CAAQ,CAC1D,EACD,aAAc,CACZ,OAAO,KAAK,MAAM,aACnB,EACD,IAAKC,GACL,UAAWA,GACX,WAAYC,GACZ,UAAWC,GACX,UAAWC,GACX,SAAUA,EACZ,CAAC,EAED,SAASH,IAAkB,CACzB,OAAO,KAAK,MAAM,WACpB,CAEA,SAASC,IAAmB,CAC1B,OAAO,KAAK,MAAM,YACpB,CAEA,SAASC,IAAkB,CACzB,OAAOE,GAAW,IAAI,EAAE,WAC1B,CAEA,SAASD,IAAkB,CACzB,OAAO,KAAK,MAAM,WACpB,CAEe,SAASL,GAAMO,EAAQ,CACpC,IAAIjG,EAAGkG,EACP,OAAAD,GAAUA,EAAS,IAAI,KAAM,EAAC,YAAW,GACjCjG,EAAIkF,GAAM,KAAKe,CAAM,IAAMC,EAAIlG,EAAE,CAAC,EAAE,OAAQA,EAAI,SAASA,EAAE,CAAC,EAAG,EAAE,EAAGkG,IAAM,EAAIC,GAAKnG,CAAC,EACtFkG,IAAM,EAAI,IAAIE,EAAKpG,GAAK,EAAI,GAAQA,GAAK,EAAI,IAAQA,GAAK,EAAI,GAAQA,EAAI,KAASA,EAAI,KAAQ,EAAMA,EAAI,GAAM,CAAC,EAChHkG,IAAM,EAAIG,GAAKrG,GAAK,GAAK,IAAMA,GAAK,GAAK,IAAMA,GAAK,EAAI,KAAOA,EAAI,KAAQ,GAAI,EAC/EkG,IAAM,EAAIG,GAAMrG,GAAK,GAAK,GAAQA,GAAK,EAAI,IAAQA,GAAK,EAAI,GAAQA,GAAK,EAAI,IAAQA,GAAK,EAAI,GAAQA,EAAI,MAAUA,EAAI,KAAQ,EAAMA,EAAI,IAAQ,GAAI,EACtJ,OACCA,EAAImF,GAAa,KAAKc,CAAM,GAAK,IAAIG,EAAIpG,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAG,CAAC,GAC5DA,EAAIoF,GAAa,KAAKa,CAAM,GAAK,IAAIG,EAAIpG,EAAE,CAAC,EAAI,IAAM,IAAKA,EAAE,CAAC,EAAI,IAAM,IAAKA,EAAE,CAAC,EAAI,IAAM,IAAK,CAAC,GAChGA,EAAIqF,GAAc,KAAKY,CAAM,GAAKI,GAAKrG,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAGA,EAAE,CAAC,CAAC,GAC7DA,EAAIsF,GAAc,KAAKW,CAAM,GAAKI,GAAKrG,EAAE,CAAC,EAAI,IAAM,IAAKA,EAAE,CAAC,EAAI,IAAM,IAAKA,EAAE,CAAC,EAAI,IAAM,IAAKA,EAAE,CAAC,CAAC,GACjGA,EAAIuF,GAAa,KAAKU,CAAM,GAAKK,GAAKtG,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,IAAKA,EAAE,CAAC,EAAI,IAAK,CAAC,GACrEA,EAAIwF,GAAc,KAAKS,CAAM,GAAKK,GAAKtG,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,IAAKA,EAAE,CAAC,EAAI,IAAKA,EAAE,CAAC,CAAC,EAC1EyF,GAAM,eAAeQ,CAAM,EAAIE,GAAKV,GAAMQ,CAAM,CAAC,EACjDA,IAAW,cAAgB,IAAIG,EAAI,IAAK,IAAK,IAAK,CAAC,EACnD,IACR,CAEA,SAASD,GAAK,EAAG,CACf,OAAO,IAAIC,EAAI,GAAK,GAAK,IAAM,GAAK,EAAI,IAAM,EAAI,IAAM,CAAC,CAC3D,CAEA,SAASC,GAAKzQ,EAAG2G,EAAGnG,EAAGD,EAAG,CACxB,OAAIA,GAAK,IAAGP,EAAI2G,EAAInG,EAAI,KACjB,IAAIgQ,EAAIxQ,EAAG2G,EAAGnG,EAAGD,CAAC,CAC3B,CAEO,SAASoQ,GAAWvQ,EAAG,CAE5B,OADMA,aAAa4O,KAAQ5O,EAAI0P,GAAM1P,CAAC,GACjCA,GACLA,EAAIA,EAAE,MACC,IAAIoQ,EAAIpQ,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,OAAO,GAFxB,IAAIoQ,CAGrB,CAEO,SAASI,GAAI5Q,EAAG2G,EAAGnG,EAAGqQ,EAAS,CACpC,OAAO,UAAU,SAAW,EAAIF,GAAW3Q,CAAC,EAAI,IAAIwQ,EAAIxQ,EAAG2G,EAAGnG,EAAGqQ,GAAkB,CAAW,CAChG,CAEO,SAASL,EAAIxQ,EAAG2G,EAAGnG,EAAGqQ,EAAS,CACpC,KAAK,EAAI,CAAC7Q,EACV,KAAK,EAAI,CAAC2G,EACV,KAAK,EAAI,CAACnG,EACV,KAAK,QAAU,CAACqQ,CAClB,CAEApC,GAAO+B,EAAKI,GAAK/B,GAAOG,GAAO,CAC7B,SAAS7E,EAAG,CACV,OAAAA,EAAIA,GAAK,KAAO+E,GAAW,KAAK,IAAIA,GAAU/E,CAAC,EACxC,IAAIqG,EAAI,KAAK,EAAIrG,EAAG,KAAK,EAAIA,EAAG,KAAK,EAAIA,EAAG,KAAK,OAAO,CAChE,EACD,OAAOA,EAAG,CACR,OAAAA,EAAIA,GAAK,KAAO8E,GAAS,KAAK,IAAIA,GAAQ9E,CAAC,EACpC,IAAIqG,EAAI,KAAK,EAAIrG,EAAG,KAAK,EAAIA,EAAG,KAAK,EAAIA,EAAG,KAAK,OAAO,CAChE,EACD,KAAM,CACJ,OAAO,IACR,EACD,OAAQ,CACN,OAAO,IAAIqG,EAAIM,GAAO,KAAK,CAAC,EAAGA,GAAO,KAAK,CAAC,EAAGA,GAAO,KAAK,CAAC,EAAGC,GAAO,KAAK,OAAO,CAAC,CACpF,EACD,aAAc,CACZ,MAAQ,KAAQ,KAAK,GAAK,KAAK,EAAI,OAC3B,KAAQ,KAAK,GAAK,KAAK,EAAI,OAC3B,KAAQ,KAAK,GAAK,KAAK,EAAI,OAC3B,GAAK,KAAK,SAAW,KAAK,SAAW,CAC9C,EACD,IAAKC,GACL,UAAWA,GACX,WAAYC,GACZ,UAAWC,GACX,SAAUA,EACZ,CAAC,CAAC,EAEF,SAASF,IAAgB,CACvB,MAAO,IAAIG,GAAI,KAAK,CAAC,CAAC,GAAGA,GAAI,KAAK,CAAC,CAAC,GAAGA,GAAI,KAAK,CAAC,CAAC,EACpD,CAEA,SAASF,IAAiB,CACxB,MAAO,IAAIE,GAAI,KAAK,CAAC,CAAC,GAAGA,GAAI,KAAK,CAAC,CAAC,GAAGA,GAAI,KAAK,CAAC,CAAC,GAAGA,IAAK,MAAM,KAAK,OAAO,EAAI,EAAI,KAAK,SAAW,GAAG,CAAC,EAC1G,CAEA,SAASD,IAAgB,CACvB,MAAM3Q,EAAIwQ,GAAO,KAAK,OAAO,EAC7B,MAAO,GAAGxQ,IAAM,EAAI,OAAS,OAAO,GAAGuQ,GAAO,KAAK,CAAC,CAAC,KAAKA,GAAO,KAAK,CAAC,CAAC,KAAKA,GAAO,KAAK,CAAC,CAAC,GAAGvQ,IAAM,EAAI,IAAM,KAAKA,CAAC,GAAG,EACzH,CAEA,SAASwQ,GAAOF,EAAS,CACvB,OAAO,MAAMA,CAAO,EAAI,EAAI,KAAK,IAAI,EAAG,KAAK,IAAI,EAAGA,CAAO,CAAC,CAC9D,CAEA,SAASC,GAAOlP,EAAO,CACrB,OAAO,KAAK,IAAI,EAAG,KAAK,IAAI,IAAK,KAAK,MAAMA,CAAK,GAAK,CAAC,CAAC,CAC1D,CAEA,SAASuP,GAAIvP,EAAO,CAClB,OAAAA,EAAQkP,GAAOlP,CAAK,GACZA,EAAQ,GAAK,IAAM,IAAMA,EAAM,SAAS,EAAE,CACpD,CAEA,SAAS8O,GAAKnN,EAAGa,EAAGkM,EAAG/P,EAAG,CACxB,OAAIA,GAAK,EAAGgD,EAAIa,EAAIkM,EAAI,IACfA,GAAK,GAAKA,GAAK,EAAG/M,EAAIa,EAAI,IAC1BA,GAAK,IAAGb,EAAI,KACd,IAAI6N,GAAI7N,EAAGa,EAAGkM,EAAG/P,CAAC,CAC3B,CAEO,SAAS6P,GAAWhQ,EAAG,CAC5B,GAAIA,aAAagR,GAAK,OAAO,IAAIA,GAAIhR,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,OAAO,EAE7D,GADMA,aAAa4O,KAAQ5O,EAAI0P,GAAM1P,CAAC,GAClC,CAACA,EAAG,OAAO,IAAIgR,GACnB,GAAIhR,aAAagR,GAAK,OAAOhR,EAC7BA,EAAIA,EAAE,MACN,IAAIJ,EAAII,EAAE,EAAI,IACVuG,EAAIvG,EAAE,EAAI,IACVI,EAAIJ,EAAE,EAAI,IACVgF,EAAM,KAAK,IAAIpF,EAAG2G,EAAGnG,CAAC,EACtB6E,EAAM,KAAK,IAAIrF,EAAG2G,EAAGnG,CAAC,EACtB+C,EAAI,IACJa,EAAIiB,EAAMD,EACVkL,GAAKjL,EAAMD,GAAO,EACtB,OAAIhB,GACEpE,IAAMqF,EAAK9B,GAAKoD,EAAInG,GAAK4D,GAAKuC,EAAInG,GAAK,EAClCmG,IAAMtB,EAAK9B,GAAK/C,EAAIR,GAAKoE,EAAI,EACjCb,GAAKvD,EAAI2G,GAAKvC,EAAI,EACvBA,GAAKkM,EAAI,GAAMjL,EAAMD,EAAM,EAAIC,EAAMD,EACrC7B,GAAK,IAELa,EAAIkM,EAAI,GAAKA,EAAI,EAAI,EAAI/M,EAEpB,IAAI6N,GAAI7N,EAAGa,EAAGkM,EAAGlQ,EAAE,OAAO,CACnC,CAEO,SAASiR,GAAI9N,EAAGa,EAAGkM,EAAGO,EAAS,CACpC,OAAO,UAAU,SAAW,EAAIT,GAAW7M,CAAC,EAAI,IAAI6N,GAAI7N,EAAGa,EAAGkM,EAAGO,GAAkB,CAAW,CAChG,CAEA,SAASO,GAAI7N,EAAGa,EAAGkM,EAAGO,EAAS,CAC7B,KAAK,EAAI,CAACtN,EACV,KAAK,EAAI,CAACa,EACV,KAAK,EAAI,CAACkM,EACV,KAAK,QAAU,CAACO,CAClB,CAEApC,GAAO2C,GAAKC,GAAKxC,GAAOG,GAAO,CAC7B,SAAS7E,EAAG,CACV,OAAAA,EAAIA,GAAK,KAAO+E,GAAW,KAAK,IAAIA,GAAU/E,CAAC,EACxC,IAAIiH,GAAI,KAAK,EAAG,KAAK,EAAG,KAAK,EAAIjH,EAAG,KAAK,OAAO,CACxD,EACD,OAAOA,EAAG,CACR,OAAAA,EAAIA,GAAK,KAAO8E,GAAS,KAAK,IAAIA,GAAQ9E,CAAC,EACpC,IAAIiH,GAAI,KAAK,EAAG,KAAK,EAAG,KAAK,EAAIjH,EAAG,KAAK,OAAO,CACxD,EACD,KAAM,CACJ,IAAI5G,EAAI,KAAK,EAAI,KAAO,KAAK,EAAI,GAAK,IAClCa,EAAI,MAAMb,CAAC,GAAK,MAAM,KAAK,CAAC,EAAI,EAAI,KAAK,EACzC+M,EAAI,KAAK,EACTgB,EAAKhB,GAAKA,EAAI,GAAMA,EAAI,EAAIA,GAAKlM,EACjCmN,EAAK,EAAIjB,EAAIgB,EACjB,OAAO,IAAId,EACTgB,GAAQjO,GAAK,IAAMA,EAAI,IAAMA,EAAI,IAAKgO,EAAID,CAAE,EAC5CE,GAAQjO,EAAGgO,EAAID,CAAE,EACjBE,GAAQjO,EAAI,IAAMA,EAAI,IAAMA,EAAI,IAAKgO,EAAID,CAAE,EAC3C,KAAK,OACX,CACG,EACD,OAAQ,CACN,OAAO,IAAIF,GAAIK,GAAO,KAAK,CAAC,EAAGC,GAAO,KAAK,CAAC,EAAGA,GAAO,KAAK,CAAC,EAAGX,GAAO,KAAK,OAAO,CAAC,CACpF,EACD,aAAc,CACZ,OAAQ,GAAK,KAAK,GAAK,KAAK,GAAK,GAAK,MAAM,KAAK,CAAC,IAC1C,GAAK,KAAK,GAAK,KAAK,GAAK,GACzB,GAAK,KAAK,SAAW,KAAK,SAAW,CAC9C,EACD,WAAY,CACV,MAAMxQ,EAAIwQ,GAAO,KAAK,OAAO,EAC7B,MAAO,GAAGxQ,IAAM,EAAI,OAAS,OAAO,GAAGkR,GAAO,KAAK,CAAC,CAAC,KAAKC,GAAO,KAAK,CAAC,EAAI,GAAG,MAAMA,GAAO,KAAK,CAAC,EAAI,GAAG,IAAInR,IAAM,EAAI,IAAM,KAAKA,CAAC,GAAG,EACtI,CACH,CAAC,CAAC,EAEF,SAASkR,GAAO7P,EAAO,CACrB,OAAAA,GAASA,GAAS,GAAK,IAChBA,EAAQ,EAAIA,EAAQ,IAAMA,CACnC,CAEA,SAAS8P,GAAO9P,EAAO,CACrB,OAAO,KAAK,IAAI,EAAG,KAAK,IAAI,EAAGA,GAAS,CAAC,CAAC,CAC5C,CAGA,SAAS4P,GAAQjO,EAAGgO,EAAID,EAAI,CAC1B,OAAQ/N,EAAI,GAAKgO,GAAMD,EAAKC,GAAMhO,EAAI,GAChCA,EAAI,IAAM+N,EACV/N,EAAI,IAAMgO,GAAMD,EAAKC,IAAO,IAAMhO,GAAK,GACvCgO,GAAM,GACd,CC3YO,MAAMI,GAAU,KAAK,GAAK,IACpBC,GAAU,IAAM,KAAK,GCI5BC,GAAI,GACNC,GAAK,OACLC,GAAK,EACLC,GAAK,OACLC,GAAK,EAAI,GACTC,GAAK,EAAI,GACTC,GAAK,EAAID,GAAKA,GACdE,GAAKF,GAAKA,GAAKA,GAEnB,SAASG,GAAWjS,EAAG,CACrB,GAAIA,aAAakS,GAAK,OAAO,IAAIA,GAAIlS,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,OAAO,EAC7D,GAAIA,aAAamS,GAAK,OAAOC,GAAQpS,CAAC,EAChCA,aAAaoQ,IAAMpQ,EAAIuQ,GAAWvQ,CAAC,GACzC,IAAIJ,EAAIyS,GAASrS,EAAE,CAAC,EAChBuG,EAAI8L,GAASrS,EAAE,CAAC,EAChBI,EAAIiS,GAASrS,EAAE,CAAC,EAChBoD,EAAIkP,IAAS,SAAY1S,EAAI,SAAY2G,EAAI,SAAYnG,GAAKuR,EAAE,EAAGhR,EAAGsJ,EAC1E,OAAIrK,IAAM2G,GAAKA,IAAMnG,EAAGO,EAAIsJ,EAAI7G,GAC9BzC,EAAI2R,IAAS,SAAY1S,EAAI,SAAY2G,EAAI,SAAYnG,GAAKsR,EAAE,EAChEzH,EAAIqI,IAAS,SAAY1S,EAAI,SAAY2G,EAAI,SAAYnG,GAAKwR,EAAE,GAE3D,IAAIM,GAAI,IAAM9O,EAAI,GAAI,KAAOzC,EAAIyC,GAAI,KAAOA,EAAI6G,GAAIjK,EAAE,OAAO,CACtE,CAEO,SAASuS,GAAKrC,EAAGO,EAAS,CAC/B,OAAO,IAAIyB,GAAIhC,EAAG,EAAG,EAAGO,GAAkB,CAAW,CACvD,CAEe,SAAS+B,GAAItC,EAAG/P,EAAGC,EAAGqQ,EAAS,CAC5C,OAAO,UAAU,SAAW,EAAIwB,GAAW/B,CAAC,EAAI,IAAIgC,GAAIhC,EAAG/P,EAAGC,EAAGqQ,GAAkB,CAAW,CAChG,CAEO,SAASyB,GAAIhC,EAAG/P,EAAGC,EAAGqQ,EAAS,CACpC,KAAK,EAAI,CAACP,EACV,KAAK,EAAI,CAAC/P,EACV,KAAK,EAAI,CAACC,EACV,KAAK,QAAU,CAACqQ,CAClB,CAEApC,GAAO6D,GAAKM,GAAK/D,GAAOG,GAAO,CAC7B,SAAS7E,EAAG,CACV,OAAO,IAAImI,GAAI,KAAK,EAAIT,IAAK1H,GAAY,GAAQ,KAAK,EAAG,KAAK,EAAG,KAAK,OAAO,CAC9E,EACD,OAAOA,EAAG,CACR,OAAO,IAAImI,GAAI,KAAK,EAAIT,IAAK1H,GAAY,GAAQ,KAAK,EAAG,KAAK,EAAG,KAAK,OAAO,CAC9E,EACD,KAAM,CACJ,IAAI3G,GAAK,KAAK,EAAI,IAAM,IACpBzC,EAAI,MAAM,KAAK,CAAC,EAAIyC,EAAIA,EAAI,KAAK,EAAI,IACrC6G,EAAI,MAAM,KAAK,CAAC,EAAI7G,EAAIA,EAAI,KAAK,EAAI,IACzC,OAAAzC,EAAI+Q,GAAKe,GAAQ9R,CAAC,EAClByC,EAAIuO,GAAKc,GAAQrP,CAAC,EAClB6G,EAAI2H,GAAKa,GAAQxI,CAAC,EACX,IAAImG,EACTsC,GAAU,UAAY/R,EAAI,UAAYyC,EAAI,SAAY6G,CAAC,EACvDyI,GAAS,UAAa/R,EAAI,UAAYyC,EAAI,QAAY6G,CAAC,EACvDyI,GAAU,SAAY/R,EAAI,SAAYyC,EAAI,UAAY6G,CAAC,EACvD,KAAK,OACX,CACG,CACH,CAAC,CAAC,EAEF,SAASqI,GAAQ3O,EAAG,CAClB,OAAOA,EAAIqO,GAAK,KAAK,IAAIrO,EAAG,EAAI,CAAC,EAAIA,EAAIoO,GAAKF,EAChD,CAEA,SAASY,GAAQ9O,EAAG,CAClB,OAAOA,EAAImO,GAAKnO,EAAIA,EAAIA,EAAIoO,IAAMpO,EAAIkO,GACxC,CAEA,SAASa,GAAS/R,EAAG,CACnB,MAAO,MAAOA,GAAK,SAAY,MAAQA,EAAI,MAAQ,KAAK,IAAIA,EAAG,EAAI,GAAG,EAAI,KAC5E,CAEA,SAAS0R,GAAS1R,EAAG,CACnB,OAAQA,GAAK,MAAQ,OAAUA,EAAI,MAAQ,KAAK,KAAKA,EAAI,MAAS,MAAO,GAAG,CAC9E,CAEA,SAASgS,GAAW3S,EAAG,CACrB,GAAIA,aAAamS,GAAK,OAAO,IAAIA,GAAInS,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,OAAO,EAE7D,GADMA,aAAakS,KAAMlS,EAAIiS,GAAWjS,CAAC,GACrCA,EAAE,IAAM,GAAKA,EAAE,IAAM,EAAG,OAAO,IAAImS,GAAI,IAAK,EAAInS,EAAE,GAAKA,EAAE,EAAI,IAAM,EAAI,IAAKA,EAAE,EAAGA,EAAE,OAAO,EAC9F,IAAImD,EAAI,KAAK,MAAMnD,EAAE,EAAGA,EAAE,CAAC,EAAIwR,GAC/B,OAAO,IAAIW,GAAIhP,EAAI,EAAIA,EAAI,IAAMA,EAAG,KAAK,KAAKnD,EAAE,EAAIA,EAAE,EAAIA,EAAE,EAAIA,EAAE,CAAC,EAAGA,EAAE,EAAGA,EAAE,OAAO,CACtF,CAEO,SAAS4S,GAAI1C,EAAG9I,EAAGjE,EAAGsN,EAAS,CACpC,OAAO,UAAU,SAAW,EAAIkC,GAAWzC,CAAC,EAAI,IAAIiC,GAAIhP,EAAGiE,EAAG8I,EAAGO,GAAkB,CAAW,CAChG,CAEO,SAASoC,GAAI1P,EAAGiE,EAAG8I,EAAGO,EAAS,CACpC,OAAO,UAAU,SAAW,EAAIkC,GAAWxP,CAAC,EAAI,IAAIgP,GAAIhP,EAAGiE,EAAG8I,EAAGO,GAAkB,CAAW,CAChG,CAEO,SAAS0B,GAAIhP,EAAGiE,EAAG8I,EAAGO,EAAS,CACpC,KAAK,EAAI,CAACtN,EACV,KAAK,EAAI,CAACiE,EACV,KAAK,EAAI,CAAC8I,EACV,KAAK,QAAU,CAACO,CAClB,CAEA,SAAS2B,GAAQpS,EAAG,CAClB,GAAI,MAAMA,EAAE,CAAC,EAAG,OAAO,IAAIkS,GAAIlS,EAAE,EAAG,EAAG,EAAGA,EAAE,OAAO,EACnD,IAAImD,EAAInD,EAAE,EAAIuR,GACd,OAAO,IAAIW,GAAIlS,EAAE,EAAG,KAAK,IAAImD,CAAC,EAAInD,EAAE,EAAG,KAAK,IAAImD,CAAC,EAAInD,EAAE,EAAGA,EAAE,OAAO,CACrE,CAEAqO,GAAO8D,GAAKU,GAAKpE,GAAOG,GAAO,CAC7B,SAAS7E,EAAG,CACV,OAAO,IAAIoI,GAAI,KAAK,EAAG,KAAK,EAAG,KAAK,EAAIV,IAAK1H,GAAY,GAAQ,KAAK,OAAO,CAC9E,EACD,OAAOA,EAAG,CACR,OAAO,IAAIoI,GAAI,KAAK,EAAG,KAAK,EAAG,KAAK,EAAIV,IAAK1H,GAAY,GAAQ,KAAK,OAAO,CAC9E,EACD,KAAM,CACJ,OAAOqI,GAAQ,IAAI,EAAE,KACtB,CACH,CAAC,CAAC,ECtHF,IAAIU,GAAI,QACJC,GAAI,QACJC,GAAI,QACJC,GAAI,QACJC,GAAI,QACJC,GAAKD,GAAID,GACTG,GAAKF,GAAIH,GACTM,GAAQN,GAAIC,GAAIC,GAAIH,GAExB,SAASQ,GAAiBtT,EAAG,CAC3B,GAAIA,aAAauT,GAAW,OAAO,IAAIA,GAAUvT,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,OAAO,EACnEA,aAAaoQ,IAAMpQ,EAAIuQ,GAAWvQ,CAAC,GACzC,IAAIJ,EAAII,EAAE,EAAI,IACVuG,EAAIvG,EAAE,EAAI,IACVI,EAAIJ,EAAE,EAAI,IACVkQ,GAAKmD,GAAQjT,EAAI+S,GAAKvT,EAAIwT,GAAK7M,IAAM8M,GAAQF,GAAKC,IAClDI,EAAKpT,EAAI8P,EACTnG,GAAKmJ,IAAK3M,EAAI2J,GAAK8C,GAAIQ,GAAMP,GAC7BjP,EAAI,KAAK,KAAK+F,EAAIA,EAAIyJ,EAAKA,CAAE,GAAKN,GAAIhD,GAAK,EAAIA,IAC/C/M,EAAIa,EAAI,KAAK,MAAM+F,EAAGyJ,CAAE,EAAIhC,GAAU,IAAM,IAChD,OAAO,IAAI+B,GAAUpQ,EAAI,EAAIA,EAAI,IAAMA,EAAGa,EAAGkM,EAAGlQ,EAAE,OAAO,CAC3D,CAEe,SAASyT,GAAUtQ,EAAGa,EAAGkM,EAAGO,EAAS,CAClD,OAAO,UAAU,SAAW,EAAI6C,GAAiBnQ,CAAC,EAAI,IAAIoQ,GAAUpQ,EAAGa,EAAGkM,EAAGO,GAAkB,CAAW,CAC5G,CAEO,SAAS8C,GAAUpQ,EAAGa,EAAGkM,EAAGO,EAAS,CAC1C,KAAK,EAAI,CAACtN,EACV,KAAK,EAAI,CAACa,EACV,KAAK,EAAI,CAACkM,EACV,KAAK,QAAU,CAACO,CAClB,CAEApC,GAAOkF,GAAWE,GAAWhF,GAAOG,GAAO,CACzC,SAAS7E,EAAG,CACV,OAAAA,EAAIA,GAAK,KAAO+E,GAAW,KAAK,IAAIA,GAAU/E,CAAC,EACxC,IAAIwJ,GAAU,KAAK,EAAG,KAAK,EAAG,KAAK,EAAIxJ,EAAG,KAAK,OAAO,CAC9D,EACD,OAAOA,EAAG,CACR,OAAAA,EAAIA,GAAK,KAAO8E,GAAS,KAAK,IAAIA,GAAQ9E,CAAC,EACpC,IAAIwJ,GAAU,KAAK,EAAG,KAAK,EAAG,KAAK,EAAIxJ,EAAG,KAAK,OAAO,CAC9D,EACD,KAAM,CACJ,IAAI5G,EAAI,MAAM,KAAK,CAAC,EAAI,GAAK,KAAK,EAAI,KAAOoO,GACzCrB,EAAI,CAAC,KAAK,EACV/P,EAAI,MAAM,KAAK,CAAC,EAAI,EAAI,KAAK,EAAI+P,GAAK,EAAIA,GAC1CwD,EAAO,KAAK,IAAIvQ,CAAC,EACjBwQ,EAAO,KAAK,IAAIxQ,CAAC,EACrB,OAAO,IAAIiN,EACT,KAAOF,EAAI/P,GAAK2S,GAAIY,EAAOX,GAAIY,IAC/B,KAAOzD,EAAI/P,GAAK6S,GAAIU,EAAOT,GAAIU,IAC/B,KAAOzD,EAAI/P,GAAK+S,GAAIQ,IACpB,KAAK,OACX,CACG,CACH,CAAC,CAAC,EC5DK,SAASE,GAAM9B,EAAI+B,EAAIC,EAAIC,EAAIC,EAAI,CACxC,IAAIjC,EAAKD,EAAKA,EAAIE,EAAKD,EAAKD,EAC5B,QAAS,EAAI,EAAIA,EAAK,EAAIC,EAAKC,GAAM6B,GAC9B,EAAI,EAAI9B,EAAK,EAAIC,GAAM8B,GACvB,EAAI,EAAIhC,EAAK,EAAIC,EAAK,EAAIC,GAAM+B,EACjC/B,EAAKgC,GAAM,CACnB,CAEe,SAAQC,GAAC3S,EAAQ,CAC9B,IAAIvB,EAAIuB,EAAO,OAAS,EACxB,OAAO,SAASqC,EAAG,CACjB,IAAIxC,EAAIwC,GAAK,EAAKA,EAAI,EAAKA,GAAK,GAAKA,EAAI,EAAG5D,EAAI,GAAK,KAAK,MAAM4D,EAAI5D,CAAC,EACjE+T,EAAKxS,EAAOH,CAAC,EACb4S,EAAKzS,EAAOH,EAAI,CAAC,EACjB0S,EAAK1S,EAAI,EAAIG,EAAOH,EAAI,CAAC,EAAI,EAAI2S,EAAKC,EACtCC,EAAK7S,EAAIpB,EAAI,EAAIuB,EAAOH,EAAI,CAAC,EAAI,EAAI4S,EAAKD,EAC9C,OAAOF,IAAOjQ,EAAIxC,EAAIpB,GAAKA,EAAG8T,EAAIC,EAAIC,EAAIC,CAAE,CAChD,CACA,CChBe,SAAQE,GAAC5S,EAAQ,CAC9B,IAAIvB,EAAIuB,EAAO,OACf,OAAO,SAASqC,EAAG,CACjB,IAAIxC,EAAI,KAAK,QAAQwC,GAAK,GAAK,EAAI,EAAEA,EAAIA,GAAK5D,CAAC,EAC3C8T,EAAKvS,GAAQH,EAAIpB,EAAI,GAAKA,CAAC,EAC3B+T,EAAKxS,EAAOH,EAAIpB,CAAC,EACjBgU,EAAKzS,GAAQH,EAAI,GAAKpB,CAAC,EACvBiU,EAAK1S,GAAQH,EAAI,GAAKpB,CAAC,EAC3B,OAAO6T,IAAOjQ,EAAIxC,EAAIpB,GAAKA,EAAG8T,EAAIC,EAAIC,EAAIC,CAAE,CAChD,CACA,CCZA,MAAeG,GAAAxT,GAAK,IAAMA,ECE1B,SAASyT,GAAOjU,EAAGO,EAAG,CACpB,OAAO,SAASiD,EAAG,CACjB,OAAOxD,EAAIwD,EAAIjD,CACnB,CACA,CAEA,SAAS2T,GAAYlU,EAAGC,EAAGgD,EAAG,CAC5B,OAAOjD,EAAI,KAAK,IAAIA,EAAGiD,CAAC,EAAGhD,EAAI,KAAK,IAAIA,EAAGgD,CAAC,EAAIjD,EAAGiD,EAAI,EAAIA,EAAG,SAASO,EAAG,CACxE,OAAO,KAAK,IAAIxD,EAAIwD,EAAIvD,EAAGgD,CAAC,CAChC,CACA,CAEO,SAASkR,GAAInU,EAAGC,EAAG,CACxB,IAAIM,EAAIN,EAAID,EACZ,OAAOO,EAAI0T,GAAOjU,EAAGO,EAAI,KAAOA,EAAI,KAAOA,EAAI,IAAM,KAAK,MAAMA,EAAI,GAAG,EAAIA,CAAC,EAAIoH,GAAS,MAAM3H,CAAC,EAAIC,EAAID,CAAC,CAC3G,CAEO,SAASoU,GAAMnR,EAAG,CACvB,OAAQA,EAAI,CAACA,IAAO,EAAIoR,EAAU,SAASrU,EAAGC,EAAG,CAC/C,OAAOA,EAAID,EAAIkU,GAAYlU,EAAGC,EAAGgD,CAAC,EAAI0E,GAAS,MAAM3H,CAAC,EAAIC,EAAID,CAAC,CACnE,CACA,CAEe,SAASqU,EAAQrU,EAAGC,EAAG,CACpC,IAAIM,EAAIN,EAAID,EACZ,OAAOO,EAAI0T,GAAOjU,EAAGO,CAAC,EAAIoH,GAAS,MAAM3H,CAAC,EAAIC,EAAID,CAAC,CACrD,CCvBA,MAAAsU,GAAgB,SAASC,EAAStR,EAAG,CACnC,IAAIsM,EAAQ6E,GAAMnR,CAAC,EAEnB,SAASoN,EAAIlN,EAAOqR,EAAK,CACvB,IAAI/U,EAAI8P,GAAOpM,EAAQsR,GAAStR,CAAK,GAAG,GAAIqR,EAAMC,GAASD,CAAG,GAAG,CAAC,EAC9DpO,EAAImJ,EAAMpM,EAAM,EAAGqR,EAAI,CAAC,EACxBvU,EAAIsP,EAAMpM,EAAM,EAAGqR,EAAI,CAAC,EACxBlE,EAAU+D,EAAQlR,EAAM,QAASqR,EAAI,OAAO,EAChD,OAAO,SAAShR,EAAG,CACjB,OAAAL,EAAM,EAAI1D,EAAE+D,CAAC,EACbL,EAAM,EAAIiD,EAAE5C,CAAC,EACbL,EAAM,EAAIlD,EAAEuD,CAAC,EACbL,EAAM,QAAUmN,EAAQ9M,CAAC,EAClBL,EAAQ,EACrB,CACG,CAEDkN,OAAAA,EAAI,MAAQkE,EAELlE,CACT,EAAG,CAAC,EAEJ,SAASqE,GAAUC,EAAQ,CACzB,OAAO,SAASC,EAAQ,CACtB,IAAIhV,EAAIgV,EAAO,OACX,EAAI,IAAI,MAAMhV,CAAC,EACfwG,EAAI,IAAI,MAAMxG,CAAC,EACfK,EAAI,IAAI,MAAML,CAAC,EACfoB,EAAGuO,EACP,IAAKvO,EAAI,EAAGA,EAAIpB,EAAG,EAAEoB,EACnBuO,EAAQkF,GAASG,EAAO5T,CAAC,CAAC,EAC1B,EAAEA,CAAC,EAAIuO,EAAM,GAAK,EAClBnJ,EAAEpF,CAAC,EAAIuO,EAAM,GAAK,EAClBtP,EAAEe,CAAC,EAAIuO,EAAM,GAAK,EAEpB,SAAIoF,EAAO,CAAC,EACZvO,EAAIuO,EAAOvO,CAAC,EACZnG,EAAI0U,EAAO1U,CAAC,EACZsP,EAAM,QAAU,EACT,SAAS/L,EAAG,CACjB,OAAA+L,EAAM,EAAI,EAAE/L,CAAC,EACb+L,EAAM,EAAInJ,EAAE5C,CAAC,EACb+L,EAAM,EAAItP,EAAEuD,CAAC,EACN+L,EAAQ,EACrB,CACA,CACA,CAEU,IAACsF,GAAWH,GAAUjB,EAAK,EAC1BqB,GAAiBJ,GAAUK,EAAW,ECtDlC,SAAAC,GAAShV,EAAGC,EAAG,CACvBA,IAAGA,EAAI,IACZ,IAAIL,EAAII,EAAI,KAAK,IAAIC,EAAE,OAAQD,EAAE,MAAM,EAAI,EACvCiH,EAAIhH,EAAE,MAAO,EACb,EACJ,OAAO,SAASuD,EAAG,CACjB,IAAK,EAAI,EAAG,EAAI5D,EAAG,EAAE,EAAGqH,EAAE,CAAC,EAAIjH,EAAE,CAAC,GAAK,EAAIwD,GAAKvD,EAAE,CAAC,EAAIuD,EACvD,OAAOyD,CACX,CACA,CAEO,SAASgO,GAAczU,EAAG,CAC/B,OAAO,YAAY,OAAOA,CAAC,GAAK,EAAEA,aAAa,SACjD,CCVe,SAAA0U,GAASlV,EAAGC,EAAG,CAC5B,OAAQgV,GAAchV,CAAC,EAAI+U,GAAcG,IAAcnV,EAAGC,CAAC,CAC7D,CAEO,SAASkV,GAAanV,EAAGC,EAAG,CACjC,IAAImV,EAAKnV,EAAIA,EAAE,OAAS,EACpBoV,EAAKrV,EAAI,KAAK,IAAIoV,EAAIpV,EAAE,MAAM,EAAI,EAClCQ,EAAI,IAAI,MAAM6U,CAAE,EAChBpO,EAAI,IAAI,MAAMmO,CAAE,EAChBpU,EAEJ,IAAKA,EAAI,EAAGA,EAAIqU,EAAI,EAAErU,EAAGR,EAAEQ,CAAC,EAAIK,GAAMrB,EAAEgB,CAAC,EAAGf,EAAEe,CAAC,CAAC,EAChD,KAAOA,EAAIoU,EAAI,EAAEpU,EAAGiG,EAAEjG,CAAC,EAAIf,EAAEe,CAAC,EAE9B,OAAO,SAASwC,EAAG,CACjB,IAAKxC,EAAI,EAAGA,EAAIqU,EAAI,EAAErU,EAAGiG,EAAEjG,CAAC,EAAIR,EAAEQ,CAAC,EAAEwC,CAAC,EACtC,OAAOyD,CACX,CACA,CCrBe,SAAAqO,GAAStV,EAAGC,EAAG,CAC5B,IAAIM,EAAI,IAAI,KACZ,OAAOP,EAAI,CAACA,EAAGC,EAAI,CAACA,EAAG,SAASuD,EAAG,CACjC,OAAOjD,EAAE,QAAQP,GAAK,EAAIwD,GAAKvD,EAAIuD,CAAC,EAAGjD,CAC3C,CACA,CCLe,SAAAgV,GAASvV,EAAGC,EAAG,CAC5B,OAAOD,EAAI,CAACA,EAAGC,EAAI,CAACA,EAAG,SAASuD,EAAG,CACjC,OAAOxD,GAAK,EAAIwD,GAAKvD,EAAIuD,CAC7B,CACA,CCFe,SAAAgS,GAASxV,EAAGC,EAAG,CAC5B,IAAIe,EAAI,CAAE,EACNiG,EAAI,CAAE,EACN2C,GAEA5J,IAAM,MAAQ,OAAOA,GAAM,YAAUA,EAAI,KACzCC,IAAM,MAAQ,OAAOA,GAAM,YAAUA,EAAI,IAE7C,IAAK2J,KAAK3J,EACJ2J,KAAK5J,EACPgB,EAAE4I,CAAC,EAAIvI,GAAMrB,EAAE4J,CAAC,EAAG3J,EAAE2J,CAAC,CAAC,EAEvB3C,EAAE2C,CAAC,EAAI3J,EAAE2J,CAAC,EAId,OAAO,SAASpG,EAAG,CACjB,IAAKoG,KAAK5I,EAAGiG,EAAE2C,CAAC,EAAI5I,EAAE4I,CAAC,EAAEpG,CAAC,EAC1B,OAAOyD,CACX,CACA,CCpBA,IAAIwO,GAAM,8CACNC,GAAM,IAAI,OAAOD,GAAI,OAAQ,GAAG,EAEpC,SAAShV,GAAKR,EAAG,CACf,OAAO,UAAW,CAChB,OAAOA,CACX,CACA,CAEA,SAAS0V,GAAI1V,EAAG,CACd,OAAO,SAAS,EAAG,CACjB,OAAOA,EAAE,CAAC,EAAI,EAClB,CACA,CAEe,SAAA2V,GAAS5V,EAAGC,EAAG,CAC5B,IAAI4V,EAAKJ,GAAI,UAAYC,GAAI,UAAY,EACrCI,EACAC,EACAC,EACAhV,EAAI,GACJ6C,EAAI,CAAE,EACNoS,EAAI,CAAA,EAMR,IAHAjW,EAAIA,EAAI,GAAIC,EAAIA,EAAI,IAGZ6V,EAAKL,GAAI,KAAKzV,CAAC,KACf+V,EAAKL,GAAI,KAAKzV,CAAC,KAChB+V,EAAKD,EAAG,OAASF,IACpBG,EAAK/V,EAAE,MAAM4V,EAAIG,CAAE,EACfnS,EAAE7C,CAAC,EAAG6C,EAAE7C,CAAC,GAAKgV,EACbnS,EAAE,EAAE7C,CAAC,EAAIgV,IAEXF,EAAKA,EAAG,CAAC,MAAQC,EAAKA,EAAG,CAAC,GACzBlS,EAAE7C,CAAC,EAAG6C,EAAE7C,CAAC,GAAK+U,EACblS,EAAE,EAAE7C,CAAC,EAAI+U,GAEdlS,EAAE,EAAE7C,CAAC,EAAI,KACTiV,EAAE,KAAK,CAAC,EAAGjV,EAAG,EAAGC,GAAO6U,EAAIC,CAAE,CAAC,CAAC,GAElCF,EAAKH,GAAI,UAIX,OAAIG,EAAK5V,EAAE,SACT+V,EAAK/V,EAAE,MAAM4V,CAAE,EACXhS,EAAE7C,CAAC,EAAG6C,EAAE7C,CAAC,GAAKgV,EACbnS,EAAE,EAAE7C,CAAC,EAAIgV,GAKTnS,EAAE,OAAS,EAAKoS,EAAE,CAAC,EACpBN,GAAIM,EAAE,CAAC,EAAE,CAAC,EACVxV,GAAKR,CAAC,GACLA,EAAIgW,EAAE,OAAQ,SAASzS,EAAG,CACzB,QAASxC,EAAI,EAAGnB,EAAGmB,EAAIf,EAAG,EAAEe,EAAG6C,GAAGhE,EAAIoW,EAAEjV,CAAC,GAAG,CAAC,EAAInB,EAAE,EAAE2D,CAAC,EACtD,OAAOK,EAAE,KAAK,EAAE,CAC1B,EACA,CCrDe,SAAAqS,GAASlW,EAAGC,EAAG,CAC5B,IAAIuD,EAAI,OAAOvD,EAAGgH,EAClB,OAAOhH,GAAK,MAAQuD,IAAM,UAAYmE,GAAS1H,CAAC,GACzCuD,IAAM,SAAWvC,GAClBuC,IAAM,UAAayD,EAAIsI,GAAMtP,CAAC,IAAMA,EAAIgH,EAAGoJ,IAAO8F,GAClDlW,aAAasP,GAAQc,GACrBpQ,aAAa,KAAOmW,GACpBnB,GAAchV,CAAC,EAAI+U,GACnB,MAAM,QAAQ/U,CAAC,EAAIkV,GACnB,OAAOlV,EAAE,SAAY,YAAc,OAAOA,EAAE,UAAa,YAAc,MAAMA,CAAC,EAAIuV,GAClFvU,IAAQjB,EAAGC,CAAC,CACpB,CCrBe,SAAQoW,GAACzK,EAAO,CAC7B,IAAIhM,EAAIgM,EAAM,OACd,OAAO,SAASpI,EAAG,CACjB,OAAOoI,EAAM,KAAK,IAAI,EAAG,KAAK,IAAIhM,EAAI,EAAG,KAAK,MAAM4D,EAAI5D,CAAC,CAAC,CAAC,CAAC,CAChE,CACA,CCHe,SAAAuU,GAASnU,EAAGC,EAAG,CAC5B,IAAIe,EAAImT,GAAI,CAACnU,EAAG,CAACC,CAAC,EAClB,OAAO,SAASuD,EAAG,CACjB,IAAIhD,EAAIQ,EAAEwC,CAAC,EACX,OAAOhD,EAAI,IAAM,KAAK,MAAMA,EAAI,GAAG,CACvC,CACA,CCRe,SAAA8V,GAAStW,EAAGC,EAAG,CAC5B,OAAOD,EAAI,CAACA,EAAGC,EAAI,CAACA,EAAG,SAASuD,EAAG,CACjC,OAAO,KAAK,MAAMxD,GAAK,EAAIwD,GAAKvD,EAAIuD,CAAC,CACzC,CACA,CCJA,IAAI6N,GAAU,IAAM,KAAK,GAEdvL,GAAW,CACpB,WAAY,EACZ,WAAY,EACZ,OAAQ,EACR,MAAO,EACP,OAAQ,EACR,OAAQ,CACV,EAEe,SAAAyQ,GAASvW,EAAGC,EAAGgH,EAAG1G,EAAGb,EAAGC,EAAG,CACxC,IAAI6W,EAAQC,EAAQC,EACpB,OAAIF,EAAS,KAAK,KAAKxW,EAAIA,EAAIC,EAAIA,CAAC,KAAGD,GAAKwW,EAAQvW,GAAKuW,IACrDE,EAAQ1W,EAAIiH,EAAIhH,EAAIM,KAAG0G,GAAKjH,EAAI0W,EAAOnW,GAAKN,EAAIyW,IAChDD,EAAS,KAAK,KAAKxP,EAAIA,EAAI1G,EAAIA,CAAC,KAAG0G,GAAKwP,EAAQlW,GAAKkW,EAAQC,GAASD,GACtEzW,EAAIO,EAAIN,EAAIgH,IAAGjH,EAAI,CAACA,EAAGC,EAAI,CAACA,EAAGyW,EAAQ,CAACA,EAAOF,EAAS,CAACA,GACtD,CACL,WAAY9W,EACZ,WAAYC,EACZ,OAAQ,KAAK,MAAMM,EAAGD,CAAC,EAAIqR,GAC3B,MAAO,KAAK,KAAKqF,CAAK,EAAIrF,GAC1B,OAAQmF,EACR,OAAQC,CACZ,CACA,CCvBA,IAAIE,GAGG,SAASC,GAASvV,EAAO,CAC9B,MAAMwI,EAAI,IAAK,OAAO,WAAc,WAAa,UAAY,iBAAiBxI,EAAQ,EAAE,EACxF,OAAOwI,EAAE,WAAa/D,GAAWyQ,GAAU1M,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,CAAC,CACzE,CAEO,SAASgN,GAASxV,EAAO,CAI9B,OAHIA,GAAS,OACRsV,KAASA,GAAU,SAAS,gBAAgB,6BAA8B,GAAG,GAClFA,GAAQ,aAAa,YAAatV,CAAK,EACnC,EAAEA,EAAQsV,GAAQ,UAAU,QAAQ,YAAa,IAAU7Q,IAC/DzE,EAAQA,EAAM,OACPkV,GAAUlV,EAAM,EAAGA,EAAM,EAAGA,EAAM,EAAGA,EAAM,EAAGA,EAAM,EAAGA,EAAM,CAAC,EACvE,CCdA,SAASyV,GAAqBC,EAAOC,EAASC,EAASC,EAAU,CAE/D,SAASC,EAAI,EAAG,CACd,OAAO,EAAE,OAAS,EAAE,IAAK,EAAG,IAAM,EACnC,CAED,SAASC,EAAUC,EAAIC,EAAIC,EAAIC,EAAI3T,EAAGoS,EAAG,CACvC,GAAIoB,IAAOE,GAAMD,IAAOE,EAAI,CAC1B,IAAIxW,EAAI6C,EAAE,KAAK,aAAc,KAAMmT,EAAS,KAAMC,CAAO,EACzDhB,EAAE,KAAK,CAAC,EAAGjV,EAAI,EAAG,EAAGC,GAAOoW,EAAIE,CAAE,CAAC,EAAG,CAAC,EAAGvW,EAAI,EAAG,EAAGC,GAAOqW,EAAIE,CAAE,CAAC,CAAC,CACzE,MAAeD,GAAMC,IACf3T,EAAE,KAAK,aAAe0T,EAAKP,EAAUQ,EAAKP,CAAO,CAEpD,CAED,SAASQ,EAAOzX,EAAGC,EAAG4D,EAAGoS,EAAG,CACtBjW,IAAMC,GACJD,EAAIC,EAAI,IAAKA,GAAK,IAAcA,EAAID,EAAI,MAAKA,GAAK,KACtDiW,EAAE,KAAK,CAAC,EAAGpS,EAAE,KAAKsT,EAAItT,CAAC,EAAI,UAAW,KAAMqT,CAAQ,EAAI,EAAG,EAAGjW,GAAOjB,EAAGC,CAAC,CAAC,CAAC,GAClEA,GACT4D,EAAE,KAAKsT,EAAItT,CAAC,EAAI,UAAY5D,EAAIiX,CAAQ,CAE3C,CAED,SAASR,EAAM1W,EAAGC,EAAG4D,EAAGoS,EAAG,CACrBjW,IAAMC,EACRgW,EAAE,KAAK,CAAC,EAAGpS,EAAE,KAAKsT,EAAItT,CAAC,EAAI,SAAU,KAAMqT,CAAQ,EAAI,EAAG,EAAGjW,GAAOjB,EAAGC,CAAC,CAAC,CAAC,EACjEA,GACT4D,EAAE,KAAKsT,EAAItT,CAAC,EAAI,SAAW5D,EAAIiX,CAAQ,CAE1C,CAED,SAASQ,EAAML,EAAIC,EAAIC,EAAIC,EAAI3T,EAAGoS,EAAG,CACnC,GAAIoB,IAAOE,GAAMD,IAAOE,EAAI,CAC1B,IAAIxW,EAAI6C,EAAE,KAAKsT,EAAItT,CAAC,EAAI,SAAU,KAAM,IAAK,KAAM,GAAG,EACtDoS,EAAE,KAAK,CAAC,EAAGjV,EAAI,EAAG,EAAGC,GAAOoW,EAAIE,CAAE,CAAC,EAAG,CAAC,EAAGvW,EAAI,EAAG,EAAGC,GAAOqW,EAAIE,CAAE,CAAC,CAAC,CACpE,MAAUD,IAAO,GAAKC,IAAO,IAC5B3T,EAAE,KAAKsT,EAAItT,CAAC,EAAI,SAAW0T,EAAK,IAAMC,EAAK,GAAG,CAEjD,CAED,OAAO,SAASxX,EAAGC,EAAG,CACpB,IAAI4D,EAAI,CAAE,EACNoS,EAAI,CAAA,EACR,OAAAjW,EAAI+W,EAAM/W,CAAC,EAAGC,EAAI8W,EAAM9W,CAAC,EACzBmX,EAAUpX,EAAE,WAAYA,EAAE,WAAYC,EAAE,WAAYA,EAAE,WAAY4D,EAAGoS,CAAC,EACtEwB,EAAOzX,EAAE,OAAQC,EAAE,OAAQ4D,EAAGoS,CAAC,EAC/BS,EAAM1W,EAAE,MAAOC,EAAE,MAAO4D,EAAGoS,CAAC,EAC5ByB,EAAM1X,EAAE,OAAQA,EAAE,OAAQC,EAAE,OAAQA,EAAE,OAAQ4D,EAAGoS,CAAC,EAClDjW,EAAIC,EAAI,KACD,SAASuD,EAAG,CAEjB,QADIxC,EAAI,GAAIpB,EAAIqW,EAAE,OAAQpW,EACnB,EAAEmB,EAAIpB,GAAGiE,GAAGhE,EAAIoW,EAAEjV,CAAC,GAAG,CAAC,EAAInB,EAAE,EAAE2D,CAAC,EACvC,OAAOK,EAAE,KAAK,EAAE,CACtB,CACA,CACA,CAEU,IAAC8T,GAA0Bb,GAAqBF,GAAU,OAAQ,MAAO,MAAM,EAC9EgB,GAA0Bd,GAAqBD,GAAU,KAAM,IAAK,GAAG,EC9D9EgB,GAAW,MAEf,SAAStE,GAAK/S,EAAG,CACf,QAASA,EAAI,KAAK,IAAIA,CAAC,GAAK,EAAIA,GAAK,CACvC,CAEA,SAASgT,GAAKhT,EAAG,CACf,QAASA,EAAI,KAAK,IAAIA,CAAC,GAAK,EAAIA,GAAK,CACvC,CAEA,SAASsX,GAAKtX,EAAG,CACf,QAASA,EAAI,KAAK,IAAI,EAAIA,CAAC,GAAK,IAAMA,EAAI,EAC5C,CAEA,MAAeuX,GAAC,SAASC,EAAQC,EAAKC,EAAMC,EAAM,CAIhD,SAASC,EAAKC,EAAIC,EAAI,CACpB,IAAIC,EAAMF,EAAG,CAAC,EAAGG,EAAMH,EAAG,CAAC,EAAGI,EAAKJ,EAAG,CAAC,EACnCK,EAAMJ,EAAG,CAAC,EAAGK,EAAML,EAAG,CAAC,EAAGM,EAAKN,EAAG,CAAC,EACnCO,EAAKH,EAAMH,EACXO,EAAKH,EAAMH,EACXO,EAAKF,EAAKA,EAAKC,EAAKA,EACpB9X,EACA8B,EAGJ,GAAIiW,EAAKlB,GACP/U,EAAI,KAAK,IAAI8V,EAAKH,CAAE,EAAIR,EACxBjX,EAAI,SAASwC,EAAG,CACd,MAAO,CACL+U,EAAM/U,EAAIqV,EACVL,EAAMhV,EAAIsV,EACVL,EAAK,KAAK,IAAIR,EAAMzU,EAAIV,CAAC,CACnC,CACO,MAIE,CACH,IAAIkW,EAAK,KAAK,KAAKD,CAAE,EACjBE,GAAML,EAAKA,EAAKH,EAAKA,EAAKN,EAAOY,IAAO,EAAIN,EAAKP,EAAOc,GACxDE,GAAMN,EAAKA,EAAKH,EAAKA,EAAKN,EAAOY,IAAO,EAAIH,EAAKV,EAAOc,GACxDG,EAAK,KAAK,IAAI,KAAK,KAAKF,EAAKA,EAAK,CAAC,EAAIA,CAAE,EACzCG,EAAK,KAAK,IAAI,KAAK,KAAKF,EAAKA,EAAK,CAAC,EAAIA,CAAE,EAC7CpW,GAAKsW,EAAKD,GAAMlB,EAChBjX,EAAI,SAASwC,EAAG,CACd,IAAIK,EAAIL,EAAIV,EACRuW,EAAS9F,GAAK4F,CAAE,EAChBG,EAAIb,GAAMP,EAAOc,IAAOK,EAASvB,GAAKG,EAAMpU,EAAIsV,CAAE,EAAI3F,GAAK2F,CAAE,GACjE,MAAO,CACLZ,EAAMe,EAAIT,EACVL,EAAMc,EAAIR,EACVL,EAAKY,EAAS9F,GAAK0E,EAAMpU,EAAIsV,CAAE,CACzC,CACO,CACF,CAED,OAAAnY,EAAE,SAAW8B,EAAI,IAAOmV,EAAM,KAAK,MAE5BjX,CACR,CAED,OAAAoX,EAAK,IAAM,SAAS5O,EAAG,CACrB,IAAI+P,EAAK,KAAK,IAAI,KAAM,CAAC/P,CAAC,EAAGgQ,EAAKD,EAAKA,EAAIE,EAAKD,EAAKA,EACrD,OAAOxB,EAAQuB,EAAIC,EAAIC,CAAE,CAC7B,EAESrB,CACT,EAAG,KAAK,MAAO,EAAG,CAAC,ECnEnB,SAAStH,GAAIqD,EAAK,CAChB,OAAO,SAAShR,EAAOqR,EAAK,CAC1B,IAAIxR,EAAImR,GAAKhR,EAAQuW,GAASvW,CAAK,GAAG,GAAIqR,EAAMkF,GAASlF,CAAG,GAAG,CAAC,EAC5D3Q,EAAI0L,EAAMpM,EAAM,EAAGqR,EAAI,CAAC,EACxBzE,EAAIR,EAAMpM,EAAM,EAAGqR,EAAI,CAAC,EACxBlE,EAAUf,EAAMpM,EAAM,QAASqR,EAAI,OAAO,EAC9C,OAAO,SAAShR,EAAG,CACjB,OAAAL,EAAM,EAAIH,EAAEQ,CAAC,EACbL,EAAM,EAAIU,EAAEL,CAAC,EACbL,EAAM,EAAI4M,EAAEvM,CAAC,EACbL,EAAM,QAAUmN,EAAQ9M,CAAC,EAClBL,EAAQ,EACrB,CACG,CACH,CAEA,MAAAwW,GAAe7I,GAAIqD,EAAG,EACZ,IAACyF,GAAU9I,GAAIvB,CAAK,ECjBf,SAAS8C,GAAIlP,EAAOqR,EAAK,CACtC,IAAIzE,EAAIR,GAAOpM,EAAQ0W,GAAS1W,CAAK,GAAG,GAAIqR,EAAMqF,GAASrF,CAAG,GAAG,CAAC,EAC9DxU,EAAIuP,EAAMpM,EAAM,EAAGqR,EAAI,CAAC,EACxBvU,EAAIsP,EAAMpM,EAAM,EAAGqR,EAAI,CAAC,EACxBlE,EAAUf,EAAMpM,EAAM,QAASqR,EAAI,OAAO,EAC9C,OAAO,SAAShR,EAAG,CACjB,OAAAL,EAAM,EAAI4M,EAAEvM,CAAC,EACbL,EAAM,EAAInD,EAAEwD,CAAC,EACbL,EAAM,EAAIlD,EAAEuD,CAAC,EACbL,EAAM,QAAUmN,EAAQ9M,CAAC,EAClBL,EAAQ,EACnB,CACA,CCZA,SAASuP,GAAIyB,EAAK,CAChB,OAAO,SAAShR,EAAOqR,EAAK,CAC1B,IAAIxR,EAAImR,GAAKhR,EAAQ2W,GAAS3W,CAAK,GAAG,GAAIqR,EAAMsF,GAAStF,CAAG,GAAG,CAAC,EAC5DvN,EAAIsI,EAAMpM,EAAM,EAAGqR,EAAI,CAAC,EACxBzE,EAAIR,EAAMpM,EAAM,EAAGqR,EAAI,CAAC,EACxBlE,EAAUf,EAAMpM,EAAM,QAASqR,EAAI,OAAO,EAC9C,OAAO,SAAShR,EAAG,CACjB,OAAAL,EAAM,EAAIH,EAAEQ,CAAC,EACbL,EAAM,EAAI8D,EAAEzD,CAAC,EACbL,EAAM,EAAI4M,EAAEvM,CAAC,EACbL,EAAM,QAAUmN,EAAQ9M,CAAC,EAClBL,EAAQ,EACrB,CACG,CACH,CAEA,MAAA4W,GAAerH,GAAIyB,EAAG,EACZ,IAAC6F,GAAUtH,GAAInD,CAAK,ECjB9B,SAAS+D,GAAUa,EAAK,CACtB,OAAQ,SAAS8F,EAAehX,EAAG,CACjCA,EAAI,CAACA,EAEL,SAASqQ,EAAUnQ,EAAOqR,EAAK,CAC7B,IAAIxR,EAAImR,GAAKhR,EAAQ+W,GAAe/W,CAAK,GAAG,GAAIqR,EAAM0F,GAAe1F,CAAG,GAAG,CAAC,EACxE3Q,EAAI0L,EAAMpM,EAAM,EAAGqR,EAAI,CAAC,EACxBzE,EAAIR,EAAMpM,EAAM,EAAGqR,EAAI,CAAC,EACxBlE,EAAUf,EAAMpM,EAAM,QAASqR,EAAI,OAAO,EAC9C,OAAO,SAAShR,EAAG,CACjB,OAAAL,EAAM,EAAIH,EAAEQ,CAAC,EACbL,EAAM,EAAIU,EAAEL,CAAC,EACbL,EAAM,EAAI4M,EAAE,KAAK,IAAIvM,EAAGP,CAAC,CAAC,EAC1BE,EAAM,QAAUmN,EAAQ9M,CAAC,EAClBL,EAAQ,EACvB,CACK,CAED,OAAAmQ,EAAU,MAAQ2G,EAEX3G,CACR,EAAE,CAAC,CACN,CAEA,MAAA6G,GAAe7G,GAAUa,EAAG,EAClB,IAACiG,GAAgB9G,GAAU/D,CAAK,EC1B3B,SAAS8K,GAAUnE,EAAa/U,EAAQ,CACjDA,IAAW,SAAWA,EAAS+U,EAAaA,EAAc7U,IAE9D,QADIL,EAAI,EAAGpB,EAAIuB,EAAO,OAAS,EAAGqD,EAAIrD,EAAO,CAAC,EAAGmZ,EAAI,IAAI,MAAM1a,EAAI,EAAI,EAAIA,CAAC,EACrEoB,EAAIpB,GAAG0a,EAAEtZ,CAAC,EAAIkV,EAAY1R,EAAGA,EAAIrD,EAAO,EAAEH,CAAC,CAAC,EACnD,OAAO,SAASwC,EAAG,CACjB,IAAIxC,EAAI,KAAK,IAAI,EAAG,KAAK,IAAIpB,EAAI,EAAG,KAAK,MAAM4D,GAAK5D,CAAC,CAAC,CAAC,EACvD,OAAO0a,EAAEtZ,CAAC,EAAEwC,EAAIxC,CAAC,CACrB,CACA,CCVe,SAAAuZ,GAASC,EAAc5a,EAAG,CAEvC,QADI6a,EAAU,IAAI,MAAM7a,CAAC,EAChBoB,EAAI,EAAGA,EAAIpB,EAAG,EAAEoB,EAAGyZ,EAAQzZ,CAAC,EAAIwZ,EAAaxZ,GAAKpB,EAAI,EAAE,EACjE,OAAO6a,CACT,CCJA,MAAMC,GAAK,KAAK,GACZC,GAAM,EAAID,GACVE,GAAU,KACVC,GAAaF,GAAMC,GAEvB,SAASE,GAAOC,EAAS,CACvB,KAAK,GAAKA,EAAQ,CAAC,EACnB,QAAS/Z,EAAI,EAAGpB,EAAImb,EAAQ,OAAQ/Z,EAAIpB,EAAG,EAAEoB,EAC3C,KAAK,GAAK,UAAUA,CAAC,EAAI+Z,EAAQ/Z,CAAC,CAEtC,CAEA,SAASga,GAAYC,EAAQ,CAC3B,IAAI1a,EAAI,KAAK,MAAM0a,CAAM,EACzB,GAAI,EAAE1a,GAAK,GAAI,MAAM,IAAI,MAAM,mBAAmB0a,CAAM,EAAE,EAC1D,GAAI1a,EAAI,GAAI,OAAOua,GACnB,MAAMlR,EAAI,IAAMrJ,EAChB,OAAO,SAASwa,EAAS,CACvB,KAAK,GAAKA,EAAQ,CAAC,EACnB,QAAS,EAAI,EAAGnb,EAAImb,EAAQ,OAAQ,EAAInb,EAAG,EAAE,EAC3C,KAAK,GAAK,KAAK,MAAM,UAAU,CAAC,EAAIgK,CAAC,EAAIA,EAAImR,EAAQ,CAAC,CAE5D,CACA,CAEO,MAAMG,EAAK,CAChB,YAAYD,EAAQ,CAClB,KAAK,IAAM,KAAK,IAChB,KAAK,IAAM,KAAK,IAAM,KACtB,KAAK,EAAI,GACT,KAAK,QAAUA,GAAU,KAAOH,GAASE,GAAYC,CAAM,CAC5D,CACD,OAAOza,EAAGyC,EAAG,CACX,KAAK,WAAW,KAAK,IAAM,KAAK,IAAM,CAACzC,CAAC,IAAI,KAAK,IAAM,KAAK,IAAM,CAACyC,CAAC,EACrE,CACD,WAAY,CACN,KAAK,MAAQ,OACf,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IACrC,KAAK,WAER,CACD,OAAOzC,EAAGyC,EAAG,CACX,KAAK,WAAW,KAAK,IAAM,CAACzC,CAAC,IAAI,KAAK,IAAM,CAACyC,CAAC,EAC/C,CACD,iBAAiBmG,EAAI+R,EAAI3a,EAAGyC,EAAG,CAC7B,KAAK,WAAW,CAACmG,CAAE,IAAI,CAAC+R,CAAE,IAAI,KAAK,IAAM,CAAC3a,CAAC,IAAI,KAAK,IAAM,CAACyC,CAAC,EAC7D,CACD,cAAcmG,EAAI+R,EAAIC,EAAIC,EAAI7a,EAAGyC,EAAG,CAClC,KAAK,WAAW,CAACmG,CAAE,IAAI,CAAC+R,CAAE,IAAI,CAACC,CAAE,IAAI,CAACC,CAAE,IAAI,KAAK,IAAM,CAAC7a,CAAC,IAAI,KAAK,IAAM,CAACyC,CAAC,EAC3E,CACD,MAAMmG,EAAI+R,EAAIC,EAAIC,EAAI5b,EAAG,CAIvB,GAHA2J,EAAK,CAACA,EAAI+R,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAI5b,EAAI,CAACA,EAGzCA,EAAI,EAAG,MAAM,IAAI,MAAM,oBAAoBA,CAAC,EAAE,EAElD,IAAI0J,EAAK,KAAK,IACVmS,EAAK,KAAK,IACVC,EAAMH,EAAKhS,EACXoS,EAAMH,EAAKF,EACXM,EAAMtS,EAAKC,EACXsS,EAAMJ,EAAKH,EACXQ,EAAQF,EAAMA,EAAMC,EAAMA,EAG9B,GAAI,KAAK,MAAQ,KACf,KAAK,WAAW,KAAK,IAAMtS,CAAE,IAAI,KAAK,IAAM+R,CAAE,WAIrCQ,EAAQf,GAKd,GAAI,EAAE,KAAK,IAAIc,EAAMH,EAAMC,EAAMC,CAAG,EAAIb,KAAY,CAACnb,EACxD,KAAK,WAAW,KAAK,IAAM2J,CAAE,IAAI,KAAK,IAAM+R,CAAE,OAI3C,CACH,IAAIS,EAAMR,EAAKjS,EACX0S,EAAMR,EAAKC,EACXQ,EAAQP,EAAMA,EAAMC,EAAMA,EAC1BO,EAAQH,EAAMA,EAAMC,EAAMA,EAC1BG,EAAM,KAAK,KAAKF,CAAK,EACrBG,EAAM,KAAK,KAAKN,CAAK,EACrB5L,EAAItQ,EAAI,KAAK,KAAKib,GAAK,KAAK,MAAMoB,EAAQH,EAAQI,IAAU,EAAIC,EAAMC,EAAI,GAAK,CAAC,EAChFC,EAAMnM,EAAIkM,EACVE,EAAMpM,EAAIiM,EAGV,KAAK,IAAIE,EAAM,CAAC,EAAItB,IACtB,KAAK,WAAWxR,EAAK8S,EAAMT,CAAG,IAAIN,EAAKe,EAAMR,CAAG,GAGlD,KAAK,WAAWjc,CAAC,IAAIA,CAAC,QAAQ,EAAEic,EAAME,EAAMH,EAAMI,EAAI,IAAI,KAAK,IAAMzS,EAAK+S,EAAMZ,CAAG,IAAI,KAAK,IAAMJ,EAAKgB,EAAMX,CAAG,EACjH,CACF,CACD,IAAIhb,EAAGyC,EAAG,EAAGmZ,EAAIC,EAAIC,EAAK,CAIxB,GAHA9b,EAAI,CAACA,EAAGyC,EAAI,CAACA,EAAG,EAAI,CAAC,EAAGqZ,EAAM,CAAC,CAACA,EAG5B,EAAI,EAAG,MAAM,IAAI,MAAM,oBAAoB,CAAC,EAAE,EAElD,IAAIzD,EAAK,EAAI,KAAK,IAAIuD,CAAE,EACpBtD,EAAK,EAAI,KAAK,IAAIsD,CAAE,EACpBjT,EAAK3I,EAAIqY,EACTyC,EAAKrY,EAAI6V,EACTyD,EAAK,EAAID,EACTE,EAAKF,EAAMF,EAAKC,EAAKA,EAAKD,EAG1B,KAAK,MAAQ,KACf,KAAK,WAAWjT,CAAE,IAAImS,CAAE,IAIjB,KAAK,IAAI,KAAK,IAAMnS,CAAE,EAAIyR,IAAW,KAAK,IAAI,KAAK,IAAMU,CAAE,EAAIV,KACtE,KAAK,WAAWzR,CAAE,IAAImS,CAAE,GAIrB,IAGDkB,EAAK,IAAGA,EAAKA,EAAK7B,GAAMA,IAGxB6B,EAAK3B,GACP,KAAK,WAAW,CAAC,IAAI,CAAC,QAAQ0B,CAAE,IAAI/b,EAAIqY,CAAE,IAAI5V,EAAI6V,CAAE,IAAI,CAAC,IAAI,CAAC,QAAQyD,CAAE,IAAI,KAAK,IAAMpT,CAAE,IAAI,KAAK,IAAMmS,CAAE,GAInGkB,EAAK5B,IACZ,KAAK,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE4B,GAAM9B,GAAG,IAAI6B,CAAE,IAAI,KAAK,IAAM/b,EAAI,EAAI,KAAK,IAAI6b,CAAE,CAAC,IAAI,KAAK,IAAMpZ,EAAI,EAAI,KAAK,IAAIoZ,CAAE,CAAC,GAEtH,CACD,KAAK7b,EAAGyC,EAAGF,EAAGC,EAAG,CACf,KAAK,WAAW,KAAK,IAAM,KAAK,IAAM,CAACxC,CAAC,IAAI,KAAK,IAAM,KAAK,IAAM,CAACyC,CAAC,IAAIF,EAAI,CAACA,CAAC,IAAI,CAACC,CAAC,IAAI,CAACD,CAAC,GAC3F,CACD,UAAW,CACT,OAAO,KAAK,CACb,CACH,CAEO,SAAS0Z,IAAO,CACrB,OAAO,IAAIvB,EACb,CAGAuB,GAAK,UAAYvB,GAAK,UAEf,SAASwB,GAAUzB,EAAS,EAAG,CACpC,OAAO,IAAIC,GAAK,CAACD,CAAM,CACzB,CC3Je,SAAQ0B,GAACnc,EAAG,CACzB,OAAO,KAAK,IAAIA,EAAI,KAAK,MAAMA,CAAC,CAAC,GAAK,KAChCA,EAAE,eAAe,IAAI,EAAE,QAAQ,KAAM,EAAE,EACvCA,EAAE,SAAS,EAAE,CACrB,CAKO,SAASoc,GAAmBpc,EAAGwE,EAAG,CACvC,IAAKhE,GAAKR,EAAIwE,EAAIxE,EAAE,cAAcwE,EAAI,CAAC,EAAIxE,EAAE,cAAa,GAAI,QAAQ,GAAG,GAAK,EAAG,OAAO,KACxF,IAAIQ,EAAG6b,EAAcrc,EAAE,MAAM,EAAGQ,CAAC,EAIjC,MAAO,CACL6b,EAAY,OAAS,EAAIA,EAAY,CAAC,EAAIA,EAAY,MAAM,CAAC,EAAIA,EACjE,CAACrc,EAAE,MAAMQ,EAAI,CAAC,CAClB,CACA,CCjBe,SAAQ8b,GAACtc,EAAG,CACzB,OAAOA,EAAIoc,GAAmB,KAAK,IAAIpc,CAAC,CAAC,EAAGA,EAAIA,EAAE,CAAC,EAAI,GACzD,CCJe,SAAAuc,GAASC,EAAUC,EAAW,CAC3C,OAAO,SAAS5b,EAAOkB,EAAO,CAO5B,QANI,EAAIlB,EAAM,OACVmC,EAAI,CAAE,EACNI,EAAI,EACJwC,EAAI4W,EAAS,CAAC,EACdnb,EAAS,EAEN,EAAI,GAAKuE,EAAI,IACdvE,EAASuE,EAAI,EAAI7D,IAAO6D,EAAI,KAAK,IAAI,EAAG7D,EAAQV,CAAM,GAC1D2B,EAAE,KAAKnC,EAAM,UAAU,GAAK+E,EAAG,EAAIA,CAAC,CAAC,EAChC,GAAAvE,GAAUuE,EAAI,GAAK7D,KACxB6D,EAAI4W,EAASpZ,GAAKA,EAAI,GAAKoZ,EAAS,MAAM,EAG5C,OAAOxZ,EAAE,QAAO,EAAG,KAAKyZ,CAAS,CACrC,CACA,CCjBe,SAAQC,GAACC,EAAU,CAChC,OAAO,SAAS9b,EAAO,CACrB,OAAOA,EAAM,QAAQ,SAAU,SAASL,EAAG,CACzC,OAAOmc,EAAS,CAACnc,CAAC,CACxB,CAAK,CACL,CACA,CCLA,IAAIoc,GAAK,2EAEM,SAASC,GAAgBC,EAAW,CACjD,GAAI,EAAEC,EAAQH,GAAG,KAAKE,CAAS,GAAI,MAAM,IAAI,MAAM,mBAAqBA,CAAS,EACjF,IAAIC,EACJ,OAAO,IAAIC,GAAgB,CACzB,KAAMD,EAAM,CAAC,EACb,MAAOA,EAAM,CAAC,EACd,KAAMA,EAAM,CAAC,EACb,OAAQA,EAAM,CAAC,EACf,KAAMA,EAAM,CAAC,EACb,MAAOA,EAAM,CAAC,EACd,MAAOA,EAAM,CAAC,EACd,UAAWA,EAAM,CAAC,GAAKA,EAAM,CAAC,EAAE,MAAM,CAAC,EACvC,KAAMA,EAAM,CAAC,EACb,KAAMA,EAAM,EAAE,CAClB,CAAG,CACH,CAEAF,GAAgB,UAAYG,GAAgB,UAErC,SAASA,GAAgBF,EAAW,CACzC,KAAK,KAAOA,EAAU,OAAS,OAAY,IAAMA,EAAU,KAAO,GAClE,KAAK,MAAQA,EAAU,QAAU,OAAY,IAAMA,EAAU,MAAQ,GACrE,KAAK,KAAOA,EAAU,OAAS,OAAY,IAAMA,EAAU,KAAO,GAClE,KAAK,OAASA,EAAU,SAAW,OAAY,GAAKA,EAAU,OAAS,GACvE,KAAK,KAAO,CAAC,CAACA,EAAU,KACxB,KAAK,MAAQA,EAAU,QAAU,OAAY,OAAY,CAACA,EAAU,MACpE,KAAK,MAAQ,CAAC,CAACA,EAAU,MACzB,KAAK,UAAYA,EAAU,YAAc,OAAY,OAAY,CAACA,EAAU,UAC5E,KAAK,KAAO,CAAC,CAACA,EAAU,KACxB,KAAK,KAAOA,EAAU,OAAS,OAAY,GAAKA,EAAU,KAAO,EACnE,CAEAE,GAAgB,UAAU,SAAW,UAAW,CAC9C,OAAO,KAAK,KACN,KAAK,MACL,KAAK,KACL,KAAK,QACJ,KAAK,KAAO,IAAM,KAClB,KAAK,QAAU,OAAY,GAAK,KAAK,IAAI,EAAG,KAAK,MAAQ,CAAC,IAC1D,KAAK,MAAQ,IAAM,KACnB,KAAK,YAAc,OAAY,GAAK,IAAM,KAAK,IAAI,EAAG,KAAK,UAAY,CAAC,IACxE,KAAK,KAAO,IAAM,IACnB,KAAK,IACb,EC7Ce,SAAQC,GAAC5Z,EAAG,CACzB8J,EAAK,QAAS/N,EAAIiE,EAAE,OAAQ7C,EAAI,EAAGwJ,EAAK,GAAIrC,EAAInH,EAAIpB,EAAG,EAAEoB,EACvD,OAAQ6C,EAAE7C,CAAC,EAAC,CACV,IAAK,IAAKwJ,EAAKrC,EAAKnH,EAAG,MACvB,IAAK,IAASwJ,IAAO,IAAGA,EAAKxJ,GAAGmH,EAAKnH,EAAG,MACxC,QAAS,GAAI,CAAC,CAAC6C,EAAE7C,CAAC,EAAG,MAAM2M,EAASnD,EAAK,IAAGA,EAAK,GAAG,KACrD,CAEH,OAAOA,EAAK,EAAI3G,EAAE,MAAM,EAAG2G,CAAE,EAAI3G,EAAE,MAAMsE,EAAK,CAAC,EAAItE,CACrD,CCRO,IAAI6Z,GAEI,SAAAC,GAASnd,EAAGwE,EAAG,CAC5B,IAAIzE,EAAIqc,GAAmBpc,EAAGwE,CAAC,EAC/B,GAAI,CAACzE,EAAG,OAAOC,EAAI,GACnB,IAAIqc,EAActc,EAAE,CAAC,EACjBuc,EAAWvc,EAAE,CAAC,EACdS,EAAI8b,GAAYY,GAAiB,KAAK,IAAI,GAAI,KAAK,IAAI,EAAG,KAAK,MAAMZ,EAAW,CAAC,CAAC,CAAC,EAAI,GAAK,EAC5Fld,EAAIid,EAAY,OACpB,OAAO7b,IAAMpB,EAAIid,EACX7b,EAAIpB,EAAIid,EAAc,IAAI,MAAM7b,EAAIpB,EAAI,CAAC,EAAE,KAAK,GAAG,EACnDoB,EAAI,EAAI6b,EAAY,MAAM,EAAG7b,CAAC,EAAI,IAAM6b,EAAY,MAAM7b,CAAC,EAC3D,KAAO,IAAI,MAAM,EAAIA,CAAC,EAAE,KAAK,GAAG,EAAI4b,GAAmBpc,EAAG,KAAK,IAAI,EAAGwE,EAAIhE,EAAI,CAAC,CAAC,EAAE,CAAC,CAC3F,CCbe,SAAA4c,GAASpd,EAAGwE,EAAG,CAC5B,IAAIzE,EAAIqc,GAAmBpc,EAAGwE,CAAC,EAC/B,GAAI,CAACzE,EAAG,OAAOC,EAAI,GACnB,IAAIqc,EAActc,EAAE,CAAC,EACjBuc,EAAWvc,EAAE,CAAC,EAClB,OAAOuc,EAAW,EAAI,KAAO,IAAI,MAAM,CAACA,CAAQ,EAAE,KAAK,GAAG,EAAID,EACxDA,EAAY,OAASC,EAAW,EAAID,EAAY,MAAM,EAAGC,EAAW,CAAC,EAAI,IAAMD,EAAY,MAAMC,EAAW,CAAC,EAC7GD,EAAc,IAAI,MAAMC,EAAWD,EAAY,OAAS,CAAC,EAAE,KAAK,GAAG,CAC3E,CCNA,MAAegB,GAAA,CACb,IAAK,CAACrd,EAAGwE,KAAOxE,EAAI,KAAK,QAAQwE,CAAC,EAClC,EAAMxE,GAAM,KAAK,MAAMA,CAAC,EAAE,SAAS,CAAC,EACpC,EAAMA,GAAMA,EAAI,GAChB,EAAKmc,GACL,EAAK,CAACnc,EAAGwE,IAAMxE,EAAE,cAAcwE,CAAC,EAChC,EAAK,CAACxE,EAAGwE,IAAMxE,EAAE,QAAQwE,CAAC,EAC1B,EAAK,CAACxE,EAAGwE,IAAMxE,EAAE,YAAYwE,CAAC,EAC9B,EAAMxE,GAAM,KAAK,MAAMA,CAAC,EAAE,SAAS,CAAC,EACpC,EAAK,CAACA,EAAGwE,IAAM4Y,GAAcpd,EAAI,IAAKwE,CAAC,EACvC,EAAK4Y,GACL,EAAKD,GACL,EAAMnd,GAAM,KAAK,MAAMA,CAAC,EAAE,SAAS,EAAE,EAAE,YAAa,EACpD,EAAMA,GAAM,KAAK,MAAMA,CAAC,EAAE,SAAS,EAAE,CACvC,EClBe,SAAQsd,GAACtd,EAAG,CACzB,OAAOA,CACT,CCOA,IAAImG,GAAM,MAAM,UAAU,IACtBoX,GAAW,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,EAEnE,SAAQC,GAACC,EAAQ,CAC9B,IAAIlY,EAAQkY,EAAO,WAAa,QAAaA,EAAO,YAAc,OAAYnY,GAAWiX,GAAYpW,GAAI,KAAKsX,EAAO,SAAU,MAAM,EAAGA,EAAO,UAAY,EAAE,EACzJC,EAAiBD,EAAO,WAAa,OAAY,GAAKA,EAAO,SAAS,CAAC,EAAI,GAC3EE,EAAiBF,EAAO,WAAa,OAAY,GAAKA,EAAO,SAAS,CAAC,EAAI,GAC3EG,EAAUH,EAAO,UAAY,OAAY,IAAMA,EAAO,QAAU,GAChEd,EAAWc,EAAO,WAAa,OAAYnY,GAAWoX,GAAevW,GAAI,KAAKsX,EAAO,SAAU,MAAM,CAAC,EACtGI,EAAUJ,EAAO,UAAY,OAAY,IAAMA,EAAO,QAAU,GAChEK,EAAQL,EAAO,QAAU,OAAY,IAAMA,EAAO,MAAQ,GAC1DM,EAAMN,EAAO,MAAQ,OAAY,MAAQA,EAAO,IAAM,GAE1D,SAASO,EAAUlB,EAAW,CAC5BA,EAAYD,GAAgBC,CAAS,EAErC,IAAImB,EAAOnB,EAAU,KACjBoB,EAAQpB,EAAU,MAClBqB,EAAOrB,EAAU,KACjBsB,EAAStB,EAAU,OACnB7c,EAAO6c,EAAU,KACjB/a,EAAQ+a,EAAU,MAClBuB,EAAQvB,EAAU,MAClBwB,EAAYxB,EAAU,UACtByB,EAAOzB,EAAU,KACjB0B,EAAO1B,EAAU,KAGjB0B,IAAS,KAAKH,EAAQ,GAAMG,EAAO,KAG7BnB,GAAYmB,CAAI,IAAGF,IAAc,SAAcA,EAAY,IAAKC,EAAO,GAAMC,EAAO,MAG1Fve,GAASge,IAAS,KAAOC,IAAU,OAAMje,EAAO,GAAMge,EAAO,IAAKC,EAAQ,KAI9E,IAAIO,EAASL,IAAW,IAAMV,EAAiBU,IAAW,KAAO,SAAS,KAAKI,CAAI,EAAI,IAAMA,EAAK,YAAa,EAAG,GAC9GE,EAASN,IAAW,IAAMT,EAAiB,OAAO,KAAKa,CAAI,EAAIX,EAAU,GAKzEc,EAAatB,GAAYmB,CAAI,EAC7BI,EAAc,aAAa,KAAKJ,CAAI,EAMxCF,EAAYA,IAAc,OAAY,EAChC,SAAS,KAAKE,CAAI,EAAI,KAAK,IAAI,EAAG,KAAK,IAAI,GAAIF,CAAS,CAAC,EACzD,KAAK,IAAI,EAAG,KAAK,IAAI,GAAIA,CAAS,CAAC,EAEzC,SAAShP,EAAOzO,EAAO,CACrB,IAAIge,EAAcJ,EACdK,EAAcJ,EACdle,EAAGpB,GAAGqH,GAEV,GAAI+X,IAAS,IACXM,EAAcH,EAAW9d,CAAK,EAAIie,EAClCje,EAAQ,OACH,CACLA,EAAQ,CAACA,EAGT,IAAIke,EAAgBle,EAAQ,GAAK,EAAIA,EAAQ,EAiB7C,GAdAA,EAAQ,MAAMA,CAAK,EAAIkd,EAAMY,EAAW,KAAK,IAAI9d,CAAK,EAAGyd,CAAS,EAG9DC,IAAM1d,EAAQoc,GAAWpc,CAAK,GAG9Bke,GAAiB,CAACle,GAAU,GAAKsd,IAAS,MAAKY,EAAgB,IAGnEF,GAAeE,EAAiBZ,IAAS,IAAMA,EAAOL,EAASK,IAAS,KAAOA,IAAS,IAAM,GAAKA,GAAQU,EAC3GC,GAAeN,IAAS,IAAMjB,GAAS,EAAIL,GAAiB,CAAC,EAAI,IAAM4B,GAAeC,GAAiBZ,IAAS,IAAM,IAAM,IAIxHS,GAEF,IADApe,EAAI,GAAIpB,GAAIyB,EAAM,OACX,EAAEL,EAAIpB,IACX,GAAIqH,GAAI5F,EAAM,WAAWL,CAAC,EAAG,GAAKiG,IAAKA,GAAI,GAAI,CAC7CqY,GAAerY,KAAM,GAAKmX,EAAU/c,EAAM,MAAML,EAAI,CAAC,EAAIK,EAAM,MAAML,CAAC,GAAKse,EAC3Eje,EAAQA,EAAM,MAAM,EAAGL,CAAC,EACxB,KACD,EAGN,CAGG6d,GAAS,CAACpe,IAAMY,EAAQ0E,EAAM1E,EAAO,GAAQ,GAGjD,IAAIQ,EAASwd,EAAY,OAAShe,EAAM,OAASie,EAAY,OACzDE,EAAU3d,EAASU,EAAQ,IAAI,MAAMA,EAAQV,EAAS,CAAC,EAAE,KAAK4c,CAAI,EAAI,GAM1E,OAHII,GAASpe,IAAMY,EAAQ0E,EAAMyZ,EAAUne,EAAOme,EAAQ,OAASjd,EAAQ+c,EAAY,OAAS,GAAQ,EAAGE,EAAU,IAG7Gd,EAAK,CACX,IAAK,IAAKrd,EAAQge,EAAche,EAAQie,EAAcE,EAAS,MAC/D,IAAK,IAAKne,EAAQge,EAAcG,EAAUne,EAAQie,EAAa,MAC/D,IAAK,IAAKje,EAAQme,EAAQ,MAAM,EAAG3d,EAAS2d,EAAQ,QAAU,CAAC,EAAIH,EAAche,EAAQie,EAAcE,EAAQ,MAAM3d,CAAM,EAAG,MAC9H,QAASR,EAAQme,EAAUH,EAAche,EAAQie,EAAa,KAC/D,CAED,OAAOnC,EAAS9b,CAAK,CACtB,CAED,OAAAyO,EAAO,SAAW,UAAW,CAC3B,OAAOwN,EAAY,EACzB,EAEWxN,CACR,CAED,SAAS2P,EAAanC,EAAWjc,EAAO,CACtC,IAAI1B,EAAI6e,GAAWlB,EAAYD,GAAgBC,CAAS,EAAGA,EAAU,KAAO,IAAKA,EAAW,EACxF5d,EAAI,KAAK,IAAI,GAAI,KAAK,IAAI,EAAG,KAAK,MAAMod,GAASzb,CAAK,EAAI,CAAC,CAAC,CAAC,EAAI,EACjEuI,EAAI,KAAK,IAAI,GAAI,CAAClK,CAAC,EACnBuf,EAASlB,GAAS,EAAIre,EAAI,CAAC,EAC/B,OAAO,SAAS2B,EAAO,CACrB,OAAO1B,EAAEiK,EAAIvI,CAAK,EAAI4d,CAC5B,CACG,CAED,MAAO,CACL,OAAQT,EACR,aAAciB,CAClB,CACA,CCjJA,IAAIxB,GACOnO,GACA2P,GAEXC,GAAc,CACZ,UAAW,IACX,SAAU,CAAC,CAAC,EACZ,SAAU,CAAC,IAAK,EAAE,CACpB,CAAC,EAEc,SAASA,GAAclR,EAAY,CAChDyP,OAAAA,GAAS0B,GAAanR,CAAU,EAChCsB,GAASmO,GAAO,OAChBwB,GAAexB,GAAO,aACfA,EACT,CCfe,SAAQ2B,GAACvc,EAAM,CAC5B,OAAO,KAAK,IAAI,EAAG,CAACyZ,GAAS,KAAK,IAAIzZ,CAAI,CAAC,CAAC,CAC9C,CCFe,SAAAwc,GAASxc,EAAMhC,EAAO,CACnC,OAAO,KAAK,IAAI,EAAG,KAAK,IAAI,GAAI,KAAK,IAAI,EAAG,KAAK,MAAMyb,GAASzb,CAAK,EAAI,CAAC,CAAC,CAAC,EAAI,EAAIyb,GAAS,KAAK,IAAIzZ,CAAI,CAAC,CAAC,CAC9G,CCFe,SAAAyc,GAASzc,EAAMyB,EAAK,CACjC,OAAAzB,EAAO,KAAK,IAAIA,CAAI,EAAGyB,EAAM,KAAK,IAAIA,CAAG,EAAIzB,EACtC,KAAK,IAAI,EAAGyZ,GAAShY,CAAG,EAAIgY,GAASzZ,CAAI,CAAC,EAAI,CACvD,CCLO,SAAS0c,GAAUjX,EAAQ8C,EAAO,CACvC,OAAQ,UAAU,OAAM,CACtB,IAAK,GAAG,MACR,IAAK,GAAG,KAAK,MAAM9C,CAAM,EAAG,MAC5B,QAAS,KAAK,MAAM8C,CAAK,EAAE,OAAO9C,CAAM,EAAG,KAC5C,CACD,OAAO,IACT,CAEO,SAASkX,GAAiBlX,EAAQ0R,EAAc,CACrD,OAAQ,UAAU,OAAM,CACtB,IAAK,GAAG,MACR,IAAK,GAAG,CACF,OAAO1R,GAAW,WAAY,KAAK,aAAaA,CAAM,EACrD,KAAK,MAAMA,CAAM,EACtB,KACD,CACD,QAAS,CACP,KAAK,OAAOA,CAAM,EACd,OAAO0R,GAAiB,WAAY,KAAK,aAAaA,CAAY,EACjE,KAAK,MAAMA,CAAY,EAC5B,KACD,CACF,CACD,OAAO,IACT,CCtBY,MAACyF,GAAW,OAAO,UAAU,EAE1B,SAASC,IAAU,CAChC,IAAI5e,EAAQ,IAAI8D,GACZ0D,EAAS,CAAE,EACX8C,EAAQ,CAAE,EACVuU,EAAUF,GAEd,SAASvI,EAAMnX,EAAG,CAChB,IAAIS,EAAIM,EAAM,IAAIf,CAAC,EACnB,GAAIS,IAAM,OAAW,CACnB,GAAImf,IAAYF,GAAU,OAAOE,EACjC7e,EAAM,IAAIf,EAAGS,EAAI8H,EAAO,KAAKvI,CAAC,EAAI,CAAC,CACpC,CACD,OAAOqL,EAAM5K,EAAI4K,EAAM,MAAM,CAC9B,CAED,OAAA8L,EAAM,OAAS,SAASlO,EAAG,CACzB,GAAI,CAAC,UAAU,OAAQ,OAAOV,EAAO,MAAK,EAC1CA,EAAS,CAAE,EAAExH,EAAQ,IAAI8D,GACzB,UAAW/D,KAASmI,EACdlI,EAAM,IAAID,CAAK,GACnBC,EAAM,IAAID,EAAOyH,EAAO,KAAKzH,CAAK,EAAI,CAAC,EAEzC,OAAOqW,CACX,EAEEA,EAAM,MAAQ,SAASlO,EAAG,CACxB,OAAO,UAAU,QAAUoC,EAAQ,MAAM,KAAKpC,CAAC,EAAGkO,GAAS9L,EAAM,OACrE,EAEE8L,EAAM,QAAU,SAASlO,EAAG,CAC1B,OAAO,UAAU,QAAU2W,EAAU3W,EAAGkO,GAASyI,CACrD,EAEEzI,EAAM,KAAO,UAAW,CACtB,OAAOwI,GAAQpX,EAAQ8C,CAAK,EAAE,QAAQuU,CAAO,CACjD,EAEEJ,GAAU,MAAMrI,EAAO,SAAS,EAEzBA,CACT,CCzCe,SAAS0I,IAAO,CAC7B,IAAI1I,EAAQwI,KAAU,QAAQ,MAAS,EACnCpX,EAAS4O,EAAM,OACf2I,EAAe3I,EAAM,MACrByB,EAAK,EACLC,EAAK,EACL/V,EACAid,EACAC,EAAQ,GACRC,EAAe,EACfC,EAAe,EACf/B,EAAQ,GAEZ,OAAOhH,EAAM,QAEb,SAASgJ,GAAU,CACjB,IAAI9gB,EAAIkJ,EAAM,EAAG,OACbP,EAAU6Q,EAAKD,EACfhW,EAAQoF,EAAU6Q,EAAKD,EACvB/V,EAAOmF,EAAU4Q,EAAKC,EAC1B/V,GAAQD,EAAOD,GAAS,KAAK,IAAI,EAAGvD,EAAI4gB,EAAeC,EAAe,CAAC,EACnEF,IAAOld,EAAO,KAAK,MAAMA,CAAI,GACjCF,IAAUC,EAAOD,EAAQE,GAAQzD,EAAI4gB,IAAiB9B,EACtD4B,EAAYjd,GAAQ,EAAImd,GACpBD,IAAOpd,EAAQ,KAAK,MAAMA,CAAK,EAAGmd,EAAY,KAAK,MAAMA,CAAS,GACtE,IAAInf,EAASwf,GAAS/gB,CAAC,EAAE,IAAI,SAASoB,EAAG,CAAE,OAAOmC,EAAQE,EAAOrC,CAAI,CAAA,EACrE,OAAOqf,EAAa9X,EAAUpH,EAAO,QAAS,EAAGA,CAAM,CACxD,CAED,OAAAuW,EAAM,OAAS,SAASlO,EAAG,CACzB,OAAO,UAAU,QAAUV,EAAOU,CAAC,EAAGkX,EAAO,GAAM5X,GACvD,EAEE4O,EAAM,MAAQ,SAASlO,EAAG,CACxB,OAAO,UAAU,QAAU,CAAC2P,EAAIC,CAAE,EAAI5P,EAAG2P,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAIsH,EAAS,GAAI,CAACvH,EAAIC,CAAE,CACrF,EAEE1B,EAAM,WAAa,SAASlO,EAAG,CAC7B,MAAO,CAAC2P,EAAIC,CAAE,EAAI5P,EAAG2P,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAImH,EAAQ,GAAMG,EAAO,CAClE,EAEEhJ,EAAM,UAAY,UAAW,CAC3B,OAAO4I,CACX,EAEE5I,EAAM,KAAO,UAAW,CACtB,OAAOrU,CACX,EAEEqU,EAAM,MAAQ,SAASlO,EAAG,CACxB,OAAO,UAAU,QAAU+W,EAAQ,CAAC,CAAC/W,EAAGkX,EAAS,GAAIH,CACzD,EAEE7I,EAAM,QAAU,SAASlO,EAAG,CAC1B,OAAO,UAAU,QAAUgX,EAAe,KAAK,IAAI,EAAGC,EAAe,CAACjX,CAAC,EAAGkX,EAAO,GAAMF,CAC3F,EAEE9I,EAAM,aAAe,SAASlO,EAAG,CAC/B,OAAO,UAAU,QAAUgX,EAAe,KAAK,IAAI,EAAGhX,CAAC,EAAGkX,EAAS,GAAIF,CAC3E,EAEE9I,EAAM,aAAe,SAASlO,EAAG,CAC/B,OAAO,UAAU,QAAUiX,EAAe,CAACjX,EAAGkX,EAAS,GAAID,CAC/D,EAEE/I,EAAM,MAAQ,SAASlO,EAAG,CACxB,OAAO,UAAU,QAAUkV,EAAQ,KAAK,IAAI,EAAG,KAAK,IAAI,EAAGlV,CAAC,CAAC,EAAGkX,EAAO,GAAMhC,CACjF,EAEEhH,EAAM,KAAO,UAAW,CACtB,OAAO0I,GAAKtX,EAAM,EAAI,CAACqQ,EAAIC,CAAE,CAAC,EACzB,MAAMmH,CAAK,EACX,aAAaC,CAAY,EACzB,aAAaC,CAAY,EACzB,MAAM/B,CAAK,CACpB,EAESqB,GAAU,MAAMW,EAAS,EAAE,SAAS,CAC7C,CAEA,SAASE,GAASlJ,EAAO,CACvB,IAAImJ,EAAOnJ,EAAM,KAEjB,OAAAA,EAAM,QAAUA,EAAM,aACtB,OAAOA,EAAM,aACb,OAAOA,EAAM,aAEbA,EAAM,KAAO,UAAW,CACtB,OAAOkJ,GAASC,EAAI,CAAE,CAC1B,EAESnJ,CACT,CAEO,SAASoJ,IAAQ,CACtB,OAAOF,GAASR,GAAK,MAAM,KAAM,SAAS,EAAE,aAAa,CAAC,CAAC,CAC7D,CCpGe,SAASW,GAAUvgB,EAAG,CACnC,OAAO,UAAW,CAChB,OAAOA,CACX,CACA,CCJe,SAASS,GAAOT,EAAG,CAChC,MAAO,CAACA,CACV,CCGA,IAAIwgB,GAAO,CAAC,EAAG,CAAC,EAET,SAASlb,EAAStF,EAAG,CAC1B,OAAOA,CACT,CAEA,SAASygB,GAAUjhB,EAAGC,EAAG,CACvB,OAAQA,GAAMD,EAAI,CAACA,GACb,SAASQ,EAAG,CAAE,OAAQA,EAAIR,GAAKC,CAAI,EACnC0H,GAAS,MAAM1H,CAAC,EAAI,IAAM,EAAG,CACrC,CAEA,SAASihB,GAAQlhB,EAAGC,EAAG,CACrB,IAAIuD,EACJ,OAAIxD,EAAIC,IAAGuD,EAAIxD,EAAGA,EAAIC,EAAGA,EAAIuD,GACtB,SAAShD,EAAG,CAAE,OAAO,KAAK,IAAIR,EAAG,KAAK,IAAIC,EAAGO,CAAC,CAAC,CAAE,CAC1D,CAIA,SAAS2gB,GAAMrY,EAAQ8C,EAAOsK,EAAa,CACzC,IAAIkL,EAAKtY,EAAO,CAAC,EAAGkQ,EAAKlQ,EAAO,CAAC,EAAGqQ,EAAKvN,EAAM,CAAC,EAAGwN,EAAKxN,EAAM,CAAC,EAC/D,OAAIoN,EAAKoI,GAAIA,EAAKH,GAAUjI,EAAIoI,CAAE,EAAGjI,EAAKjD,EAAYkD,EAAID,CAAE,IACvDiI,EAAKH,GAAUG,EAAIpI,CAAE,EAAGG,EAAKjD,EAAYiD,EAAIC,CAAE,GAC7C,SAAS5Y,EAAG,CAAE,OAAO2Y,EAAGiI,EAAG5gB,CAAC,CAAC,EACtC,CAEA,SAAS6gB,GAAQvY,EAAQ8C,EAAOsK,EAAa,CAC3C,IAAItS,EAAI,KAAK,IAAIkF,EAAO,OAAQ8C,EAAM,MAAM,EAAI,EAC5CrL,EAAI,IAAI,MAAMqD,CAAC,EACfnE,EAAI,IAAI,MAAMmE,CAAC,EACf5C,EAAI,GAQR,IALI8H,EAAOlF,CAAC,EAAIkF,EAAO,CAAC,IACtBA,EAASA,EAAO,MAAO,EAAC,QAAO,EAC/B8C,EAAQA,EAAM,MAAO,EAAC,QAAO,GAGxB,EAAE5K,EAAI4C,GACXrD,EAAES,CAAC,EAAIigB,GAAUnY,EAAO9H,CAAC,EAAG8H,EAAO9H,EAAI,CAAC,CAAC,EACzCvB,EAAEuB,CAAC,EAAIkV,EAAYtK,EAAM5K,CAAC,EAAG4K,EAAM5K,EAAI,CAAC,CAAC,EAG3C,OAAO,SAASR,EAAG,CACjB,IAAIQ,EAAIW,GAAOmH,EAAQtI,EAAG,EAAGoD,CAAC,EAAI,EAClC,OAAOnE,EAAEuB,CAAC,EAAET,EAAES,CAAC,EAAER,CAAC,CAAC,CACvB,CACA,CAEO,SAASqgB,GAAK/Z,EAAQwa,EAAQ,CACnC,OAAOA,EACF,OAAOxa,EAAO,QAAQ,EACtB,MAAMA,EAAO,OAAO,EACpB,YAAYA,EAAO,aAAa,EAChC,MAAMA,EAAO,OAAO,EACpB,QAAQA,EAAO,QAAO,CAAE,CAC/B,CAEO,SAASya,IAAc,CAC5B,IAAIzY,EAASkY,GACTpV,EAAQoV,GACR9K,EAAcsL,GACdC,EACAC,EACAvB,EACAwB,EAAQ7b,EACRuU,EACAuH,EACAC,EAEJ,SAASnB,GAAU,CACjB,IAAI9gB,EAAI,KAAK,IAAIkJ,EAAO,OAAQ8C,EAAM,MAAM,EAC5C,OAAI+V,IAAU7b,IAAU6b,EAAQT,GAAQpY,EAAO,CAAC,EAAGA,EAAOlJ,EAAI,CAAC,CAAC,GAChEya,EAAYza,EAAI,EAAIyhB,GAAUF,GAC9BS,EAASC,EAAQ,KACVnK,CACR,CAED,SAASA,EAAMlX,EAAG,CAChB,OAAOA,GAAK,MAAQ,MAAMA,EAAI,CAACA,CAAC,EAAI2f,GAAWyB,IAAWA,EAASvH,EAAUvR,EAAO,IAAI2Y,CAAS,EAAG7V,EAAOsK,CAAW,IAAIuL,EAAUE,EAAMnhB,CAAC,CAAC,CAAC,CAC9I,CAED,OAAAkX,EAAM,OAAS,SAASzU,EAAG,CACzB,OAAO0e,EAAMD,GAAaG,IAAUA,EAAQxH,EAAUzO,EAAO9C,EAAO,IAAI2Y,CAAS,EAAGlM,EAAiB,IAAItS,CAAC,CAAC,CAAC,CAChH,EAEEyU,EAAM,OAAS,SAASlO,EAAG,CACzB,OAAO,UAAU,QAAUV,EAAS,MAAM,KAAKU,EAAGvI,EAAM,EAAGyf,EAAO,GAAM5X,EAAO,MAAK,CACxF,EAEE4O,EAAM,MAAQ,SAASlO,EAAG,CACxB,OAAO,UAAU,QAAUoC,EAAQ,MAAM,KAAKpC,CAAC,EAAGkX,EAAO,GAAM9U,EAAM,MAAK,CAC9E,EAEE8L,EAAM,WAAa,SAASlO,EAAG,CAC7B,OAAOoC,EAAQ,MAAM,KAAKpC,CAAC,EAAG0M,EAAcI,GAAkBoK,GAClE,EAEEhJ,EAAM,MAAQ,SAASlO,EAAG,CACxB,OAAO,UAAU,QAAUmY,EAAQnY,EAAI,GAAO1D,EAAU4a,EAAO,GAAMiB,IAAU7b,CACnF,EAEE4R,EAAM,YAAc,SAASlO,EAAG,CAC9B,OAAO,UAAU,QAAU0M,EAAc1M,EAAGkX,EAAS,GAAIxK,CAC7D,EAEEwB,EAAM,QAAU,SAASlO,EAAG,CAC1B,OAAO,UAAU,QAAU2W,EAAU3W,EAAGkO,GAASyI,CACrD,EAES,SAAS3c,EAAG8V,EAAG,CACpB,OAAAmI,EAAYje,EAAGke,EAAcpI,EACtBoH,EAAO,CAClB,CACA,CAEe,SAASoB,IAAa,CACnC,OAAOP,GAAa,EAACzb,EAAUA,CAAQ,CACzC,CCzHe,SAASic,GAAW5e,EAAOC,EAAMU,EAAOwZ,EAAW,CAChE,IAAIja,EAAOoF,GAAStF,EAAOC,EAAMU,CAAK,EAClCgb,EAEJ,OADAxB,EAAYD,GAAgBC,GAAoB,IAAgB,EACxDA,EAAU,KAAI,CACpB,IAAK,IAAK,CACR,IAAIjc,EAAQ,KAAK,IAAI,KAAK,IAAI8B,CAAK,EAAG,KAAK,IAAIC,CAAI,CAAC,EACpD,OAAIka,EAAU,WAAa,MAAQ,CAAC,MAAMwB,EAAYe,GAAgBxc,EAAMhC,CAAK,CAAC,IAAGic,EAAU,UAAYwB,GACpGW,GAAanC,EAAWjc,CAAK,CACrC,CACD,IAAK,GACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IAAK,CACJic,EAAU,WAAa,MAAQ,CAAC,MAAMwB,EAAYgB,GAAezc,EAAM,KAAK,IAAI,KAAK,IAAIF,CAAK,EAAG,KAAK,IAAIC,CAAI,CAAC,CAAC,CAAC,IAAGka,EAAU,UAAYwB,GAAaxB,EAAU,OAAS,MAC9K,KACD,CACD,IAAK,IACL,IAAK,IAAK,CACJA,EAAU,WAAa,MAAQ,CAAC,MAAMwB,EAAYc,GAAevc,CAAI,CAAC,IAAGia,EAAU,UAAYwB,GAAaxB,EAAU,OAAS,KAAO,GAC1I,KACD,CACF,CACD,OAAOxN,GAAOwN,CAAS,CACzB,CCvBO,SAAS0E,GAAUtK,EAAO,CAC/B,IAAI5O,EAAS4O,EAAM,OAEnB,OAAAA,EAAM,MAAQ,SAAS5T,EAAO,CAC5B,IAAIvD,EAAIuI,IACR,OAAOR,GAAM/H,EAAE,CAAC,EAAGA,EAAEA,EAAE,OAAS,CAAC,EAAGuD,GAAgB,EAAU,CAClE,EAEE4T,EAAM,WAAa,SAAS5T,EAAOwZ,EAAW,CAC5C,IAAI/c,EAAIuI,IACR,OAAOiZ,GAAWxhB,EAAE,CAAC,EAAGA,EAAEA,EAAE,OAAS,CAAC,EAAGuD,GAAgB,GAAYwZ,CAAS,CAClF,EAEE5F,EAAM,KAAO,SAAS5T,EAAO,CACvBA,GAAS,OAAMA,EAAQ,IAE3B,IAAIvD,EAAIuI,IACJ0B,EAAK,EACLrC,EAAK5H,EAAE,OAAS,EAChB4C,EAAQ5C,EAAEiK,CAAE,EACZpH,EAAO7C,EAAE4H,CAAE,EACXQ,EACAtF,EACA4e,EAAU,GAOd,IALI7e,EAAOD,IACTE,EAAOF,EAAOA,EAAQC,EAAMA,EAAOC,EACnCA,EAAOmH,EAAIA,EAAKrC,EAAIA,EAAK9E,GAGpB4e,KAAY,GAAG,CAEpB,GADA5e,EAAOmF,GAAcrF,EAAOC,EAAMU,CAAK,EACnCT,IAASsF,EACX,OAAApI,EAAEiK,CAAE,EAAIrH,EACR5C,EAAE4H,CAAE,EAAI/E,EACD0F,EAAOvI,CAAC,EACV,GAAI8C,EAAO,EAChBF,EAAQ,KAAK,MAAMA,EAAQE,CAAI,EAAIA,EACnCD,EAAO,KAAK,KAAKA,EAAOC,CAAI,EAAIA,UACvBA,EAAO,EAChBF,EAAQ,KAAK,KAAKA,EAAQE,CAAI,EAAIA,EAClCD,EAAO,KAAK,MAAMA,EAAOC,CAAI,EAAIA,MAEjC,OAEFsF,EAAUtF,CACX,CAED,OAAOqU,CACX,EAESA,CACT,CAEe,SAASzD,IAAS,CAC/B,IAAIyD,EAAQoK,KAEZ,OAAApK,EAAM,KAAO,UAAW,CACtB,OAAOmJ,GAAKnJ,EAAOzD,GAAM,CAAE,CAC/B,EAEE8L,GAAU,MAAMrI,EAAO,SAAS,EAEzBsK,GAAUtK,CAAK,CACxB,CClEe,SAAS5R,GAASgD,EAAQ,CACvC,IAAIqX,EAEJ,SAASzI,EAAMlX,EAAG,CAChB,OAAOA,GAAK,MAAQ,MAAMA,EAAI,CAACA,CAAC,EAAI2f,EAAU3f,CAC/C,CAED,OAAAkX,EAAM,OAASA,EAEfA,EAAM,OAASA,EAAM,MAAQ,SAASlO,EAAG,CACvC,OAAO,UAAU,QAAUV,EAAS,MAAM,KAAKU,EAAGvI,EAAM,EAAGyW,GAAS5O,EAAO,MAAK,CACpF,EAEE4O,EAAM,QAAU,SAASlO,EAAG,CAC1B,OAAO,UAAU,QAAU2W,EAAU3W,EAAGkO,GAASyI,CACrD,EAEEzI,EAAM,KAAO,UAAW,CACtB,OAAO5R,GAASgD,CAAM,EAAE,QAAQqX,CAAO,CAC3C,EAEErX,EAAS,UAAU,OAAS,MAAM,KAAKA,EAAQ7H,EAAM,EAAI,CAAC,EAAG,CAAC,EAEvD+gB,GAAUtK,CAAK,CACxB,CC3Be,SAAShP,GAAKI,EAAQoZ,EAAU,CAC7CpZ,EAASA,EAAO,QAEhB,IAAI0B,EAAK,EACLrC,EAAKW,EAAO,OAAS,EACrBK,EAAKL,EAAO0B,CAAE,EACdpB,EAAKN,EAAOX,CAAE,EACd3E,EAEJ,OAAI4F,EAAKD,IACP3F,EAAIgH,EAAIA,EAAKrC,EAAIA,EAAK3E,EACtBA,EAAI2F,EAAIA,EAAKC,EAAIA,EAAK5F,GAGxBsF,EAAO0B,CAAE,EAAI0X,EAAS,MAAM/Y,CAAE,EAC9BL,EAAOX,CAAE,EAAI+Z,EAAS,KAAK9Y,CAAE,EACtBN,CACT,CCXA,SAASqZ,GAAa3hB,EAAG,CACvB,OAAO,KAAK,IAAIA,CAAC,CACnB,CAEA,SAAS4hB,GAAa5hB,EAAG,CACvB,OAAO,KAAK,IAAIA,CAAC,CACnB,CAEA,SAAS6hB,GAAc7hB,EAAG,CACxB,MAAO,CAAC,KAAK,IAAI,CAACA,CAAC,CACrB,CAEA,SAAS8hB,GAAc9hB,EAAG,CACxB,MAAO,CAAC,KAAK,IAAI,CAACA,CAAC,CACrB,CAEA,SAAS+hB,GAAM/hB,EAAG,CAChB,OAAO,SAASA,CAAC,EAAI,EAAE,KAAOA,GAAKA,EAAI,EAAI,EAAIA,CACjD,CAEA,SAASgiB,GAAKC,EAAM,CAClB,OAAOA,IAAS,GAAKF,GACfE,IAAS,KAAK,EAAI,KAAK,IACvBjiB,GAAK,KAAK,IAAIiiB,EAAMjiB,CAAC,CAC7B,CAEA,SAASkiB,GAAKD,EAAM,CAClB,OAAOA,IAAS,KAAK,EAAI,KAAK,IACxBA,IAAS,IAAM,KAAK,OACnBA,IAAS,GAAK,KAAK,OAClBA,EAAO,KAAK,IAAIA,CAAI,EAAGjiB,GAAK,KAAK,IAAIA,CAAC,EAAIiiB,EACpD,CAEA,SAASE,GAAQhjB,EAAG,CAClB,MAAO,CAACa,EAAGoJ,IAAM,CAACjK,EAAE,CAACa,EAAGoJ,CAAC,CAC3B,CAEO,SAASgZ,GAAQnB,EAAW,CACjC,MAAM/J,EAAQ+J,EAAUU,GAAcC,EAAY,EAC5CtZ,EAAS4O,EAAM,OACrB,IAAI+K,EAAO,GACPI,EACAC,EAEJ,SAASpC,GAAU,CACjB,OAAAmC,EAAOH,GAAKD,CAAI,EAAGK,EAAON,GAAKC,CAAI,EAC/B3Z,EAAQ,EAAC,CAAC,EAAI,GAChB+Z,EAAOF,GAAQE,CAAI,EAAGC,EAAOH,GAAQG,CAAI,EACzCrB,EAAUY,GAAeC,EAAa,GAEtCb,EAAUU,GAAcC,EAAY,EAE/B1K,CACR,CAED,OAAAA,EAAM,KAAO,SAASlO,EAAG,CACvB,OAAO,UAAU,QAAUiZ,EAAO,CAACjZ,EAAGkX,EAAS,GAAI+B,CACvD,EAEE/K,EAAM,OAAS,SAASlO,EAAG,CACzB,OAAO,UAAU,QAAUV,EAAOU,CAAC,EAAGkX,EAAO,GAAM5X,GACvD,EAEE4O,EAAM,MAAQ5T,GAAS,CACrB,MAAMvD,EAAIuI,IACV,IAAIwQ,EAAI/Y,EAAE,CAAC,EACPiE,EAAIjE,EAAEA,EAAE,OAAS,CAAC,EACtB,MAAMd,EAAI+E,EAAI8U,EAEV7Z,IAAI,CAAC6Z,EAAG9U,CAAC,EAAI,CAACA,EAAG8U,CAAC,GAEtB,IAAItY,EAAI6hB,EAAKvJ,CAAC,EACV1V,EAAIif,EAAKre,CAAC,EACVoF,EACApG,EACJ,MAAM5D,EAAIkE,GAAS,KAAO,GAAK,CAACA,EAChC,IAAIgG,EAAI,CAAA,EAER,GAAI,EAAE2Y,EAAO,IAAM7e,EAAI5C,EAAIpB,EAAG,CAE5B,GADAoB,EAAI,KAAK,MAAMA,CAAC,EAAG4C,EAAI,KAAK,KAAKA,CAAC,EAC9B0V,EAAI,GAAG,KAAOtY,GAAK4C,EAAG,EAAE5C,EAC1B,IAAK4I,EAAI,EAAGA,EAAI6Y,EAAM,EAAE7Y,EAEtB,GADApG,EAAIxC,EAAI,EAAI4I,EAAIkZ,EAAK,CAAC9hB,CAAC,EAAI4I,EAAIkZ,EAAK9hB,CAAC,EACjC,EAAAwC,EAAI8V,GACR,IAAI9V,EAAIgB,EAAG,MACXsF,EAAE,KAAKtG,CAAC,OAEL,MAAOxC,GAAK4C,EAAG,EAAE5C,EACtB,IAAK4I,EAAI6Y,EAAO,EAAG7Y,GAAK,EAAG,EAAEA,EAE3B,GADApG,EAAIxC,EAAI,EAAI4I,EAAIkZ,EAAK,CAAC9hB,CAAC,EAAI4I,EAAIkZ,EAAK9hB,CAAC,EACjC,EAAAwC,EAAI8V,GACR,IAAI9V,EAAIgB,EAAG,MACXsF,EAAE,KAAKtG,CAAC,EAGRsG,EAAE,OAAS,EAAIlK,IAAGkK,EAAIxB,GAAMgR,EAAG9U,EAAG5E,CAAC,EAC7C,MACMkK,EAAIxB,GAAMtH,EAAG4C,EAAG,KAAK,IAAIA,EAAI5C,EAAGpB,CAAC,CAAC,EAAE,IAAIkjB,CAAI,EAE9C,OAAOrjB,EAAIqK,EAAE,QAAO,EAAKA,CAC7B,EAEE4N,EAAM,WAAa,CAAC5T,EAAOwZ,IAAc,CAOvC,GANIxZ,GAAS,OAAMA,EAAQ,IACvBwZ,GAAa,OAAMA,EAAYmF,IAAS,GAAK,IAAM,KACnD,OAAOnF,GAAc,aACnB,EAAEmF,EAAO,KAAOnF,EAAYD,GAAgBC,CAAS,GAAG,WAAa,OAAMA,EAAU,KAAO,IAChGA,EAAYxN,GAAOwN,CAAS,GAE1BxZ,IAAU,IAAU,OAAOwZ,EAC/B,MAAM1T,EAAI,KAAK,IAAI,EAAG6Y,EAAO3e,EAAQ4T,EAAM,QAAQ,MAAM,EACzD,OAAOnX,GAAK,CACV,IAAIS,EAAIT,EAAIuiB,EAAK,KAAK,MAAMD,EAAKtiB,CAAC,CAAC,CAAC,EACpC,OAAIS,EAAIyhB,EAAOA,EAAO,KAAKzhB,GAAKyhB,GACzBzhB,GAAK4I,EAAI0T,EAAU/c,CAAC,EAAI,EACrC,CACA,EAEEmX,EAAM,KAAO,IACJ5O,EAAOJ,GAAKI,IAAU,CAC3B,MAAOtI,GAAKsiB,EAAK,KAAK,MAAMD,EAAKriB,CAAC,CAAC,CAAC,EACpC,KAAMA,GAAKsiB,EAAK,KAAK,KAAKD,EAAKriB,CAAC,CAAC,CAAC,CACnC,CAAA,CAAC,EAGGkX,CACT,CAEe,SAASqL,IAAM,CAC5B,MAAMrL,EAAQkL,GAAQrB,GAAa,CAAA,EAAE,OAAO,CAAC,EAAG,EAAE,CAAC,EACnD,OAAA7J,EAAM,KAAO,IAAMmJ,GAAKnJ,EAAOqL,IAAK,EAAE,KAAKrL,EAAM,KAAI,CAAE,EACvDqI,GAAU,MAAMrI,EAAO,SAAS,EACzBA,CACT,CCvIA,SAASsL,GAAgB/b,EAAG,CAC1B,OAAO,SAASzG,EAAG,CACjB,OAAO,KAAK,KAAKA,CAAC,EAAI,KAAK,MAAM,KAAK,IAAIA,EAAIyG,CAAC,CAAC,CACpD,CACA,CAEA,SAASgc,GAAgBhc,EAAG,CAC1B,OAAO,SAASzG,EAAG,CACjB,OAAO,KAAK,KAAKA,CAAC,EAAI,KAAK,MAAM,KAAK,IAAIA,CAAC,CAAC,EAAIyG,CACpD,CACA,CAEO,SAASic,GAAUzB,EAAW,CACnC,IAAIxa,EAAI,EAAGyQ,EAAQ+J,EAAUuB,GAAgB/b,CAAC,EAAGgc,GAAgBhc,CAAC,CAAC,EAEnE,OAAAyQ,EAAM,SAAW,SAASlO,EAAG,CAC3B,OAAO,UAAU,OAASiY,EAAUuB,GAAgB/b,EAAI,CAACuC,CAAC,EAAGyZ,GAAgBhc,CAAC,CAAC,EAAIA,CACvF,EAES+a,GAAUtK,CAAK,CACxB,CAEe,SAASyL,IAAS,CAC/B,IAAIzL,EAAQwL,GAAU3B,GAAW,CAAE,EAEnC,OAAA7J,EAAM,KAAO,UAAW,CACtB,OAAOmJ,GAAKnJ,EAAOyL,GAAQ,CAAA,EAAE,SAASzL,EAAM,SAAQ,CAAE,CAC1D,EAESqI,GAAU,MAAMrI,EAAO,SAAS,CACzC,CC9BA,SAAS0L,GAAatG,EAAU,CAC9B,OAAO,SAAStc,EAAG,CACjB,OAAOA,EAAI,EAAI,CAAC,KAAK,IAAI,CAACA,EAAGsc,CAAQ,EAAI,KAAK,IAAItc,EAAGsc,CAAQ,CACjE,CACA,CAEA,SAASuG,GAAc7iB,EAAG,CACxB,OAAOA,EAAI,EAAI,CAAC,KAAK,KAAK,CAACA,CAAC,EAAI,KAAK,KAAKA,CAAC,CAC7C,CAEA,SAAS8iB,GAAgB9iB,EAAG,CAC1B,OAAOA,EAAI,EAAI,CAACA,EAAIA,EAAIA,EAAIA,CAC9B,CAEO,SAAS+iB,GAAO9B,EAAW,CAChC,IAAI/J,EAAQ+J,EAAU3b,EAAUA,CAAQ,EACpCgX,EAAW,EAEf,SAAS4D,GAAU,CACjB,OAAO5D,IAAa,EAAI2E,EAAU3b,EAAUA,CAAQ,EAC9CgX,IAAa,GAAM2E,EAAU4B,GAAeC,EAAe,EAC3D7B,EAAU2B,GAAatG,CAAQ,EAAGsG,GAAa,EAAItG,CAAQ,CAAC,CACnE,CAED,OAAApF,EAAM,SAAW,SAASlO,EAAG,CAC3B,OAAO,UAAU,QAAUsT,EAAW,CAACtT,EAAGkX,EAAS,GAAI5D,CAC3D,EAESkF,GAAUtK,CAAK,CACxB,CAEe,SAAS8L,IAAM,CAC5B,IAAI9L,EAAQ6L,GAAOhC,GAAW,CAAE,EAEhC,OAAA7J,EAAM,KAAO,UAAW,CACtB,OAAOmJ,GAAKnJ,EAAO8L,GAAK,CAAA,EAAE,SAAS9L,EAAM,SAAQ,CAAE,CACvD,EAEEqI,GAAU,MAAMrI,EAAO,SAAS,EAEzBA,CACT,CAEO,SAAS+L,IAAO,CACrB,OAAOD,GAAI,MAAM,KAAM,SAAS,EAAE,SAAS,EAAG,CAChD,CC5CA,SAASE,GAAOljB,EAAG,CACjB,OAAO,KAAK,KAAKA,CAAC,EAAIA,EAAIA,CAC5B,CAEA,SAASmjB,GAASnjB,EAAG,CACnB,OAAO,KAAK,KAAKA,CAAC,EAAI,KAAK,KAAK,KAAK,IAAIA,CAAC,CAAC,CAC7C,CAEe,SAASojB,IAAS,CAC/B,IAAIC,EAAU/B,GAAY,EACtBlW,EAAQ,CAAC,EAAG,CAAC,EACb2U,EAAQ,GACRJ,EAEJ,SAASzI,EAAMlX,EAAG,CAChB,IAAIyC,EAAI0gB,GAASE,EAAQrjB,CAAC,CAAC,EAC3B,OAAO,MAAMyC,CAAC,EAAIkd,EAAUI,EAAQ,KAAK,MAAMtd,CAAC,EAAIA,CACrD,CAED,OAAAyU,EAAM,OAAS,SAASzU,EAAG,CACzB,OAAO4gB,EAAQ,OAAOH,GAAOzgB,CAAC,CAAC,CACnC,EAEEyU,EAAM,OAAS,SAASlO,EAAG,CACzB,OAAO,UAAU,QAAUqa,EAAQ,OAAOra,CAAC,EAAGkO,GAASmM,EAAQ,QACnE,EAEEnM,EAAM,MAAQ,SAASlO,EAAG,CACxB,OAAO,UAAU,QAAUqa,EAAQ,OAAOjY,EAAQ,MAAM,KAAKpC,EAAGvI,EAAM,GAAG,IAAIyiB,EAAM,CAAC,EAAGhM,GAAS9L,EAAM,OAC1G,EAEE8L,EAAM,WAAa,SAASlO,EAAG,CAC7B,OAAOkO,EAAM,MAAMlO,CAAC,EAAE,MAAM,EAAI,CACpC,EAEEkO,EAAM,MAAQ,SAASlO,EAAG,CACxB,OAAO,UAAU,QAAU+W,EAAQ,CAAC,CAAC/W,EAAGkO,GAAS6I,CACrD,EAEE7I,EAAM,MAAQ,SAASlO,EAAG,CACxB,OAAO,UAAU,QAAUqa,EAAQ,MAAMra,CAAC,EAAGkO,GAASmM,EAAQ,OAClE,EAEEnM,EAAM,QAAU,SAASlO,EAAG,CAC1B,OAAO,UAAU,QAAU2W,EAAU3W,EAAGkO,GAASyI,CACrD,EAEEzI,EAAM,KAAO,UAAW,CACtB,OAAOkM,GAAOC,EAAQ,OAAM,EAAIjY,CAAK,EAChC,MAAM2U,CAAK,EACX,MAAMsD,EAAQ,OAAO,EACrB,QAAQ1D,CAAO,CACxB,EAEEJ,GAAU,MAAMrI,EAAO,SAAS,EAEzBsK,GAAUtK,CAAK,CACxB,CC3De,SAASnN,IAAW,CACjC,IAAIzB,EAAS,CAAE,EACX8C,EAAQ,CAAE,EACVkY,EAAa,CAAE,EACf3D,EAEJ,SAASO,GAAU,CACjB,IAAI1f,EAAI,EAAGpB,EAAI,KAAK,IAAI,EAAGgM,EAAM,MAAM,EAEvC,IADAkY,EAAa,IAAI,MAAMlkB,EAAI,CAAC,EACrB,EAAEoB,EAAIpB,GAAGkkB,EAAW9iB,EAAI,CAAC,EAAI+H,GAAUD,EAAQ9H,EAAIpB,CAAC,EAC3D,OAAO8X,CACR,CAED,SAASA,EAAMlX,EAAG,CAChB,OAAOA,GAAK,MAAQ,MAAMA,EAAI,CAACA,CAAC,EAAI2f,EAAUvU,EAAMjK,GAAOmiB,EAAYtjB,CAAC,CAAC,CAC1E,CAED,OAAAkX,EAAM,aAAe,SAASzU,EAAG,CAC/B,IAAIjC,EAAI4K,EAAM,QAAQ3I,CAAC,EACvB,OAAOjC,EAAI,EAAI,CAAC,IAAK,GAAG,EAAI,CAC1BA,EAAI,EAAI8iB,EAAW9iB,EAAI,CAAC,EAAI8H,EAAO,CAAC,EACpC9H,EAAI8iB,EAAW,OAASA,EAAW9iB,CAAC,EAAI8H,EAAOA,EAAO,OAAS,CAAC,CACtE,CACA,EAEE4O,EAAM,OAAS,SAASlO,EAAG,CACzB,GAAI,CAAC,UAAU,OAAQ,OAAOV,EAAO,MAAK,EAC1CA,EAAS,CAAA,EACT,QAASvI,KAAKiJ,EAAOjJ,GAAK,MAAQ,CAAC,MAAMA,EAAI,CAACA,CAAC,GAAGuI,EAAO,KAAKvI,CAAC,EAC/D,OAAAuI,EAAO,KAAK/I,CAAS,EACd2gB,EAAO,CAClB,EAEEhJ,EAAM,MAAQ,SAASlO,EAAG,CACxB,OAAO,UAAU,QAAUoC,EAAQ,MAAM,KAAKpC,CAAC,EAAGkX,EAAO,GAAM9U,EAAM,MAAK,CAC9E,EAEE8L,EAAM,QAAU,SAASlO,EAAG,CAC1B,OAAO,UAAU,QAAU2W,EAAU3W,EAAGkO,GAASyI,CACrD,EAEEzI,EAAM,UAAY,UAAW,CAC3B,OAAOoM,EAAW,OACtB,EAEEpM,EAAM,KAAO,UAAW,CACtB,OAAOnN,GAAU,EACZ,OAAOzB,CAAM,EACb,MAAM8C,CAAK,EACX,QAAQuU,CAAO,CACxB,EAESJ,GAAU,MAAMrI,EAAO,SAAS,CACzC,CCpDe,SAASqM,IAAW,CACjC,IAAI5a,EAAK,EACLC,EAAK,EACLxJ,EAAI,EACJkJ,EAAS,CAAC,EAAG,EACb8C,EAAQ,CAAC,EAAG,CAAC,EACbuU,EAEJ,SAASzI,EAAMlX,EAAG,CAChB,OAAOA,GAAK,MAAQA,GAAKA,EAAIoL,EAAMjK,GAAOmH,EAAQtI,EAAG,EAAGZ,CAAC,CAAC,EAAIugB,CAC/D,CAED,SAASO,GAAU,CACjB,IAAI1f,EAAI,GAER,IADA8H,EAAS,IAAI,MAAMlJ,CAAC,EACb,EAAEoB,EAAIpB,GAAGkJ,EAAO9H,CAAC,IAAMA,EAAI,GAAKoI,GAAMpI,EAAIpB,GAAKuJ,IAAOvJ,EAAI,GACjE,OAAO8X,CACR,CAED,OAAAA,EAAM,OAAS,SAASlO,EAAG,CACzB,OAAO,UAAU,QAAU,CAACL,EAAIC,CAAE,EAAII,EAAGL,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAIsX,EAAS,GAAI,CAACvX,EAAIC,CAAE,CACrF,EAEEsO,EAAM,MAAQ,SAASlO,EAAG,CACxB,OAAO,UAAU,QAAU5J,GAAKgM,EAAQ,MAAM,KAAKpC,CAAC,GAAG,OAAS,EAAGkX,EAAS,GAAI9U,EAAM,MAAK,CAC/F,EAEE8L,EAAM,aAAe,SAASzU,EAAG,CAC/B,IAAIjC,EAAI4K,EAAM,QAAQ3I,CAAC,EACvB,OAAOjC,EAAI,EAAI,CAAC,IAAK,GAAG,EAClBA,EAAI,EAAI,CAACmI,EAAIL,EAAO,CAAC,CAAC,EACtB9H,GAAKpB,EAAI,CAACkJ,EAAOlJ,EAAI,CAAC,EAAGwJ,CAAE,EAC3B,CAACN,EAAO9H,EAAI,CAAC,EAAG8H,EAAO9H,CAAC,CAAC,CACnC,EAEE0W,EAAM,QAAU,SAASlO,EAAG,CAC1B,OAAO,UAAU,SAAU2W,EAAU3W,GAAGkO,CAC5C,EAEEA,EAAM,WAAa,UAAW,CAC5B,OAAO5O,EAAO,OAClB,EAEE4O,EAAM,KAAO,UAAW,CACtB,OAAOqM,GAAU,EACZ,OAAO,CAAC5a,EAAIC,CAAE,CAAC,EACf,MAAMwC,CAAK,EACX,QAAQuU,CAAO,CACxB,EAESJ,GAAU,MAAMiC,GAAUtK,CAAK,EAAG,SAAS,CACpD,CCpDe,SAAS3O,IAAY,CAClC,IAAID,EAAS,CAAC,EAAG,EACb8C,EAAQ,CAAC,EAAG,CAAC,EACbuU,EACAvgB,EAAI,EAER,SAAS8X,EAAMlX,EAAG,CAChB,OAAOA,GAAK,MAAQA,GAAKA,EAAIoL,EAAMjK,GAAOmH,EAAQtI,EAAG,EAAGZ,CAAC,CAAC,EAAIugB,CAC/D,CAED,OAAAzI,EAAM,OAAS,SAASlO,EAAG,CACzB,OAAO,UAAU,QAAUV,EAAS,MAAM,KAAKU,CAAC,EAAG5J,EAAI,KAAK,IAAIkJ,EAAO,OAAQ8C,EAAM,OAAS,CAAC,EAAG8L,GAAS5O,EAAO,OACtH,EAEE4O,EAAM,MAAQ,SAASlO,EAAG,CACxB,OAAO,UAAU,QAAUoC,EAAQ,MAAM,KAAKpC,CAAC,EAAG5J,EAAI,KAAK,IAAIkJ,EAAO,OAAQ8C,EAAM,OAAS,CAAC,EAAG8L,GAAS9L,EAAM,OACpH,EAEE8L,EAAM,aAAe,SAASzU,EAAG,CAC/B,IAAIjC,EAAI4K,EAAM,QAAQ3I,CAAC,EACvB,MAAO,CAAC6F,EAAO9H,EAAI,CAAC,EAAG8H,EAAO9H,CAAC,CAAC,CACpC,EAEE0W,EAAM,QAAU,SAASlO,EAAG,CAC1B,OAAO,UAAU,QAAU2W,EAAU3W,EAAGkO,GAASyI,CACrD,EAEEzI,EAAM,KAAO,UAAW,CACtB,OAAO3O,GAAW,EACb,OAAOD,CAAM,EACb,MAAM8C,CAAK,EACX,QAAQuU,CAAO,CACxB,EAESJ,GAAU,MAAMrI,EAAO,SAAS,CACzC,CCtCA,MAAMhG,GAAK,IAAI,KAAMC,GAAK,IAAI,KAEvB,SAASqS,EAAaC,EAAQC,EAASpgB,EAAOqgB,EAAO,CAE1D,SAASjC,EAAS9L,EAAM,CACtB,OAAO6N,EAAO7N,EAAO,UAAU,SAAW,EAAI,IAAI,KAAO,IAAI,KAAK,CAACA,CAAI,CAAC,EAAGA,CAC5E,CAED,OAAA8L,EAAS,MAAS9L,IACT6N,EAAO7N,EAAO,IAAI,KAAK,CAACA,CAAI,CAAC,EAAGA,GAGzC8L,EAAS,KAAQ9L,IACR6N,EAAO7N,EAAO,IAAI,KAAKA,EAAO,CAAC,CAAC,EAAG8N,EAAQ9N,EAAM,CAAC,EAAG6N,EAAO7N,CAAI,EAAGA,GAG5E8L,EAAS,MAAS9L,GAAS,CACzB,MAAMgL,EAAKc,EAAS9L,CAAI,EAAG4C,EAAKkJ,EAAS,KAAK9L,CAAI,EAClD,OAAOA,EAAOgL,EAAKpI,EAAK5C,EAAOgL,EAAKpI,CACxC,EAEEkJ,EAAS,OAAS,CAAC9L,EAAM/S,KAChB6gB,EAAQ9N,EAAO,IAAI,KAAK,CAACA,CAAI,EAAG/S,GAAQ,KAAO,EAAI,KAAK,MAAMA,CAAI,CAAC,EAAG+S,GAG/E8L,EAAS,MAAQ,CAAC/e,EAAOC,EAAMC,IAAS,CACtC,MAAMuI,EAAQ,CAAA,EAGd,GAFAzI,EAAQ+e,EAAS,KAAK/e,CAAK,EAC3BE,EAAOA,GAAQ,KAAO,EAAI,KAAK,MAAMA,CAAI,EACrC,EAAEF,EAAQC,IAAS,EAAEC,EAAO,GAAI,OAAOuI,EAC3C,IAAIF,EACJ,GAAGE,EAAM,KAAKF,EAAW,IAAI,KAAK,CAACvI,CAAK,CAAC,EAAG+gB,EAAQ/gB,EAAOE,CAAI,EAAG4gB,EAAO9gB,CAAK,QACvEuI,EAAWvI,GAASA,EAAQC,GACnC,OAAOwI,CACX,EAEEsW,EAAS,OAAUpV,GACVkX,EAAc5N,GAAS,CAC5B,GAAIA,GAAQA,EAAM,KAAO6N,EAAO7N,CAAI,EAAG,CAACtJ,EAAKsJ,CAAI,GAAGA,EAAK,QAAQA,EAAO,CAAC,CAC/E,EAAO,CAACA,EAAM/S,IAAS,CACjB,GAAI+S,GAAQA,EACV,GAAI/S,EAAO,EAAG,KAAO,EAAEA,GAAQ,GAC7B,KAAO6gB,EAAQ9N,EAAM,EAAE,EAAG,CAACtJ,EAAKsJ,CAAI,GAAG,KAClC,MAAO,EAAE/S,GAAQ,GACtB,KAAO6gB,EAAQ9N,EAAM,CAAE,EAAG,CAACtJ,EAAKsJ,CAAI,GAAG,CAGjD,CAAK,EAGCtS,IACFoe,EAAS,MAAQ,CAAC/e,EAAOqR,KACvB9C,GAAG,QAAQ,CAACvO,CAAK,EAAGwO,GAAG,QAAQ,CAAC6C,CAAG,EACnCyP,EAAOvS,EAAE,EAAGuS,EAAOtS,EAAE,EACd,KAAK,MAAM7N,EAAM4N,GAAIC,EAAE,CAAC,GAGjCuQ,EAAS,MAAS7e,IAChBA,EAAO,KAAK,MAAMA,CAAI,EACf,CAAC,SAASA,CAAI,GAAK,EAAEA,EAAO,GAAK,KAChCA,EAAO,EACT6e,EAAS,OAAOiC,EACX5jB,GAAM4jB,EAAM5jB,CAAC,EAAI8C,IAAS,EAC1B9C,GAAM2hB,EAAS,MAAM,EAAG3hB,CAAC,EAAI8C,IAAS,CAAC,EAH9B6e,IAOjBA,CACT,CClEY,MAACkC,GAAcJ,EAAa,IAAM,CAE9C,EAAG,CAAC5N,EAAM/S,IAAS,CACjB+S,EAAK,QAAQ,CAACA,EAAO/S,CAAI,CAC3B,EAAG,CAACF,EAAOqR,IACFA,EAAMrR,CACd,EAGDihB,GAAY,MAASxa,IACnBA,EAAI,KAAK,MAAMA,CAAC,EACZ,CAAC,SAASA,CAAC,GAAK,EAAEA,EAAI,GAAW,KAC/BA,EAAI,EACHoa,EAAc5N,GAAS,CAC5BA,EAAK,QAAQ,KAAK,MAAMA,EAAOxM,CAAC,EAAIA,CAAC,CACzC,EAAK,CAACwM,EAAM/S,IAAS,CACjB+S,EAAK,QAAQ,CAACA,EAAO/S,EAAOuG,CAAC,CACjC,EAAK,CAACzG,EAAOqR,KACDA,EAAMrR,GAASyG,CACxB,EAPoBwa,IAUX,MAACC,GAAeD,GAAY,MCxB3BE,GAAiB,IACjBC,GAAiBD,GAAiB,GAClCE,GAAeD,GAAiB,GAChCE,GAAcD,GAAe,GAC7BE,GAAeD,GAAc,EAC7BE,GAAgBF,GAAc,GAC9BG,GAAeH,GAAc,ICH7BI,GAASb,EAAc5N,GAAS,CAC3CA,EAAK,QAAQA,EAAOA,EAAK,gBAAiB,CAAA,CAC5C,EAAG,CAACA,EAAM/S,IAAS,CACjB+S,EAAK,QAAQ,CAACA,EAAO/S,EAAOihB,EAAc,CAC5C,EAAG,CAACnhB,EAAOqR,KACDA,EAAMrR,GAASmhB,GACrBlO,GACKA,EAAK,eACb,EAEY0O,GAAUD,GAAO,MCVjBE,GAAaf,EAAc5N,GAAS,CAC/CA,EAAK,QAAQA,EAAOA,EAAK,gBAAiB,EAAGA,EAAK,aAAekO,EAAc,CACjF,EAAG,CAAClO,EAAM/S,IAAS,CACjB+S,EAAK,QAAQ,CAACA,EAAO/S,EAAOkhB,EAAc,CAC5C,EAAG,CAACphB,EAAOqR,KACDA,EAAMrR,GAASohB,GACrBnO,GACKA,EAAK,YACb,EAEY4O,GAAcD,GAAW,MAEzBE,GAAYjB,EAAc5N,GAAS,CAC9CA,EAAK,cAAc,EAAG,CAAC,CACzB,EAAG,CAACA,EAAM/S,IAAS,CACjB+S,EAAK,QAAQ,CAACA,EAAO/S,EAAOkhB,EAAc,CAC5C,EAAG,CAACphB,EAAOqR,KACDA,EAAMrR,GAASohB,GACrBnO,GACKA,EAAK,eACb,EAEY8O,GAAaD,GAAU,MCtBvBE,GAAWnB,EAAc5N,GAAS,CAC7CA,EAAK,QAAQA,EAAOA,EAAK,gBAAe,EAAKA,EAAK,WAAU,EAAKkO,GAAiBlO,EAAK,WAAY,EAAGmO,EAAc,CACtH,EAAG,CAACnO,EAAM/S,IAAS,CACjB+S,EAAK,QAAQ,CAACA,EAAO/S,EAAOmhB,EAAY,CAC1C,EAAG,CAACrhB,EAAOqR,KACDA,EAAMrR,GAASqhB,GACrBpO,GACKA,EAAK,UACb,EAEYgP,GAAYD,GAAS,MAErBE,GAAUrB,EAAc5N,GAAS,CAC5CA,EAAK,cAAc,EAAG,EAAG,CAAC,CAC5B,EAAG,CAACA,EAAM/S,IAAS,CACjB+S,EAAK,QAAQ,CAACA,EAAO/S,EAAOmhB,EAAY,CAC1C,EAAG,CAACrhB,EAAOqR,KACDA,EAAMrR,GAASqhB,GACrBpO,GACKA,EAAK,aACb,EAEYkP,GAAWD,GAAQ,MCtBnBE,GAAUvB,EACrB5N,GAAQA,EAAK,SAAS,EAAG,EAAG,EAAG,CAAC,EAChC,CAACA,EAAM/S,IAAS+S,EAAK,QAAQA,EAAK,QAAS,EAAG/S,CAAI,EAClD,CAACF,EAAOqR,KAASA,EAAMrR,GAASqR,EAAI,kBAAmB,EAAGrR,EAAM,kBAAmB,GAAIohB,IAAkBE,GACzGrO,GAAQA,EAAK,QAAO,EAAK,CAC3B,EAEaoP,GAAWD,GAAQ,MAEnBE,GAASzB,EAAc5N,GAAS,CAC3CA,EAAK,YAAY,EAAG,EAAG,EAAG,CAAC,CAC7B,EAAG,CAACA,EAAM/S,IAAS,CACjB+S,EAAK,WAAWA,EAAK,WAAY,EAAG/S,CAAI,CAC1C,EAAG,CAACF,EAAOqR,KACDA,EAAMrR,GAASshB,GACrBrO,GACKA,EAAK,WAAY,EAAG,CAC5B,EAEYsP,GAAUD,GAAO,MAEjBE,GAAU3B,EAAc5N,GAAS,CAC5CA,EAAK,YAAY,EAAG,EAAG,EAAG,CAAC,CAC7B,EAAG,CAACA,EAAM/S,IAAS,CACjB+S,EAAK,WAAWA,EAAK,WAAY,EAAG/S,CAAI,CAC1C,EAAG,CAACF,EAAOqR,KACDA,EAAMrR,GAASshB,GACrBrO,GACK,KAAK,MAAMA,EAAOqO,EAAW,CACrC,EAEYmB,GAAWD,GAAQ,MC/BhC,SAASE,GAAY7kB,EAAG,CACtB,OAAOgjB,EAAc5N,GAAS,CAC5BA,EAAK,QAAQA,EAAK,WAAaA,EAAK,SAAW,EAAIpV,GAAK,CAAC,EACzDoV,EAAK,SAAS,EAAG,EAAG,EAAG,CAAC,CAC5B,EAAK,CAACA,EAAM/S,IAAS,CACjB+S,EAAK,QAAQA,EAAK,QAAO,EAAK/S,EAAO,CAAC,CAC1C,EAAK,CAACF,EAAOqR,KACDA,EAAMrR,GAASqR,EAAI,oBAAsBrR,EAAM,kBAAiB,GAAMohB,IAAkBG,EACjG,CACH,CAEY,MAACoB,GAAaD,GAAY,CAAC,EAC1BE,GAAaF,GAAY,CAAC,EAC1BG,GAAcH,GAAY,CAAC,EAC3BI,GAAgBJ,GAAY,CAAC,EAC7BK,GAAeL,GAAY,CAAC,EAC5BM,GAAaN,GAAY,CAAC,EAC1BO,GAAeP,GAAY,CAAC,EAE5BQ,GAAcP,GAAW,MACzBQ,GAAcP,GAAW,MACzBQ,GAAeP,GAAY,MAC3BQ,GAAiBP,GAAc,MAC/BQ,GAAgBP,GAAa,MAC7BQ,GAAcP,GAAW,MACzBQ,GAAgBP,GAAa,MAE1C,SAASQ,GAAW5lB,EAAG,CACrB,OAAOgjB,EAAc5N,GAAS,CAC5BA,EAAK,WAAWA,EAAK,cAAgBA,EAAK,YAAc,EAAIpV,GAAK,CAAC,EAClEoV,EAAK,YAAY,EAAG,EAAG,EAAG,CAAC,CAC/B,EAAK,CAACA,EAAM/S,IAAS,CACjB+S,EAAK,WAAWA,EAAK,WAAU,EAAK/S,EAAO,CAAC,CAChD,EAAK,CAACF,EAAOqR,KACDA,EAAMrR,GAASuhB,EACxB,CACH,CAEY,MAACmC,GAAYD,GAAW,CAAC,EACxBE,GAAYF,GAAW,CAAC,EACxBG,GAAaH,GAAW,CAAC,EACzBI,GAAeJ,GAAW,CAAC,EAC3BK,GAAcL,GAAW,CAAC,EAC1BM,GAAYN,GAAW,CAAC,EACxBO,GAAcP,GAAW,CAAC,EAE1BQ,GAAaP,GAAU,MACvBQ,GAAaP,GAAU,MACvBQ,GAAcP,GAAW,MACzBQ,GAAgBP,GAAa,MAC7BQ,GAAeP,GAAY,MAC3BQ,GAAaP,GAAU,MACvBQ,GAAeP,GAAY,MCrD3BQ,GAAY3D,EAAc5N,GAAS,CAC9CA,EAAK,QAAQ,CAAC,EACdA,EAAK,SAAS,EAAG,EAAG,EAAG,CAAC,CAC1B,EAAG,CAACA,EAAM/S,IAAS,CACjB+S,EAAK,SAASA,EAAK,SAAU,EAAG/S,CAAI,CACtC,EAAG,CAACF,EAAOqR,IACFA,EAAI,WAAarR,EAAM,SAAU,GAAIqR,EAAI,YAAW,EAAKrR,EAAM,YAAW,GAAM,GACrFiT,GACKA,EAAK,UACb,EAEYwR,GAAaD,GAAU,MAEvBE,GAAW7D,EAAc5N,GAAS,CAC7CA,EAAK,WAAW,CAAC,EACjBA,EAAK,YAAY,EAAG,EAAG,EAAG,CAAC,CAC7B,EAAG,CAACA,EAAM/S,IAAS,CACjB+S,EAAK,YAAYA,EAAK,YAAa,EAAG/S,CAAI,CAC5C,EAAG,CAACF,EAAOqR,IACFA,EAAI,cAAgBrR,EAAM,YAAa,GAAIqR,EAAI,eAAc,EAAKrR,EAAM,eAAc,GAAM,GACjGiT,GACKA,EAAK,aACb,EAEY0R,GAAYD,GAAS,MCxBrBE,GAAW/D,EAAc5N,GAAS,CAC7CA,EAAK,SAAS,EAAG,CAAC,EAClBA,EAAK,SAAS,EAAG,EAAG,EAAG,CAAC,CAC1B,EAAG,CAACA,EAAM/S,IAAS,CACjB+S,EAAK,YAAYA,EAAK,YAAa,EAAG/S,CAAI,CAC5C,EAAG,CAACF,EAAOqR,IACFA,EAAI,YAAW,EAAKrR,EAAM,YAAW,EAC1CiT,GACKA,EAAK,aACb,EAGD2R,GAAS,MAASne,GACT,CAAC,SAASA,EAAI,KAAK,MAAMA,CAAC,CAAC,GAAK,EAAEA,EAAI,GAAK,KAAOoa,EAAc5N,GAAS,CAC9EA,EAAK,YAAY,KAAK,MAAMA,EAAK,cAAgBxM,CAAC,EAAIA,CAAC,EACvDwM,EAAK,SAAS,EAAG,CAAC,EAClBA,EAAK,SAAS,EAAG,EAAG,EAAG,CAAC,CAC5B,EAAK,CAACA,EAAM/S,IAAS,CACjB+S,EAAK,YAAYA,EAAK,YAAW,EAAK/S,EAAOuG,CAAC,CAClD,CAAG,EAGS,MAACoe,GAAYD,GAAS,MAErBE,GAAUjE,EAAc5N,GAAS,CAC5CA,EAAK,YAAY,EAAG,CAAC,EACrBA,EAAK,YAAY,EAAG,EAAG,EAAG,CAAC,CAC7B,EAAG,CAACA,EAAM/S,IAAS,CACjB+S,EAAK,eAAeA,EAAK,eAAgB,EAAG/S,CAAI,CAClD,EAAG,CAACF,EAAOqR,IACFA,EAAI,eAAc,EAAKrR,EAAM,eAAc,EAChDiT,GACKA,EAAK,gBACb,EAGD6R,GAAQ,MAASre,GACR,CAAC,SAASA,EAAI,KAAK,MAAMA,CAAC,CAAC,GAAK,EAAEA,EAAI,GAAK,KAAOoa,EAAc5N,GAAS,CAC9EA,EAAK,eAAe,KAAK,MAAMA,EAAK,iBAAmBxM,CAAC,EAAIA,CAAC,EAC7DwM,EAAK,YAAY,EAAG,CAAC,EACrBA,EAAK,YAAY,EAAG,EAAG,EAAG,CAAC,CAC/B,EAAK,CAACA,EAAM/S,IAAS,CACjB+S,EAAK,eAAeA,EAAK,eAAc,EAAK/S,EAAOuG,CAAC,CACxD,CAAG,EAGS,MAACse,GAAWD,GAAQ,MCrChC,SAASE,GAAOC,EAAMC,EAAOC,EAAMC,EAAKC,EAAMC,EAAQ,CAEpD,MAAMC,EAAgB,CACpB,CAAC7D,GAAS,EAAQP,EAAc,EAChC,CAACO,GAAS,EAAI,EAAIP,EAAc,EAChC,CAACO,GAAQ,GAAI,GAAKP,EAAc,EAChC,CAACO,GAAQ,GAAI,GAAKP,EAAc,EAChC,CAACmE,EAAS,EAAQlE,EAAc,EAChC,CAACkE,EAAS,EAAI,EAAIlE,EAAc,EAChC,CAACkE,EAAQ,GAAI,GAAKlE,EAAc,EAChC,CAACkE,EAAQ,GAAI,GAAKlE,EAAc,EAChC,CAAGiE,EAAO,EAAQhE,EAAe,EACjC,CAAGgE,EAAO,EAAI,EAAIhE,EAAe,EACjC,CAAGgE,EAAO,EAAI,EAAIhE,EAAe,EACjC,CAAGgE,EAAM,GAAI,GAAKhE,EAAe,EACjC,CAAI+D,EAAM,EAAQ9D,EAAe,EACjC,CAAI8D,EAAM,EAAI,EAAI9D,EAAe,EACjC,CAAG6D,EAAO,EAAQ5D,EAAe,EACjC,CAAE2D,EAAQ,EAAQ1D,EAAe,EACjC,CAAE0D,EAAQ,EAAI,EAAI1D,EAAe,EACjC,CAAGyD,EAAO,EAAQxD,EAAe,CACrC,EAEE,SAAStc,EAAMnF,EAAOC,EAAMU,EAAO,CACjC,MAAMyE,EAAUnF,EAAOD,EACnBoF,IAAS,CAACpF,EAAOC,CAAI,EAAI,CAACA,EAAMD,CAAK,GACzC,MAAM+e,EAAWpe,GAAS,OAAOA,EAAM,OAAU,WAAaA,EAAQ6kB,EAAaxlB,EAAOC,EAAMU,CAAK,EAC/FwE,EAAQ4Z,EAAWA,EAAS,MAAM/e,EAAO,CAACC,EAAO,CAAC,EAAI,GAC5D,OAAOmF,EAAUD,EAAM,QAAO,EAAKA,CACpC,CAED,SAASqgB,EAAaxlB,EAAOC,EAAMU,EAAO,CACxC,MAAMwd,EAAS,KAAK,IAAIle,EAAOD,CAAK,EAAIW,EAClC9C,EAAIb,GAAS,CAAC,GAAIkD,CAAI,IAAMA,CAAI,EAAE,MAAMqlB,EAAepH,CAAM,EACnE,GAAItgB,IAAM0nB,EAAc,OAAQ,OAAON,EAAK,MAAM3f,GAAStF,EAAQyhB,GAAcxhB,EAAOwhB,GAAc9gB,CAAK,CAAC,EAC5G,GAAI9C,IAAM,EAAG,OAAOojB,GAAY,MAAM,KAAK,IAAI3b,GAAStF,EAAOC,EAAMU,CAAK,EAAG,CAAC,CAAC,EAC/E,KAAM,CAACN,EAAGH,CAAI,EAAIqlB,EAAcpH,EAASoH,EAAc1nB,EAAI,CAAC,EAAE,CAAC,EAAI0nB,EAAc1nB,CAAC,EAAE,CAAC,EAAIsgB,EAAStgB,EAAI,EAAIA,CAAC,EAC3G,OAAOwC,EAAE,MAAMH,CAAI,CACpB,CAED,MAAO,CAACiF,EAAOqgB,CAAY,CAC7B,CAEK,KAAC,CAACC,GAAUC,EAAe,EAAIV,GAAOF,GAASJ,GAAUhB,GAAWlB,GAASN,GAASJ,EAAS,EAC9F,CAAC6D,GAAWC,EAAgB,EAAIZ,GAAOJ,GAAUJ,GAAW7B,GAAYP,GAASJ,GAAUJ,EAAU,EC1C3G,SAASiE,GAAUzoB,EAAG,CACpB,GAAI,GAAKA,EAAE,GAAKA,EAAE,EAAI,IAAK,CACzB,IAAI6V,EAAO,IAAI,KAAK,GAAI7V,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,CAAC,EACpD,OAAA6V,EAAK,YAAY7V,EAAE,CAAC,EACb6V,CACR,CACD,OAAO,IAAI,KAAK7V,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,CAAC,CACnD,CAEA,SAAS0oB,GAAQ1oB,EAAG,CAClB,GAAI,GAAKA,EAAE,GAAKA,EAAE,EAAI,IAAK,CACzB,IAAI6V,EAAO,IAAI,KAAK,KAAK,IAAI,GAAI7V,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,CAAC,CAAC,EAC9D,OAAA6V,EAAK,eAAe7V,EAAE,CAAC,EAChB6V,CACR,CACD,OAAO,IAAI,KAAK,KAAK,IAAI7V,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,EAAGA,EAAE,CAAC,CAAC,CAC7D,CAEA,SAAS2oB,GAAQjmB,EAAG4G,EAAGtJ,EAAG,CACxB,MAAO,CAAC,EAAG0C,EAAG,EAAG4G,EAAG,EAAGtJ,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,CAClD,CAEe,SAASof,GAAa1B,EAAQ,CAC3C,IAAIkL,EAAkBlL,EAAO,SACzBmL,EAAcnL,EAAO,KACrBoL,EAAcpL,EAAO,KACrBqL,EAAiBrL,EAAO,QACxBsL,EAAkBtL,EAAO,KACzBuL,EAAuBvL,EAAO,UAC9BwL,EAAgBxL,EAAO,OACvByL,EAAqBzL,EAAO,YAE5B0L,EAAWC,GAASN,CAAc,EAClCO,EAAeC,GAAaR,CAAc,EAC1CS,EAAYH,GAASL,CAAe,EACpCS,EAAgBF,GAAaP,CAAe,EAC5CU,EAAiBL,GAASJ,CAAoB,EAC9CU,EAAqBJ,GAAaN,CAAoB,EACtDW,EAAUP,GAASH,CAAa,EAChCW,EAAcN,GAAaL,CAAa,EACxCY,EAAeT,GAASF,CAAkB,EAC1CY,EAAmBR,GAAaJ,CAAkB,EAElDa,EAAU,CACZ,EAAKC,EACL,EAAKC,EACL,EAAKC,EACL,EAAKC,GACL,EAAK,KACL,EAAKC,GACL,EAAKA,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAK,KACL,EAAK,KACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,IAAKC,EACT,EAEMC,EAAa,CACf,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAK,KACL,EAAKC,GACL,EAAKA,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAK5B,GACL,EAAKC,GACL,EAAK4B,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAK,KACL,EAAK,KACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,IAAK1B,EACT,EAEM2B,EAAS,CACX,EAAKC,EACL,EAAKC,EACL,EAAKC,EACL,EAAKC,EACL,EAAKC,EACL,EAAKC,GACL,EAAKA,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKA,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,EACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKC,GACL,EAAKlB,GACL,EAAKC,GACL,EAAKkB,GACL,IAAKC,EACT,EAGEnF,EAAQ,EAAI/L,EAAU4K,EAAamB,CAAO,EAC1CA,EAAQ,EAAI/L,EAAU6K,EAAakB,CAAO,EAC1CA,EAAQ,EAAI/L,EAAU2K,EAAiBoB,CAAO,EAC9C6B,EAAW,EAAI5N,EAAU4K,EAAagD,CAAU,EAChDA,EAAW,EAAI5N,EAAU6K,EAAa+C,CAAU,EAChDA,EAAW,EAAI5N,EAAU2K,EAAiBiD,CAAU,EAEpD,SAAS5N,EAAUlB,EAAWiN,EAAS,CACrC,OAAO,SAASnU,EAAM,CACpB,IAAID,EAAS,CAAE,EACXnV,EAAI,GACJ4C,EAAI,EACJhE,EAAI0d,EAAU,OACdrW,EACA0oB,GACA7f,GAIJ,IAFMsG,aAAgB,OAAOA,EAAO,IAAI,KAAK,CAACA,CAAI,GAE3C,EAAEpV,EAAIpB,GACP0d,EAAU,WAAWtc,CAAC,IAAM,KAC9BmV,EAAO,KAAKmH,EAAU,MAAM1Z,EAAG5C,CAAC,CAAC,GAC5B2uB,GAAMC,GAAK3oB,EAAIqW,EAAU,OAAO,EAAEtc,CAAC,CAAC,IAAM,KAAMiG,EAAIqW,EAAU,OAAO,EAAEtc,CAAC,EACxE2uB,GAAM1oB,IAAM,IAAM,IAAM,KACzB6I,GAASya,EAAQtjB,CAAC,KAAGA,EAAI6I,GAAOsG,EAAMuZ,EAAG,GAC7CxZ,EAAO,KAAKlP,CAAC,EACbrD,EAAI5C,EAAI,GAIZ,OAAAmV,EAAO,KAAKmH,EAAU,MAAM1Z,EAAG5C,CAAC,CAAC,EAC1BmV,EAAO,KAAK,EAAE,CAC3B,CACG,CAED,SAAS0Z,EAASvS,EAAWwS,EAAG,CAC9B,OAAO,SAAS3Z,EAAQ,CACtB,IAAI5V,EAAI2oB,GAAQ,KAAM,OAAW,CAAC,EAC9BloB,EAAI+uB,EAAexvB,EAAG+c,EAAWnH,GAAU,GAAI,CAAC,EAChDmS,EAAMC,EACV,GAAIvnB,GAAKmV,EAAO,OAAQ,OAAO,KAG/B,GAAI,MAAO5V,EAAG,OAAO,IAAI,KAAKA,EAAE,CAAC,EACjC,GAAI,MAAOA,EAAG,OAAO,IAAI,KAAKA,EAAE,EAAI,KAAQ,MAAOA,EAAIA,EAAE,EAAI,EAAE,EAY/D,GATIuvB,GAAK,EAAE,MAAOvvB,KAAIA,EAAE,EAAI,GAGxB,MAAOA,IAAGA,EAAE,EAAIA,EAAE,EAAI,GAAKA,EAAE,EAAI,IAGjCA,EAAE,IAAM,SAAWA,EAAE,EAAI,MAAOA,EAAIA,EAAE,EAAI,GAG1C,MAAOA,EAAG,CACZ,GAAIA,EAAE,EAAI,GAAKA,EAAE,EAAI,GAAI,OAAO,KAC1B,MAAOA,IAAIA,EAAE,EAAI,GACnB,MAAOA,GACT+nB,EAAOW,GAAQC,GAAQ3oB,EAAE,EAAG,EAAG,CAAC,CAAC,EAAGgoB,EAAMD,EAAK,UAAS,EACxDA,EAAOC,EAAM,GAAKA,IAAQ,EAAIzB,GAAU,KAAKwB,CAAI,EAAIxB,GAAUwB,CAAI,EACnEA,EAAO7C,GAAO,OAAO6C,GAAO/nB,EAAE,EAAI,GAAK,CAAC,EACxCA,EAAE,EAAI+nB,EAAK,iBACX/nB,EAAE,EAAI+nB,EAAK,cACX/nB,EAAE,EAAI+nB,EAAK,WAAU,GAAM/nB,EAAE,EAAI,GAAK,IAEtC+nB,EAAOU,GAAUE,GAAQ3oB,EAAE,EAAG,EAAG,CAAC,CAAC,EAAGgoB,EAAMD,EAAK,OAAM,EACvDA,EAAOC,EAAM,GAAKA,IAAQ,EAAIxC,GAAW,KAAKuC,CAAI,EAAIvC,GAAWuC,CAAI,EACrEA,EAAO/C,GAAQ,OAAO+C,GAAO/nB,EAAE,EAAI,GAAK,CAAC,EACzCA,EAAE,EAAI+nB,EAAK,cACX/nB,EAAE,EAAI+nB,EAAK,WACX/nB,EAAE,EAAI+nB,EAAK,QAAO,GAAM/nB,EAAE,EAAI,GAAK,EAEtC,MAAU,MAAOA,GAAK,MAAOA,KACtB,MAAOA,IAAIA,EAAE,EAAI,MAAOA,EAAIA,EAAE,EAAI,EAAI,MAAOA,EAAI,EAAI,GAC3DgoB,EAAM,MAAOhoB,EAAI0oB,GAAQC,GAAQ3oB,EAAE,EAAG,EAAG,CAAC,CAAC,EAAE,YAAcyoB,GAAUE,GAAQ3oB,EAAE,EAAG,EAAG,CAAC,CAAC,EAAE,SACzFA,EAAE,EAAI,EACNA,EAAE,EAAI,MAAOA,GAAKA,EAAE,EAAI,GAAK,EAAIA,EAAE,EAAI,GAAKgoB,EAAM,GAAK,EAAIhoB,EAAE,EAAIA,EAAE,EAAI,GAAKgoB,EAAM,GAAK,GAKzF,MAAI,MAAOhoB,GACTA,EAAE,GAAKA,EAAE,EAAI,IAAM,EACnBA,EAAE,GAAKA,EAAE,EAAI,IACN0oB,GAAQ1oB,CAAC,GAIXyoB,GAAUzoB,CAAC,CACxB,CACG,CAED,SAASwvB,EAAexvB,EAAG+c,EAAWnH,EAAQvS,EAAG,CAO/C,QANI5C,EAAI,EACJpB,EAAI0d,EAAU,OACdzT,EAAIsM,EAAO,OACXlP,EACA8P,GAEG/V,EAAIpB,GAAG,CACZ,GAAIgE,GAAKiG,EAAG,MAAO,GAEnB,GADA5C,EAAIqW,EAAU,WAAWtc,GAAG,EACxBiG,IAAM,IAGR,GAFAA,EAAIqW,EAAU,OAAOtc,GAAG,EACxB+V,GAAQ+W,EAAO7mB,KAAK2oB,GAAOtS,EAAU,OAAOtc,GAAG,EAAIiG,CAAC,EAChD,CAAC8P,KAAWnT,EAAImT,GAAMxW,EAAG4V,EAAQvS,CAAC,GAAK,EAAI,MAAO,WAC7CqD,GAAKkP,EAAO,WAAWvS,GAAG,EACnC,MAAO,EAEV,CAED,OAAOA,CACR,CAED,SAASirB,EAAYtuB,EAAG4V,EAAQnV,EAAG,CACjC,IAAIpB,EAAI+pB,EAAS,KAAKxT,EAAO,MAAMnV,CAAC,CAAC,EACrC,OAAOpB,GAAKW,EAAE,EAAIspB,EAAa,IAAIjqB,EAAE,CAAC,EAAE,YAAa,CAAA,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAC5E,CAED,SAASmuB,EAAkBxtB,EAAG4V,EAAQnV,EAAG,CACvC,IAAIpB,EAAIqqB,EAAe,KAAK9T,EAAO,MAAMnV,CAAC,CAAC,EAC3C,OAAOpB,GAAKW,EAAE,EAAI2pB,EAAmB,IAAItqB,EAAE,CAAC,EAAE,YAAa,CAAA,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAClF,CAED,SAASouB,EAAaztB,EAAG4V,EAAQnV,EAAG,CAClC,IAAIpB,EAAImqB,EAAU,KAAK5T,EAAO,MAAMnV,CAAC,CAAC,EACtC,OAAOpB,GAAKW,EAAE,EAAIypB,EAAc,IAAIpqB,EAAE,CAAC,EAAE,YAAa,CAAA,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAC7E,CAED,SAASquB,EAAgB1tB,EAAG4V,EAAQnV,EAAG,CACrC,IAAIpB,EAAIyqB,EAAa,KAAKlU,EAAO,MAAMnV,CAAC,CAAC,EACzC,OAAOpB,GAAKW,EAAE,EAAI+pB,EAAiB,IAAI1qB,EAAE,CAAC,EAAE,YAAa,CAAA,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAChF,CAED,SAASsuB,EAAW3tB,EAAG4V,EAAQnV,EAAG,CAChC,IAAIpB,EAAIuqB,EAAQ,KAAKhU,EAAO,MAAMnV,CAAC,CAAC,EACpC,OAAOpB,GAAKW,EAAE,EAAI6pB,EAAY,IAAIxqB,EAAE,CAAC,EAAE,YAAa,CAAA,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAC3E,CAED,SAASuuB,EAAoB5tB,EAAG4V,EAAQnV,EAAG,CACzC,OAAO+uB,EAAexvB,EAAG4oB,EAAiBhT,EAAQnV,CAAC,CACpD,CAED,SAASuuB,GAAgBhvB,EAAG4V,EAAQnV,EAAG,CACrC,OAAO+uB,EAAexvB,EAAG6oB,EAAajT,EAAQnV,CAAC,CAChD,CAED,SAASwuB,GAAgBjvB,EAAG4V,EAAQnV,EAAG,CACrC,OAAO+uB,EAAexvB,EAAG8oB,EAAalT,EAAQnV,CAAC,CAChD,CAED,SAASwpB,EAAmBjqB,EAAG,CAC7B,OAAOipB,EAAqBjpB,EAAE,OAAM,CAAE,CACvC,CAED,SAASkqB,EAAclqB,EAAG,CACxB,OAAOgpB,EAAgBhpB,EAAE,OAAM,CAAE,CAClC,CAED,SAASmqB,EAAiBnqB,EAAG,CAC3B,OAAOmpB,EAAmBnpB,EAAE,SAAQ,CAAE,CACvC,CAED,SAASoqB,GAAYpqB,EAAG,CACtB,OAAOkpB,EAAclpB,EAAE,SAAQ,CAAE,CAClC,CAED,SAAS+qB,GAAa/qB,EAAG,CACvB,OAAO+oB,EAAe,EAAE/oB,EAAE,SAAQ,GAAM,GAAG,CAC5C,CAED,SAASgrB,GAAchrB,EAAG,CACxB,MAAO,GAAI,CAAC,EAAEA,EAAE,SAAU,EAAG,EAC9B,CAED,SAAS8rB,GAAsB9rB,EAAG,CAChC,OAAOipB,EAAqBjpB,EAAE,UAAS,CAAE,CAC1C,CAED,SAAS+rB,GAAiB/rB,EAAG,CAC3B,OAAOgpB,EAAgBhpB,EAAE,UAAS,CAAE,CACrC,CAED,SAASgsB,GAAoBhsB,EAAG,CAC9B,OAAOmpB,EAAmBnpB,EAAE,YAAW,CAAE,CAC1C,CAED,SAASisB,GAAejsB,EAAG,CACzB,OAAOkpB,EAAclpB,EAAE,YAAW,CAAE,CACrC,CAED,SAAS4sB,GAAgB5sB,EAAG,CAC1B,OAAO+oB,EAAe,EAAE/oB,EAAE,YAAW,GAAM,GAAG,CAC/C,CAED,SAAS6sB,GAAiB7sB,EAAG,CAC3B,MAAO,GAAI,CAAC,EAAEA,EAAE,YAAa,EAAG,EACjC,CAED,MAAO,CACL,OAAQ,SAAS+c,EAAW,CAC1B,IAAI3d,EAAI6e,EAAUlB,GAAa,GAAIiN,CAAO,EAC1C,OAAA5qB,EAAE,SAAW,UAAW,CAAE,OAAO2d,CAAU,EACpC3d,CACR,EACD,MAAO,SAAS2d,EAAW,CACzB,IAAItY,EAAI6qB,EAASvS,GAAa,GAAI,EAAK,EACvC,OAAAtY,EAAE,SAAW,UAAW,CAAE,OAAOsY,CAAU,EACpCtY,CACR,EACD,UAAW,SAASsY,EAAW,CAC7B,IAAI3d,EAAI6e,EAAUlB,GAAa,GAAI8O,CAAU,EAC7C,OAAAzsB,EAAE,SAAW,UAAW,CAAE,OAAO2d,CAAU,EACpC3d,CACR,EACD,SAAU,SAAS2d,EAAW,CAC5B,IAAItY,EAAI6qB,EAASvS,GAAa,GAAI,EAAI,EACtC,OAAAtY,EAAE,SAAW,UAAW,CAAE,OAAOsY,CAAU,EACpCtY,CACR,CACL,CACA,CAEA,IAAI4qB,GAAO,CAAC,IAAK,GAAI,EAAK,IAAK,EAAK,GAAG,EACnCI,EAAW,UACXC,GAAY,KACZC,GAAY,sBAEhB,SAASP,EAAItuB,EAAOod,EAAMlc,EAAO,CAC/B,IAAIoc,EAAOtd,EAAQ,EAAI,IAAM,GACzB8U,GAAUwI,EAAO,CAACtd,EAAQA,GAAS,GACnCQ,EAASsU,EAAO,OACpB,OAAOwI,GAAQ9c,EAASU,EAAQ,IAAI,MAAMA,EAAQV,EAAS,CAAC,EAAE,KAAK4c,CAAI,EAAItI,EAASA,EACtF,CAEA,SAASga,GAAQtsB,EAAG,CAClB,OAAOA,EAAE,QAAQqsB,GAAW,MAAM,CACpC,CAEA,SAAStG,GAASwG,EAAO,CACvB,OAAO,IAAI,OAAO,OAASA,EAAM,IAAID,EAAO,EAAE,KAAK,GAAG,EAAI,IAAK,GAAG,CACpE,CAEA,SAASrG,GAAasG,EAAO,CAC3B,OAAO,IAAI,IAAIA,EAAM,IAAI,CAACC,EAAMrvB,IAAM,CAACqvB,EAAK,YAAW,EAAIrvB,CAAC,CAAC,CAAC,CAChE,CAEA,SAASquB,GAAyB9uB,EAAG4V,EAAQnV,EAAG,CAC9C,IAAIpB,EAAIowB,EAAS,KAAK7Z,EAAO,MAAMnV,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOpB,GAAKW,EAAE,EAAI,CAACX,EAAE,CAAC,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAASsvB,GAAyB3uB,EAAG4V,EAAQnV,EAAG,CAC9C,IAAIpB,EAAIowB,EAAS,KAAK7Z,EAAO,MAAMnV,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOpB,GAAKW,EAAE,EAAI,CAACX,EAAE,CAAC,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAASuvB,GAAsB5uB,EAAG4V,EAAQnV,EAAG,CAC3C,IAAIpB,EAAIowB,EAAS,KAAK7Z,EAAO,MAAMnV,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOpB,GAAKW,EAAE,EAAI,CAACX,EAAE,CAAC,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAASwvB,GAAmB7uB,EAAG4V,EAAQnV,EAAG,CACxC,IAAIpB,EAAIowB,EAAS,KAAK7Z,EAAO,MAAMnV,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOpB,GAAKW,EAAE,EAAI,CAACX,EAAE,CAAC,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAAS0vB,GAAsB/uB,EAAG4V,EAAQnV,EAAG,CAC3C,IAAIpB,EAAIowB,EAAS,KAAK7Z,EAAO,MAAMnV,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOpB,GAAKW,EAAE,EAAI,CAACX,EAAE,CAAC,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAAS2uB,GAAchuB,EAAG4V,EAAQnV,EAAG,CACnC,IAAIpB,EAAIowB,EAAS,KAAK7Z,EAAO,MAAMnV,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOpB,GAAKW,EAAE,EAAI,CAACX,EAAE,CAAC,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAAS0uB,GAAU/tB,EAAG4V,EAAQnV,EAAG,CAC/B,IAAIpB,EAAIowB,EAAS,KAAK7Z,EAAO,MAAMnV,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOpB,GAAKW,EAAE,EAAI,CAACX,EAAE,CAAC,GAAK,CAACA,EAAE,CAAC,EAAI,GAAK,KAAO,KAAOoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAC3E,CAEA,SAAS6vB,GAAUlvB,EAAG4V,EAAQnV,EAAG,CAC/B,IAAIpB,EAAI,+BAA+B,KAAKuW,EAAO,MAAMnV,EAAGA,EAAI,CAAC,CAAC,EAClE,OAAOpB,GAAKW,EAAE,EAAIX,EAAE,CAAC,EAAI,EAAI,EAAEA,EAAE,CAAC,GAAKA,EAAE,CAAC,GAAK,OAAQoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAC5E,CAEA,SAASkvB,GAAavuB,EAAG4V,EAAQnV,EAAG,CAClC,IAAIpB,EAAIowB,EAAS,KAAK7Z,EAAO,MAAMnV,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOpB,GAAKW,EAAE,EAAIX,EAAE,CAAC,EAAI,EAAI,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EACrD,CAEA,SAAS+uB,GAAiBpuB,EAAG4V,EAAQnV,EAAG,CACtC,IAAIpB,EAAIowB,EAAS,KAAK7Z,EAAO,MAAMnV,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOpB,GAAKW,EAAE,EAAIX,EAAE,CAAC,EAAI,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EACjD,CAEA,SAASwuB,GAAgB7tB,EAAG4V,EAAQnV,EAAG,CACrC,IAAIpB,EAAIowB,EAAS,KAAK7Z,EAAO,MAAMnV,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOpB,GAAKW,EAAE,EAAI,CAACX,EAAE,CAAC,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAAS6uB,GAAeluB,EAAG4V,EAAQnV,EAAG,CACpC,IAAIpB,EAAIowB,EAAS,KAAK7Z,EAAO,MAAMnV,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOpB,GAAKW,EAAE,EAAI,EAAGA,EAAE,EAAI,CAACX,EAAE,CAAC,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EACvD,CAEA,SAAS4uB,GAAYjuB,EAAG4V,EAAQnV,EAAG,CACjC,IAAIpB,EAAIowB,EAAS,KAAK7Z,EAAO,MAAMnV,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOpB,GAAKW,EAAE,EAAI,CAACX,EAAE,CAAC,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAASgvB,GAAaruB,EAAG4V,EAAQnV,EAAG,CAClC,IAAIpB,EAAIowB,EAAS,KAAK7Z,EAAO,MAAMnV,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOpB,GAAKW,EAAE,EAAI,CAACX,EAAE,CAAC,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAASqvB,GAAa1uB,EAAG4V,EAAQnV,EAAG,CAClC,IAAIpB,EAAIowB,EAAS,KAAK7Z,EAAO,MAAMnV,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOpB,GAAKW,EAAE,EAAI,CAACX,EAAE,CAAC,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAAS8uB,GAAkBnuB,EAAG4V,EAAQnV,EAAG,CACvC,IAAIpB,EAAIowB,EAAS,KAAK7Z,EAAO,MAAMnV,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOpB,GAAKW,EAAE,EAAI,CAACX,EAAE,CAAC,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAASyuB,GAAkB9tB,EAAG4V,EAAQnV,EAAG,CACvC,IAAIpB,EAAIowB,EAAS,KAAK7Z,EAAO,MAAMnV,EAAGA,EAAI,CAAC,CAAC,EAC5C,OAAOpB,GAAKW,EAAE,EAAI,KAAK,MAAMX,EAAE,CAAC,EAAI,GAAI,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAChE,CAEA,SAAS8vB,GAAoBnvB,EAAG4V,EAAQnV,EAAG,CACzC,IAAIpB,EAAIqwB,GAAU,KAAK9Z,EAAO,MAAMnV,EAAGA,EAAI,CAAC,CAAC,EAC7C,OAAOpB,EAAIoB,EAAIpB,EAAE,CAAC,EAAE,OAAS,EAC/B,CAEA,SAASmvB,GAAmBxuB,EAAG4V,EAAQnV,EAAG,CACxC,IAAIpB,EAAIowB,EAAS,KAAK7Z,EAAO,MAAMnV,CAAC,CAAC,EACrC,OAAOpB,GAAKW,EAAE,EAAI,CAACX,EAAE,CAAC,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAASovB,GAA0BzuB,EAAG4V,EAAQnV,EAAG,CAC/C,IAAIpB,EAAIowB,EAAS,KAAK7Z,EAAO,MAAMnV,CAAC,CAAC,EACrC,OAAOpB,GAAKW,EAAE,EAAI,CAACX,EAAE,CAAC,EAAGoB,EAAIpB,EAAE,CAAC,EAAE,QAAU,EAC9C,CAEA,SAASgrB,GAAiBrqB,EAAGyE,EAAG,CAC9B,OAAO2qB,EAAIpvB,EAAE,QAAS,EAAEyE,EAAG,CAAC,CAC9B,CAEA,SAASgmB,GAAazqB,EAAGyE,EAAG,CAC1B,OAAO2qB,EAAIpvB,EAAE,SAAU,EAAEyE,EAAG,CAAC,CAC/B,CAEA,SAASimB,GAAa1qB,EAAGyE,EAAG,CAC1B,OAAO2qB,EAAIpvB,EAAE,SAAU,EAAG,IAAM,GAAIyE,EAAG,CAAC,CAC1C,CAEA,SAASkmB,GAAgB3qB,EAAGyE,EAAG,CAC7B,OAAO2qB,EAAI,EAAIpK,GAAQ,MAAMwC,GAASxnB,CAAC,EAAGA,CAAC,EAAGyE,EAAG,CAAC,CACpD,CAEA,SAASmmB,GAAmB5qB,EAAGyE,EAAG,CAChC,OAAO2qB,EAAIpvB,EAAE,gBAAiB,EAAEyE,EAAG,CAAC,CACtC,CAEA,SAAS6lB,GAAmBtqB,EAAGyE,EAAG,CAChC,OAAOmmB,GAAmB5qB,EAAGyE,CAAC,EAAI,KACpC,CAEA,SAASomB,GAAkB7qB,EAAGyE,EAAG,CAC/B,OAAO2qB,EAAIpvB,EAAE,SAAQ,EAAK,EAAGyE,EAAG,CAAC,CACnC,CAEA,SAASqmB,GAAc9qB,EAAGyE,EAAG,CAC3B,OAAO2qB,EAAIpvB,EAAE,WAAY,EAAEyE,EAAG,CAAC,CACjC,CAEA,SAAS0mB,GAAcnrB,EAAGyE,EAAG,CAC3B,OAAO2qB,EAAIpvB,EAAE,WAAY,EAAEyE,EAAG,CAAC,CACjC,CAEA,SAAS2mB,GAA0BprB,EAAG,CACpC,IAAIgoB,EAAMhoB,EAAE,SACZ,OAAOgoB,IAAQ,EAAI,EAAIA,CACzB,CAEA,SAASqD,GAAuBrrB,EAAGyE,EAAG,CACpC,OAAO2qB,EAAI7J,GAAW,MAAMiC,GAASxnB,CAAC,EAAI,EAAGA,CAAC,EAAGyE,EAAG,CAAC,CACvD,CAEA,SAASsrB,GAAK/vB,EAAG,CACf,IAAIgoB,EAAMhoB,EAAE,SACZ,OAAQgoB,GAAO,GAAKA,IAAQ,EAAKrC,GAAa3lB,CAAC,EAAI2lB,GAAa,KAAK3lB,CAAC,CACxE,CAEA,SAASsrB,GAAoBtrB,EAAGyE,EAAG,CACjC,OAAAzE,EAAI+vB,GAAK/vB,CAAC,EACHovB,EAAIzJ,GAAa,MAAM6B,GAASxnB,CAAC,EAAGA,CAAC,GAAKwnB,GAASxnB,CAAC,EAAE,OAAQ,IAAK,GAAIyE,EAAG,CAAC,CACpF,CAEA,SAAS8mB,GAA0BvrB,EAAG,CACpC,OAAOA,EAAE,QACX,CAEA,SAASwrB,GAAuBxrB,EAAGyE,EAAG,CACpC,OAAO2qB,EAAI5J,GAAW,MAAMgC,GAASxnB,CAAC,EAAI,EAAGA,CAAC,EAAGyE,EAAG,CAAC,CACvD,CAEA,SAASgnB,GAAWzrB,EAAGyE,EAAG,CACxB,OAAO2qB,EAAIpvB,EAAE,YAAW,EAAK,IAAKyE,EAAG,CAAC,CACxC,CAEA,SAAS8lB,GAAcvqB,EAAGyE,EAAG,CAC3B,OAAAzE,EAAI+vB,GAAK/vB,CAAC,EACHovB,EAAIpvB,EAAE,YAAW,EAAK,IAAKyE,EAAG,CAAC,CACxC,CAEA,SAASinB,GAAe1rB,EAAGyE,EAAG,CAC5B,OAAO2qB,EAAIpvB,EAAE,YAAW,EAAK,IAAOyE,EAAG,CAAC,CAC1C,CAEA,SAAS+lB,GAAkBxqB,EAAGyE,EAAG,CAC/B,IAAIujB,EAAMhoB,EAAE,SACZ,OAAAA,EAAKgoB,GAAO,GAAKA,IAAQ,EAAKrC,GAAa3lB,CAAC,EAAI2lB,GAAa,KAAK3lB,CAAC,EAC5DovB,EAAIpvB,EAAE,YAAW,EAAK,IAAOyE,EAAG,CAAC,CAC1C,CAEA,SAASknB,GAAW3rB,EAAG,CACrB,IAAIuJ,EAAIvJ,EAAE,oBACV,OAAQuJ,EAAI,EAAI,KAAOA,GAAK,GAAI,MAC1B6lB,EAAI7lB,EAAI,GAAK,EAAG,IAAK,CAAC,EACtB6lB,EAAI7lB,EAAI,GAAI,IAAK,CAAC,CAC1B,CAEA,SAAS2iB,GAAoBlsB,EAAGyE,EAAG,CACjC,OAAO2qB,EAAIpvB,EAAE,WAAY,EAAEyE,EAAG,CAAC,CACjC,CAEA,SAAS6nB,GAAgBtsB,EAAGyE,EAAG,CAC7B,OAAO2qB,EAAIpvB,EAAE,YAAa,EAAEyE,EAAG,CAAC,CAClC,CAEA,SAAS8nB,GAAgBvsB,EAAGyE,EAAG,CAC7B,OAAO2qB,EAAIpvB,EAAE,YAAa,EAAG,IAAM,GAAIyE,EAAG,CAAC,CAC7C,CAEA,SAAS+nB,GAAmBxsB,EAAGyE,EAAG,CAChC,OAAO2qB,EAAI,EAAIlK,GAAO,MAAMwC,GAAQ1nB,CAAC,EAAGA,CAAC,EAAGyE,EAAG,CAAC,CAClD,CAEA,SAASgoB,GAAsBzsB,EAAGyE,EAAG,CACnC,OAAO2qB,EAAIpvB,EAAE,mBAAoB,EAAEyE,EAAG,CAAC,CACzC,CAEA,SAAS0nB,GAAsBnsB,EAAGyE,EAAG,CACnC,OAAOgoB,GAAsBzsB,EAAGyE,CAAC,EAAI,KACvC,CAEA,SAASioB,GAAqB1sB,EAAGyE,EAAG,CAClC,OAAO2qB,EAAIpvB,EAAE,YAAW,EAAK,EAAGyE,EAAG,CAAC,CACtC,CAEA,SAASkoB,GAAiB3sB,EAAGyE,EAAG,CAC9B,OAAO2qB,EAAIpvB,EAAE,cAAe,EAAEyE,EAAG,CAAC,CACpC,CAEA,SAASqoB,GAAiB9sB,EAAGyE,EAAG,CAC9B,OAAO2qB,EAAIpvB,EAAE,cAAe,EAAEyE,EAAG,CAAC,CACpC,CAEA,SAASsoB,GAA6B/sB,EAAG,CACvC,IAAIgwB,EAAMhwB,EAAE,YACZ,OAAOgwB,IAAQ,EAAI,EAAIA,CACzB,CAEA,SAAShD,GAA0BhtB,EAAGyE,EAAG,CACvC,OAAO2qB,EAAI9I,GAAU,MAAMoB,GAAQ1nB,CAAC,EAAI,EAAGA,CAAC,EAAGyE,EAAG,CAAC,CACrD,CAEA,SAASwrB,GAAQjwB,EAAG,CAClB,IAAIgoB,EAAMhoB,EAAE,YACZ,OAAQgoB,GAAO,GAAKA,IAAQ,EAAKtB,GAAY1mB,CAAC,EAAI0mB,GAAY,KAAK1mB,CAAC,CACtE,CAEA,SAASitB,GAAuBjtB,EAAGyE,EAAG,CACpC,OAAAzE,EAAIiwB,GAAQjwB,CAAC,EACNovB,EAAI1I,GAAY,MAAMgB,GAAQ1nB,CAAC,EAAGA,CAAC,GAAK0nB,GAAQ1nB,CAAC,EAAE,UAAW,IAAK,GAAIyE,EAAG,CAAC,CACpF,CAEA,SAASyoB,GAA6BltB,EAAG,CACvC,OAAOA,EAAE,WACX,CAEA,SAASmtB,GAA0BntB,EAAGyE,EAAG,CACvC,OAAO2qB,EAAI7I,GAAU,MAAMmB,GAAQ1nB,CAAC,EAAI,EAAGA,CAAC,EAAGyE,EAAG,CAAC,CACrD,CAEA,SAAS2oB,GAAcptB,EAAGyE,EAAG,CAC3B,OAAO2qB,EAAIpvB,EAAE,eAAc,EAAK,IAAKyE,EAAG,CAAC,CAC3C,CAEA,SAAS2nB,GAAiBpsB,EAAGyE,EAAG,CAC9B,OAAAzE,EAAIiwB,GAAQjwB,CAAC,EACNovB,EAAIpvB,EAAE,eAAc,EAAK,IAAKyE,EAAG,CAAC,CAC3C,CAEA,SAAS4oB,GAAkBrtB,EAAGyE,EAAG,CAC/B,OAAO2qB,EAAIpvB,EAAE,eAAc,EAAK,IAAOyE,EAAG,CAAC,CAC7C,CAEA,SAAS4nB,GAAqBrsB,EAAGyE,EAAG,CAClC,IAAIujB,EAAMhoB,EAAE,YACZ,OAAAA,EAAKgoB,GAAO,GAAKA,IAAQ,EAAKtB,GAAY1mB,CAAC,EAAI0mB,GAAY,KAAK1mB,CAAC,EAC1DovB,EAAIpvB,EAAE,eAAc,EAAK,IAAOyE,EAAG,CAAC,CAC7C,CAEA,SAAS6oB,IAAgB,CACvB,MAAO,OACT,CAEA,SAAS1B,IAAuB,CAC9B,MAAO,GACT,CAEA,SAASX,GAAoBjrB,EAAG,CAC9B,MAAO,CAACA,CACV,CAEA,SAASkrB,GAA2BlrB,EAAG,CACrC,OAAO,KAAK,MAAM,CAACA,EAAI,GAAI,CAC7B,CCtrBA,IAAI0d,GACOwS,GACAC,GACAC,GACAC,GAEXlR,GAAc,CACZ,SAAU,SACV,KAAM,aACN,KAAM,eACN,QAAS,CAAC,KAAM,IAAI,EACpB,KAAM,CAAC,SAAU,SAAU,UAAW,YAAa,WAAY,SAAU,UAAU,EACnF,UAAW,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,KAAK,EAC3D,OAAQ,CAAC,UAAW,WAAY,QAAS,QAAS,MAAO,OAAQ,OAAQ,SAAU,YAAa,UAAW,WAAY,UAAU,EACjI,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,KAAK,CAClG,CAAC,EAEc,SAASA,GAAclR,EAAY,CAChD,OAAAyP,GAAS0B,GAAanR,CAAU,EAChCiiB,GAAaxS,GAAO,OACpByS,GAAYzS,GAAO,MACnB0S,GAAY1S,GAAO,UACnB2S,GAAW3S,GAAO,SACXA,EACT,CCxBO,IAAI4S,GAAe,wBAE1B,SAASC,GAAgB1a,EAAM,CAC7B,OAAOA,EAAK,aACd,CAEA,IAAI2a,GAAY,KAAK,UAAU,YACzBD,GACAH,GAAUE,EAAY,EAE5B,MAAAG,GAAeD,GCTf,SAASE,GAAe9a,EAAQ,CAC9B,IAAIC,EAAO,IAAI,KAAKD,CAAM,EAC1B,OAAO,MAAMC,CAAI,EAAI,KAAOA,CAC9B,CAEA,IAAI8a,GAAW,CAAC,IAAI,KAAK,0BAA0B,EAC7CD,GACAL,GAASC,EAAY,EAE3B,MAAAM,GAAeD,GCNf,SAAS9a,GAAK5S,EAAG,CACf,OAAO,IAAI,KAAKA,CAAC,CACnB,CAEA,SAASvC,GAAOuC,EAAG,CACjB,OAAOA,aAAa,KAAO,CAACA,EAAI,CAAC,IAAI,KAAK,CAACA,CAAC,CAC9C,CAEO,SAAS4tB,GAAS9oB,EAAOqgB,EAAcP,EAAMC,EAAOC,EAAMC,EAAKC,EAAMC,EAAQ5D,EAAQ/U,EAAQ,CAClG,IAAI4H,EAAQoK,GAAY,EACpBuP,EAAS3Z,EAAM,OACf5O,EAAS4O,EAAM,OAEf4Z,EAAoBxhB,EAAO,KAAK,EAChCyhB,EAAezhB,EAAO,KAAK,EAC3B0hB,EAAe1hB,EAAO,OAAO,EAC7B2hB,EAAa3hB,EAAO,OAAO,EAC3B4hB,EAAY5hB,EAAO,OAAO,EAC1B6hB,EAAa7hB,EAAO,OAAO,EAC3B6a,EAAc7a,EAAO,IAAI,EACzBkc,EAAalc,EAAO,IAAI,EAE5B,SAASiS,EAAW3L,EAAM,CACxB,OAAQyO,EAAOzO,CAAI,EAAIA,EAAOkb,EACxB7I,EAAOrS,CAAI,EAAIA,EAAOmb,EACtB/I,EAAKpS,CAAI,EAAIA,EAAOob,EACpBjJ,EAAInS,CAAI,EAAIA,EAAOqb,EACnBpJ,EAAMjS,CAAI,EAAIA,EAAQkS,EAAKlS,CAAI,EAAIA,EAAOsb,EAAYC,EACtDvJ,EAAKhS,CAAI,EAAIA,EAAOuU,EACpBqB,GAAY5V,CAAI,CACvB,CAED,OAAAsB,EAAM,OAAS,SAASzU,EAAG,CACzB,OAAO,IAAI,KAAKouB,EAAOpuB,CAAC,CAAC,CAC7B,EAEEyU,EAAM,OAAS,SAASlO,EAAG,CACzB,OAAO,UAAU,OAASV,EAAO,MAAM,KAAKU,EAAGvI,EAAM,CAAC,EAAI6H,EAAM,EAAG,IAAIsN,EAAI,CAC/E,EAEEsB,EAAM,MAAQ,SAASwK,EAAU,CAC/B,IAAI3hB,EAAIuI,IACR,OAAOR,EAAM/H,EAAE,CAAC,EAAGA,EAAEA,EAAE,OAAS,CAAC,EAAG2hB,GAAmB,EAAa,CACxE,EAEExK,EAAM,WAAa,SAAS5T,EAAOwZ,EAAW,CAC5C,OAAOA,GAAa,KAAOyE,EAAajS,EAAOwN,CAAS,CAC5D,EAEE5F,EAAM,KAAO,SAASwK,EAAU,CAC9B,IAAI3hB,EAAIuI,IACR,OAAI,CAACoZ,GAAY,OAAOA,EAAS,OAAU,cAAYA,EAAWyG,EAAapoB,EAAE,CAAC,EAAGA,EAAEA,EAAE,OAAS,CAAC,EAAG2hB,GAAmB,EAAa,GAC/HA,EAAWpZ,EAAOJ,GAAKnI,EAAG2hB,CAAQ,CAAC,EAAIxK,CAClD,EAEEA,EAAM,KAAO,UAAW,CACtB,OAAOmJ,GAAKnJ,EAAO0Z,GAAS9oB,EAAOqgB,EAAcP,EAAMC,EAAOC,EAAMC,EAAKC,EAAMC,EAAQ5D,EAAQ/U,CAAM,CAAC,CAC1G,EAES4H,CACT,CAEe,SAASka,IAAO,CAC7B,OAAO7R,GAAU,MAAMqR,GAAStI,GAAWC,GAAkBhB,GAAUJ,GAAWkK,GAAUtM,GAASJ,GAAUJ,GAAY+M,GAAYrB,EAAU,EAAE,OAAO,CAAC,IAAI,KAAK,IAAM,EAAG,CAAC,EAAG,IAAI,KAAK,IAAM,EAAG,CAAC,CAAC,CAAC,EAAG,SAAS,CACpN,CCjEe,SAASsB,IAAU,CAChC,OAAOhS,GAAU,MAAMqR,GAASxI,GAAUC,GAAiBZ,GAASJ,GAAUmK,GAASvM,GAAQJ,GAASJ,GAAWgN,GAAWtB,EAAS,EAAE,OAAO,CAAC,KAAK,IAAI,IAAM,EAAG,CAAC,EAAG,KAAK,IAAI,IAAM,EAAG,CAAC,CAAC,CAAC,EAAG,SAAS,CAC1M,CCCA,SAASpP,IAAc,CACrB,IAAIpY,EAAK,EACLC,EAAK,EACLsI,EACAC,EACAugB,EACAzQ,EACAjH,EAAe1U,EACf6b,EAAQ,GACRxB,EAEJ,SAASzI,EAAMlX,EAAG,CAChB,OAAOA,GAAK,MAAQ,MAAMA,EAAI,CAACA,CAAC,EAAI2f,EAAU3F,EAAa0X,IAAQ,EAAI,IAAO1xB,GAAKihB,EAAUjhB,CAAC,EAAIkR,GAAMwgB,EAAKvQ,EAAQ,KAAK,IAAI,EAAG,KAAK,IAAI,EAAGnhB,CAAC,CAAC,EAAIA,EAAE,CACtJ,CAEDkX,EAAM,OAAS,SAASlO,EAAG,CACzB,OAAO,UAAU,QAAU,CAACL,EAAIC,CAAE,EAAII,EAAGkI,EAAK+P,EAAUtY,EAAK,CAACA,CAAE,EAAGwI,EAAK8P,EAAUrY,EAAK,CAACA,CAAE,EAAG8oB,EAAMxgB,IAAOC,EAAK,EAAI,GAAKA,EAAKD,GAAKgG,GAAS,CAACvO,EAAIC,CAAE,CACtJ,EAEEsO,EAAM,MAAQ,SAASlO,EAAG,CACxB,OAAO,UAAU,QAAUmY,EAAQ,CAAC,CAACnY,EAAGkO,GAASiK,CACrD,EAEEjK,EAAM,aAAe,SAASlO,EAAG,CAC/B,OAAO,UAAU,QAAUgR,EAAehR,EAAGkO,GAAS8C,CAC1D,EAEE,SAAS5O,EAAMsK,EAAa,CAC1B,OAAO,SAAS1M,EAAG,CACjB,IAAI2P,EAAIC,EACR,OAAO,UAAU,QAAU,CAACD,EAAIC,CAAE,EAAI5P,EAAGgR,EAAetE,EAAYiD,EAAIC,CAAE,EAAG1B,GAAS,CAAC8C,EAAa,CAAC,EAAGA,EAAa,CAAC,CAAC,CAC7H,CACG,CAED,OAAA9C,EAAM,MAAQ9L,EAAMsK,EAAW,EAE/BwB,EAAM,WAAa9L,EAAM0K,EAAgB,EAEzCoB,EAAM,QAAU,SAASlO,EAAG,CAC1B,OAAO,UAAU,QAAU2W,EAAU3W,EAAGkO,GAASyI,CACrD,EAES,SAAS3c,EAAG,CACjB,OAAAie,EAAYje,EAAGkO,EAAKlO,EAAE2F,CAAE,EAAGwI,EAAKnO,EAAE4F,CAAE,EAAG8oB,EAAMxgB,IAAOC,EAAK,EAAI,GAAKA,EAAKD,GAChEgG,CACX,CACA,CAEO,SAASmJ,GAAK/Z,EAAQwa,EAAQ,CACnC,OAAOA,EACF,OAAOxa,EAAO,QAAQ,EACtB,aAAaA,EAAO,cAAc,EAClC,MAAMA,EAAO,OAAO,EACpB,QAAQA,EAAO,QAAO,CAAE,CAC/B,CAEe,SAASqrB,IAAa,CACnC,IAAIza,EAAQsK,GAAUT,GAAW,EAAGzb,CAAQ,CAAC,EAE7C,OAAA4R,EAAM,KAAO,UAAW,CACtB,OAAOmJ,GAAKnJ,EAAOya,GAAU,CAAE,CACnC,EAESnS,GAAiB,MAAMtI,EAAO,SAAS,CAChD,CAEO,SAAS0a,IAAgB,CAC9B,IAAI1a,EAAQkL,GAAQrB,GAAa,CAAA,EAAE,OAAO,CAAC,EAAG,EAAE,CAAC,EAEjD,OAAA7J,EAAM,KAAO,UAAW,CACtB,OAAOmJ,GAAKnJ,EAAO0a,GAAe,CAAA,EAAE,KAAK1a,EAAM,KAAI,CAAE,CACzD,EAESsI,GAAiB,MAAMtI,EAAO,SAAS,CAChD,CAEO,SAAS2a,IAAmB,CACjC,IAAI3a,EAAQwL,GAAU3B,GAAW,CAAE,EAEnC,OAAA7J,EAAM,KAAO,UAAW,CACtB,OAAOmJ,GAAKnJ,EAAO2a,GAAkB,CAAA,EAAE,SAAS3a,EAAM,SAAQ,CAAE,CACpE,EAESsI,GAAiB,MAAMtI,EAAO,SAAS,CAChD,CAEO,SAAS4a,IAAgB,CAC9B,IAAI5a,EAAQ6L,GAAOhC,GAAW,CAAE,EAEhC,OAAA7J,EAAM,KAAO,UAAW,CACtB,OAAOmJ,GAAKnJ,EAAO4a,GAAe,CAAA,EAAE,SAAS5a,EAAM,SAAQ,CAAE,CACjE,EAESsI,GAAiB,MAAMtI,EAAO,SAAS,CAChD,CAEO,SAAS6a,IAAiB,CAC/B,OAAOD,GAAc,MAAM,KAAM,SAAS,EAAE,SAAS,EAAG,CAC1D,CCtGe,SAASE,IAAqB,CAC3C,IAAI1pB,EAAS,CAAE,EACX0R,EAAe1U,EAEnB,SAAS4R,EAAMlX,EAAG,CAChB,GAAIA,GAAK,MAAQ,CAAC,MAAMA,EAAI,CAACA,CAAC,EAAG,OAAOga,GAAc7Y,GAAOmH,EAAQtI,EAAG,CAAC,EAAI,IAAMsI,EAAO,OAAS,EAAE,CACtG,CAED,OAAA4O,EAAM,OAAS,SAASlO,EAAG,CACzB,GAAI,CAAC,UAAU,OAAQ,OAAOV,EAAO,MAAK,EAC1CA,EAAS,CAAA,EACT,QAASvI,KAAKiJ,EAAOjJ,GAAK,MAAQ,CAAC,MAAMA,EAAI,CAACA,CAAC,GAAGuI,EAAO,KAAKvI,CAAC,EAC/D,OAAAuI,EAAO,KAAK/I,CAAS,EACd2X,CACX,EAEEA,EAAM,aAAe,SAASlO,EAAG,CAC/B,OAAO,UAAU,QAAUgR,EAAehR,EAAGkO,GAAS8C,CAC1D,EAEE9C,EAAM,MAAQ,UAAW,CACvB,OAAO5O,EAAO,IAAI,CAACvI,EAAG,IAAMia,EAAa,GAAK1R,EAAO,OAAS,EAAE,CAAC,CACrE,EAEE4O,EAAM,UAAY,SAAS9X,EAAG,CAC5B,OAAO,MAAM,KAAK,CAAC,OAAQA,EAAI,CAAC,EAAG,CAAC4J,EAAGxI,IAAMuJ,GAASzB,EAAQ9H,EAAIpB,CAAC,CAAC,CACxE,EAEE8X,EAAM,KAAO,UAAW,CACtB,OAAO8a,GAAmBhY,CAAY,EAAE,OAAO1R,CAAM,CACzD,EAESkX,GAAiB,MAAMtI,EAAO,SAAS,CAChD,CC5BA,SAAS6J,IAAc,CACrB,IAAIpY,EAAK,EACLC,EAAK,GACLgS,EAAK,EACLvX,EAAI,EACJ6N,EACAC,EACAC,EACAsgB,EACAO,EACAjY,EAAe1U,EACf2b,EACAE,EAAQ,GACRxB,EAEJ,SAASzI,EAAMlX,EAAG,CAChB,OAAO,MAAMA,EAAI,CAACA,CAAC,EAAI2f,GAAW3f,EAAI,KAAQA,EAAI,CAACihB,EAAUjhB,CAAC,GAAKmR,IAAO9N,EAAIrD,EAAIqD,EAAI8N,EAAKugB,EAAMO,GAAMjY,EAAamH,EAAQ,KAAK,IAAI,EAAG,KAAK,IAAI,EAAGnhB,CAAC,CAAC,EAAIA,CAAC,EAC5J,CAEDkX,EAAM,OAAS,SAAS,EAAG,CACzB,OAAO,UAAU,QAAU,CAACvO,EAAIC,EAAIgS,CAAE,EAAI,EAAG1J,EAAK+P,EAAUtY,EAAK,CAACA,CAAE,EAAGwI,EAAK8P,EAAUrY,EAAK,CAACA,CAAE,EAAGwI,EAAK6P,EAAUrG,EAAK,CAACA,CAAE,EAAG8W,EAAMxgB,IAAOC,EAAK,EAAI,IAAOA,EAAKD,GAAK+gB,EAAM9gB,IAAOC,EAAK,EAAI,IAAOA,EAAKD,GAAK9N,EAAI8N,EAAKD,EAAK,GAAK,EAAGgG,GAAS,CAACvO,EAAIC,EAAIgS,CAAE,CACvP,EAEE1D,EAAM,MAAQ,SAAS,EAAG,CACxB,OAAO,UAAU,QAAUiK,EAAQ,CAAC,CAAC,EAAGjK,GAASiK,CACrD,EAEEjK,EAAM,aAAe,SAAS,EAAG,CAC/B,OAAO,UAAU,QAAU8C,EAAe,EAAG9C,GAAS8C,CAC1D,EAEE,SAAS5O,EAAMsK,EAAa,CAC1B,OAAO,SAAS1M,EAAG,CACjB,IAAI2P,EAAIC,EAAIsZ,EACZ,OAAO,UAAU,QAAU,CAACvZ,EAAIC,EAAIsZ,CAAE,EAAIlpB,EAAGgR,EAAeH,GAAUnE,EAAa,CAACiD,EAAIC,EAAIsZ,CAAE,CAAC,EAAGhb,GAAS,CAAC8C,EAAa,CAAC,EAAGA,EAAa,EAAG,EAAGA,EAAa,CAAC,CAAC,CACrK,CACG,CAED,OAAA9C,EAAM,MAAQ9L,EAAMsK,EAAW,EAE/BwB,EAAM,WAAa9L,EAAM0K,EAAgB,EAEzCoB,EAAM,QAAU,SAAS,EAAG,CAC1B,OAAO,UAAU,QAAUyI,EAAU,EAAGzI,GAASyI,CACrD,EAES,SAAS3c,EAAG,CACjB,OAAAie,EAAYje,EAAGkO,EAAKlO,EAAE2F,CAAE,EAAGwI,EAAKnO,EAAE4F,CAAE,EAAGwI,EAAKpO,EAAE4X,CAAE,EAAG8W,EAAMxgB,IAAOC,EAAK,EAAI,IAAOA,EAAKD,GAAK+gB,EAAM9gB,IAAOC,EAAK,EAAI,IAAOA,EAAKD,GAAK9N,EAAI8N,EAAKD,EAAK,GAAK,EAC7IgG,CACX,CACA,CAEe,SAASib,IAAY,CAClC,IAAIjb,EAAQsK,GAAUT,GAAW,EAAGzb,CAAQ,CAAC,EAE7C,OAAA4R,EAAM,KAAO,UAAW,CACtB,OAAOmJ,GAAKnJ,EAAOib,GAAS,CAAE,CAClC,EAES3S,GAAiB,MAAMtI,EAAO,SAAS,CAChD,CAEO,SAASkb,IAAe,CAC7B,IAAIlb,EAAQkL,GAAQrB,IAAa,EAAE,OAAO,CAAC,GAAK,EAAG,EAAE,CAAC,EAEtD,OAAA7J,EAAM,KAAO,UAAW,CACtB,OAAOmJ,GAAKnJ,EAAOkb,GAAc,CAAA,EAAE,KAAKlb,EAAM,KAAI,CAAE,CACxD,EAESsI,GAAiB,MAAMtI,EAAO,SAAS,CAChD,CAEO,SAASmb,IAAkB,CAChC,IAAInb,EAAQwL,GAAU3B,GAAW,CAAE,EAEnC,OAAA7J,EAAM,KAAO,UAAW,CACtB,OAAOmJ,GAAKnJ,EAAOmb,GAAiB,CAAA,EAAE,SAASnb,EAAM,SAAQ,CAAE,CACnE,EAESsI,GAAiB,MAAMtI,EAAO,SAAS,CAChD,CAEO,SAASob,IAAe,CAC7B,IAAIpb,EAAQ6L,GAAOhC,GAAW,CAAE,EAEhC,OAAA7J,EAAM,KAAO,UAAW,CACtB,OAAOmJ,GAAKnJ,EAAOob,GAAc,CAAA,EAAE,SAASpb,EAAM,SAAQ,CAAE,CAChE,EAESsI,GAAiB,MAAMtI,EAAO,SAAS,CAChD,CAEO,SAASqb,IAAgB,CAC9B,OAAOD,GAAa,MAAM,KAAM,SAAS,EAAE,SAAS,EAAG,CACzD,CCvGe,SAAQnrB,EAACnH,EAAG,CACzB,OAAO,UAAoB,CACzB,OAAOA,CACX,CACA,CCJO,MAAMwyB,GAAM,KAAK,IACXC,EAAQ,KAAK,MACbC,GAAM,KAAK,IACXpuB,GAAM,KAAK,IACXD,GAAM,KAAK,IACXsuB,EAAM,KAAK,IACX1P,EAAO,KAAK,KAEZ7I,EAAU,MACVF,GAAK,KAAK,GACV0Y,GAAS1Y,GAAK,EACdC,GAAM,EAAID,GAEhB,SAAS2Y,GAAK7yB,EAAG,CACtB,OAAOA,EAAI,EAAI,EAAIA,EAAI,GAAKka,GAAK,KAAK,KAAKla,CAAC,CAC9C,CAEO,SAAS8yB,GAAK9yB,EAAG,CACtB,OAAOA,GAAK,EAAI4yB,GAAS5yB,GAAK,GAAK,CAAC4yB,GAAS,KAAK,KAAK5yB,CAAC,CAC1D,CCjBO,SAAS+yB,GAASC,EAAO,CAC9B,IAAIvY,EAAS,EAEb,OAAAuY,EAAM,OAAS,SAAShqB,EAAG,CACzB,GAAI,CAAC,UAAU,OAAQ,OAAOyR,EAC9B,GAAIzR,GAAK,KACPyR,EAAS,SACJ,CACL,MAAM1a,EAAI,KAAK,MAAMiJ,CAAC,EACtB,GAAI,EAAEjJ,GAAK,GAAI,MAAM,IAAI,WAAW,mBAAmBiJ,CAAC,EAAE,EAC1DyR,EAAS1a,CACV,CACD,OAAOizB,CACX,EAES,IAAM,IAAItY,GAAKD,CAAM,CAC9B,CCdA,SAASwY,GAAelzB,EAAG,CACzB,OAAOA,EAAE,WACX,CAEA,SAASmzB,GAAenzB,EAAG,CACzB,OAAOA,EAAE,WACX,CAEA,SAASozB,GAAcpzB,EAAG,CACxB,OAAOA,EAAE,UACX,CAEA,SAASqzB,GAAYrzB,EAAG,CACtB,OAAOA,EAAE,QACX,CAEA,SAASszB,GAAYtzB,EAAG,CACtB,OAAOA,GAAKA,EAAE,QAChB,CAEA,SAASuzB,GAAU3qB,EAAImS,EAAIlS,EAAI+R,EAAIC,EAAIC,EAAI0Y,EAAIC,EAAI,CACjD,IAAIC,EAAM7qB,EAAKD,EAAI+qB,EAAM/Y,EAAKG,EAC1B6Y,EAAMJ,EAAK3Y,EAAIgZ,EAAMJ,EAAK3Y,EAC1B7X,EAAI4wB,EAAMH,EAAME,EAAMD,EAC1B,GAAI,EAAA1wB,EAAIA,EAAIoX,GACZ,OAAApX,GAAK2wB,GAAO7Y,EAAKD,GAAM+Y,GAAOjrB,EAAKiS,IAAO5X,EACnC,CAAC2F,EAAK3F,EAAIywB,EAAK3Y,EAAK9X,EAAI0wB,CAAG,CACpC,CAIA,SAASG,GAAelrB,EAAImS,EAAIlS,EAAI+R,EAAI/B,EAAIkb,EAAI/X,EAAI,CAClD,IAAId,EAAMtS,EAAKC,EACXsS,EAAMJ,EAAKH,EACXxa,GAAM4b,EAAK+X,EAAK,CAACA,GAAM7Q,EAAKhI,EAAMA,EAAMC,EAAMA,CAAG,EACjD6Y,EAAK5zB,EAAK+a,EACV8Y,EAAK,CAAC7zB,EAAK8a,EACXgZ,EAAMtrB,EAAKorB,EACXG,EAAMpZ,EAAKkZ,EACXP,EAAM7qB,EAAKmrB,EACXL,EAAM/Y,EAAKqZ,EACXG,GAAOF,EAAMR,GAAO,EACpBW,GAAOF,EAAMR,GAAO,EACpBrb,EAAKob,EAAMQ,EACX3b,EAAKob,EAAMQ,EACX3b,EAAKF,EAAKA,EAAKC,EAAKA,EACpBrZ,EAAI2Z,EAAKkb,EACTxhB,EAAI2hB,EAAMP,EAAMD,EAAMS,EACtBn0B,GAAKuY,EAAK,EAAI,GAAK,GAAK2K,EAAK3e,GAAI,EAAGrF,EAAIA,EAAIsZ,EAAKjG,EAAIA,CAAC,CAAC,EACvD+hB,GAAO/hB,EAAIgG,EAAKD,EAAKtY,GAAKwY,EAC1B+b,GAAO,CAAChiB,EAAI+F,EAAKC,EAAKvY,GAAKwY,EAC3Bgc,GAAOjiB,EAAIgG,EAAKD,EAAKtY,GAAKwY,EAC1Bic,GAAO,CAACliB,EAAI+F,EAAKC,EAAKvY,GAAKwY,EAC3Bkc,EAAMJ,EAAMF,EACZO,EAAMJ,EAAMF,EACZO,EAAMJ,EAAMJ,EACZS,GAAMJ,EAAMJ,EAIhB,OAAIK,EAAMA,EAAMC,EAAMA,EAAMC,EAAMA,EAAMC,GAAMA,KAAKP,EAAME,EAAKD,EAAME,GAE7D,CACL,GAAIH,EACJ,GAAIC,EACJ,IAAK,CAACP,EACN,IAAK,CAACC,EACN,IAAKK,GAAOzb,EAAK3Z,EAAI,GACrB,IAAKq1B,GAAO1b,EAAK3Z,EAAI,EACzB,CACA,CAEe,SAAA41B,IAAW,CACxB,IAAIC,EAAc7B,GACd8B,EAAc7B,GACd8B,EAAe7tB,EAAS,CAAC,EACzB8tB,EAAY,KACZC,EAAa/B,GACbgC,EAAW/B,GACXgC,EAAW/B,GACXgC,EAAU,KACVpZ,EAAO8W,GAAS8B,CAAG,EAEvB,SAASA,GAAM,CACb,IAAIS,EACAr2B,EACA0Z,EAAK,CAACmc,EAAY,MAAM,KAAM,SAAS,EACvClc,EAAK,CAACmc,EAAY,MAAM,KAAM,SAAS,EACvCnZ,EAAKsZ,EAAW,MAAM,KAAM,SAAS,EAAItC,GACzC/W,EAAKsZ,EAAS,MAAM,KAAM,SAAS,EAAIvC,GACvC5W,EAAKwW,GAAI3W,EAAKD,CAAE,EAChBG,EAAKF,EAAKD,EAQd,GANKyZ,IAASA,EAAUC,EAASrZ,EAAI,GAGjCrD,EAAKD,IAAI1Z,EAAI2Z,EAAIA,EAAKD,EAAIA,EAAK1Z,GAG/B,EAAE2Z,EAAKwB,GAAUib,EAAQ,OAAO,EAAG,CAAC,UAG/BrZ,EAAK7B,GAAMC,EAClBib,EAAQ,OAAOzc,EAAK8Z,GAAI9W,CAAE,EAAGhD,EAAK+Z,EAAI/W,CAAE,CAAC,EACzCyZ,EAAQ,IAAI,EAAG,EAAGzc,EAAIgD,EAAIC,EAAI,CAACE,CAAE,EAC7BpD,EAAKyB,IACPib,EAAQ,OAAO1c,EAAK+Z,GAAI7W,CAAE,EAAGlD,EAAKga,EAAI9W,CAAE,CAAC,EACzCwZ,EAAQ,IAAI,EAAG,EAAG1c,EAAIkD,EAAID,EAAIG,CAAE,OAK/B,CACH,IAAIwZ,EAAM3Z,EACN4Z,EAAM3Z,EACN4Z,EAAM7Z,EACN8Z,EAAM7Z,EACN8Z,EAAM3Z,EACN4Z,EAAM5Z,EACN6Z,EAAKT,EAAS,MAAM,KAAM,SAAS,EAAI,EACvCU,EAAMD,EAAKzb,IAAa6a,EAAY,CAACA,EAAU,MAAM,KAAM,SAAS,EAAIhS,EAAKtK,EAAKA,EAAKC,EAAKA,CAAE,GAC9Fkb,EAAKzvB,GAAImuB,GAAI5Z,EAAKD,CAAE,EAAI,EAAG,CAACqc,EAAa,MAAM,KAAM,SAAS,CAAC,EAC/De,EAAMjC,EACNkC,EAAMlC,EACN5iB,EACAC,EAGJ,GAAI2kB,EAAK1b,EAAS,CAChB,IAAIvC,GAAKib,GAAKgD,EAAKnd,EAAKga,EAAIkD,CAAE,CAAC,EAC3B/d,GAAKgb,GAAKgD,EAAKld,EAAK+Z,EAAIkD,CAAE,CAAC,GAC1BF,GAAO9d,GAAK,GAAKuC,GAASvC,IAAOkE,EAAK,EAAI,GAAK0Z,GAAO5d,GAAI6d,GAAO7d,KACjE8d,EAAM,EAAGF,EAAMC,GAAO9Z,EAAKC,GAAM,IACjC+Z,GAAO9d,GAAK,GAAKsC,GAAStC,IAAOiE,EAAK,EAAI,GAAKwZ,GAAOzd,GAAI0d,GAAO1d,KACjE8d,EAAM,EAAGL,EAAMC,GAAO5Z,EAAKC,GAAM,EACvC,CAED,IAAIZ,EAAMrC,EAAK8Z,GAAI6C,CAAG,EAClBra,EAAMtC,EAAK+Z,EAAI4C,CAAG,EAClB9B,EAAM9a,EAAK+Z,GAAIgD,CAAG,EAClBhC,GAAM/a,EAAKga,EAAI+C,CAAG,EAGtB,GAAI5B,EAAK1Z,EAAS,CAChB,IAAI6Z,GAAMrb,EAAK8Z,GAAI8C,CAAG,EAClBtB,GAAMtb,EAAK+Z,EAAI6C,CAAG,EAClBrB,GAAMxb,EAAK+Z,GAAI+C,CAAG,EAClBrB,GAAMzb,EAAKga,EAAI8C,CAAG,EAClBQ,GAKJ,GAAIja,EAAK9B,GACP,GAAI+b,GAAK3C,GAAUrY,EAAKC,EAAKiZ,GAAKC,GAAKH,GAAKC,GAAKT,EAAKC,EAAG,EAAG,CAC1D,IAAIwC,GAAKjb,EAAMgb,GAAG,CAAC,EACfE,GAAKjb,EAAM+a,GAAG,CAAC,EACfG,GAAKnC,GAAMgC,GAAG,CAAC,EACfI,EAAKnC,GAAM+B,GAAG,CAAC,EACfK,EAAK,EAAI3D,EAAIE,IAAMqD,GAAKE,GAAKD,GAAKE,IAAOpT,EAAKiT,GAAKA,GAAKC,GAAKA,EAAE,EAAIlT,EAAKmT,GAAKA,GAAKC,EAAKA,CAAE,EAAE,EAAI,CAAC,EAChGE,EAAKtT,EAAKgT,GAAG,CAAC,EAAIA,GAAG,CAAC,EAAIA,GAAG,CAAC,EAAIA,GAAG,CAAC,CAAC,EAC3CF,EAAM1xB,GAAIyvB,GAAKnb,EAAK4d,IAAOD,EAAK,EAAE,EAClCN,EAAM3xB,GAAIyvB,GAAKlb,EAAK2d,IAAOD,EAAK,EAAE,CAC9C,MACYP,EAAMC,EAAM,CAGjB,CAGKJ,EAAMxb,EAGH4b,EAAM5b,GACblJ,EAAK2iB,GAAeM,GAAKC,GAAKnZ,EAAKC,EAAKtC,EAAIod,EAAKja,CAAE,EACnD5K,EAAK0iB,GAAeI,GAAKC,GAAKT,EAAKC,GAAK9a,EAAIod,EAAKja,CAAE,EAEnDsZ,EAAQ,OAAOnkB,EAAG,GAAKA,EAAG,IAAKA,EAAG,GAAKA,EAAG,GAAG,EAGzC8kB,EAAMlC,EAAIuB,EAAQ,IAAInkB,EAAG,GAAIA,EAAG,GAAI8kB,EAAKvD,EAAMvhB,EAAG,IAAKA,EAAG,GAAG,EAAGuhB,EAAMthB,EAAG,IAAKA,EAAG,GAAG,EAAG,CAAC4K,CAAE,GAI5FsZ,EAAQ,IAAInkB,EAAG,GAAIA,EAAG,GAAI8kB,EAAKvD,EAAMvhB,EAAG,IAAKA,EAAG,GAAG,EAAGuhB,EAAMvhB,EAAG,IAAKA,EAAG,GAAG,EAAG,CAAC6K,CAAE,EAChFsZ,EAAQ,IAAI,EAAG,EAAGzc,EAAI6Z,EAAMvhB,EAAG,GAAKA,EAAG,IAAKA,EAAG,GAAKA,EAAG,GAAG,EAAGuhB,EAAMthB,EAAG,GAAKA,EAAG,IAAKA,EAAG,GAAKA,EAAG,GAAG,EAAG,CAAC4K,CAAE,EACvGsZ,EAAQ,IAAIlkB,EAAG,GAAIA,EAAG,GAAI6kB,EAAKvD,EAAMthB,EAAG,IAAKA,EAAG,GAAG,EAAGshB,EAAMthB,EAAG,IAAKA,EAAG,GAAG,EAAG,CAAC4K,CAAE,KAK/EsZ,EAAQ,OAAOpa,EAAKC,CAAG,EAAGma,EAAQ,IAAI,EAAG,EAAGzc,EAAI2c,EAAKC,EAAK,CAACzZ,CAAE,GArB5CsZ,EAAQ,OAAOpa,EAAKC,CAAG,EAyBzC,EAAEvC,EAAKyB,IAAY,EAAEub,EAAMvb,GAAUib,EAAQ,OAAO5B,EAAKC,EAAG,EAGvDqC,EAAM3b,GACblJ,EAAK2iB,GAAeJ,EAAKC,GAAKO,GAAKC,GAAKvb,EAAI,CAACod,EAAKha,CAAE,EACpD5K,EAAK0iB,GAAe5Y,EAAKC,EAAKiZ,GAAKC,GAAKzb,EAAI,CAACod,EAAKha,CAAE,EAEpDsZ,EAAQ,OAAOnkB,EAAG,GAAKA,EAAG,IAAKA,EAAG,GAAKA,EAAG,GAAG,EAGzC6kB,EAAMjC,EAAIuB,EAAQ,IAAInkB,EAAG,GAAIA,EAAG,GAAI6kB,EAAKtD,EAAMvhB,EAAG,IAAKA,EAAG,GAAG,EAAGuhB,EAAMthB,EAAG,IAAKA,EAAG,GAAG,EAAG,CAAC4K,CAAE,GAI5FsZ,EAAQ,IAAInkB,EAAG,GAAIA,EAAG,GAAI6kB,EAAKtD,EAAMvhB,EAAG,IAAKA,EAAG,GAAG,EAAGuhB,EAAMvhB,EAAG,IAAKA,EAAG,GAAG,EAAG,CAAC6K,CAAE,EAChFsZ,EAAQ,IAAI,EAAG,EAAG1c,EAAI8Z,EAAMvhB,EAAG,GAAKA,EAAG,IAAKA,EAAG,GAAKA,EAAG,GAAG,EAAGuhB,EAAMthB,EAAG,GAAKA,EAAG,IAAKA,EAAG,GAAKA,EAAG,GAAG,EAAG4K,CAAE,EACtGsZ,EAAQ,IAAIlkB,EAAG,GAAIA,EAAG,GAAI4kB,EAAKtD,EAAMthB,EAAG,IAAKA,EAAG,GAAG,EAAGshB,EAAMthB,EAAG,IAAKA,EAAG,GAAG,EAAG,CAAC4K,CAAE,IAK/EsZ,EAAQ,IAAI,EAAG,EAAG1c,EAAI+c,EAAKD,EAAK1Z,CAAE,CACxC,CAID,GAFAsZ,EAAQ,UAAS,EAEbC,EAAQ,OAAOD,EAAU,KAAMC,EAAS,IAAM,IACnD,CAED,OAAAT,EAAI,SAAW,UAAW,CACxB,IAAI51B,GAAK,CAAC61B,EAAY,MAAM,KAAM,SAAS,GAAI,CAACC,EAAY,MAAM,KAAM,SAAS,GAAK,EAClFv1B,GAAK,CAAC01B,EAAW,MAAM,KAAM,SAAS,GAAI,CAACC,EAAS,MAAM,KAAM,SAAS,GAAK,EAAIjb,GAAK,EAC3F,MAAO,CAACwY,GAAIlzB,CAAC,EAAIP,EAAG0zB,EAAInzB,CAAC,EAAIP,CAAC,CAClC,EAEE41B,EAAI,YAAc,SAAS7rB,EAAG,CAC5B,OAAO,UAAU,QAAU8rB,EAAc,OAAO9rB,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAG6rB,GAAOC,CAChG,EAEED,EAAI,YAAc,SAAS7rB,EAAG,CAC5B,OAAO,UAAU,QAAU+rB,EAAc,OAAO/rB,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAG6rB,GAAOE,CAChG,EAEEF,EAAI,aAAe,SAAS7rB,EAAG,CAC7B,OAAO,UAAU,QAAUgsB,EAAe,OAAOhsB,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAG6rB,GAAOG,CACjG,EAEEH,EAAI,UAAY,SAAS7rB,EAAG,CAC1B,OAAO,UAAU,QAAUisB,EAAYjsB,GAAK,KAAO,KAAO,OAAOA,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAG6rB,GAAOI,CACjH,EAEEJ,EAAI,WAAa,SAAS7rB,EAAG,CAC3B,OAAO,UAAU,QAAUksB,EAAa,OAAOlsB,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAG6rB,GAAOK,CAC/F,EAEEL,EAAI,SAAW,SAAS7rB,EAAG,CACzB,OAAO,UAAU,QAAUmsB,EAAW,OAAOnsB,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAG6rB,GAAOM,CAC7F,EAEEN,EAAI,SAAW,SAAS7rB,EAAG,CACzB,OAAO,UAAU,QAAUosB,EAAW,OAAOpsB,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAG6rB,GAAOO,CAC7F,EAEEP,EAAI,QAAU,SAAS7rB,EAAG,CACxB,OAAO,UAAU,QAAWqsB,EAAUrsB,GAAY,KAAW6rB,GAAOQ,CACxE,EAESR,CACT,CC3QO,IAAI3tB,GAAQ,MAAM,UAAU,MAEpB,SAAQ3D,GAACvD,EAAG,CACzB,OAAO,OAAOA,GAAM,UAAY,WAAYA,EACxCA,EACA,MAAM,KAAKA,CAAC,CAClB,CCNA,SAASw2B,GAAOnB,EAAS,CACvB,KAAK,SAAWA,CAClB,CAEAmB,GAAO,UAAY,CACjB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,OAAS,CACf,EACD,QAAS,UAAW,EACd,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAClF,KAAK,MAAQ,EAAI,KAAK,KACvB,EACD,MAAO,SAASx2B,EAAGyC,EAAG,CAEpB,OADAzC,EAAI,CAACA,EAAGyC,EAAI,CAACA,EACL,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAOzC,EAAGyC,CAAC,EAAI,KAAK,SAAS,OAAOzC,EAAGyC,CAAC,EAAG,MAC/F,IAAK,GAAG,KAAK,OAAS,EACtB,QAAS,KAAK,SAAS,OAAOzC,EAAGyC,CAAC,EAAG,KACtC,CACF,CACH,EAEe,SAAQg0B,GAACpB,EAAS,CAC/B,OAAO,IAAImB,GAAOnB,CAAO,CAC3B,CC9BO,SAASr1B,GAAEwE,EAAG,CACnB,OAAOA,EAAE,CAAC,CACZ,CAEO,SAAS/B,GAAE+B,EAAG,CACnB,OAAOA,EAAE,CAAC,CACZ,CCAe,SAAAkyB,GAAS12B,EAAGyC,EAAG,CAC5B,IAAImH,EAAUzC,EAAS,EAAI,EACvBkuB,EAAU,KACVsB,EAAQF,GACRrV,EAAS,KACTnF,EAAO8W,GAAS2D,CAAI,EAExB12B,EAAI,OAAOA,GAAM,WAAaA,EAAKA,IAAM,OAAa42B,GAASzvB,EAASnH,CAAC,EACzEyC,EAAI,OAAOA,GAAM,WAAaA,EAAKA,IAAM,OAAao0B,GAAS1vB,EAAS1E,CAAC,EAEzE,SAASi0B,EAAK90B,EAAM,CAClB,IAAIpB,EACApB,GAAKwC,EAAO2B,GAAM3B,CAAI,GAAG,OACzB7B,EACA+2B,EAAW,GACXxB,EAIJ,IAFID,GAAW,OAAMjU,EAASuV,EAAMrB,EAASrZ,EAAI,CAAE,GAE9Czb,EAAI,EAAGA,GAAKpB,EAAG,EAAEoB,EAChB,EAAEA,EAAIpB,GAAKwK,EAAQ7J,EAAI6B,EAAKpB,CAAC,EAAGA,EAAGoB,CAAI,KAAOk1B,KAC5CA,EAAW,CAACA,GAAU1V,EAAO,UAAS,EACrCA,EAAO,QAAO,GAEjB0V,GAAU1V,EAAO,MAAM,CAACphB,EAAED,EAAGS,EAAGoB,CAAI,EAAG,CAACa,EAAE1C,EAAGS,EAAGoB,CAAI,CAAC,EAG3D,GAAI0zB,EAAQ,OAAOlU,EAAS,KAAMkU,EAAS,IAAM,IAClD,CAED,OAAAoB,EAAK,EAAI,SAAS1tB,EAAG,CACnB,OAAO,UAAU,QAAUhJ,EAAI,OAAOgJ,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAG0tB,GAAQ12B,CACvF,EAEE02B,EAAK,EAAI,SAAS1tB,EAAG,CACnB,OAAO,UAAU,QAAUvG,EAAI,OAAOuG,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAG0tB,GAAQj0B,CACvF,EAEEi0B,EAAK,QAAU,SAAS1tB,EAAG,CACzB,OAAO,UAAU,QAAUY,EAAU,OAAOZ,GAAM,WAAaA,EAAI7B,EAAS,CAAC,CAAC6B,CAAC,EAAG0tB,GAAQ9sB,CAC9F,EAEE8sB,EAAK,MAAQ,SAAS1tB,EAAG,CACvB,OAAO,UAAU,QAAU2tB,EAAQ3tB,EAAGqsB,GAAW,OAASjU,EAASuV,EAAMtB,CAAO,GAAIqB,GAAQC,CAChG,EAEED,EAAK,QAAU,SAAS1tB,EAAG,CACzB,OAAO,UAAU,QAAUA,GAAK,KAAOqsB,EAAUjU,EAAS,KAAOA,EAASuV,EAAMtB,EAAUrsB,CAAC,EAAG0tB,GAAQrB,CAC1G,EAESqB,CACT,CClDe,SAAAK,GAASpuB,EAAImS,EAAIH,EAAI,CAClC,IAAI/R,EAAK,KACLgB,EAAUzC,EAAS,EAAI,EACvBkuB,EAAU,KACVsB,EAAQF,GACRrV,EAAS,KACTnF,EAAO8W,GAASgE,CAAI,EAExBpuB,EAAK,OAAOA,GAAO,WAAaA,EAAMA,IAAO,OAAaiuB,GAASzvB,EAAS,CAACwB,CAAE,EAC/EmS,EAAK,OAAOA,GAAO,WAAaA,EAA0B3T,EAApB2T,IAAO,OAAsB,EAAc,CAACA,CAAd,EACpEH,EAAK,OAAOA,GAAO,WAAaA,EAAMA,IAAO,OAAakc,GAAS1vB,EAAS,CAACwT,CAAE,EAE/E,SAASoc,EAAKn1B,EAAM,CAClB,IAAIpB,EACA4C,EACAgG,EACAhK,GAAKwC,EAAO2B,GAAM3B,CAAI,GAAG,OACzB7B,EACA+2B,EAAW,GACXxB,EACA0B,EAAM,IAAI,MAAM53B,CAAC,EACjB63B,EAAM,IAAI,MAAM73B,CAAC,EAIrB,IAFIi2B,GAAW,OAAMjU,EAASuV,EAAMrB,EAASrZ,EAAI,CAAE,GAE9Czb,EAAI,EAAGA,GAAKpB,EAAG,EAAEoB,EAAG,CACvB,GAAI,EAAEA,EAAIpB,GAAKwK,EAAQ7J,EAAI6B,EAAKpB,CAAC,EAAGA,EAAGoB,CAAI,KAAOk1B,EAChD,GAAIA,EAAW,CAACA,EACd1zB,EAAI5C,EACJ4gB,EAAO,UAAS,EAChBA,EAAO,UAAS,MACX,CAGL,IAFAA,EAAO,QAAO,EACdA,EAAO,UAAS,EACXhY,EAAI5I,EAAI,EAAG4I,GAAKhG,EAAG,EAAEgG,EACxBgY,EAAO,MAAM4V,EAAI5tB,CAAC,EAAG6tB,EAAI7tB,CAAC,CAAC,EAE7BgY,EAAO,QAAO,EACdA,EAAO,QAAO,CACf,CAEC0V,IACFE,EAAIx2B,CAAC,EAAI,CAACmI,EAAG5I,EAAGS,EAAGoB,CAAI,EAAGq1B,EAAIz2B,CAAC,EAAI,CAACsa,EAAG/a,EAAGS,EAAGoB,CAAI,EACjDwf,EAAO,MAAMxY,EAAK,CAACA,EAAG7I,EAAGS,EAAGoB,CAAI,EAAIo1B,EAAIx2B,CAAC,EAAGma,EAAK,CAACA,EAAG5a,EAAGS,EAAGoB,CAAI,EAAIq1B,EAAIz2B,CAAC,CAAC,EAE5E,CAED,GAAI80B,EAAQ,OAAOlU,EAAS,KAAMkU,EAAS,IAAM,IAClD,CAED,SAAS4B,GAAW,CAClB,OAAOR,GAAI,EAAG,QAAQ9sB,CAAO,EAAE,MAAM+sB,CAAK,EAAE,QAAQtB,CAAO,CAC5D,CAED,OAAA0B,EAAK,EAAI,SAAS/tB,EAAG,CACnB,OAAO,UAAU,QAAUL,EAAK,OAAOK,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAGJ,EAAK,KAAMmuB,GAAQpuB,CACnG,EAEEouB,EAAK,GAAK,SAAS/tB,EAAG,CACpB,OAAO,UAAU,QAAUL,EAAK,OAAOK,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAG+tB,GAAQpuB,CACxF,EAEEouB,EAAK,GAAK,SAAS/tB,EAAG,CACpB,OAAO,UAAU,QAAUJ,EAAKI,GAAK,KAAO,KAAO,OAAOA,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAG+tB,GAAQnuB,CAC3G,EAEEmuB,EAAK,EAAI,SAAS/tB,EAAG,CACnB,OAAO,UAAU,QAAU8R,EAAK,OAAO9R,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAG2R,EAAK,KAAMoc,GAAQjc,CACnG,EAEEic,EAAK,GAAK,SAAS/tB,EAAG,CACpB,OAAO,UAAU,QAAU8R,EAAK,OAAO9R,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAG+tB,GAAQjc,CACxF,EAEEic,EAAK,GAAK,SAAS/tB,EAAG,CACpB,OAAO,UAAU,QAAU2R,EAAK3R,GAAK,KAAO,KAAO,OAAOA,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAG+tB,GAAQpc,CAC3G,EAEEoc,EAAK,OACLA,EAAK,OAAS,UAAW,CACvB,OAAOG,EAAU,EAAC,EAAEvuB,CAAE,EAAE,EAAEmS,CAAE,CAChC,EAEEic,EAAK,OAAS,UAAW,CACvB,OAAOG,EAAU,EAAC,EAAEvuB,CAAE,EAAE,EAAEgS,CAAE,CAChC,EAEEoc,EAAK,OAAS,UAAW,CACvB,OAAOG,EAAU,EAAC,EAAEtuB,CAAE,EAAE,EAAEkS,CAAE,CAChC,EAEEic,EAAK,QAAU,SAAS/tB,EAAG,CACzB,OAAO,UAAU,QAAUY,EAAU,OAAOZ,GAAM,WAAaA,EAAI7B,EAAS,CAAC,CAAC6B,CAAC,EAAG+tB,GAAQntB,CAC9F,EAEEmtB,EAAK,MAAQ,SAAS/tB,EAAG,CACvB,OAAO,UAAU,QAAU2tB,EAAQ3tB,EAAGqsB,GAAW,OAASjU,EAASuV,EAAMtB,CAAO,GAAI0B,GAAQJ,CAChG,EAEEI,EAAK,QAAU,SAAS/tB,EAAG,CACzB,OAAO,UAAU,QAAUA,GAAK,KAAOqsB,EAAUjU,EAAS,KAAOA,EAASuV,EAAMtB,EAAUrsB,CAAC,EAAG+tB,GAAQ1B,CAC1G,EAES0B,CACT,CC/Ge,SAAAI,GAAS33B,EAAGC,EAAG,CAC5B,OAAOA,EAAID,EAAI,GAAKC,EAAID,EAAI,EAAIC,GAAKD,EAAI,EAAI,GAC/C,CCFe,SAAQ8F,GAACvF,EAAG,CACzB,OAAOA,CACT,CCIe,SAAAq3B,IAAW,CACxB,IAAIv2B,EAAQyE,GACR+xB,EAAa33B,GACb6G,EAAO,KACP2uB,EAAa/tB,EAAS,CAAC,EACvBguB,EAAWhuB,EAASgT,EAAG,EACvBib,EAAWjuB,EAAS,CAAC,EAEzB,SAASiwB,EAAIx1B,EAAM,CACjB,IAAIpB,EACApB,GAAKwC,EAAO2B,GAAM3B,CAAI,GAAG,OACzBwB,EACAgG,EACAnG,EAAM,EACNnC,EAAQ,IAAI,MAAM1B,CAAC,EACnBk4B,EAAO,IAAI,MAAMl4B,CAAC,EAClBwc,EAAK,CAACsZ,EAAW,MAAM,KAAM,SAAS,EACtClZ,EAAK,KAAK,IAAI7B,GAAK,KAAK,IAAI,CAACA,GAAKgb,EAAS,MAAM,KAAM,SAAS,EAAIvZ,CAAE,CAAC,EACvEC,EACArX,EAAI,KAAK,IAAI,KAAK,IAAIwX,CAAE,EAAI5c,EAAGg2B,EAAS,MAAM,KAAM,SAAS,CAAC,EAC9DmC,EAAK/yB,GAAKwX,EAAK,EAAI,GAAK,GACxB,EAEJ,IAAKxb,EAAI,EAAGA,EAAIpB,EAAG,EAAEoB,GACd,EAAI82B,EAAKx2B,EAAMN,CAAC,EAAIA,CAAC,EAAI,CAACK,EAAMe,EAAKpB,CAAC,EAAGA,EAAGoB,CAAI,GAAK,IACxDqB,GAAO,GASX,IAJIo0B,GAAc,KAAMv2B,EAAM,KAAK,SAASN,EAAG4C,EAAG,CAAE,OAAOi0B,EAAWC,EAAK92B,CAAC,EAAG82B,EAAKl0B,CAAC,CAAC,CAAE,CAAE,EACjFmD,GAAQ,MAAMzF,EAAM,KAAK,SAASN,EAAG4C,EAAG,CAAE,OAAOmD,EAAK3E,EAAKpB,CAAC,EAAGoB,EAAKwB,CAAC,CAAC,CAAE,CAAE,EAG9E5C,EAAI,EAAG4I,EAAInG,GAAO+Y,EAAK5c,EAAIm4B,GAAMt0B,EAAM,EAAGzC,EAAIpB,EAAG,EAAEoB,EAAGob,EAAKC,EAC9DzY,EAAItC,EAAMN,CAAC,EAAG,EAAI82B,EAAKl0B,CAAC,EAAGyY,EAAKD,GAAM,EAAI,EAAI,EAAIxS,EAAI,GAAKmuB,EAAID,EAAKl0B,CAAC,EAAI,CACvE,KAAMxB,EAAKwB,CAAC,EACZ,MAAO5C,EACP,MAAO,EACP,WAAYob,EACZ,SAAUC,EACV,SAAUrX,CAClB,EAGI,OAAO8yB,CACR,CAED,OAAAF,EAAI,MAAQ,SAASpuB,EAAG,CACtB,OAAO,UAAU,QAAUnI,EAAQ,OAAOmI,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAGouB,GAAOv2B,CAC1F,EAEEu2B,EAAI,WAAa,SAASpuB,EAAG,CAC3B,OAAO,UAAU,QAAUquB,EAAaruB,EAAGzC,EAAO,KAAM6wB,GAAOC,CACnE,EAEED,EAAI,KAAO,SAASpuB,EAAG,CACrB,OAAO,UAAU,QAAUzC,EAAOyC,EAAGquB,EAAa,KAAMD,GAAO7wB,CACnE,EAEE6wB,EAAI,WAAa,SAASpuB,EAAG,CAC3B,OAAO,UAAU,QAAUksB,EAAa,OAAOlsB,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAGouB,GAAOlC,CAC/F,EAEEkC,EAAI,SAAW,SAASpuB,EAAG,CACzB,OAAO,UAAU,QAAUmsB,EAAW,OAAOnsB,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAGouB,GAAOjC,CAC7F,EAEEiC,EAAI,SAAW,SAASpuB,EAAG,CACzB,OAAO,UAAU,QAAUosB,EAAW,OAAOpsB,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAGouB,GAAOhC,CAC7F,EAESgC,CACT,CC7EO,IAAII,GAAoBC,GAAYhB,EAAW,EAEtD,SAASiB,GAAOf,EAAO,CACrB,KAAK,OAASA,CAChB,CAEAe,GAAO,UAAY,CACjB,UAAW,UAAW,CACpB,KAAK,OAAO,WACb,EACD,QAAS,UAAW,CAClB,KAAK,OAAO,SACb,EACD,UAAW,UAAW,CACpB,KAAK,OAAO,WACb,EACD,QAAS,UAAW,CAClB,KAAK,OAAO,SACb,EACD,MAAO,SAASl4B,EAAGP,EAAG,CACpB,KAAK,OAAO,MAAMA,EAAI,KAAK,IAAIO,CAAC,EAAGP,EAAI,CAAC,KAAK,IAAIO,CAAC,CAAC,CACpD,CACH,EAEe,SAASi4B,GAAYd,EAAO,CAEzC,SAASvT,EAAOiS,EAAS,CACvB,OAAO,IAAIqC,GAAOf,EAAMtB,CAAO,CAAC,CACjC,CAED,OAAAjS,EAAO,OAASuT,EAETvT,CACT,CChCO,SAASuU,GAAWpoB,EAAG,CAC5B,IAAI9I,EAAI8I,EAAE,MAEV,OAAAA,EAAE,MAAQA,EAAE,EAAG,OAAOA,EAAE,EACxBA,EAAE,OAASA,EAAE,EAAG,OAAOA,EAAE,EAEzBA,EAAE,MAAQ,SAASvG,EAAG,CACpB,OAAO,UAAU,OAASvC,EAAEgxB,GAAYzuB,CAAC,CAAC,EAAIvC,EAAG,EAAC,MACtD,EAES8I,CACT,CAEe,SAAAqoB,IAAW,CACxB,OAAOD,GAAWjB,GAAI,EAAG,MAAMc,EAAiB,CAAC,CACnD,CCde,SAAAK,IAAW,CACxB,IAAIr4B,EAAIu3B,KAAO,MAAMS,EAAiB,EAClC/wB,EAAIjH,EAAE,MACNmJ,EAAKnJ,EAAE,OACPoJ,EAAKpJ,EAAE,OACPsb,EAAKtb,EAAE,OACPmb,EAAKnb,EAAE,OAEX,OAAAA,EAAE,MAAQA,EAAE,EAAG,OAAOA,EAAE,EACxBA,EAAE,WAAaA,EAAE,GAAI,OAAOA,EAAE,GAC9BA,EAAE,SAAWA,EAAE,GAAI,OAAOA,EAAE,GAC5BA,EAAE,OAASA,EAAE,EAAG,OAAOA,EAAE,EACzBA,EAAE,YAAcA,EAAE,GAAI,OAAOA,EAAE,GAC/BA,EAAE,YAAcA,EAAE,GAAI,OAAOA,EAAE,GAC/BA,EAAE,eAAiB,UAAW,CAAE,OAAOm4B,GAAWhvB,EAAI,CAAA,CAAI,EAAE,OAAOnJ,EAAE,OACrEA,EAAE,aAAe,UAAW,CAAE,OAAOm4B,GAAW/uB,EAAI,CAAA,CAAI,EAAE,OAAOpJ,EAAE,OACnEA,EAAE,gBAAkB,UAAW,CAAE,OAAOm4B,GAAW7c,EAAI,CAAA,CAAI,EAAE,OAAOtb,EAAE,OACtEA,EAAE,gBAAkB,UAAW,CAAE,OAAOm4B,GAAWhd,EAAI,CAAA,CAAI,EAAE,OAAOnb,EAAE,OAEtEA,EAAE,MAAQ,SAASwJ,EAAG,CACpB,OAAO,UAAU,OAASvC,EAAEgxB,GAAYzuB,CAAC,CAAC,EAAIvC,EAAG,EAAC,MACtD,EAESjH,CACT,CC5Be,SAAAs4B,GAAS93B,EAAGyC,EAAG,CAC5B,MAAO,EAAEA,EAAI,CAACA,GAAK,KAAK,IAAIzC,GAAK,KAAK,GAAK,CAAC,EAAGyC,EAAI,KAAK,IAAIzC,CAAC,CAAC,CAChE,CCAA,MAAM+3B,EAAK,CACT,YAAY1C,EAASr1B,EAAG,CACtB,KAAK,SAAWq1B,EAChB,KAAK,GAAKr1B,CACX,CACD,WAAY,CACV,KAAK,MAAQ,CACd,CACD,SAAU,CACR,KAAK,MAAQ,GACd,CACD,WAAY,CACV,KAAK,OAAS,CACf,CACD,SAAU,EACJ,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAClF,KAAK,MAAQ,EAAI,KAAK,KACvB,CACD,MAAMA,EAAGyC,EAAG,CAEV,OADAzC,EAAI,CAACA,EAAGyC,EAAI,CAACA,EACL,KAAK,OAAM,CACjB,IAAK,GAAG,CACN,KAAK,OAAS,EACV,KAAK,MAAO,KAAK,SAAS,OAAOzC,EAAGyC,CAAC,EACpC,KAAK,SAAS,OAAOzC,EAAGyC,CAAC,EAC9B,KACD,CACD,IAAK,GAAG,KAAK,OAAS,EACtB,QAAS,CACH,KAAK,GAAI,KAAK,SAAS,cAAc,KAAK,KAAO,KAAK,IAAMzC,GAAK,EAAG,KAAK,IAAK,KAAK,IAAKyC,EAAGzC,EAAGyC,CAAC,EAC9F,KAAK,SAAS,cAAc,KAAK,IAAK,KAAK,KAAO,KAAK,IAAMA,GAAK,EAAGzC,EAAG,KAAK,IAAKA,EAAGyC,CAAC,EAC3F,KACD,CACF,CACD,KAAK,IAAMzC,EAAG,KAAK,IAAMyC,CAC1B,CACH,CAEA,MAAMu1B,EAAW,CACf,YAAY3C,EAAS,CACnB,KAAK,SAAWA,CACjB,CACD,WAAY,CACV,KAAK,OAAS,CACf,CACD,SAAU,CAAE,CACZ,MAAMr1B,EAAGyC,EAAG,CAEV,GADAzC,EAAI,CAACA,EAAGyC,EAAI,CAACA,EACT,KAAK,SAAW,EAClB,KAAK,OAAS,MACT,CACL,MAAMoV,EAAKigB,GAAY,KAAK,IAAK,KAAK,GAAG,EACnChgB,EAAKggB,GAAY,KAAK,IAAK,KAAK,KAAO,KAAK,IAAMr1B,GAAK,CAAC,EACxDw1B,EAAKH,GAAY93B,EAAG,KAAK,GAAG,EAC5Bk4B,EAAKJ,GAAY93B,EAAGyC,CAAC,EAC3B,KAAK,SAAS,OAAO,GAAGoV,CAAE,EAC1B,KAAK,SAAS,cAAc,GAAGC,EAAI,GAAGmgB,EAAI,GAAGC,CAAE,CAChD,CACD,KAAK,IAAMl4B,EAAG,KAAK,IAAMyC,CAC1B,CACH,CAEO,SAAS01B,GAAM9C,EAAS,CAC7B,OAAO,IAAI0C,GAAK1C,EAAS,EAAI,CAC/B,CAEO,SAAS+C,GAAM/C,EAAS,CAC7B,OAAO,IAAI0C,GAAK1C,EAAS,EAAK,CAChC,CAEO,SAASgD,GAAWhD,EAAS,CAClC,OAAO,IAAI2C,GAAW3C,CAAO,CAC/B,CCpEA,SAASiD,GAAWv4B,EAAG,CACrB,OAAOA,EAAE,MACX,CAEA,SAASw4B,GAAWx4B,EAAG,CACrB,OAAOA,EAAE,MACX,CAEO,SAASy4B,GAAK7B,EAAO,CAC1B,IAAIrwB,EAASgyB,GACTxX,EAASyX,GACTv4B,EAAI42B,GACJn0B,EAAIo0B,GACJxB,EAAU,KACVjU,EAAS,KACTnF,EAAO8W,GAASyF,CAAI,EAExB,SAASA,GAAO,CACd,IAAIlD,EACJ,MAAMmD,EAAOvxB,GAAM,KAAK,SAAS,EAC3B7D,EAAIiD,EAAO,MAAM,KAAMmyB,CAAI,EAC3Bz1B,EAAI8d,EAAO,MAAM,KAAM2X,CAAI,EAMjC,GALIpD,GAAW,OAAMjU,EAASuV,EAAMrB,EAASrZ,EAAI,CAAE,GACnDmF,EAAO,UAAS,EAChBqX,EAAK,CAAC,EAAIp1B,EAAG+d,EAAO,MAAM,CAACphB,EAAE,MAAM,KAAMy4B,CAAI,EAAG,CAACh2B,EAAE,MAAM,KAAMg2B,CAAI,CAAC,EACpEA,EAAK,CAAC,EAAIz1B,EAAGoe,EAAO,MAAM,CAACphB,EAAE,MAAM,KAAMy4B,CAAI,EAAG,CAACh2B,EAAE,MAAM,KAAMg2B,CAAI,CAAC,EACpErX,EAAO,QAAO,EACVkU,EAAQ,OAAOlU,EAAS,KAAMkU,EAAS,IAAM,IAClD,CAED,OAAAkD,EAAK,OAAS,SAASxvB,EAAG,CACxB,OAAO,UAAU,QAAU1C,EAAS0C,EAAGwvB,GAAQlyB,CACnD,EAEEkyB,EAAK,OAAS,SAASxvB,EAAG,CACxB,OAAO,UAAU,QAAU8X,EAAS9X,EAAGwvB,GAAQ1X,CACnD,EAEE0X,EAAK,EAAI,SAASxvB,EAAG,CACnB,OAAO,UAAU,QAAUhJ,EAAI,OAAOgJ,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAGwvB,GAAQx4B,CACvF,EAEEw4B,EAAK,EAAI,SAASxvB,EAAG,CACnB,OAAO,UAAU,QAAUvG,EAAI,OAAOuG,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAGwvB,GAAQ/1B,CACvF,EAEE+1B,EAAK,QAAU,SAASxvB,EAAG,CACzB,OAAO,UAAU,QAAUA,GAAK,KAAOqsB,EAAUjU,EAAS,KAAOA,EAASuV,EAAMtB,EAAUrsB,CAAC,EAAGwvB,GAAQnD,CAC1G,EAESmD,CACT,CAEO,SAASE,IAAiB,CAC/B,OAAOF,GAAKL,EAAK,CACnB,CAEO,SAASQ,IAAe,CAC7B,OAAOH,GAAKJ,EAAK,CACnB,CAEO,SAASQ,IAAa,CAC3B,MAAMrpB,EAAIipB,GAAKH,EAAU,EACzB,OAAA9oB,EAAE,MAAQA,EAAE,EAAG,OAAOA,EAAE,EACxBA,EAAE,OAASA,EAAE,EAAG,OAAOA,EAAE,EAClBA,CACT,CCtEA,MAAMspB,GAAQ5V,EAAK,CAAC,EAEL6V,GAAA,CACb,KAAKzD,EAAS0D,EAAM,CAClB,MAAM95B,EAAIgkB,EAAK8V,EAAO10B,GAAI00B,EAAO,GAAI,GAAI,CAAC,EAAI,OACxC/1B,EAAI/D,EAAI,EACR6Z,EAAI9V,EAAI61B,GACdxD,EAAQ,OAAO,EAAGp2B,CAAC,EACnBo2B,EAAQ,OAAO,EAAG,CAACp2B,CAAC,EACpBo2B,EAAQ,OAAO,CAACvc,EAAG,CAAC9V,CAAC,EACrBqyB,EAAQ,OAAOvc,EAAG9V,CAAC,EACnBqyB,EAAQ,OAAO,CAACvc,EAAG9V,CAAC,EACpBqyB,EAAQ,OAAOvc,EAAG,CAAC9V,CAAC,CACrB,CACH,ECdeg2B,GAAA,CACb,KAAK3D,EAAS0D,EAAM,CAClB,MAAM95B,EAAIgkB,EAAK8V,EAAO7e,EAAE,EACxBmb,EAAQ,OAAOp2B,EAAG,CAAC,EACnBo2B,EAAQ,IAAI,EAAG,EAAGp2B,EAAG,EAAGkb,EAAG,CAC5B,CACH,ECNevW,GAAA,CACb,KAAKyxB,EAAS0D,EAAM,CAClB,MAAM95B,EAAIgkB,EAAK8V,EAAO,CAAC,EAAI,EAC3B1D,EAAQ,OAAO,GAAKp2B,EAAG,CAACA,CAAC,EACzBo2B,EAAQ,OAAO,CAACp2B,EAAG,CAACA,CAAC,EACrBo2B,EAAQ,OAAO,CAACp2B,EAAG,GAAKA,CAAC,EACzBo2B,EAAQ,OAAOp2B,EAAG,GAAKA,CAAC,EACxBo2B,EAAQ,OAAOp2B,EAAG,CAACA,CAAC,EACpBo2B,EAAQ,OAAO,EAAIp2B,EAAG,CAACA,CAAC,EACxBo2B,EAAQ,OAAO,EAAIp2B,EAAGA,CAAC,EACvBo2B,EAAQ,OAAOp2B,EAAGA,CAAC,EACnBo2B,EAAQ,OAAOp2B,EAAG,EAAIA,CAAC,EACvBo2B,EAAQ,OAAO,CAACp2B,EAAG,EAAIA,CAAC,EACxBo2B,EAAQ,OAAO,CAACp2B,EAAGA,CAAC,EACpBo2B,EAAQ,OAAO,GAAKp2B,EAAGA,CAAC,EACxBo2B,EAAQ,UAAS,CAClB,CACH,ECjBM4D,GAAQhW,EAAK,EAAI,CAAC,EAClBiW,GAAUD,GAAQ,EAETE,GAAA,CACb,KAAK9D,EAAS0D,EAAM,CAClB,MAAMt2B,EAAIwgB,EAAK8V,EAAOG,EAAO,EACvBl5B,EAAIyC,EAAIw2B,GACd5D,EAAQ,OAAO,EAAG,CAAC5yB,CAAC,EACpB4yB,EAAQ,OAAOr1B,EAAG,CAAC,EACnBq1B,EAAQ,OAAO,EAAG5yB,CAAC,EACnB4yB,EAAQ,OAAO,CAACr1B,EAAG,CAAC,EACpBq1B,EAAQ,UAAS,CAClB,CACH,ECbe+D,GAAA,CACb,KAAK/D,EAAS0D,EAAM,CAClB,MAAM95B,EAAIgkB,EAAK8V,CAAI,EAAI,OACvB1D,EAAQ,OAAO,EAAG,CAACp2B,CAAC,EACpBo2B,EAAQ,OAAOp2B,EAAG,CAAC,EACnBo2B,EAAQ,OAAO,EAAGp2B,CAAC,EACnBo2B,EAAQ,OAAO,CAACp2B,EAAG,CAAC,EACpBo2B,EAAQ,UAAS,CAClB,CACH,ECTegE,GAAA,CACb,KAAKhE,EAAS0D,EAAM,CAClB,MAAM95B,EAAIgkB,EAAK8V,EAAO10B,GAAI00B,EAAO,EAAG,CAAC,CAAC,EAAI,OAC1C1D,EAAQ,OAAO,CAACp2B,EAAG,CAAC,EACpBo2B,EAAQ,OAAOp2B,EAAG,CAAC,EACnBo2B,EAAQ,OAAO,EAAGp2B,CAAC,EACnBo2B,EAAQ,OAAO,EAAG,CAACp2B,CAAC,CACrB,CACH,ECReikB,GAAA,CACb,KAAKmS,EAAS0D,EAAM,CAClB,MAAMx2B,EAAI0gB,EAAK8V,CAAI,EACb/4B,EAAI,CAACuC,EAAI,EACf8yB,EAAQ,KAAKr1B,EAAGA,EAAGuC,EAAGA,CAAC,CACxB,CACH,ECNe+2B,GAAA,CACb,KAAKjE,EAAS0D,EAAM,CAClB,MAAM95B,EAAIgkB,EAAK8V,CAAI,EAAI,MACvB1D,EAAQ,OAAOp2B,EAAGA,CAAC,EACnBo2B,EAAQ,OAAOp2B,EAAG,CAACA,CAAC,EACpBo2B,EAAQ,OAAO,CAACp2B,EAAG,CAACA,CAAC,EACrBo2B,EAAQ,OAAO,CAACp2B,EAAGA,CAAC,EACpBo2B,EAAQ,UAAS,CAClB,CACH,ECTMkE,GAAK,kBACLC,GAAK7G,EAAIzY,GAAK,EAAE,EAAIyY,EAAI,EAAIzY,GAAK,EAAE,EACnCuf,GAAK9G,EAAIxY,GAAM,EAAE,EAAIqf,GACrBE,GAAK,CAAChH,GAAIvY,GAAM,EAAE,EAAIqf,GAEbG,GAAA,CACb,KAAKtE,EAAS0D,EAAM,CAClB,MAAM95B,EAAIgkB,EAAK8V,EAAOQ,EAAE,EAClBv5B,EAAIy5B,GAAKx6B,EACTwD,EAAIi3B,GAAKz6B,EACfo2B,EAAQ,OAAO,EAAG,CAACp2B,CAAC,EACpBo2B,EAAQ,OAAOr1B,EAAGyC,CAAC,EACnB,QAASjC,EAAI,EAAGA,EAAI,EAAG,EAAEA,EAAG,CAC1B,MAAMhB,EAAI2a,GAAM3Z,EAAI,EACdiG,EAAIisB,GAAIlzB,CAAC,EACT6D,EAAIsvB,EAAInzB,CAAC,EACf61B,EAAQ,OAAOhyB,EAAIpE,EAAG,CAACwH,EAAIxH,CAAC,EAC5Bo2B,EAAQ,OAAO5uB,EAAIzG,EAAIqD,EAAIZ,EAAGY,EAAIrD,EAAIyG,EAAIhE,CAAC,CAC5C,CACD4yB,EAAQ,UAAS,CAClB,CACH,ECrBMwD,GAAQ5V,EAAK,CAAC,EAEL2W,GAAA,CACb,KAAKvE,EAAS0D,EAAM,CAClB,MAAMt2B,EAAI,CAACwgB,EAAK8V,GAAQF,GAAQ,EAAE,EAClCxD,EAAQ,OAAO,EAAG5yB,EAAI,CAAC,EACvB4yB,EAAQ,OAAO,CAACwD,GAAQp2B,EAAG,CAACA,CAAC,EAC7B4yB,EAAQ,OAAOwD,GAAQp2B,EAAG,CAACA,CAAC,EAC5B4yB,EAAQ,UAAS,CAClB,CACH,ECVMwD,GAAQ5V,EAAK,CAAC,EAEL4W,GAAA,CACb,KAAKxE,EAAS0D,EAAM,CAClB,MAAM11B,EAAI4f,EAAK8V,CAAI,EAAI,MACjB/1B,EAAIK,EAAK,EACTyV,EAAKzV,EAAIw1B,GAAS,EACxBxD,EAAQ,OAAO,EAAG,CAAChyB,CAAC,EACpBgyB,EAAQ,OAAOvc,EAAG9V,CAAC,EACnBqyB,EAAQ,OAAO,CAACvc,EAAG9V,CAAC,EACpBqyB,EAAQ,UAAS,CAClB,CACH,ECZM5uB,GAAI,IACJpD,GAAI4f,EAAK,CAAC,EAAI,EACd7Z,GAAI,EAAI6Z,EAAK,EAAE,EACfzjB,IAAK4J,GAAI,EAAI,GAAK,EAET0wB,GAAA,CACb,KAAKzE,EAAS0D,EAAM,CAClB,MAAM95B,EAAIgkB,EAAK8V,EAAOv5B,EAAC,EACjBmJ,EAAK1J,EAAI,EAAG6b,EAAK7b,EAAImK,GACrBR,EAAKD,EAAIgS,EAAK1b,EAAImK,GAAInK,EACtB2b,EAAK,CAAChS,EAAIiS,EAAKF,EACrB0a,EAAQ,OAAO1sB,EAAImS,CAAE,EACrBua,EAAQ,OAAOzsB,EAAI+R,CAAE,EACrB0a,EAAQ,OAAOza,EAAIC,CAAE,EACrBwa,EAAQ,OAAO5uB,GAAIkC,EAAKtF,GAAIyX,EAAIzX,GAAIsF,EAAKlC,GAAIqU,CAAE,EAC/Cua,EAAQ,OAAO5uB,GAAImC,EAAKvF,GAAIsX,EAAItX,GAAIuF,EAAKnC,GAAIkU,CAAE,EAC/C0a,EAAQ,OAAO5uB,GAAImU,EAAKvX,GAAIwX,EAAIxX,GAAIuX,EAAKnU,GAAIoU,CAAE,EAC/Cwa,EAAQ,OAAO5uB,GAAIkC,EAAKtF,GAAIyX,EAAIrU,GAAIqU,EAAKzX,GAAIsF,CAAE,EAC/C0sB,EAAQ,OAAO5uB,GAAImC,EAAKvF,GAAIsX,EAAIlU,GAAIkU,EAAKtX,GAAIuF,CAAE,EAC/CysB,EAAQ,OAAO5uB,GAAImU,EAAKvX,GAAIwX,EAAIpU,GAAIoU,EAAKxX,GAAIuX,CAAE,EAC/Cya,EAAQ,UAAS,CAClB,CACH,ECtBe0E,GAAA,CACb,KAAK1E,EAAS0D,EAAM,CAClB,MAAM95B,EAAIgkB,EAAK8V,EAAO10B,GAAI00B,EAAO,EAAG,GAAG,CAAC,EAAI,MAC5C1D,EAAQ,OAAO,CAACp2B,EAAG,CAACA,CAAC,EACrBo2B,EAAQ,OAAOp2B,EAAGA,CAAC,EACnBo2B,EAAQ,OAAO,CAACp2B,EAAGA,CAAC,EACpBo2B,EAAQ,OAAOp2B,EAAG,CAACA,CAAC,CACrB,CACH,ECOa+6B,GAAc,CACzBhB,GACAp1B,GACAu1B,GACAjW,GACAyW,GACAC,GACAE,EACF,EAGaG,GAAgB,CAC3BjB,GACAK,GACAU,GACAF,GACAf,GACAQ,GACAF,EACF,EAEe,SAASc,GAAO1b,EAAMua,EAAM,CACzC,IAAI1D,EAAU,KACVpZ,EAAO8W,GAAS3U,CAAM,EAE1BI,EAAO,OAAOA,GAAS,WAAaA,EAAOrX,EAASqX,GAAQwa,EAAM,EAClED,EAAO,OAAOA,GAAS,WAAaA,EAAO5xB,EAAS4xB,IAAS,OAAY,GAAK,CAACA,CAAI,EAEnF,SAAS3a,GAAS,CAChB,IAAIkX,EAGJ,GAFKD,IAASA,EAAUC,EAASrZ,EAAI,GACrCuC,EAAK,MAAM,KAAM,SAAS,EAAE,KAAK6W,EAAS,CAAC0D,EAAK,MAAM,KAAM,SAAS,CAAC,EAClEzD,EAAQ,OAAOD,EAAU,KAAMC,EAAS,IAAM,IACnD,CAED,OAAAlX,EAAO,KAAO,SAASpV,EAAG,CACxB,OAAO,UAAU,QAAUwV,EAAO,OAAOxV,GAAM,WAAaA,EAAI7B,EAAS6B,CAAC,EAAGoV,GAAUI,CAC3F,EAEEJ,EAAO,KAAO,SAASpV,EAAG,CACxB,OAAO,UAAU,QAAU+vB,EAAO,OAAO/vB,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAGoV,GAAU2a,CAC5F,EAEE3a,EAAO,QAAU,SAASpV,EAAG,CAC3B,OAAO,UAAU,QAAUqsB,EAAUrsB,GAAY,KAAUoV,GAAUiX,CACzE,EAESjX,CACT,CCjEe,SAAA+b,IAAW,CAAA,CCAnB,SAAS7Z,GAAM8Z,EAAMp6B,EAAGyC,EAAG,CAChC23B,EAAK,SAAS,eACX,EAAIA,EAAK,IAAMA,EAAK,KAAO,GAC3B,EAAIA,EAAK,IAAMA,EAAK,KAAO,GAC3BA,EAAK,IAAM,EAAIA,EAAK,KAAO,GAC3BA,EAAK,IAAM,EAAIA,EAAK,KAAO,GAC3BA,EAAK,IAAM,EAAIA,EAAK,IAAMp6B,GAAK,GAC/Bo6B,EAAK,IAAM,EAAIA,EAAK,IAAM33B,GAAK,CACpC,CACA,CAEO,SAAS43B,GAAMhF,EAAS,CAC7B,KAAK,SAAWA,CAClB,CAEAgF,GAAM,UAAY,CAChB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAChB,KAAK,IAAM,KAAK,IAAM,IACtB,KAAK,OAAS,CACf,EACD,QAAS,UAAW,CAClB,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG/Z,GAAM,KAAM,KAAK,IAAK,KAAK,GAAG,EACtC,IAAK,GAAG,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EAAG,KACnD,EACG,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAClF,KAAK,MAAQ,EAAI,KAAK,KACvB,EACD,MAAO,SAAStgB,EAAGyC,EAAG,CAEpB,OADAzC,EAAI,CAACA,EAAGyC,EAAI,CAACA,EACL,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAOzC,EAAGyC,CAAC,EAAI,KAAK,SAAS,OAAOzC,EAAGyC,CAAC,EAAG,MAC/F,IAAK,GAAG,KAAK,OAAS,EAAG,MACzB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,SAAS,QAAQ,EAAI,KAAK,IAAM,KAAK,KAAO,GAAI,EAAI,KAAK,IAAM,KAAK,KAAO,CAAC,EAC1G,QAAS6d,GAAM,KAAMtgB,EAAGyC,CAAC,EAAG,KAC7B,CACD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMzC,EAChC,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMyC,CACjC,CACH,EAEe,SAAQwQ,GAACoiB,EAAS,CAC/B,OAAO,IAAIgF,GAAMhF,CAAO,CAC1B,CC/CA,SAASiF,GAAYjF,EAAS,CAC5B,KAAK,SAAWA,CAClB,CAEAiF,GAAY,UAAY,CACtB,UAAWH,GACX,QAASA,GACT,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IACjD,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,IACvD,KAAK,OAAS,CACf,EACD,QAAS,UAAW,CAClB,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,CACN,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EACvC,KAAK,SAAS,YACd,KACD,CACD,IAAK,GAAG,CACN,KAAK,SAAS,QAAQ,KAAK,IAAM,EAAI,KAAK,KAAO,GAAI,KAAK,IAAM,EAAI,KAAK,KAAO,CAAC,EACjF,KAAK,SAAS,QAAQ,KAAK,IAAM,EAAI,KAAK,KAAO,GAAI,KAAK,IAAM,EAAI,KAAK,KAAO,CAAC,EACjF,KAAK,SAAS,YACd,KACD,CACD,IAAK,GAAG,CACN,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAC7B,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAC7B,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAC7B,KACD,CACF,CACF,EACD,MAAO,SAASn6B,EAAGyC,EAAG,CAEpB,OADAzC,EAAI,CAACA,EAAGyC,EAAI,CAACA,EACL,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,IAAMzC,EAAG,KAAK,IAAMyC,EAAG,MACrD,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,IAAMzC,EAAG,KAAK,IAAMyC,EAAG,MACrD,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,IAAMzC,EAAG,KAAK,IAAMyC,EAAG,KAAK,SAAS,QAAQ,KAAK,IAAM,EAAI,KAAK,IAAMzC,GAAK,GAAI,KAAK,IAAM,EAAI,KAAK,IAAMyC,GAAK,CAAC,EAAG,MACjJ,QAAS6d,GAAM,KAAMtgB,EAAGyC,CAAC,EAAG,KAC7B,CACD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMzC,EAChC,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMyC,CACjC,CACH,EAEe,SAAQ8R,GAAC8gB,EAAS,CAC/B,OAAO,IAAIiF,GAAYjF,CAAO,CAChC,CCjDA,SAASkF,GAAUlF,EAAS,CAC1B,KAAK,SAAWA,CAClB,CAEAkF,GAAU,UAAY,CACpB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAChB,KAAK,IAAM,KAAK,IAAM,IACtB,KAAK,OAAS,CACf,EACD,QAAS,UAAW,EACd,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAClF,KAAK,MAAQ,EAAI,KAAK,KACvB,EACD,MAAO,SAASv6B,EAAGyC,EAAG,CAEpB,OADAzC,EAAI,CAACA,EAAGyC,EAAI,CAACA,EACL,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,MACzB,IAAK,GAAG,KAAK,OAAS,EAAG,MACzB,IAAK,GAAG,KAAK,OAAS,EAAG,IAAIkG,GAAM,KAAK,IAAM,EAAI,KAAK,IAAM3I,GAAK,EAAG8a,GAAM,KAAK,IAAM,EAAI,KAAK,IAAMrY,GAAK,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAOkG,EAAImS,CAAE,EAAI,KAAK,SAAS,OAAOnS,EAAImS,CAAE,EAAG,MACvL,IAAK,GAAG,KAAK,OAAS,EACtB,QAASwF,GAAM,KAAMtgB,EAAGyC,CAAC,EAAG,KAC7B,CACD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMzC,EAChC,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMyC,CACjC,CACH,EAEe,SAAQ+3B,GAACnF,EAAS,CAC/B,OAAO,IAAIkF,GAAUlF,CAAO,CAC9B,CCpCA,SAASoF,GAAOpF,EAASqF,EAAM,CAC7B,KAAK,OAAS,IAAIL,GAAMhF,CAAO,EAC/B,KAAK,MAAQqF,CACf,CAEAD,GAAO,UAAY,CACjB,UAAW,UAAW,CACpB,KAAK,GAAK,GACV,KAAK,GAAK,GACV,KAAK,OAAO,WACb,EACD,QAAS,UAAW,CAClB,IAAIz6B,EAAI,KAAK,GACTyC,EAAI,KAAK,GACTW,EAAIpD,EAAE,OAAS,EAEnB,GAAIoD,EAAI,EAQN,QAPIuF,EAAK3I,EAAE,CAAC,EACR8a,EAAKrY,EAAE,CAAC,EACR4V,EAAKrY,EAAEoD,CAAC,EAAIuF,EACZ2P,EAAK7V,EAAEW,CAAC,EAAI0X,EACZta,EAAI,GACJwC,EAEG,EAAExC,GAAK4C,GACZJ,EAAIxC,EAAI4C,EACR,KAAK,OAAO,MACV,KAAK,MAAQpD,EAAEQ,CAAC,GAAK,EAAI,KAAK,QAAUmI,EAAK3F,EAAIqV,GACjD,KAAK,MAAQ5V,EAAEjC,CAAC,GAAK,EAAI,KAAK,QAAUsa,EAAK9X,EAAIsV,EAC3D,EAII,KAAK,GAAK,KAAK,GAAK,KACpB,KAAK,OAAO,SACb,EACD,MAAO,SAAStY,EAAGyC,EAAG,CACpB,KAAK,GAAG,KAAK,CAACzC,CAAC,EACf,KAAK,GAAG,KAAK,CAACyC,CAAC,CAChB,CACH,EAEA,MAAAk4B,GAAgB,SAASC,EAAOF,EAAM,CAEpC,SAASC,EAAOtF,EAAS,CACvB,OAAOqF,IAAS,EAAI,IAAIL,GAAMhF,CAAO,EAAI,IAAIoF,GAAOpF,EAASqF,CAAI,CAClE,CAED,OAAAC,EAAO,KAAO,SAASD,EAAM,CAC3B,OAAOE,EAAO,CAACF,CAAI,CACvB,EAESC,CACT,EAAG,GAAI,ECvDA,SAASra,GAAM8Z,EAAMp6B,EAAGyC,EAAG,CAChC23B,EAAK,SAAS,cACZA,EAAK,IAAMA,EAAK,IAAMA,EAAK,IAAMA,EAAK,KACtCA,EAAK,IAAMA,EAAK,IAAMA,EAAK,IAAMA,EAAK,KACtCA,EAAK,IAAMA,EAAK,IAAMA,EAAK,IAAMp6B,GACjCo6B,EAAK,IAAMA,EAAK,IAAMA,EAAK,IAAM33B,GACjC23B,EAAK,IACLA,EAAK,GACT,CACA,CAEO,SAASS,GAASxF,EAASyF,EAAS,CACzC,KAAK,SAAWzF,EAChB,KAAK,IAAM,EAAIyF,GAAW,CAC5B,CAEAD,GAAS,UAAY,CACnB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAAM,KAAK,IAC3B,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,IACjC,KAAK,OAAS,CACf,EACD,QAAS,UAAW,CAClB,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EAAG,MAClD,IAAK,GAAGva,GAAM,KAAM,KAAK,IAAK,KAAK,GAAG,EAAG,KAC1C,EACG,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAClF,KAAK,MAAQ,EAAI,KAAK,KACvB,EACD,MAAO,SAAStgB,EAAGyC,EAAG,CAEpB,OADAzC,EAAI,CAACA,EAAGyC,EAAI,CAACA,EACL,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAOzC,EAAGyC,CAAC,EAAI,KAAK,SAAS,OAAOzC,EAAGyC,CAAC,EAAG,MAC/F,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,IAAMzC,EAAG,KAAK,IAAMyC,EAAG,MACrD,IAAK,GAAG,KAAK,OAAS,EACtB,QAAS6d,GAAM,KAAMtgB,EAAGyC,CAAC,EAAG,KAC7B,CACD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMzC,EACrD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMyC,CACtD,CACH,EAEA,MAAAs4B,GAAgB,SAASH,EAAOE,EAAS,CAEvC,SAASC,EAAS1F,EAAS,CACzB,OAAO,IAAIwF,GAASxF,EAASyF,CAAO,CACrC,CAED,OAAAC,EAAS,QAAU,SAASD,EAAS,CACnC,OAAOF,EAAO,CAACE,CAAO,CAC1B,EAESC,CACT,EAAG,CAAC,ECzDG,SAASC,GAAe3F,EAASyF,EAAS,CAC/C,KAAK,SAAWzF,EAChB,KAAK,IAAM,EAAIyF,GAAW,CAC5B,CAEAE,GAAe,UAAY,CACzB,UAAWb,GACX,QAASA,GACT,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAC5D,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,IAClE,KAAK,OAAS,CACf,EACD,QAAS,UAAW,CAClB,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,CACN,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EACvC,KAAK,SAAS,YACd,KACD,CACD,IAAK,GAAG,CACN,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EACvC,KAAK,SAAS,YACd,KACD,CACD,IAAK,GAAG,CACN,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAC7B,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAC7B,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAC7B,KACD,CACF,CACF,EACD,MAAO,SAASn6B,EAAGyC,EAAG,CAEpB,OADAzC,EAAI,CAACA,EAAGyC,EAAI,CAACA,EACL,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,IAAMzC,EAAG,KAAK,IAAMyC,EAAG,MACrD,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,SAAS,OAAO,KAAK,IAAMzC,EAAG,KAAK,IAAMyC,CAAC,EAAG,MAC3E,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,IAAMzC,EAAG,KAAK,IAAMyC,EAAG,MACrD,QAAS6d,GAAM,KAAMtgB,EAAGyC,CAAC,EAAG,KAC7B,CACD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMzC,EACrD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMyC,CACtD,CACH,EAEA,MAAAw4B,GAAgB,SAASL,EAAOE,EAAS,CAEvC,SAASC,EAAS1F,EAAS,CACzB,OAAO,IAAI2F,GAAe3F,EAASyF,CAAO,CAC3C,CAED,OAAAC,EAAS,QAAU,SAASD,EAAS,CACnC,OAAOF,EAAO,CAACE,CAAO,CAC1B,EAESC,CACT,EAAG,CAAC,EC1DG,SAASG,GAAa7F,EAASyF,EAAS,CAC7C,KAAK,SAAWzF,EAChB,KAAK,IAAM,EAAIyF,GAAW,CAC5B,CAEAI,GAAa,UAAY,CACvB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAAM,KAAK,IAC3B,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,IACjC,KAAK,OAAS,CACf,EACD,QAAS,UAAW,EACd,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAClF,KAAK,MAAQ,EAAI,KAAK,KACvB,EACD,MAAO,SAASl7B,EAAGyC,EAAG,CAEpB,OADAzC,EAAI,CAACA,EAAGyC,EAAI,CAACA,EACL,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,MACzB,IAAK,GAAG,KAAK,OAAS,EAAG,MACzB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EAAI,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EAAG,MAC3H,IAAK,GAAG,KAAK,OAAS,EACtB,QAAS6d,GAAM,KAAMtgB,EAAGyC,CAAC,EAAG,KAC7B,CACD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMzC,EACrD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMyC,CACtD,CACH,EAEA,MAAA04B,GAAgB,SAASP,EAAOE,EAAS,CAEvC,SAASC,EAAS1F,EAAS,CACzB,OAAO,IAAI6F,GAAa7F,EAASyF,CAAO,CACzC,CAED,OAAAC,EAAS,QAAU,SAASD,EAAS,CACnC,OAAOF,EAAO,CAACE,CAAO,CAC1B,EAESC,CACT,EAAG,CAAC,EC7CG,SAASza,GAAM8Z,EAAMp6B,EAAGyC,EAAG,CAChC,IAAImG,EAAKwxB,EAAK,IACVzf,EAAKyf,EAAK,IACVxf,EAAKwf,EAAK,IACVvf,EAAKuf,EAAK,IAEd,GAAIA,EAAK,OAAShgB,EAAS,CACzB,IAAI,EAAI,EAAIggB,EAAK,QAAU,EAAIA,EAAK,OAASA,EAAK,OAASA,EAAK,QAC5Dh7B,EAAI,EAAIg7B,EAAK,QAAUA,EAAK,OAASA,EAAK,QAC9CxxB,GAAMA,EAAK,EAAIwxB,EAAK,IAAMA,EAAK,QAAUA,EAAK,IAAMA,EAAK,SAAWh7B,EACpEub,GAAMA,EAAK,EAAIyf,EAAK,IAAMA,EAAK,QAAUA,EAAK,IAAMA,EAAK,SAAWh7B,CACrE,CAED,GAAIg7B,EAAK,OAAShgB,EAAS,CACzB,IAAI3a,EAAI,EAAI26B,EAAK,QAAU,EAAIA,EAAK,OAASA,EAAK,OAASA,EAAK,QAC5D/wB,EAAI,EAAI+wB,EAAK,QAAUA,EAAK,OAASA,EAAK,QAC9Cxf,GAAMA,EAAKnb,EAAI26B,EAAK,IAAMA,EAAK,QAAUp6B,EAAIo6B,EAAK,SAAW/wB,EAC7DwR,GAAMA,EAAKpb,EAAI26B,EAAK,IAAMA,EAAK,QAAU33B,EAAI23B,EAAK,SAAW/wB,CAC9D,CAED+wB,EAAK,SAAS,cAAcxxB,EAAI+R,EAAIC,EAAIC,EAAIuf,EAAK,IAAKA,EAAK,GAAG,CAChE,CAEA,SAASgB,GAAW/F,EAASgG,EAAO,CAClC,KAAK,SAAWhG,EAChB,KAAK,OAASgG,CAChB,CAEAD,GAAW,UAAY,CACrB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAAM,KAAK,IAC3B,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,IACjC,KAAK,OAAS,KAAK,OAAS,KAAK,OACjC,KAAK,QAAU,KAAK,QAAU,KAAK,QACnC,KAAK,OAAS,CACf,EACD,QAAS,UAAW,CAClB,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EAAG,MAClD,IAAK,GAAG,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAAG,KACzC,EACG,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAClF,KAAK,MAAQ,EAAI,KAAK,KACvB,EACD,MAAO,SAASp7B,EAAGyC,EAAG,CAGpB,GAFAzC,EAAI,CAACA,EAAGyC,EAAI,CAACA,EAET,KAAK,OAAQ,CACf,IAAI64B,EAAM,KAAK,IAAMt7B,EACjBu7B,EAAM,KAAK,IAAM94B,EACrB,KAAK,OAAS,KAAK,KAAK,KAAK,QAAU,KAAK,IAAI64B,EAAMA,EAAMC,EAAMA,EAAK,KAAK,MAAM,CAAC,CACpF,CAED,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAOv7B,EAAGyC,CAAC,EAAI,KAAK,SAAS,OAAOzC,EAAGyC,CAAC,EAAG,MAC/F,IAAK,GAAG,KAAK,OAAS,EAAG,MACzB,IAAK,GAAG,KAAK,OAAS,EACtB,QAAS6d,GAAM,KAAMtgB,EAAGyC,CAAC,EAAG,KAC7B,CAED,KAAK,OAAS,KAAK,OAAQ,KAAK,OAAS,KAAK,OAC9C,KAAK,QAAU,KAAK,QAAS,KAAK,QAAU,KAAK,QACjD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMzC,EACrD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMyC,CACtD,CACH,EAEA,MAAA+4B,GAAgB,SAASZ,EAAOS,EAAO,CAErC,SAASG,EAAWnG,EAAS,CAC3B,OAAOgG,EAAQ,IAAID,GAAW/F,EAASgG,CAAK,EAAI,IAAIR,GAASxF,EAAS,CAAC,CACxE,CAED,OAAAmG,EAAW,MAAQ,SAASH,EAAO,CACjC,OAAOT,EAAO,CAACS,CAAK,CACxB,EAESG,CACT,EAAG,EAAG,ECnFN,SAASC,GAAiBpG,EAASgG,EAAO,CACxC,KAAK,SAAWhG,EAChB,KAAK,OAASgG,CAChB,CAEAI,GAAiB,UAAY,CAC3B,UAAWtB,GACX,QAASA,GACT,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAC5D,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,IAClE,KAAK,OAAS,KAAK,OAAS,KAAK,OACjC,KAAK,QAAU,KAAK,QAAU,KAAK,QACnC,KAAK,OAAS,CACf,EACD,QAAS,UAAW,CAClB,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,CACN,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EACvC,KAAK,SAAS,YACd,KACD,CACD,IAAK,GAAG,CACN,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EACvC,KAAK,SAAS,YACd,KACD,CACD,IAAK,GAAG,CACN,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAC7B,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAC7B,KAAK,MAAM,KAAK,IAAK,KAAK,GAAG,EAC7B,KACD,CACF,CACF,EACD,MAAO,SAASn6B,EAAGyC,EAAG,CAGpB,GAFAzC,EAAI,CAACA,EAAGyC,EAAI,CAACA,EAET,KAAK,OAAQ,CACf,IAAI64B,EAAM,KAAK,IAAMt7B,EACjBu7B,EAAM,KAAK,IAAM94B,EACrB,KAAK,OAAS,KAAK,KAAK,KAAK,QAAU,KAAK,IAAI64B,EAAMA,EAAMC,EAAMA,EAAK,KAAK,MAAM,CAAC,CACpF,CAED,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,IAAMv7B,EAAG,KAAK,IAAMyC,EAAG,MACrD,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,SAAS,OAAO,KAAK,IAAMzC,EAAG,KAAK,IAAMyC,CAAC,EAAG,MAC3E,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,IAAMzC,EAAG,KAAK,IAAMyC,EAAG,MACrD,QAAS6d,GAAM,KAAMtgB,EAAGyC,CAAC,EAAG,KAC7B,CAED,KAAK,OAAS,KAAK,OAAQ,KAAK,OAAS,KAAK,OAC9C,KAAK,QAAU,KAAK,QAAS,KAAK,QAAU,KAAK,QACjD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMzC,EACrD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMyC,CACtD,CACH,EAEA,MAAAi5B,GAAgB,SAASd,EAAOS,EAAO,CAErC,SAASG,EAAWnG,EAAS,CAC3B,OAAOgG,EAAQ,IAAII,GAAiBpG,EAASgG,CAAK,EAAI,IAAIL,GAAe3F,EAAS,CAAC,CACpF,CAED,OAAAmG,EAAW,MAAQ,SAASH,EAAO,CACjC,OAAOT,EAAO,CAACS,CAAK,CACxB,EAESG,CACT,EAAG,EAAG,ECtEN,SAASG,GAAetG,EAASgG,EAAO,CACtC,KAAK,SAAWhG,EAChB,KAAK,OAASgG,CAChB,CAEAM,GAAe,UAAY,CACzB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAAM,KAAK,IAC3B,KAAK,IAAM,KAAK,IAAM,KAAK,IAAM,IACjC,KAAK,OAAS,KAAK,OAAS,KAAK,OACjC,KAAK,QAAU,KAAK,QAAU,KAAK,QACnC,KAAK,OAAS,CACf,EACD,QAAS,UAAW,EACd,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAClF,KAAK,MAAQ,EAAI,KAAK,KACvB,EACD,MAAO,SAAS37B,EAAGyC,EAAG,CAGpB,GAFAzC,EAAI,CAACA,EAAGyC,EAAI,CAACA,EAET,KAAK,OAAQ,CACf,IAAI64B,EAAM,KAAK,IAAMt7B,EACjBu7B,EAAM,KAAK,IAAM94B,EACrB,KAAK,OAAS,KAAK,KAAK,KAAK,QAAU,KAAK,IAAI64B,EAAMA,EAAMC,EAAMA,EAAK,KAAK,MAAM,CAAC,CACpF,CAED,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,MACzB,IAAK,GAAG,KAAK,OAAS,EAAG,MACzB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EAAI,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EAAG,MAC3H,IAAK,GAAG,KAAK,OAAS,EACtB,QAASjb,GAAM,KAAMtgB,EAAGyC,CAAC,EAAG,KAC7B,CAED,KAAK,OAAS,KAAK,OAAQ,KAAK,OAAS,KAAK,OAC9C,KAAK,QAAU,KAAK,QAAS,KAAK,QAAU,KAAK,QACjD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMzC,EACrD,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMyC,CACtD,CACH,EAEA,MAAAm5B,GAAgB,SAAShB,EAAOS,EAAO,CAErC,SAASG,EAAWnG,EAAS,CAC3B,OAAOgG,EAAQ,IAAIM,GAAetG,EAASgG,CAAK,EAAI,IAAIH,GAAa7F,EAAS,CAAC,CAChF,CAED,OAAAmG,EAAW,MAAQ,SAASH,EAAO,CACjC,OAAOT,EAAO,CAACS,CAAK,CACxB,EAESG,CACT,EAAG,EAAG,EC3DN,SAASK,GAAaxG,EAAS,CAC7B,KAAK,SAAWA,CAClB,CAEAwG,GAAa,UAAY,CACvB,UAAW1B,GACX,QAASA,GACT,UAAW,UAAW,CACpB,KAAK,OAAS,CACf,EACD,QAAS,UAAW,CACd,KAAK,QAAQ,KAAK,SAAS,UAAS,CACzC,EACD,MAAO,SAASn6B,EAAGyC,EAAG,CACpBzC,EAAI,CAACA,EAAGyC,EAAI,CAACA,EACT,KAAK,OAAQ,KAAK,SAAS,OAAOzC,EAAGyC,CAAC,GACrC,KAAK,OAAS,EAAG,KAAK,SAAS,OAAOzC,EAAGyC,CAAC,EAChD,CACH,EAEe,SAAQq5B,GAACzG,EAAS,CAC/B,OAAO,IAAIwG,GAAaxG,CAAO,CACjC,CCxBA,SAASlX,GAAKne,EAAG,CACf,OAAOA,EAAI,EAAI,GAAK,CACtB,CAMA,SAAS+7B,GAAO3B,EAAMxf,EAAIC,EAAI,CAC5B,IAAImhB,EAAK5B,EAAK,IAAMA,EAAK,IACrB6B,EAAKrhB,EAAKwf,EAAK,IACfl3B,GAAMk3B,EAAK,IAAMA,EAAK,MAAQ4B,GAAMC,EAAK,GAAK,IAC9C94B,GAAM0X,EAAKuf,EAAK,MAAQ6B,GAAMD,EAAK,GAAK,IACxCx3B,GAAKtB,EAAK+4B,EAAK94B,EAAK64B,IAAOA,EAAKC,GACpC,OAAQ9d,GAAKjb,CAAE,EAAIib,GAAKhb,CAAE,GAAK,KAAK,IAAI,KAAK,IAAID,CAAE,EAAG,KAAK,IAAIC,CAAE,EAAG,GAAM,KAAK,IAAIqB,CAAC,CAAC,GAAK,CAC5F,CAGA,SAAS03B,GAAO9B,EAAM,EAAG,CACvB,IAAI53B,EAAI43B,EAAK,IAAMA,EAAK,IACxB,OAAO53B,GAAK,GAAK43B,EAAK,IAAMA,EAAK,KAAO53B,EAAI,GAAK,EAAI,CACvD,CAKA,SAAS8d,GAAM8Z,EAAMlpB,EAAIC,EAAI,CAC3B,IAAIxI,EAAKyxB,EAAK,IACVtf,EAAKsf,EAAK,IACVxxB,EAAKwxB,EAAK,IACVzf,EAAKyf,EAAK,IACV/hB,GAAMzP,EAAKD,GAAM,EACrByxB,EAAK,SAAS,cAAczxB,EAAK0P,EAAIyC,EAAKzC,EAAKnH,EAAItI,EAAKyP,EAAIsC,EAAKtC,EAAKlH,EAAIvI,EAAI+R,CAAE,CAClF,CAEA,SAASwhB,GAAU9G,EAAS,CAC1B,KAAK,SAAWA,CAClB,CAEA8G,GAAU,UAAY,CACpB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,IAAM,KAAK,IAChB,KAAK,IAAM,KAAK,IAChB,KAAK,IAAM,IACX,KAAK,OAAS,CACf,EACD,QAAS,UAAW,CAClB,OAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,SAAS,OAAO,KAAK,IAAK,KAAK,GAAG,EAAG,MAClD,IAAK,GAAG7b,GAAM,KAAM,KAAK,IAAK4b,GAAO,KAAM,KAAK,GAAG,CAAC,EAAG,KACxD,EACG,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAClF,KAAK,MAAQ,EAAI,KAAK,KACvB,EACD,MAAO,SAASl8B,EAAGyC,EAAG,CACpB,IAAI0O,EAAK,IAGT,GADAnR,EAAI,CAACA,EAAGyC,EAAI,CAACA,EACT,EAAAzC,IAAM,KAAK,KAAOyC,IAAM,KAAK,KACjC,QAAQ,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAOzC,EAAGyC,CAAC,EAAI,KAAK,SAAS,OAAOzC,EAAGyC,CAAC,EAAG,MAC/F,IAAK,GAAG,KAAK,OAAS,EAAG,MACzB,IAAK,GAAG,KAAK,OAAS,EAAG6d,GAAM,KAAM4b,GAAO,KAAM/qB,EAAK4qB,GAAO,KAAM/7B,EAAGyC,CAAC,CAAC,EAAG0O,CAAE,EAAG,MACjF,QAASmP,GAAM,KAAM,KAAK,IAAKnP,EAAK4qB,GAAO,KAAM/7B,EAAGyC,CAAC,CAAC,EAAG,KAC1D,CAED,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMzC,EAChC,KAAK,IAAM,KAAK,IAAK,KAAK,IAAMyC,EAChC,KAAK,IAAM0O,EACZ,CACH,EAEA,SAASirB,GAAU/G,EAAS,CAC1B,KAAK,SAAW,IAAIgH,GAAehH,CAAO,CAC5C,EAEC+G,GAAU,UAAY,OAAO,OAAOD,GAAU,SAAS,GAAG,MAAQ,SAASn8B,EAAGyC,EAAG,CAChF05B,GAAU,UAAU,MAAM,KAAK,KAAM15B,EAAGzC,CAAC,CAC3C,EAEA,SAASq8B,GAAehH,EAAS,CAC/B,KAAK,SAAWA,CAClB,CAEAgH,GAAe,UAAY,CACzB,OAAQ,SAASr8B,EAAGyC,EAAG,CAAE,KAAK,SAAS,OAAOA,EAAGzC,CAAC,CAAI,EACtD,UAAW,UAAW,CAAE,KAAK,SAAS,UAAW,CAAG,EACpD,OAAQ,SAASA,EAAGyC,EAAG,CAAE,KAAK,SAAS,OAAOA,EAAGzC,CAAC,CAAI,EACtD,cAAe,SAAS4I,EAAI+R,EAAIC,EAAIC,EAAI7a,EAAGyC,EAAG,CAAE,KAAK,SAAS,cAAckY,EAAI/R,EAAIiS,EAAID,EAAInY,EAAGzC,CAAC,CAAI,CACtG,EAEO,SAASs8B,GAAUjH,EAAS,CACjC,OAAO,IAAI8G,GAAU9G,CAAO,CAC9B,CAEO,SAASkH,GAAUlH,EAAS,CACjC,OAAO,IAAI+G,GAAU/G,CAAO,CAC9B,CCvGA,SAASmH,GAAQnH,EAAS,CACxB,KAAK,SAAWA,CAClB,CAEAmH,GAAQ,UAAY,CAClB,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,GAAK,GACV,KAAK,GAAK,EACX,EACD,QAAS,UAAW,CAClB,IAAIx8B,EAAI,KAAK,GACTyC,EAAI,KAAK,GACTrD,EAAIY,EAAE,OAEV,GAAIZ,EAEF,GADA,KAAK,MAAQ,KAAK,SAAS,OAAOY,EAAE,CAAC,EAAGyC,EAAE,CAAC,CAAC,EAAI,KAAK,SAAS,OAAOzC,EAAE,CAAC,EAAGyC,EAAE,CAAC,CAAC,EAC3ErD,IAAM,EACR,KAAK,SAAS,OAAOY,EAAE,CAAC,EAAGyC,EAAE,CAAC,CAAC,MAI/B,SAFIg6B,EAAKC,GAAc18B,CAAC,EACpB28B,EAAKD,GAAcj6B,CAAC,EACfuH,EAAK,EAAGrC,EAAK,EAAGA,EAAKvI,EAAG,EAAE4K,EAAI,EAAErC,EACvC,KAAK,SAAS,cAAc80B,EAAG,CAAC,EAAEzyB,CAAE,EAAG2yB,EAAG,CAAC,EAAE3yB,CAAE,EAAGyyB,EAAG,CAAC,EAAEzyB,CAAE,EAAG2yB,EAAG,CAAC,EAAE3yB,CAAE,EAAGhK,EAAE2H,CAAE,EAAGlF,EAAEkF,CAAE,CAAC,GAKtF,KAAK,OAAU,KAAK,QAAU,GAAKvI,IAAM,IAAI,KAAK,SAAS,UAAS,EACxE,KAAK,MAAQ,EAAI,KAAK,MACtB,KAAK,GAAK,KAAK,GAAK,IACrB,EACD,MAAO,SAASY,EAAGyC,EAAG,CACpB,KAAK,GAAG,KAAK,CAACzC,CAAC,EACf,KAAK,GAAG,KAAK,CAACyC,CAAC,CAChB,CACH,EAGA,SAASi6B,GAAc18B,EAAG,CACxB,IAAIQ,EACApB,EAAIY,EAAE,OAAS,EACfqJ,EACA7J,EAAI,IAAI,MAAMJ,CAAC,EACfK,EAAI,IAAI,MAAML,CAAC,EACfH,EAAI,IAAI,MAAMG,CAAC,EAEnB,IADAI,EAAE,CAAC,EAAI,EAAGC,EAAE,CAAC,EAAI,EAAGR,EAAE,CAAC,EAAIe,EAAE,CAAC,EAAI,EAAIA,EAAE,CAAC,EACpCQ,EAAI,EAAGA,EAAIpB,EAAI,EAAG,EAAEoB,EAAGhB,EAAEgB,CAAC,EAAI,EAAGf,EAAEe,CAAC,EAAI,EAAGvB,EAAEuB,CAAC,EAAI,EAAIR,EAAEQ,CAAC,EAAI,EAAIR,EAAEQ,EAAI,CAAC,EAE7E,IADAhB,EAAEJ,EAAI,CAAC,EAAI,EAAGK,EAAEL,EAAI,CAAC,EAAI,EAAGH,EAAEG,EAAI,CAAC,EAAI,EAAIY,EAAEZ,EAAI,CAAC,EAAIY,EAAEZ,CAAC,EACpDoB,EAAI,EAAGA,EAAIpB,EAAG,EAAEoB,EAAG6I,EAAI7J,EAAEgB,CAAC,EAAIf,EAAEe,EAAI,CAAC,EAAGf,EAAEe,CAAC,GAAK6I,EAAGpK,EAAEuB,CAAC,GAAK6I,EAAIpK,EAAEuB,EAAI,CAAC,EAE3E,IADAhB,EAAEJ,EAAI,CAAC,EAAIH,EAAEG,EAAI,CAAC,EAAIK,EAAEL,EAAI,CAAC,EACxBoB,EAAIpB,EAAI,EAAGoB,GAAK,EAAG,EAAEA,EAAGhB,EAAEgB,CAAC,GAAKvB,EAAEuB,CAAC,EAAIhB,EAAEgB,EAAI,CAAC,GAAKf,EAAEe,CAAC,EAE3D,IADAf,EAAEL,EAAI,CAAC,GAAKY,EAAEZ,CAAC,EAAII,EAAEJ,EAAI,CAAC,GAAK,EAC1BoB,EAAI,EAAGA,EAAIpB,EAAI,EAAG,EAAEoB,EAAGf,EAAEe,CAAC,EAAI,EAAIR,EAAEQ,EAAI,CAAC,EAAIhB,EAAEgB,EAAI,CAAC,EACzD,MAAO,CAAChB,EAAGC,CAAC,CACd,CAEe,SAAQm9B,GAACvH,EAAS,CAC/B,OAAO,IAAImH,GAAQnH,CAAO,CAC5B,CChEA,SAASwH,GAAKxH,EAAS,EAAG,CACxB,KAAK,SAAWA,EAChB,KAAK,GAAK,CACZ,CAEAwH,GAAK,UAAY,CACf,UAAW,UAAW,CACpB,KAAK,MAAQ,CACd,EACD,QAAS,UAAW,CAClB,KAAK,MAAQ,GACd,EACD,UAAW,UAAW,CACpB,KAAK,GAAK,KAAK,GAAK,IACpB,KAAK,OAAS,CACf,EACD,QAAS,UAAW,CACd,EAAI,KAAK,IAAM,KAAK,GAAK,GAAK,KAAK,SAAW,GAAG,KAAK,SAAS,OAAO,KAAK,GAAI,KAAK,EAAE,GACtF,KAAK,OAAU,KAAK,QAAU,GAAK,KAAK,SAAW,IAAI,KAAK,SAAS,UAAS,EAC9E,KAAK,OAAS,IAAG,KAAK,GAAK,EAAI,KAAK,GAAI,KAAK,MAAQ,EAAI,KAAK,MACnE,EACD,MAAO,SAAS78B,EAAGyC,EAAG,CAEpB,OADAzC,EAAI,CAACA,EAAGyC,EAAI,CAACA,EACL,KAAK,OAAM,CACjB,IAAK,GAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,KAAK,SAAS,OAAOzC,EAAGyC,CAAC,EAAI,KAAK,SAAS,OAAOzC,EAAGyC,CAAC,EAAG,MAC/F,IAAK,GAAG,KAAK,OAAS,EACtB,QAAS,CACP,GAAI,KAAK,IAAM,EACb,KAAK,SAAS,OAAO,KAAK,GAAIA,CAAC,EAC/B,KAAK,SAAS,OAAOzC,EAAGyC,CAAC,MACpB,CACL,IAAImG,EAAK,KAAK,IAAM,EAAI,KAAK,IAAM5I,EAAI,KAAK,GAC5C,KAAK,SAAS,OAAO4I,EAAI,KAAK,EAAE,EAChC,KAAK,SAAS,OAAOA,EAAInG,CAAC,CAC3B,CACD,KACD,CACF,CACD,KAAK,GAAKzC,EAAG,KAAK,GAAKyC,CACxB,CACH,EAEe,SAAQI,GAACwyB,EAAS,CAC/B,OAAO,IAAIwH,GAAKxH,EAAS,EAAG,CAC9B,CAEO,SAASyH,GAAWzH,EAAS,CAClC,OAAO,IAAIwH,GAAKxH,EAAS,CAAC,CAC5B,CAEO,SAAS0H,GAAU1H,EAAS,CACjC,OAAO,IAAIwH,GAAKxH,EAAS,CAAC,CAC5B,CCpDe,SAAA2H,GAASC,EAAQC,EAAO,CACrC,IAAO99B,EAAI69B,EAAO,QAAU,EAC5B,QAASz8B,EAAI,EAAG4C,EAAGF,EAAIC,EAAK85B,EAAOC,EAAM,CAAC,CAAC,EAAG99B,EAAGiK,EAAIlG,EAAG,OAAQ3C,EAAIpB,EAAG,EAAEoB,EAEvE,IADA0C,EAAKC,EAAIA,EAAK85B,EAAOC,EAAM18B,CAAC,CAAC,EACxB4C,EAAI,EAAGA,EAAIiG,EAAG,EAAEjG,EACnBD,EAAGC,CAAC,EAAE,CAAC,GAAKD,EAAGC,CAAC,EAAE,CAAC,EAAI,MAAMF,EAAGE,CAAC,EAAE,CAAC,CAAC,EAAIF,EAAGE,CAAC,EAAE,CAAC,EAAIF,EAAGE,CAAC,EAAE,CAAC,CAGjE,CCRe,SAAQ+5B,GAACF,EAAQ,CAE9B,QADI79B,EAAI69B,EAAO,OAAQ59B,EAAI,IAAI,MAAMD,CAAC,EAC/B,EAAEA,GAAK,GAAGC,EAAED,CAAC,EAAIA,EACxB,OAAOC,CACT,CCCA,SAAS+9B,GAAWr9B,EAAG+E,EAAK,CAC1B,OAAO/E,EAAE+E,CAAG,CACd,CAEA,SAASu4B,GAAYv4B,EAAK,CACxB,MAAMm4B,EAAS,CAAA,EACf,OAAAA,EAAO,IAAMn4B,EACNm4B,CACT,CAEe,SAAAK,IAAW,CACxB,IAAI93B,EAAO2B,EAAS,EAAE,EAClB+1B,EAAQK,GACRC,EAASC,GACT58B,EAAQu8B,GAEZ,SAASE,EAAM17B,EAAM,CACnB,IAAI87B,EAAK,MAAM,KAAKl4B,EAAK,MAAM,KAAM,SAAS,EAAG63B,EAAW,EACxD78B,EAAGpB,EAAIs+B,EAAG,OAAQt6B,EAAI,GACtBu6B,EAEJ,UAAW59B,KAAK6B,EACd,IAAKpB,EAAI,EAAG,EAAE4C,EAAG5C,EAAIpB,EAAG,EAAEoB,GACvBk9B,EAAGl9B,CAAC,EAAE4C,CAAC,EAAI,CAAC,EAAG,CAACvC,EAAMd,EAAG29B,EAAGl9B,CAAC,EAAE,IAAK4C,EAAGxB,CAAI,CAAC,GAAG,KAAO7B,EAI3D,IAAKS,EAAI,EAAGm9B,EAAKp6B,GAAM25B,EAAMQ,CAAE,CAAC,EAAGl9B,EAAIpB,EAAG,EAAEoB,EAC1Ck9B,EAAGC,EAAGn9B,CAAC,CAAC,EAAE,MAAQA,EAGpB,OAAAg9B,EAAOE,EAAIC,CAAE,EACND,CACR,CAED,OAAAJ,EAAM,KAAO,SAASt0B,EAAG,CACvB,OAAO,UAAU,QAAUxD,EAAO,OAAOwD,GAAM,WAAaA,EAAI7B,EAAS,MAAM,KAAK6B,CAAC,CAAC,EAAGs0B,GAAS93B,CACtG,EAEE83B,EAAM,MAAQ,SAASt0B,EAAG,CACxB,OAAO,UAAU,QAAUnI,EAAQ,OAAOmI,GAAM,WAAaA,EAAI7B,EAAS,CAAC6B,CAAC,EAAGs0B,GAASz8B,CAC5F,EAEEy8B,EAAM,MAAQ,SAASt0B,EAAG,CACxB,OAAO,UAAU,QAAUk0B,EAAQl0B,GAAK,KAAOu0B,GAAY,OAAOv0B,GAAM,WAAaA,EAAI7B,EAAS,MAAM,KAAK6B,CAAC,CAAC,EAAGs0B,GAASJ,CAC/H,EAEEI,EAAM,OAAS,SAASt0B,EAAG,CACzB,OAAO,UAAU,QAAUw0B,EAASx0B,GAAYy0B,GAAgBH,GAASE,CAC7E,EAESF,CACT,CCvDe,SAAAM,GAASX,EAAQC,EAAO,CACrC,IAAO99B,EAAI69B,EAAO,QAAU,EAC5B,SAASz8B,EAAGpB,EAAGgE,EAAI,EAAGiG,EAAI4zB,EAAO,CAAC,EAAE,OAAQx6B,EAAGW,EAAIiG,EAAG,EAAEjG,EAAG,CACzD,IAAKX,EAAIjC,EAAI,EAAGA,EAAIpB,EAAG,EAAEoB,EAAGiC,GAAKw6B,EAAOz8B,CAAC,EAAE4C,CAAC,EAAE,CAAC,GAAK,EACpD,GAAIX,EAAG,IAAKjC,EAAI,EAAGA,EAAIpB,EAAG,EAAEoB,EAAGy8B,EAAOz8B,CAAC,EAAE4C,CAAC,EAAE,CAAC,GAAKX,CACnD,CACD06B,GAAKF,EAAQC,CAAK,EACpB,CCTe,SAAA/K,GAAS8K,EAAQC,EAAO,CACrC,IAAO99B,EAAI69B,EAAO,QAAU,EAC5B,QAASz8B,EAAG4C,EAAI,EAAGrD,EAAGuY,EAAIulB,EAAIC,EAAI1+B,EAAGiK,EAAI4zB,EAAOC,EAAM,CAAC,CAAC,EAAE,OAAQ95B,EAAIiG,EAAG,EAAEjG,EACzE,IAAKy6B,EAAKC,EAAK,EAAGt9B,EAAI,EAAGA,EAAIpB,EAAG,EAAEoB,GAC3B8X,GAAMvY,EAAIk9B,EAAOC,EAAM18B,CAAC,CAAC,EAAE4C,CAAC,GAAG,CAAC,EAAIrD,EAAE,CAAC,GAAK,GAC/CA,EAAE,CAAC,EAAI89B,EAAI99B,EAAE,CAAC,EAAI89B,GAAMvlB,GACfA,EAAK,GACdvY,EAAE,CAAC,EAAI+9B,EAAI/9B,EAAE,CAAC,EAAI+9B,GAAMxlB,IAExBvY,EAAE,CAAC,EAAI,EAAGA,EAAE,CAAC,EAAIuY,EAIzB,CCXe,SAAAylB,GAASd,EAAQC,EAAO,CACrC,IAAO99B,EAAI69B,EAAO,QAAU,EAC5B,SAAS75B,EAAI,EAAGF,EAAK+5B,EAAOC,EAAM,CAAC,CAAC,EAAG99B,EAAGiK,EAAInG,EAAG,OAAQE,EAAIiG,EAAG,EAAEjG,EAAG,CACnE,QAAS5C,EAAI,EAAGiC,EAAI,EAAGjC,EAAIpB,EAAG,EAAEoB,EAAGiC,GAAKw6B,EAAOz8B,CAAC,EAAE4C,CAAC,EAAE,CAAC,GAAK,EAC3DF,EAAGE,CAAC,EAAE,CAAC,GAAKF,EAAGE,CAAC,EAAE,CAAC,EAAI,CAACX,EAAI,CAC7B,CACD06B,GAAKF,EAAQC,CAAK,EACpB,CCPe,SAAAc,GAASf,EAAQC,EAAO,CACrC,GAAI,KAAG99B,EAAI69B,EAAO,QAAU,IAAM,GAAG5zB,GAAKnG,EAAK+5B,EAAOC,EAAM,CAAC,CAAC,GAAG,QAAU,IAC3E,SAASz6B,EAAI,EAAGW,EAAI,EAAGF,EAAImG,EAAGjK,EAAGgE,EAAIiG,EAAG,EAAEjG,EAAG,CAC3C,QAAS5C,EAAI,EAAG2C,EAAK,EAAG86B,EAAK,EAAGz9B,EAAIpB,EAAG,EAAEoB,EAAG,CAK1C,QAJI09B,EAAKjB,EAAOC,EAAM18B,CAAC,CAAC,EACpB29B,EAAOD,EAAG96B,CAAC,EAAE,CAAC,GAAK,EACnBg7B,EAAOF,EAAG96B,EAAI,CAAC,EAAE,CAAC,GAAK,EACvBi7B,GAAMF,EAAOC,GAAQ,EAChBh1B,EAAI,EAAGA,EAAI5I,EAAG,EAAE4I,EAAG,CAC1B,IAAIk1B,EAAKrB,EAAOC,EAAM9zB,CAAC,CAAC,EACpBm1B,EAAOD,EAAGl7B,CAAC,EAAE,CAAC,GAAK,EACnBo7B,EAAOF,EAAGl7B,EAAI,CAAC,EAAE,CAAC,GAAK,EAC3Bi7B,GAAME,EAAOC,CACd,CACDr7B,GAAMg7B,EAAMF,GAAMI,EAAKF,CACxB,CACDj7B,EAAGE,EAAI,CAAC,EAAE,CAAC,GAAKF,EAAGE,EAAI,CAAC,EAAE,CAAC,EAAIX,EAC3BU,IAAIV,GAAKw7B,EAAK96B,EACnB,CACDD,EAAGE,EAAI,CAAC,EAAE,CAAC,GAAKF,EAAGE,EAAI,CAAC,EAAE,CAAC,EAAIX,EAC/B06B,GAAKF,EAAQC,CAAK,EACpB,CCrBe,SAAQuB,GAACxB,EAAQ,CAC9B,IAAIyB,EAAQzB,EAAO,IAAI0B,EAAI,EAC3B,OAAOxB,GAAKF,CAAM,EAAE,KAAK,SAASz9B,EAAGC,EAAG,CAAE,OAAOi/B,EAAMl/B,CAAC,EAAIk/B,EAAMj/B,CAAC,CAAI,CAAA,CACzE,CAEA,SAASk/B,GAAK1B,EAAQ,CAEpB,QADIz8B,EAAI,GAAI4C,EAAI,EAAGhE,EAAI69B,EAAO,OAAQ2B,EAAIC,EAAK,KACxC,EAAEr+B,EAAIpB,IAAQw/B,EAAK,CAAC3B,EAAOz8B,CAAC,EAAE,CAAC,GAAKq+B,IAAIA,EAAKD,EAAIx7B,EAAI5C,GAC5D,OAAO4C,CACT,CCTe,SAAQ7D,GAAC09B,EAAQ,CAC9B,IAAI6B,EAAO7B,EAAO,IAAIh6B,EAAG,EACzB,OAAOk6B,GAAKF,CAAM,EAAE,KAAK,SAASz9B,EAAGC,EAAG,CAAE,OAAOq/B,EAAKt/B,CAAC,EAAIs/B,EAAKr/B,CAAC,CAAI,CAAA,CACvE,CAEO,SAASwD,GAAIg6B,EAAQ,CAE1B,QADI55B,EAAI,EAAG7C,EAAI,GAAIpB,EAAI69B,EAAO,OAAQj5B,EAC/B,EAAExD,EAAIpB,IAAO4E,EAAI,CAACi5B,EAAOz8B,CAAC,EAAE,CAAC,KAAG6C,GAAKW,GAC5C,OAAOX,CACT,CCTe,SAAQ3D,GAACu9B,EAAQ,CAC9B,OAAO19B,GAAU09B,CAAM,EAAE,SAC3B,CCDe,SAAQ8B,GAAC9B,EAAQ,CAC9B,IAAI79B,EAAI69B,EAAO,OACXz8B,EACA4C,EACA07B,EAAO7B,EAAO,IAAIh6B,EAAG,EACrBi6B,EAAQuB,GAAWxB,CAAM,EACzB+B,EAAM,EACNC,EAAS,EACTC,EAAO,CAAE,EACTC,EAAU,CAAA,EAEd,IAAK3+B,EAAI,EAAGA,EAAIpB,EAAG,EAAEoB,EACnB4C,EAAI85B,EAAM18B,CAAC,EACPw+B,EAAMC,GACRD,GAAOF,EAAK17B,CAAC,EACb87B,EAAK,KAAK97B,CAAC,IAEX67B,GAAUH,EAAK17B,CAAC,EAChB+7B,EAAQ,KAAK/7B,CAAC,GAIlB,OAAO+7B,EAAQ,QAAO,EAAG,OAAOD,CAAI,CACtC,CCxBe,SAAQn3B,GAACk1B,EAAQ,CAC9B,OAAOE,GAAKF,CAAM,EAAE,SACtB", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203]}