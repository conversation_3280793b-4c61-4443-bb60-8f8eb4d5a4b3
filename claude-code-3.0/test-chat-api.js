#!/usr/bin/env node

/**
 * Test script to verify the chat API is working correctly
 */

async function testChatAPI() {
  console.log('🧪 Testing Chat API...')
  
  try {
    // Test 1: Check API status
    console.log('\n1. Testing API status...')
    const statusResponse = await fetch('http://localhost:8080/api/status')
    const statusData = await statusResponse.json()
    console.log('✅ API Status:', statusData.status)
    
    // Test 2: Test message endpoint
    console.log('\n2. Testing message endpoint...')
    const messageResponse = await fetch('http://localhost:8080/api/messages', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        content: 'Hello from test script',
        sessionId: 'test-session',
        capabilities: ['general']
      })
    })
    
    if (!messageResponse.ok) {
      throw new Error(`HTTP error! status: ${messageResponse.status}`)
    }
    
    const messageData = await messageResponse.json()
    console.log('✅ Message Response:', {
      id: messageData.id,
      agentId: messageData.agentId,
      processingTime: messageData.processingTime,
      contentLength: messageData.content.length
    })
    
    // Test 3: Test agents endpoint
    console.log('\n3. Testing agents endpoint...')
    const agentsResponse = await fetch('http://localhost:8080/api/agents')
    const agentsData = await agentsResponse.json()
    console.log('✅ Agents:', agentsData.length, 'agents available')
    
    // Test 4: Test metrics endpoint
    console.log('\n4. Testing metrics endpoint...')
    const metricsResponse = await fetch('http://localhost:8080/api/metrics')
    const metricsData = await metricsResponse.json()
    console.log('✅ Metrics:', {
      totalRequests: metricsData.totalRequests,
      activeAgents: metricsData.activeAgents,
      averageLatency: metricsData.averageLatency
    })
    
    console.log('\n🎉 All API tests passed!')
    
  } catch (error) {
    console.error('❌ API test failed:', error.message)
    process.exit(1)
  }
}

// Run the test
testChatAPI()
