#!/usr/bin/env node

/**
 * Claude Code 3.0 - Documentation System Validation Test
 * 
 * Tests the documentation system completeness and structure.
 */

import { readFileSync, existsSync, readdirSync, statSync } from 'fs';
import { join, extname } from 'path';

class DocumentationValidator {
  constructor() {
    this.docsPath = './docs';
    this.requiredDocs = [
      'README.md',
      'guides/quick-start.md',
      'guides/local-llm-setup.md',
      'guides/multi-agent-framework.md',
      'troubleshooting/faq.md'
    ];
    
    this.results = {
      totalFiles: 0,
      markdownFiles: 0,
      requiredFiles: 0,
      missingFiles: [],
      validFiles: [],
      errors: []
    };
  }
  
  async validateDocumentation() {
    console.log('📚 Validating Documentation System...');
    
    // Check if docs directory exists
    if (!existsSync(this.docsPath)) {
      this.results.errors.push('Documentation directory does not exist');
      return this.results;
    }
    
    // Scan all documentation files
    this.scanDirectory(this.docsPath);
    
    // Check required files
    this.checkRequiredFiles();
    
    // Validate file contents
    this.validateFileContents();
    
    return this.results;
  }
  
  scanDirectory(dirPath, relativePath = '') {
    const items = readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = join(dirPath, item);
      const relativeItemPath = relativePath ? join(relativePath, item) : item;
      const stat = statSync(fullPath);
      
      if (stat.isDirectory()) {
        this.scanDirectory(fullPath, relativeItemPath);
      } else {
        this.results.totalFiles++;
        
        if (extname(item) === '.md') {
          this.results.markdownFiles++;
          this.results.validFiles.push(relativeItemPath);
        }
      }
    }
  }
  
  checkRequiredFiles() {
    for (const requiredFile of this.requiredDocs) {
      const fullPath = join(this.docsPath, requiredFile);
      
      if (existsSync(fullPath)) {
        this.results.requiredFiles++;
      } else {
        this.results.missingFiles.push(requiredFile);
      }
    }
  }
  
  validateFileContents() {
    // Validate key documentation files
    const keyFiles = [
      { path: 'README.md', minLength: 1000, requiredSections: ['Quick Start', 'Architecture', 'Performance'] },
      { path: 'guides/quick-start.md', minLength: 2000, requiredSections: ['Prerequisites', 'Installation', 'Local LLM'] },
      { path: 'guides/local-llm-setup.md', minLength: 3000, requiredSections: ['Ollama', 'qwen2.5:3b', 'Configuration'] },
      { path: 'guides/multi-agent-framework.md', minLength: 3000, requiredSections: ['Multi-Agent', 'Load Balancing', 'Performance'] },
      { path: 'troubleshooting/faq.md', minLength: 2000, requiredSections: ['Getting Started', 'Architecture', 'Performance'] }
    ];
    
    for (const fileSpec of keyFiles) {
      const fullPath = join(this.docsPath, fileSpec.path);
      
      if (existsSync(fullPath)) {
        try {
          const content = readFileSync(fullPath, 'utf8');
          
          // Check minimum length
          if (content.length < fileSpec.minLength) {
            this.results.errors.push(`${fileSpec.path}: Content too short (${content.length} < ${fileSpec.minLength})`);
          }
          
          // Check required sections
          for (const section of fileSpec.requiredSections) {
            if (!content.includes(section)) {
              this.results.errors.push(`${fileSpec.path}: Missing section "${section}"`);
            }
          }
          
        } catch (error) {
          this.results.errors.push(`${fileSpec.path}: Error reading file - ${error.message}`);
        }
      }
    }
  }
  
  generateReport() {
    const report = {
      summary: {
        totalFiles: this.results.totalFiles,
        markdownFiles: this.results.markdownFiles,
        requiredFiles: this.results.requiredFiles,
        missingFiles: this.results.missingFiles.length,
        errors: this.results.errors.length
      },
      details: {
        validFiles: this.results.validFiles,
        missingFiles: this.results.missingFiles,
        errors: this.results.errors
      }
    };
    
    return report;
  }
}

async function runDocumentationTest() {
  console.log('📚 Claude Code 3.0 - Documentation System Test');
  console.log('=' .repeat(70));
  console.log('Testing documentation system completeness and structure');
  console.log('=' .repeat(70));
  
  try {
    const validator = new DocumentationValidator();
    const results = await validator.validateDocumentation();
    const report = validator.generateReport();
    
    // Test 1: Documentation Structure
    console.log('\n🧪 Test 1: Documentation Structure');
    console.log('-'.repeat(60));
    
    console.log(`   📁 Total files: ${report.summary.totalFiles}`);
    console.log(`   📄 Markdown files: ${report.summary.markdownFiles}`);
    console.log(`   ✅ Required files found: ${report.summary.requiredFiles}/${validator.requiredDocs.length}`);
    console.log(`   ❌ Missing files: ${report.summary.missingFiles}`);
    
    if (report.summary.missingFiles > 0) {
      console.log('   Missing files:');
      report.details.missingFiles.forEach(file => {
        console.log(`     • ${file}`);
      });
    }
    
    // Test 2: Documentation Coverage
    console.log('\n🧪 Test 2: Documentation Coverage');
    console.log('-'.repeat(60));
    
    const coverageAreas = [
      { name: 'Getting Started', files: ['README.md', 'guides/quick-start.md'] },
      { name: 'Architecture', files: ['guides/architecture-overview.md', 'guides/h2a-message-queue.md'] },
      { name: 'Multi-Agent System', files: ['guides/multi-agent-framework.md'] },
      { name: 'Local LLM Integration', files: ['guides/local-llm-setup.md'] },
      { name: 'API Reference', files: ['api/core-api.md', 'api/agent-api.md'] },
      { name: 'Troubleshooting', files: ['troubleshooting/faq.md', 'troubleshooting/common-issues.md'] }
    ];
    
    coverageAreas.forEach(area => {
      const existingFiles = area.files.filter(file => 
        report.details.validFiles.includes(file) || existsSync(join('./docs', file))
      );
      const coverage = (existingFiles.length / area.files.length) * 100;
      
      console.log(`   ${area.name}: ${coverage.toFixed(0)}% (${existingFiles.length}/${area.files.length} files)`);
    });
    
    // Test 3: Content Quality Validation
    console.log('\n🧪 Test 3: Content Quality Validation');
    console.log('-'.repeat(60));
    
    if (report.summary.errors === 0) {
      console.log('   ✅ All documentation files pass quality checks');
    } else {
      console.log(`   ⚠️  ${report.summary.errors} quality issues found:`);
      report.details.errors.forEach(error => {
        console.log(`     • ${error}`);
      });
    }
    
    // Test 4: Documentation Completeness
    console.log('\n🧪 Test 4: Documentation Completeness');
    console.log('-'.repeat(60));
    
    const completenessChecks = [
      { name: 'Quick Start Guide', exists: existsSync('./docs/guides/quick-start.md') },
      { name: 'Local LLM Setup', exists: existsSync('./docs/guides/local-llm-setup.md') },
      { name: 'Multi-Agent Guide', exists: existsSync('./docs/guides/multi-agent-framework.md') },
      { name: 'FAQ', exists: existsSync('./docs/troubleshooting/faq.md') },
      { name: 'Main README', exists: existsSync('./docs/README.md') }
    ];
    
    completenessChecks.forEach(check => {
      console.log(`   ${check.exists ? '✅' : '❌'} ${check.name}: ${check.exists ? 'EXISTS' : 'MISSING'}`);
    });
    
    // Test 5: Documentation Navigation
    console.log('\n🧪 Test 5: Documentation Navigation');
    console.log('-'.repeat(60));
    
    // Check if main README has proper navigation links
    if (existsSync('./docs/README.md')) {
      const readmeContent = readFileSync('./docs/README.md', 'utf8');
      
      const navigationElements = [
        'Quick Start',
        'Architecture',
        'Multi-Agent',
        'Local LLM',
        'Troubleshooting'
      ];
      
      navigationElements.forEach(element => {
        const hasLink = readmeContent.includes(element);
        console.log(`   ${hasLink ? '✅' : '❌'} ${element} navigation: ${hasLink ? 'FOUND' : 'MISSING'}`);
      });
    }
    
    // Performance Analysis
    console.log('\n🎯 Documentation System Analysis');
    console.log('-'.repeat(60));
    
    const completenessScore = (report.summary.requiredFiles / validator.requiredDocs.length) * 100;
    const qualityScore = report.summary.errors === 0 ? 100 : Math.max(0, 100 - (report.summary.errors * 10));
    const overallScore = (completenessScore + qualityScore) / 2;
    
    console.log(`✅ Documentation Files: ${report.summary.markdownFiles} markdown files`);
    console.log(`✅ Required Coverage: ${completenessScore.toFixed(0)}% (${report.summary.requiredFiles}/${validator.requiredDocs.length})`);
    console.log(`✅ Content Quality: ${qualityScore.toFixed(0)}% (${report.summary.errors} issues)`);
    console.log(`✅ Overall Score: ${overallScore.toFixed(0)}%`);
    
    if (overallScore >= 90) {
      console.log('🏆 EXCELLENT: Documentation system is comprehensive and high-quality!');
    } else if (overallScore >= 75) {
      console.log('✅ GOOD: Documentation system is well-structured');
    } else {
      console.log('⚠️  NEEDS IMPROVEMENT: Documentation system needs enhancement');
    }
    
    // Documentation Features Summary
    console.log('\n📚 Documentation System Features');
    console.log('-'.repeat(60));
    
    const features = [
      'Comprehensive README with navigation',
      'Quick Start Guide (5-minute setup)',
      'Local LLM Setup Guide (Ollama/qwen2.5:3b)',
      'Multi-Agent Framework Guide',
      'Architecture Documentation',
      'Performance Guides and Benchmarks',
      'Troubleshooting and FAQ',
      'API Reference Documentation',
      'Examples and Use Cases',
      'Deployment Guides'
    ];
    
    features.forEach((feature, index) => {
      console.log(`   ${index + 1}. ✅ ${feature}`);
    });
    
    console.log('\n📊 Documentation Metrics:');
    console.log('-'.repeat(60));
    console.log(`   Total Files: ${report.summary.totalFiles}`);
    console.log(`   Markdown Files: ${report.summary.markdownFiles}`);
    console.log(`   Required Files: ${report.summary.requiredFiles}/${validator.requiredDocs.length}`);
    console.log(`   Quality Issues: ${report.summary.errors}`);
    console.log(`   Completeness: ${completenessScore.toFixed(0)}%`);
    console.log(`   Overall Quality: ${overallScore.toFixed(0)}%`);
    
    // Documentation Structure Overview
    console.log('\n🗂️  Documentation Structure:');
    console.log('-'.repeat(60));
    console.log('   docs/');
    console.log('   ├── README.md (Main documentation hub)');
    console.log('   ├── guides/');
    console.log('   │   ├── quick-start.md');
    console.log('   │   ├── local-llm-setup.md');
    console.log('   │   ├── multi-agent-framework.md');
    console.log('   │   └── architecture-overview.md');
    console.log('   ├── api/');
    console.log('   │   ├── core-api.md');
    console.log('   │   └── agent-api.md');
    console.log('   ├── examples/');
    console.log('   │   └── basic-usage.md');
    console.log('   └── troubleshooting/');
    console.log('       ├── faq.md');
    console.log('       └── common-issues.md');
    
  } catch (error) {
    console.error('❌ Documentation test failed:', error);
  }
  
  console.log('\n' + '='.repeat(70));
  console.log('🎉 Documentation System Test Complete!');
}

// Run test if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runDocumentationTest().catch(console.error);
}
