{"name": "claude-code-3.0", "version": "3.0.0", "description": "Claude Code 3.0 - AI-driven code generation and management platform based on 'Documentation as Software' philosophy", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "scripts": {"build": "tsc && npm run build:ui", "build:watch": "tsc --watch", "build:ui": "vite build", "dev": "tsx watch src/api-server.ts", "dev:ui": "cd ui && npm run dev", "dev:full": "concurrently \"npm run dev\" \"npm run dev:ui\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --config jest.integration.config.js", "benchmark": "node --expose-gc dist/tests/benchmarks/performance.benchmark.js", "lint": "eslint src/**/*.ts src/**/*.tsx", "lint:fix": "eslint src/**/*.ts src/**/*.tsx --fix", "clean": "<PERSON><PERSON><PERSON> dist", "docs": "typedoc", "docs:serve": "docusaurus start", "docs:build": "docusaurus build", "validate": "npm run lint && npm run test && npm run test:integration", "start": "node dist/cli/index.js", "start:dev": "tsx src/cli/index.ts"}, "keywords": ["ai", "code-generation", "claude", "agent", "typescript", "react", "event-driven", "message-queue", "documentation-as-software"], "author": "Claude Code 3.0 Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/shareAI-lab/analysis_claude_code.git", "directory": "claude-code-3.0"}, "engines": {"node": ">=18.0.0"}, "files": ["dist/", "README.md", "LICENSE"], "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "zustand": "^4.4.7", "@tanstack/react-query": "^5.8.4", "express": "^4.18.2", "cors": "^2.8.5", "commander": "^11.1.0", "chalk": "^5.3.0", "ora": "^7.0.1", "inquirer": "^9.2.12", "ws": "^8.14.2", "uuid": "^9.0.1", "lodash": "^4.17.21", "eventemitter3": "^5.0.1"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/node": "^20.8.10", "@types/express": "^4.17.21", "@types/cors": "^2.8.15", "@types/ws": "^8.5.8", "@types/uuid": "^9.0.6", "@types/lodash": "^4.14.200", "@types/jest": "^29.5.7", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "typescript": "^5.2.2", "vite": "^4.5.0", "@vitejs/plugin-react": "^4.1.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tsx": "^4.1.4", "eslint": "^8.53.0", "rimraf": "^5.0.5", "concurrently": "^8.2.2", "typedoc": "^0.25.3", "@docusaurus/core": "^3.0.0", "@docusaurus/preset-classic": "^3.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "extensionsToTreatAsEsm": [".ts"], "moduleNameMapping": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "transform": {"^.+\\.tsx?$": ["ts-jest", {"useESM": true}]}, "testMatch": ["**/tests/**/*.test.ts", "**/tests/**/*.test.tsx"], "collectCoverageFrom": ["src/**/*.ts", "src/**/*.tsx", "!src/**/*.d.ts", "!src/examples/**"], "coverageReporters": ["text", "lcov", "html"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}}