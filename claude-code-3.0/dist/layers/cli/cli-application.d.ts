/**
 * Claude Code 3.0 - CLI Layer
 *
 * Layer 1: Command Line Interface
 * Provides command-line access to the Claude Code 3.0 system.
 */
export { CLIApplication } from './cli-application.js';
export declare const DEFAULT_CLI_CONFIG: {
    enabled: boolean;
    interactive: boolean;
    colors: boolean;
    verbose: boolean;
};
/**
 * Create CLI application instance
 */
export declare function createCLIApplication(config?: {}): CLIApplication;
/**
 * CLI Application Class
 */
export declare class CLIApplication {
    private system?;
    private agent?;
    constructor();
    /**
     * Run the CLI application
     */
    run(args: string[]): Promise<void>;
    private setupCommands;
    private startSystem;
    private stopSystem;
    private showStatus;
    private startChat;
    private askQuestion;
    private runTests;
    private startDevelopment;
    private getStatusColor;
    private formatUptime;
    private formatBytes;
    private calculateSuccessRate;
}
//# sourceMappingURL=cli-application.d.ts.map