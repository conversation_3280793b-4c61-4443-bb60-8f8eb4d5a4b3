/**
 * Claude Code 3.0 - Event Layer
 *
 * Layer 4: Event Processing and Routing
 * Advanced event-driven processing and routing system.
 */
import { EventEmitter } from 'eventemitter3';
export interface EventConfig {
    enabled: boolean;
    maxListeners: number;
    enableMetrics: boolean;
    eventHistory: number;
}
export declare const DEFAULT_EVENT_CONFIG: EventConfig;
export interface SystemEvent<T = any> {
    id: string;
    type: string;
    timestamp: Date;
    source: string;
    data: T;
    metadata?: Record<string, any>;
}
/**
 * Event Manager
 * Handles system-wide event processing and routing
 */
export declare class EventManager extends EventEmitter {
    private config;
    private eventHistory;
    private metrics;
    constructor(config?: Partial<EventConfig>);
    /**
     * Emit a system event
     */
    emitSystemEvent<T>(type: string, data: T, source?: string): void;
    /**
     * Get event history
     */
    getEventHistory(type?: string): SystemEvent[];
    /**
     * Get event metrics
     */
    getMetrics(): {
        totalEvents: number;
        eventsPerSecond: number;
        lastEventTime: number;
    };
    /**
     * Clear event history
     */
    clearHistory(): void;
    private generateEventId;
    private updateMetrics;
}
/**
 * Create event manager instance
 */
export declare function createEventManager(config?: Partial<EventConfig>): EventManager;
//# sourceMappingURL=index.d.ts.map