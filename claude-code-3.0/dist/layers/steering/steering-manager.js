/**
 * Claude Code 3.0 - Steering Manager
 *
 * Real-time steering mechanism that orchestrates the h2A message queue system
 * and provides intelligent flow control for the entire system.
 */
import { EventEmitter } from 'eventemitter3';
import { createMessageQueue } from './message-queue.js';
import { QueueEventType, DEFAULT_STEERING_CONFIG } from '../../types/message-queue.js';
/**
 * Steering Manager - Real-time Message Flow Control
 *
 * Responsibilities:
 * - Orchestrate multiple message queues
 * - Implement adaptive throttling
 * - Provide flow control mechanisms
 * - Integrate with Agent, UI, and Tool layers
 * - Monitor system performance and adjust accordingly
 */
export class SteeringManager extends EventEmitter {
    config;
    state;
    // Message Queues
    agentQueue;
    uiQueue;
    toolQueue;
    systemQueue;
    // Flow Control
    steeringInterval;
    loadMonitorInterval;
    // Performance Tracking
    performanceHistory = [];
    lastSteeringTime = Date.now();
    constructor(config = {}) {
        super();
        this.config = { ...DEFAULT_STEERING_CONFIG, ...config };
        this.state = this.initializeState();
        this.initializeQueues();
        this.setupEventHandlers();
    }
    // ============================================================================
    // Public Interface
    // ============================================================================
    /**
     * Start the steering system
     */
    async start() {
        // Start all queues
        await Promise.all([
            this.agentQueue.start(),
            this.uiQueue.start(),
            this.toolQueue.start(),
            this.systemQueue.start()
        ]);
        // Start steering process
        if (this.config.enableRealTimeProcessing) {
            this.startSteering();
        }
        // Start load monitoring
        this.startLoadMonitoring();
        this.state.isActive = true;
        this.emit('steering_started');
    }
    /**
     * Stop the steering system
     */
    async stop() {
        this.stopSteering();
        this.stopLoadMonitoring();
        // Stop all queues
        await Promise.all([
            this.agentQueue.stop(),
            this.uiQueue.stop(),
            this.toolQueue.stop(),
            this.systemQueue.stop()
        ]);
        this.state.isActive = false;
        this.emit('steering_stopped');
    }
    /**
     * Send message to appropriate queue based on destination
     */
    async routeMessage(message) {
        const queue = this.getQueueForMessage(message);
        if (!queue) {
            this.emit('routing_error', { message, error: 'No suitable queue found' });
            return false;
        }
        return await queue.enqueue(message);
    }
    /**
     * Send message to agent processing queue
     */
    async sendToAgent(message) {
        return await this.agentQueue.enqueue(message);
    }
    /**
     * Send message to UI update queue
     */
    async sendToUI(message) {
        return await this.uiQueue.enqueue(message);
    }
    /**
     * Send message to tool execution queue
     */
    async sendToTool(message) {
        return await this.toolQueue.enqueue(message);
    }
    /**
     * Send system-level message
     */
    async sendSystemMessage(message) {
        return await this.systemQueue.enqueue(message);
    }
    /**
     * Get current steering state
     */
    getState() {
        this.updateState();
        return { ...this.state };
    }
    /**
     * Get comprehensive system metrics
     */
    getSystemMetrics() {
        return {
            agent: this.agentQueue.getMetrics(),
            ui: this.uiQueue.getMetrics(),
            tool: this.toolQueue.getMetrics(),
            system: this.systemQueue.getMetrics(),
            steering: {
                currentLoad: this.state.currentLoad,
                throttleLevel: this.state.throttleLevel,
                isActive: this.state.isActive,
                lastSteeringAction: this.state.lastSteeringAction
            }
        };
    }
    /**
     * Manually trigger steering adjustment
     */
    async performSteering() {
        const startTime = Date.now();
        try {
            // Analyze current system load
            const systemLoad = this.calculateSystemLoad();
            // Apply adaptive throttling if needed
            if (this.config.adaptiveThrottling) {
                await this.applyAdaptiveThrottling(systemLoad);
            }
            // Perform flow control
            if (this.config.enableFlowControl) {
                await this.performFlowControl(systemLoad);
            }
            // Update state
            this.state.currentLoad = systemLoad;
            this.state.lastSteeringAction = new Date();
            // Track performance
            const steeringTime = Date.now() - startTime;
            this.performanceHistory.push(steeringTime);
            // Keep performance history manageable
            if (this.performanceHistory.length > 1000) {
                this.performanceHistory = this.performanceHistory.slice(-1000);
            }
            this.emit('steering_performed', {
                systemLoad,
                steeringTime,
                throttleLevel: this.state.throttleLevel
            });
        }
        catch (error) {
            this.emit('steering_error', { error });
        }
    }
    // ============================================================================
    // Private Implementation
    // ============================================================================
    initializeState() {
        return {
            isActive: false,
            currentLoad: 0,
            throttleLevel: 0,
            lastSteeringAction: new Date(),
            queueState: {
                status: 'idle',
                activeBuffer: 'primary',
                primaryBufferSize: 0,
                secondaryBufferSize: 0,
                totalMessages: 0,
                processingRate: 0,
                lastActivity: new Date()
            }
        };
    }
    initializeQueues() {
        // Create specialized queues for different components
        this.agentQueue = createMessageQueue({
            ...this.config.queueConfig,
            maxBufferSize: 2000, // Larger buffer for agent processing
            batchSize: 5 // Smaller batches for responsive agent interaction
        });
        this.uiQueue = createMessageQueue({
            ...this.config.queueConfig,
            maxBufferSize: 500, // Smaller buffer for UI updates
            batchSize: 20, // Larger batches for efficient UI updates
            flushInterval: 16 // ~60fps for smooth UI
        });
        this.toolQueue = createMessageQueue({
            ...this.config.queueConfig,
            maxBufferSize: 1000,
            batchSize: 10
        });
        this.systemQueue = createMessageQueue({
            ...this.config.queueConfig,
            maxBufferSize: 200, // Small buffer for system messages
            batchSize: 1 // Process system messages immediately
        });
    }
    setupEventHandlers() {
        // Forward queue events
        [this.agentQueue, this.uiQueue, this.toolQueue, this.systemQueue].forEach((queue, index) => {
            const queueNames = ['agent', 'ui', 'tool', 'system'];
            const queueName = queueNames[index];
            queue.on(QueueEventType.MESSAGE_PROCESSED, (data) => {
                this.emit('message_processed', { queue: queueName, ...data });
            });
            queue.on(QueueEventType.BACKPRESSURE_ACTIVATED, (data) => {
                this.emit('backpressure_detected', { queue: queueName, ...data });
                this.handleBackpressure(queueName);
            });
            queue.on('error', (error) => {
                this.emit('queue_error', { queue: queueName, error });
            });
        });
    }
    getQueueForMessage(message) {
        // Route based on message metadata or type
        const destination = message.metadata?.destination;
        switch (destination) {
            case 'agent':
                return this.agentQueue;
            case 'ui':
                return this.uiQueue;
            case 'tool':
                return this.toolQueue;
            case 'system':
                return this.systemQueue;
            default:
                // Default routing based on message type
                if (message.type.startsWith('agent_')) {
                    return this.agentQueue;
                }
                else if (message.type.startsWith('ui_')) {
                    return this.uiQueue;
                }
                else if (message.type.startsWith('tool_')) {
                    return this.toolQueue;
                }
                else {
                    return this.systemQueue;
                }
        }
    }
    startSteering() {
        this.steeringInterval = setInterval(() => {
            this.performSteering();
        }, this.config.steeringInterval);
    }
    stopSteering() {
        if (this.steeringInterval) {
            clearInterval(this.steeringInterval);
            this.steeringInterval = undefined;
        }
    }
    startLoadMonitoring() {
        this.loadMonitorInterval = setInterval(() => {
            this.updateState();
        }, 1000); // Update every second
    }
    stopLoadMonitoring() {
        if (this.loadMonitorInterval) {
            clearInterval(this.loadMonitorInterval);
            this.loadMonitorInterval = undefined;
        }
    }
    updateState() {
        this.state.currentLoad = this.calculateSystemLoad();
        // Update queue state with aggregate information
        const allQueues = [this.agentQueue, this.uiQueue, this.toolQueue, this.systemQueue];
        const totalMessages = allQueues.reduce((sum, queue) => sum + queue.getState().totalMessages, 0);
        this.state.queueState.totalMessages = totalMessages;
        this.state.queueState.lastActivity = new Date();
    }
    calculateSystemLoad() {
        const queues = [this.agentQueue, this.uiQueue, this.toolQueue, this.systemQueue];
        const weights = [0.4, 0.2, 0.3, 0.1]; // Weight different queues by importance
        let totalLoad = 0;
        queues.forEach((queue, index) => {
            const metrics = queue.getMetrics();
            const queueLoad = metrics.bufferUtilization * weights[index];
            totalLoad += queueLoad;
        });
        return Math.min(totalLoad, 1.0); // Cap at 100%
    }
    async applyAdaptiveThrottling(systemLoad) {
        let newThrottleLevel = 0;
        if (systemLoad > 0.8) {
            newThrottleLevel = 0.5; // 50% throttling
        }
        else if (systemLoad > 0.6) {
            newThrottleLevel = 0.25; // 25% throttling
        }
        else if (systemLoad > 0.4) {
            newThrottleLevel = 0.1; // 10% throttling
        }
        if (newThrottleLevel !== this.state.throttleLevel) {
            this.state.throttleLevel = newThrottleLevel;
            this.emit('throttle_changed', {
                oldLevel: this.state.throttleLevel,
                newLevel: newThrottleLevel,
                systemLoad
            });
        }
    }
    async performFlowControl(systemLoad) {
        if (systemLoad > this.config.flowControlThreshold) {
            // Implement flow control strategies
            // For now, we'll emit an event that other components can listen to
            this.emit('flow_control_activated', { systemLoad });
        }
    }
    handleBackpressure(queueName) {
        // Implement backpressure handling strategies
        this.emit('backpressure_handled', { queueName, action: 'throttle_increased' });
    }
}
//# sourceMappingURL=steering-manager.js.map