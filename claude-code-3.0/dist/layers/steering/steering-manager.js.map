{"version": 3, "file": "steering-manager.js", "sourceRoot": "", "sources": ["../../../src/layers/steering/steering-manager.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,eAAe,CAAC;AAC7C,OAAO,EAAmB,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AACzE,OAAO,EAKL,cAAc,EACd,uBAAuB,EACxB,MAAM,8BAA8B,CAAC;AAEtC;;;;;;;;;GASG;AACH,MAAM,OAAO,eAAgB,SAAQ,YAAY;IACvC,MAAM,CAAiB;IACvB,KAAK,CAAgB;IAE7B,iBAAiB;IACT,UAAU,CAAkB;IAC5B,OAAO,CAAkB;IACzB,SAAS,CAAkB;IAC3B,WAAW,CAAkB;IAErC,eAAe;IACP,gBAAgB,CAAkB;IAClC,mBAAmB,CAAkB;IAE7C,uBAAuB;IACf,kBAAkB,GAAa,EAAE,CAAC;IAClC,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAEtC,YAAY,SAAkC,EAAE;QAC9C,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,uBAAuB,EAAE,GAAG,MAAM,EAAE,CAAC;QACxD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAEpC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED,+EAA+E;IAC/E,mBAAmB;IACnB,+EAA+E;IAE/E;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,mBAAmB;QACnB,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;YACvB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACpB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;YACtB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;SACzB,CAAC,CAAC;QAEH,yBAAyB;QACzB,IAAI,IAAI,CAAC,MAAM,CAAC,wBAAwB,EAAE,CAAC;YACzC,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;QAED,wBAAwB;QACxB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,kBAAkB;QAClB,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;YACtB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YACnB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;YACrB,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;SACxB,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAI,OAAmB;QACvC,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAE/C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC,CAAC;YAC1E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,MAAM,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAI,OAAmB;QACtC,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAI,OAAmB;QACnC,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAI,OAAmB;QACrC,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAI,OAAmB;QAC5C,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;YACnC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;YAC7B,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;YACjC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE;YACrC,QAAQ,EAAE;gBACR,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW;gBACnC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;gBACvC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAC7B,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB;aAClD;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QACnB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,8BAA8B;YAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAE9C,sCAAsC;YACtC,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;gBACnC,MAAM,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;YACjD,CAAC;YAED,uBAAuB;YACvB,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;gBAClC,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAC5C,CAAC;YAED,eAAe;YACf,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,UAAU,CAAC;YACpC,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI,IAAI,EAAE,CAAC;YAE3C,oBAAoB;YACpB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC5C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE3C,sCAAsC;YACtC,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;gBAC1C,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;YACjE,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAC9B,UAAU;gBACV,YAAY;gBACZ,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;aACxC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED,+EAA+E;IAC/E,yBAAyB;IACzB,+EAA+E;IAEvE,eAAe;QACrB,OAAO;YACL,QAAQ,EAAE,KAAK;YACf,WAAW,EAAE,CAAC;YACd,aAAa,EAAE,CAAC;YAChB,kBAAkB,EAAE,IAAI,IAAI,EAAE;YAC9B,UAAU,EAAE;gBACV,MAAM,EAAE,MAAa;gBACrB,YAAY,EAAE,SAAS;gBACvB,iBAAiB,EAAE,CAAC;gBACpB,mBAAmB,EAAE,CAAC;gBACtB,aAAa,EAAE,CAAC;gBAChB,cAAc,EAAE,CAAC;gBACjB,YAAY,EAAE,IAAI,IAAI,EAAE;aACzB;SACF,CAAC;IACJ,CAAC;IAEO,gBAAgB;QACtB,qDAAqD;QACrD,IAAI,CAAC,UAAU,GAAG,kBAAkB,CAAC;YACnC,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW;YAC1B,aAAa,EAAE,IAAI,EAAE,qCAAqC;YAC1D,SAAS,EAAE,CAAC,CAAC,mDAAmD;SACjE,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,GAAG,kBAAkB,CAAC;YAChC,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW;YAC1B,aAAa,EAAE,GAAG,EAAE,gCAAgC;YACpD,SAAS,EAAE,EAAE,EAAE,0CAA0C;YACzD,aAAa,EAAE,EAAE,CAAC,uBAAuB;SAC1C,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,GAAG,kBAAkB,CAAC;YAClC,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW;YAC1B,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,EAAE;SACd,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,GAAG,kBAAkB,CAAC;YACpC,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW;YAC1B,aAAa,EAAE,GAAG,EAAE,mCAAmC;YACvD,SAAS,EAAE,CAAC,CAAC,sCAAsC;SACpD,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB;QACxB,uBAAuB;QACvB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YACzF,MAAM,UAAU,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;YACrD,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;YAEpC,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC,IAAI,EAAE,EAAE;gBAClD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC;YAEH,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,sBAAsB,EAAE,CAAC,IAAI,EAAE,EAAE;gBACvD,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC;gBAClE,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC;YAEH,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC1B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAI,OAAmB;QAC/C,0CAA0C;QAC1C,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,WAAW,CAAC;QAElD,QAAQ,WAAW,EAAE,CAAC;YACpB,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,UAAgC,CAAC;YAC/C,KAAK,IAAI;gBACP,OAAO,IAAI,CAAC,OAA6B,CAAC;YAC5C,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,SAA+B,CAAC;YAC9C,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,WAAiC,CAAC;YAChD;gBACE,wCAAwC;gBACxC,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACtC,OAAO,IAAI,CAAC,UAAgC,CAAC;gBAC/C,CAAC;qBAAM,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC1C,OAAO,IAAI,CAAC,OAA6B,CAAC;gBAC5C,CAAC;qBAAM,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC5C,OAAO,IAAI,CAAC,SAA+B,CAAC;gBAC9C,CAAC;qBAAM,CAAC;oBACN,OAAO,IAAI,CAAC,WAAiC,CAAC;gBAChD,CAAC;QACL,CAAC;IACH,CAAC;IAEO,aAAa;QACnB,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,GAAG,EAAE;YACvC,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACnC,CAAC;IAEO,YAAY;QAClB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACrC,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QACpC,CAAC;IACH,CAAC;IAEO,mBAAmB;QACzB,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC,GAAG,EAAE;YAC1C,IAAI,CAAC,WAAW,EAAE,CAAC;QACrB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,sBAAsB;IAClC,CAAC;IAEO,kBAAkB;QACxB,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACxC,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;QACvC,CAAC;IACH,CAAC;IAEO,WAAW;QACjB,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEpD,gDAAgD;QAChD,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACpF,MAAM,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QAEhG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,aAAa,GAAG,aAAa,CAAC;QACpD,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;IAClD,CAAC;IAEO,mBAAmB;QACzB,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACjF,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,wCAAwC;QAE9E,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;YACnC,MAAM,SAAS,GAAG,OAAO,CAAC,iBAAiB,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;YAC7D,SAAS,IAAI,SAAS,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,cAAc;IACjD,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,UAAkB;QACtD,IAAI,gBAAgB,GAAG,CAAC,CAAC;QAEzB,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YACrB,gBAAgB,GAAG,GAAG,CAAC,CAAC,iBAAiB;QAC3C,CAAC;aAAM,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YAC5B,gBAAgB,GAAG,IAAI,CAAC,CAAC,iBAAiB;QAC5C,CAAC;aAAM,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YAC5B,gBAAgB,GAAG,GAAG,CAAC,CAAC,iBAAiB;QAC3C,CAAC;QAED,IAAI,gBAAgB,KAAK,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YAClD,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,gBAAgB,CAAC;YAC5C,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBAC5B,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;gBAClC,QAAQ,EAAE,gBAAgB;gBAC1B,UAAU;aACX,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,UAAkB;QACjD,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;YAClD,oCAAoC;YACpC,mEAAmE;YACnE,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,SAAiB;QAC1C,6CAA6C;QAC7C,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,oBAAoB,EAAE,CAAC,CAAC;IACjF,CAAC;CACF"}