/**
 * Claude Code 3.0 - Steering Manager
 *
 * Real-time steering mechanism that orchestrates the h2A message queue system
 * and provides intelligent flow control for the entire system.
 */
import { EventEmitter } from 'eventemitter3';
import { Message, SteeringConfig, SteeringState } from '../../types/message-queue.js';
/**
 * Steering Manager - Real-time Message Flow Control
 *
 * Responsibilities:
 * - Orchestrate multiple message queues
 * - Implement adaptive throttling
 * - Provide flow control mechanisms
 * - Integrate with Agent, UI, and Tool layers
 * - Monitor system performance and adjust accordingly
 */
export declare class SteeringManager extends EventEmitter {
    private config;
    private state;
    private agentQueue;
    private uiQueue;
    private toolQueue;
    private systemQueue;
    private steeringInterval?;
    private loadMonitorInterval?;
    private performanceHistory;
    private lastSteeringTime;
    constructor(config?: Partial<SteeringConfig>);
    /**
     * Start the steering system
     */
    start(): Promise<void>;
    /**
     * Stop the steering system
     */
    stop(): Promise<void>;
    /**
     * Send message to appropriate queue based on destination
     */
    routeMessage<T>(message: Message<T>): Promise<boolean>;
    /**
     * Send message to agent processing queue
     */
    sendToAgent<T>(message: Message<T>): Promise<boolean>;
    /**
     * Send message to UI update queue
     */
    sendToUI<T>(message: Message<T>): Promise<boolean>;
    /**
     * Send message to tool execution queue
     */
    sendToTool<T>(message: Message<T>): Promise<boolean>;
    /**
     * Send system-level message
     */
    sendSystemMessage<T>(message: Message<T>): Promise<boolean>;
    /**
     * Get current steering state
     */
    getState(): SteeringState;
    /**
     * Get comprehensive system metrics
     */
    getSystemMetrics(): {
        agent: import("../../types/message-queue.js").QueueMetrics;
        ui: import("../../types/message-queue.js").QueueMetrics;
        tool: import("../../types/message-queue.js").QueueMetrics;
        system: import("../../types/message-queue.js").QueueMetrics;
        steering: {
            currentLoad: number;
            throttleLevel: number;
            isActive: boolean;
            lastSteeringAction: Date;
        };
    };
    /**
     * Manually trigger steering adjustment
     */
    performSteering(): Promise<void>;
    private initializeState;
    private initializeQueues;
    private setupEventHandlers;
    private getQueueForMessage;
    private startSteering;
    private stopSteering;
    private startLoadMonitoring;
    private stopLoadMonitoring;
    private updateState;
    private calculateSystemLoad;
    private applyAdaptiveThrottling;
    private performFlowControl;
    private handleBackpressure;
}
//# sourceMappingURL=steering-manager.d.ts.map