/**
 * Claude Code 3.0 - h2A Dual-Buffer Async Message Queue
 *
 * Core implementation of the h2A (High-performance Async) message queue system
 * with dual-buffer architecture for real-time steering mechanism.
 */
import { EventEmitter } from 'eventemitter3';
import { Message, QueueConfig, QueueState, QueueMetrics, IMessageQueue } from '../../types/message-queue.js';
/**
 * h2A Dual-Buffer Async Message Queue Implementation
 *
 * Features:
 * - Dual-buffer architecture for non-blocking operations
 * - Priority-based message handling
 * - Backpressure management
 * - Real-time metrics and monitoring
 * - Event-driven architecture
 * - Automatic error recovery
 */
export declare class h2AMessageQueue<T = any> extends EventEmitter implements IMessageQueue<T> {
    private config;
    private state;
    private metrics;
    private primaryBuffer;
    private secondaryBuffer;
    private activeBuffer;
    private isProcessing;
    private processingInterval?;
    private healthCheckInterval?;
    private processingTimes;
    private lastProcessingTime;
    constructor(config?: Partial<QueueConfig>);
    enqueue(message: Message<T>): Promise<boolean>;
    dequeue(): Promise<Message<T> | null>;
    peek(): Promise<Message<T> | null>;
    clear(): Promise<void>;
    enqueueBatch(messages: Message<T>[]): Promise<number>;
    dequeueBatch(count: number): Promise<Message<T>[]>;
    getState(): QueueState;
    getMetrics(): QueueMetrics;
    isHealthy(): boolean;
    start(): Promise<void>;
    stop(): Promise<void>;
    pause(): Promise<void>;
    resume(): Promise<void>;
    private initializeState;
    private initializeMetrics;
    private setupEventHandlers;
    private validateMessage;
    private getActiveBuffer;
    private getProcessingBuffer;
    private getHighestPriorityMessage;
    private isBackpressureActive;
    private handleBackpressure;
    private dropOldestAndEnqueue;
    private expandBufferAndEnqueue;
    private shouldSwitchBuffer;
    private switchBuffer;
    private calculateBufferUtilization;
    private updateBufferSizes;
    private updateMetrics;
    private startProcessing;
    private stopProcessing;
    private startHealthCheck;
    private stopHealthCheck;
    private processMessages;
    private performHealthCheck;
    private handleError;
}
/**
 * Create a standard h2A message queue with default configuration
 */
export declare function createMessageQueue<T = any>(config?: Partial<QueueConfig>): h2AMessageQueue<T>;
/**
 * Create a high-performance message queue optimized for throughput
 */
export declare function createHighPerformanceQueue<T = any>(): h2AMessageQueue<T>;
/**
 * Create a memory-efficient message queue optimized for low resource usage
 */
export declare function createMemoryEfficientQueue<T = any>(): h2AMessageQueue<T>;
//# sourceMappingURL=message-queue.d.ts.map