/**
 * Claude Code 3.0 - Steering Layer
 *
 * h2A dual-buffer async message queue system with real-time steering mechanism.
 * This layer provides the foundation for all inter-component communication.
 */
export { h2AMessageQueue, createMessageQueue, createHighPerformanceQueue, createMemoryEfficientQueue } from './message-queue.js';
export { SteeringManager } from './steering-manager.js';
export * from '../../types/message-queue.js';
import { SteeringManager } from './steering-manager.js';
import { SteeringConfig } from '../../types/message-queue.js';
/**
 * Create a complete steering environment with default configuration
 */
export declare function createSteeringEnvironment(config?: Partial<SteeringConfig>): SteeringManager;
/**
 * Create a high-performance steering environment optimized for throughput
 */
export declare function createHighPerformanceSteeringEnvironment(): SteeringManager;
/**
 * Create a memory-efficient steering environment for resource-constrained environments
 */
export declare function createMemoryEfficientSteeringEnvironment(): SteeringManager;
/**
 * Create a development steering environment with enhanced debugging
 */
export declare function createDevelopmentSteeringEnvironment(): SteeringManager;
//# sourceMappingURL=index.d.ts.map