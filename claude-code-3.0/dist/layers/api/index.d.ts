/**
 * Claude Code 3.0 - API Layer
 *
 * Layer 8: External API Integration
 * REST API endpoints and external service integrations.
 */
/// <reference types="node" />
export interface APIConfig {
    enabled: boolean;
    port: number;
    host: string;
    enableCORS: boolean;
    enableRateLimit: boolean;
    rateLimit: {
        windowMs: number;
        max: number;
    };
    enableAuth: boolean;
    enableMetrics: boolean;
}
export declare const DEFAULT_API_CONFIG: APIConfig;
export interface APIEndpoint {
    path: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    handler: (req: any, res: any) => Promise<any>;
    middleware?: any[];
}
export interface APIMetrics {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    requestsPerSecond: number;
}
/**
 * API Server Manager
 * Manages REST API endpoints and external integrations
 */
export declare class APIManager {
    private config;
    private endpoints;
    private server?;
    private metrics;
    constructor(config?: Partial<APIConfig>);
    /**
     * Register API endpoint
     */
    registerEndpoint(endpoint: APIEndpoint): void;
    /**
     * Unregister API endpoint
     */
    unregisterEndpoint(method: string, path: string): boolean;
    /**
     * Start API server
     */
    start(): Promise<void>;
    /**
     * Stop API server
     */
    stop(): Promise<void>;
    /**
     * Get API metrics
     */
    getMetrics(): APIMetrics;
    /**
     * Get registered endpoints
     */
    getEndpoints(): APIEndpoint[];
    /**
     * Check if server is running
     */
    isRunning(): boolean;
    /**
     * Get server configuration
     */
    getConfig(): APIConfig;
}
/**
 * Built-in API endpoints
 */
export declare const builtInEndpoints: {
    health: {
        path: string;
        method: "GET";
        handler(req: any, res: any): Promise<{
            status: string;
            timestamp: string;
            uptime: number;
            memory: NodeJS.MemoryUsage;
        }>;
    };
    status: {
        path: string;
        method: "GET";
        handler(req: any, res: any): Promise<{
            status: string;
            version: string;
            environment: string;
            timestamp: string;
        }>;
    };
    metrics: {
        path: string;
        method: "GET";
        handler(req: any, res: any): Promise<{
            requests: {
                total: number;
                successful: number;
                failed: number;
            };
            performance: {
                averageResponseTime: number;
                requestsPerSecond: number;
            };
            system: {
                uptime: number;
                memory: NodeJS.MemoryUsage;
                cpu: NodeJS.CpuUsage;
            };
        }>;
    };
};
/**
 * Create API manager instance
 */
export declare function createAPIManager(config?: Partial<APIConfig>): APIManager;
//# sourceMappingURL=index.d.ts.map