/**
 * Claude Code 3.0 - Message Layer
 *
 * Layer 5: Message Processing and Transformation
 * Message handling, transformation, and routing logic.
 */
export interface MessageConfig {
    enabled: boolean;
    enableTransformation: boolean;
    enableValidation: boolean;
    enableCompression: boolean;
    maxMessageSize: number;
}
export declare const DEFAULT_MESSAGE_CONFIG: MessageConfig;
export interface MessageTransformer {
    name: string;
    transform(message: any): Promise<any>;
}
export interface MessageValidator {
    name: string;
    validate(message: any): Promise<boolean>;
}
/**
 * Message Processor
 * Handles message transformation, validation, and processing
 */
export declare class MessageProcessor {
    private config;
    private transformers;
    private validators;
    constructor(config?: Partial<MessageConfig>);
    /**
     * Add message transformer
     */
    addTransformer(transformer: MessageTransformer): void;
    /**
     * Add message validator
     */
    addValidator(validator: MessageValidator): void;
    /**
     * Process a message through the pipeline
     */
    processMessage(message: any): Promise<any>;
    /**
     * Get processor configuration
     */
    getConfig(): MessageConfig;
    /**
     * Get registered transformers
     */
    getTransformers(): MessageTransformer[];
    /**
     * Get registered validators
     */
    getValidators(): MessageValidator[];
    private compressMessage;
}
/**
 * Built-in message transformers
 */
export declare const builtInTransformers: {
    timestampTransformer: {
        name: string;
        transform(message: any): Promise<any>;
    };
    idTransformer: {
        name: string;
        transform(message: any): Promise<any>;
    };
};
/**
 * Built-in message validators
 */
export declare const builtInValidators: {
    requiredFieldsValidator: {
        name: string;
        validate(message: any): Promise<any>;
    };
    typeValidator: {
        name: string;
        validate(message: any): Promise<boolean>;
    };
};
/**
 * Create message processor instance
 */
export declare function createMessageProcessor(config?: Partial<MessageConfig>): MessageProcessor;
//# sourceMappingURL=index.d.ts.map