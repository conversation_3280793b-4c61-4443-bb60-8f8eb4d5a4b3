{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/layers/message/index.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAUH,MAAM,CAAC,MAAM,sBAAsB,GAAkB;IACnD,OAAO,EAAE,IAAI;IACb,oBAAoB,EAAE,IAAI;IAC1B,gBAAgB,EAAE,IAAI;IACtB,iBAAiB,EAAE,KAAK;IACxB,cAAc,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM;CACnC,CAAC;AAYF;;;GAGG;AACH,MAAM,OAAO,gBAAgB;IACnB,MAAM,CAAgB;IACtB,YAAY,GAAyB,EAAE,CAAC;IACxC,UAAU,GAAuB,EAAE,CAAC;IAE5C,YAAY,SAAiC,EAAE;QAC7C,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,sBAAsB,EAAE,GAAG,MAAM,EAAE,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,WAA+B;QAC5C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,SAA2B;QACtC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,OAAY;QAC/B,IAAI,gBAAgB,GAAG,OAAO,CAAC;QAE/B,aAAa;QACb,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YACjC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACxC,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;gBAC3D,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,MAAM,IAAI,KAAK,CAAC,8BAA8B,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;gBAClE,CAAC;YACH,CAAC;QACH,CAAC;QAED,aAAa;QACb,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC;QAC5D,IAAI,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,gBAAgB,WAAW,kBAAkB,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;QAC7F,CAAC;QAED,iBAAiB;QACjB,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;YACrC,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC5C,gBAAgB,GAAG,MAAM,WAAW,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;YAClC,gBAAgB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;QAClE,CAAC;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;IAC9B,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,OAAY;QACxC,gCAAgC;QAChC,uDAAuD;QACvD,OAAO;YACL,GAAG,OAAO;YACV,WAAW,EAAE,IAAI;YACjB,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,MAAM;SAC9C,CAAC;IACJ,CAAC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG;IACjC,oBAAoB,EAAE;QACpB,IAAI,EAAE,WAAW;QACjB,KAAK,CAAC,SAAS,CAAC,OAAY;YAC1B,OAAO;gBACL,GAAG,OAAO;gBACV,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACzD,CAAC;QACJ,CAAC;KACF;IAED,aAAa,EAAE;QACb,IAAI,EAAE,IAAI;QACV,KAAK,CAAC,SAAS,CAAC,OAAY;YAC1B,OAAO;gBACL,GAAG,OAAO;gBACV,EAAE,EAAE,OAAO,CAAC,EAAE,IAAI,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;aACjF,CAAC;QACJ,CAAC;KACF;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG;IAC/B,uBAAuB,EAAE;QACvB,IAAI,EAAE,iBAAiB;QACvB,KAAK,CAAC,QAAQ,CAAC,OAAY;YACzB,OAAO,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;QAChE,CAAC;KACF;IAED,aAAa,EAAE;QACb,IAAI,EAAE,MAAM;QACZ,KAAK,CAAC,QAAQ,CAAC,OAAY;YACzB,OAAO,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QACrE,CAAC;KACF;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,sBAAsB,CAAC,SAAiC,EAAE;IACxE,MAAM,SAAS,GAAG,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAE/C,4BAA4B;IAC5B,SAAS,CAAC,cAAc,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,CAAC;IACnE,SAAS,CAAC,cAAc,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;IAE5D,0BAA0B;IAC1B,SAAS,CAAC,YAAY,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,CAAC;IAClE,SAAS,CAAC,YAAY,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;IAExD,OAAO,SAAS,CAAC;AACnB,CAAC"}