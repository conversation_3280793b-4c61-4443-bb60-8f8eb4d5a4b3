/**
 * Claude Code 3.0 - Message Layer
 *
 * Layer 5: Message Processing and Transformation
 * Message handling, transformation, and routing logic.
 */
export const DEFAULT_MESSAGE_CONFIG = {
    enabled: true,
    enableTransformation: true,
    enableValidation: true,
    enableCompression: false,
    maxMessageSize: 1024 * 1024 // 1MB
};
/**
 * Message Processor
 * Handles message transformation, validation, and processing
 */
export class MessageProcessor {
    config;
    transformers = [];
    validators = [];
    constructor(config = {}) {
        this.config = { ...DEFAULT_MESSAGE_CONFIG, ...config };
    }
    /**
     * Add message transformer
     */
    addTransformer(transformer) {
        this.transformers.push(transformer);
    }
    /**
     * Add message validator
     */
    addValidator(validator) {
        this.validators.push(validator);
    }
    /**
     * Process a message through the pipeline
     */
    async processMessage(message) {
        let processedMessage = message;
        // Validation
        if (this.config.enableValidation) {
            for (const validator of this.validators) {
                const isValid = await validator.validate(processedMessage);
                if (!isValid) {
                    throw new Error(`Message validation failed: ${validator.name}`);
                }
            }
        }
        // Size check
        const messageSize = JSON.stringify(processedMessage).length;
        if (messageSize > this.config.maxMessageSize) {
            throw new Error(`Message size ${messageSize} exceeds limit ${this.config.maxMessageSize}`);
        }
        // Transformation
        if (this.config.enableTransformation) {
            for (const transformer of this.transformers) {
                processedMessage = await transformer.transform(processedMessage);
            }
        }
        // Compression (if enabled)
        if (this.config.enableCompression) {
            processedMessage = await this.compressMessage(processedMessage);
        }
        return processedMessage;
    }
    /**
     * Get processor configuration
     */
    getConfig() {
        return { ...this.config };
    }
    /**
     * Get registered transformers
     */
    getTransformers() {
        return [...this.transformers];
    }
    /**
     * Get registered validators
     */
    getValidators() {
        return [...this.validators];
    }
    async compressMessage(message) {
        // Simple compression simulation
        // In real implementation, would use actual compression
        return {
            ...message,
            _compressed: true,
            _originalSize: JSON.stringify(message).length
        };
    }
}
/**
 * Built-in message transformers
 */
export const builtInTransformers = {
    timestampTransformer: {
        name: 'timestamp',
        async transform(message) {
            return {
                ...message,
                timestamp: message.timestamp || new Date().toISOString()
            };
        }
    },
    idTransformer: {
        name: 'id',
        async transform(message) {
            return {
                ...message,
                id: message.id || `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
            };
        }
    }
};
/**
 * Built-in message validators
 */
export const builtInValidators = {
    requiredFieldsValidator: {
        name: 'required-fields',
        async validate(message) {
            return message && typeof message === 'object' && message.type;
        }
    },
    typeValidator: {
        name: 'type',
        async validate(message) {
            return typeof message.type === 'string' && message.type.length > 0;
        }
    }
};
/**
 * Create message processor instance
 */
export function createMessageProcessor(config = {}) {
    const processor = new MessageProcessor(config);
    // Add built-in transformers
    processor.addTransformer(builtInTransformers.timestampTransformer);
    processor.addTransformer(builtInTransformers.idTransformer);
    // Add built-in validators
    processor.addValidator(builtInValidators.requiredFieldsValidator);
    processor.addValidator(builtInValidators.typeValidator);
    return processor;
}
//# sourceMappingURL=index.js.map