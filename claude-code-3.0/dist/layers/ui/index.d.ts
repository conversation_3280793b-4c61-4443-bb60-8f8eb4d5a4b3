/**
 * Claude Code 3.0 - UI Layer
 *
 * Layer 2: User Interface
 * React-based user interface components and management.
 */
export interface UIConfig {
    enabled: boolean;
    port: number;
    theme: 'light' | 'dark' | 'auto';
    enableHotReload: boolean;
}
export declare const DEFAULT_UI_CONFIG: UIConfig;
/**
 * UI Manager (placeholder)
 */
export declare class UIManager {
    private config;
    constructor(config?: Partial<UIConfig>);
    start(): Promise<void>;
    stop(): Promise<void>;
    getConfig(): UIConfig;
}
/**
 * Create UI manager instance
 */
export declare function createUIManager(config?: Partial<UIConfig>): UIManager;
//# sourceMappingURL=index.d.ts.map