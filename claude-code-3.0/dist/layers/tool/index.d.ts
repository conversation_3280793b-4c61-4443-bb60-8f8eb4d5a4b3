/**
 * Claude Code 3.0 - Tool Layer
 *
 * Layer 7: Tool Execution and Management
 * Extensible tool system for agent capabilities.
 */
export interface ToolConfig {
    enabled: boolean;
    maxConcurrentExecutions: number;
    executionTimeout: number;
    enableSandbox: boolean;
    enableMetrics: boolean;
}
export declare const DEFAULT_TOOL_CONFIG: ToolConfig;
export interface ToolDefinition {
    name: string;
    description: string;
    parameters: Record<string, any>;
    execute: (parameters: any, context?: ToolExecutionContext) => Promise<any>;
}
export interface ToolExecutionContext {
    sessionId?: string;
    userId?: string;
    agentId?: string;
    metadata?: Record<string, any>;
}
export interface ToolExecutionResult {
    success: boolean;
    result?: any;
    error?: string;
    executionTime: number;
    metadata?: Record<string, any>;
}
/**
 * Tool Manager
 * Manages tool registration, execution, and lifecycle
 */
export declare class ToolManager {
    private config;
    private tools;
    private activeExecutions;
    private metrics;
    constructor(config?: Partial<ToolConfig>);
    /**
     * Register a tool
     */
    registerTool(tool: ToolDefinition): void;
    /**
     * Unregister a tool
     */
    unregisterTool(name: string): boolean;
    /**
     * Get registered tool
     */
    getTool(name: string): ToolDefinition | undefined;
    /**
     * List all registered tools
     */
    listTools(): ToolDefinition[];
    /**
     * Execute a tool
     */
    executeTool(name: string, parameters: any, context?: ToolExecutionContext): Promise<ToolExecutionResult>;
    /**
     * Get tool execution metrics
     */
    getMetrics(): {
        totalExecutions: number;
        successfulExecutions: number;
        failedExecutions: number;
        averageExecutionTime: number;
    };
    /**
     * Get active executions count
     */
    getActiveExecutionsCount(): number;
    private updateMetrics;
}
/**
 * Built-in tools
 */
export declare const builtInTools: {
    echo: {
        name: string;
        description: string;
        parameters: {
            message: {
                type: string;
                required: boolean;
            };
        };
        execute(parameters: any): Promise<{
            echo: any;
        }>;
    };
    timestamp: {
        name: string;
        description: string;
        parameters: {};
        execute(): Promise<{
            timestamp: string;
        }>;
    };
    random: {
        name: string;
        description: string;
        parameters: {
            min: {
                type: string;
                default: number;
            };
            max: {
                type: string;
                default: number;
            };
        };
        execute(parameters: any): Promise<{
            random: any;
        }>;
    };
};
/**
 * Create tool manager instance
 */
export declare function createToolManager(config?: Partial<ToolConfig>): ToolManager;
//# sourceMappingURL=index.d.ts.map