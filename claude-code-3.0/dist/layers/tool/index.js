/**
 * Claude Code 3.0 - Tool Layer
 *
 * Layer 7: Tool Execution and Management
 * Extensible tool system for agent capabilities.
 */
export const DEFAULT_TOOL_CONFIG = {
    enabled: true,
    maxConcurrentExecutions: 5,
    executionTimeout: 30000, // 30 seconds
    enableSandbox: true,
    enableMetrics: true
};
/**
 * Tool Manager
 * Manages tool registration, execution, and lifecycle
 */
export class ToolManager {
    config;
    tools = new Map();
    activeExecutions = new Set();
    metrics = {
        totalExecutions: 0,
        successfulExecutions: 0,
        failedExecutions: 0,
        averageExecutionTime: 0
    };
    constructor(config = {}) {
        this.config = { ...DEFAULT_TOOL_CONFIG, ...config };
    }
    /**
     * Register a tool
     */
    registerTool(tool) {
        this.tools.set(tool.name, tool);
    }
    /**
     * Unregister a tool
     */
    unregisterTool(name) {
        return this.tools.delete(name);
    }
    /**
     * Get registered tool
     */
    getTool(name) {
        return this.tools.get(name);
    }
    /**
     * List all registered tools
     */
    listTools() {
        return Array.from(this.tools.values());
    }
    /**
     * Execute a tool
     */
    async executeTool(name, parameters, context) {
        const tool = this.tools.get(name);
        if (!tool) {
            return {
                success: false,
                error: `Tool '${name}' not found`,
                executionTime: 0
            };
        }
        // Check concurrent execution limit
        if (this.activeExecutions.size >= this.config.maxConcurrentExecutions) {
            return {
                success: false,
                error: 'Maximum concurrent executions reached',
                executionTime: 0
            };
        }
        const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.activeExecutions.add(executionId);
        const startTime = Date.now();
        try {
            // Create execution timeout
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Tool execution timeout')), this.config.executionTimeout);
            });
            // Execute tool with timeout
            const result = await Promise.race([
                tool.execute(parameters, context),
                timeoutPromise
            ]);
            const executionTime = Date.now() - startTime;
            // Update metrics
            if (this.config.enableMetrics) {
                this.updateMetrics(executionTime, true);
            }
            return {
                success: true,
                result,
                executionTime,
                metadata: {
                    toolName: name,
                    executionId
                }
            };
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            // Update metrics
            if (this.config.enableMetrics) {
                this.updateMetrics(executionTime, false);
            }
            return {
                success: false,
                error: error.message,
                executionTime,
                metadata: {
                    toolName: name,
                    executionId
                }
            };
        }
        finally {
            this.activeExecutions.delete(executionId);
        }
    }
    /**
     * Get tool execution metrics
     */
    getMetrics() {
        return { ...this.metrics };
    }
    /**
     * Get active executions count
     */
    getActiveExecutionsCount() {
        return this.activeExecutions.size;
    }
    updateMetrics(executionTime, success) {
        this.metrics.totalExecutions++;
        if (success) {
            this.metrics.successfulExecutions++;
        }
        else {
            this.metrics.failedExecutions++;
        }
        // Update average execution time
        const totalTime = this.metrics.averageExecutionTime * (this.metrics.totalExecutions - 1) + executionTime;
        this.metrics.averageExecutionTime = totalTime / this.metrics.totalExecutions;
    }
}
/**
 * Built-in tools
 */
export const builtInTools = {
    echo: {
        name: 'echo',
        description: 'Echo back the input parameters',
        parameters: {
            message: { type: 'string', required: true }
        },
        async execute(parameters) {
            return { echo: parameters.message };
        }
    },
    timestamp: {
        name: 'timestamp',
        description: 'Get current timestamp',
        parameters: {},
        async execute() {
            return { timestamp: new Date().toISOString() };
        }
    },
    random: {
        name: 'random',
        description: 'Generate random number',
        parameters: {
            min: { type: 'number', default: 0 },
            max: { type: 'number', default: 100 }
        },
        async execute(parameters) {
            const min = parameters.min || 0;
            const max = parameters.max || 100;
            return { random: Math.floor(Math.random() * (max - min + 1)) + min };
        }
    }
};
/**
 * Create tool manager instance
 */
export function createToolManager(config = {}) {
    const manager = new ToolManager(config);
    // Register built-in tools
    Object.values(builtInTools).forEach(tool => {
        manager.registerTool(tool);
    });
    return manager;
}
//# sourceMappingURL=index.js.map