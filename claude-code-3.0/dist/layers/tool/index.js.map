{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/layers/tool/index.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAUH,MAAM,CAAC,MAAM,mBAAmB,GAAe;IAC7C,OAAO,EAAE,IAAI;IACb,uBAAuB,EAAE,CAAC;IAC1B,gBAAgB,EAAE,KAAK,EAAE,aAAa;IACtC,aAAa,EAAE,IAAI;IACnB,aAAa,EAAE,IAAI;CACpB,CAAC;AAwBF;;;GAGG;AACH,MAAM,OAAO,WAAW;IACd,MAAM,CAAa;IACnB,KAAK,GAAG,IAAI,GAAG,EAA0B,CAAC;IAC1C,gBAAgB,GAAG,IAAI,GAAG,EAAU,CAAC;IACrC,OAAO,GAAG;QAChB,eAAe,EAAE,CAAC;QAClB,oBAAoB,EAAE,CAAC;QACvB,gBAAgB,EAAE,CAAC;QACnB,oBAAoB,EAAE,CAAC;KACxB,CAAC;IAEF,YAAY,SAA8B,EAAE;QAC1C,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,mBAAmB,EAAE,GAAG,MAAM,EAAE,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,IAAoB;QAC/B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,IAAY;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,IAAY;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACf,IAAY,EACZ,UAAe,EACf,OAA8B;QAE9B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,SAAS,IAAI,aAAa;gBACjC,aAAa,EAAE,CAAC;aACjB,CAAC;QACJ,CAAC;QAED,mCAAmC;QACnC,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;YACtE,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,uCAAuC;gBAC9C,aAAa,EAAE,CAAC;aACjB,CAAC;QACJ,CAAC;QAED,MAAM,WAAW,GAAG,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QACpF,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAEvC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,2BAA2B;YAC3B,MAAM,cAAc,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;gBAC/C,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;YAC9F,CAAC,CAAC,CAAC;YAEH,4BAA4B;YAC5B,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;gBAChC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC;gBACjC,cAAc;aACf,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE7C,iBAAiB;YACjB,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;gBAC9B,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;YAC1C,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM;gBACN,aAAa;gBACb,QAAQ,EAAE;oBACR,QAAQ,EAAE,IAAI;oBACd,WAAW;iBACZ;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE7C,iBAAiB;YACjB,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;gBAC9B,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YAC3C,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,aAAa;gBACb,QAAQ,EAAE;oBACR,QAAQ,EAAE,IAAI;oBACd,WAAW;iBACZ;aACF,CAAC;QAEJ,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;IACpC,CAAC;IAEO,aAAa,CAAC,aAAqB,EAAE,OAAgB;QAC3D,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;QAE/B,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC;QACtC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;QAClC,CAAC;QAED,gCAAgC;QAChC,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC;QACzG,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;IAC/E,CAAC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG;IAC1B,IAAI,EAAE;QACJ,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,gCAAgC;QAC7C,UAAU,EAAE;YACV,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE;SAC5C;QACD,KAAK,CAAC,OAAO,CAAC,UAAe;YAC3B,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,OAAO,EAAE,CAAC;QACtC,CAAC;KACF;IAED,SAAS,EAAE;QACT,IAAI,EAAE,WAAW;QACjB,WAAW,EAAE,uBAAuB;QACpC,UAAU,EAAE,EAAE;QACd,KAAK,CAAC,OAAO;YACX,OAAO,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC;QACjD,CAAC;KACF;IAED,MAAM,EAAE;QACN,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,wBAAwB;QACrC,UAAU,EAAE;YACV,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE;YACnC,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE;SACtC;QACD,KAAK,CAAC,OAAO,CAAC,UAAe;YAC3B,MAAM,GAAG,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC;YAChC,MAAM,GAAG,GAAG,UAAU,CAAC,GAAG,IAAI,GAAG,CAAC;YAClC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;QACvE,CAAC;KACF;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,SAA8B,EAAE;IAChE,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;IAExC,0BAA0B;IAC1B,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACzC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;IAEH,OAAO,OAAO,CAAC;AACjB,CAAC"}