/**
 * Claude Code 3.0 - Agent Core Engine
 *
 * Core implementation of the AI agent with nO async generator functions
 * and event-driven processing system.
 */
import { EventEmitter } from 'eventemitter3';
import { AgentConfig, AgentState, AgentMessage, AgentSession, Conversation, StreamingResponse, nOGeneratorConfig, nOGeneratorState } from '../../types/agent.js';
/**
 * Agent Core Engine
 *
 * Features:
 * - nO async generator-based processing
 * - Event-driven architecture
 * - Streaming response support
 * - Context compression
 * - Tool integration
 * - Error recovery
 * - Performance monitoring
 */
export declare class AgentCore extends EventEmitter {
    private config;
    private state;
    private generatorConfig;
    private generatorState;
    private activeSessions;
    private conversations;
    private abortController?;
    private processingQueue;
    private isProcessing;
    private performanceMetrics;
    private lastProcessingTime;
    constructor(config?: Partial<AgentConfig>, generatorConfig?: Partial<nOGeneratorConfig>);
    /**
     * Start the agent core
     */
    start(): Promise<void>;
    /**
     * Stop the agent core
     */
    stop(): Promise<void>;
    /**
     * Process a message using the nO async generator
     */
    processMessage(message: AgentMessage, sessionId?: string): Promise<StreamingResponse>;
    /**
     * Get agent state
     */
    getState(): AgentState;
    /**
     * Get generator state
     */
    getGeneratorState(): nOGeneratorState;
    /**
     * Get active sessions
     */
    getActiveSessions(): AgentSession[];
    /**
     * Get conversation by ID
     */
    getConversation(conversationId: string): Conversation | undefined;
    /**
     * Main nO async generator loop
     *
     * This is the core processing loop that handles:
     * - Message processing
     * - Context management
     * - Tool execution
     * - Streaming responses
     * - Error recovery
     */
    private nOMainLoop;
    /**
     * Process a single iteration of the nO loop
     */
    private processIteration;
    /**
     * Execute a tool call
     */
    private executeToolCall;
    private initializeState;
    private initializeGeneratorState;
    private setupEventHandlers;
    private getId;
    private createSession;
    private getOrCreateSession;
    private getOrCreateConversation;
    private shouldCompressContext;
    private compressContext;
    private shouldYield;
    private yieldControl;
    private isProcessingComplete;
    private simulateAIResponse;
    private extractToolCalls;
    private simulateToolExecution;
    private chunkResponse;
    private estimateTokens;
    private estimateConversationTokens;
    private updateState;
    private updatePerformanceMetrics;
    private updateIterationMetrics;
    private recoverFromError;
    private handleError;
}
//# sourceMappingURL=agent-core.d.ts.map