/**
 * Claude Code 3.0 - Multi-Agent Manager
 *
 * Manages multiple concurrent agents with orchestration, load balancing,
 * and inter-agent communication through the h2A message queue system.
 */
import { EventEmitter } from 'eventemitter3';
import { AgentCore } from './agent-core.js';
import { generateId } from '../../utils/index.js';
export const DEFAULT_MULTI_AGENT_CONFIG = {
    maxAgents: 10,
    loadBalancingStrategy: 'least-loaded',
    enableInterAgentCommunication: true,
    agentSpawnDelay: 1000,
    healthCheckInterval: 30000,
    enableMetrics: true,
    defaultAgentConfig: {
        enableStreaming: true,
        enableMetrics: true,
        maxConcurrentRequests: 5
    }
};
/**
 * Multi-Agent Manager
 *
 * Orchestrates multiple AI agents with load balancing, health monitoring,
 * and inter-agent communication capabilities.
 */
export class MultiAgentManager extends EventEmitter {
    config;
    agents = new Map();
    requestQueue = [];
    metrics = {
        totalAgents: 0,
        activeAgents: 0,
        idleAgents: 0,
        busyAgents: 0,
        errorAgents: 0,
        averageLoad: 0,
        totalRequests: 0,
        requestsPerSecond: 0
    };
    healthCheckTimer;
    lastRequestTime = Date.now();
    constructor(config = {}) {
        super();
        this.config = { ...DEFAULT_MULTI_AGENT_CONFIG, ...config };
    }
    /**
     * Start the multi-agent system
     */
    async start() {
        console.log('🤖 Starting Multi-Agent Manager...');
        // Start health check monitoring
        if (this.config.healthCheckInterval > 0) {
            this.startHealthChecking();
        }
        // Spawn initial agents
        await this.spawnInitialAgents();
        this.emit('manager_started', { agentCount: this.agents.size });
        console.log(`✅ Multi-Agent Manager started with ${this.agents.size} agents`);
    }
    /**
     * Stop the multi-agent system
     */
    async stop() {
        console.log('🛑 Stopping Multi-Agent Manager...');
        // Stop health checking
        if (this.healthCheckTimer) {
            clearInterval(this.healthCheckTimer);
            this.healthCheckTimer = undefined;
        }
        // Terminate all agents
        const terminationPromises = Array.from(this.agents.values()).map(agentInfo => this.terminateAgent(agentInfo.id));
        await Promise.all(terminationPromises);
        this.emit('manager_stopped');
        console.log('✅ Multi-Agent Manager stopped');
    }
    /**
     * Process message with load balancing
     */
    async processMessage(message, sessionId, requiredCapabilities) {
        return new Promise((resolve, reject) => {
            const requestId = generateId('req');
            // Add to request queue
            this.requestQueue.push({
                id: requestId,
                message,
                sessionId,
                resolve,
                reject,
                timestamp: new Date(),
                requiredCapabilities
            });
            // Process queue
            setImmediate(() => this.processRequestQueue());
            // Update metrics
            this.metrics.totalRequests++;
            this.lastRequestTime = Date.now();
        });
    }
    /**
     * Spawn a new agent
     */
    async spawnAgent(config, capabilities = []) {
        if (this.agents.size >= this.config.maxAgents) {
            throw new Error(`Maximum agent limit reached: ${this.config.maxAgents}`);
        }
        const agentId = generateId('agent');
        const agentConfig = { ...this.config.defaultAgentConfig, ...config };
        const agent = new AgentCore(agentConfig);
        await agent.start();
        const agentInfo = {
            id: agentId,
            agent,
            status: 'idle',
            capabilities,
            currentLoad: 0,
            totalRequests: 0,
            lastActivity: new Date(),
            spawnTime: new Date(),
            config: agentConfig
        };
        this.agents.set(agentId, agentInfo);
        this.updateMetrics();
        // Set up agent event listeners
        this.setupAgentEventListeners(agentInfo);
        this.emit('agent_spawned', { agentId, capabilities });
        console.log(`🤖 Agent spawned: ${agentId} with capabilities: [${capabilities.join(', ')}]`);
        return agentId;
    }
    /**
     * Terminate an agent
     */
    async terminateAgent(agentId) {
        const agentInfo = this.agents.get(agentId);
        if (!agentInfo) {
            return false;
        }
        try {
            await agentInfo.agent.stop();
            agentInfo.status = 'terminated';
            this.agents.delete(agentId);
            this.updateMetrics();
            this.emit('agent_terminated', { agentId });
            console.log(`🗑️  Agent terminated: ${agentId}`);
            return true;
        }
        catch (error) {
            console.error(`Failed to terminate agent ${agentId}:`, error);
            return false;
        }
    }
    /**
     * Get agent information
     */
    getAgent(agentId) {
        return this.agents.get(agentId);
    }
    /**
     * List all agents
     */
    listAgents() {
        return Array.from(this.agents.values());
    }
    /**
     * Get load balancing metrics
     */
    getMetrics() {
        this.updateMetrics();
        return { ...this.metrics };
    }
    /**
     * Send message between agents
     */
    async sendInterAgentMessage(fromAgentId, toAgentId, message) {
        if (!this.config.enableInterAgentCommunication) {
            throw new Error('Inter-agent communication is disabled');
        }
        const fromAgent = this.agents.get(fromAgentId);
        const toAgent = this.agents.get(toAgentId);
        if (!fromAgent || !toAgent) {
            return false;
        }
        // Create inter-agent message
        const interAgentMessage = {
            id: generateId('inter'),
            role: 'system',
            content: `Inter-agent message from ${fromAgentId}: ${JSON.stringify(message)}`,
            timestamp: new Date()
        };
        try {
            await toAgent.agent.processMessage(interAgentMessage);
            this.emit('inter_agent_message', { fromAgentId, toAgentId, message });
            return true;
        }
        catch (error) {
            console.error(`Inter-agent message failed: ${fromAgentId} -> ${toAgentId}`, error);
            return false;
        }
    }
    // Private methods
    async spawnInitialAgents() {
        const initialAgentCount = Math.min(3, this.config.maxAgents);
        for (let i = 0; i < initialAgentCount; i++) {
            await this.spawnAgent({ model: `agent-${i}` }, ['general', 'text-processing']);
            if (i < initialAgentCount - 1) {
                await new Promise(resolve => setTimeout(resolve, this.config.agentSpawnDelay));
            }
        }
    }
    async processRequestQueue() {
        while (this.requestQueue.length > 0) {
            const request = this.requestQueue.shift();
            if (!request)
                continue;
            try {
                const selectedAgent = this.selectAgent(request.requiredCapabilities);
                if (!selectedAgent) {
                    // Try to spawn a new agent if possible
                    if (this.agents.size < this.config.maxAgents) {
                        await this.spawnAgent(undefined, request.requiredCapabilities || ['general']);
                        const newAgent = this.selectAgent(request.requiredCapabilities);
                        if (newAgent) {
                            await this.processRequestWithAgent(request, newAgent);
                        }
                        else {
                            request.reject(new Error('Failed to spawn agent for request'));
                        }
                    }
                    else {
                        request.reject(new Error('No available agents and maximum limit reached'));
                    }
                }
                else {
                    await this.processRequestWithAgent(request, selectedAgent);
                }
            }
            catch (error) {
                request.reject(error);
            }
        }
    }
    async processRequestWithAgent(request, agentInfo) {
        agentInfo.status = 'busy';
        agentInfo.currentLoad++;
        agentInfo.totalRequests++;
        agentInfo.lastActivity = new Date();
        try {
            const response = await agentInfo.agent.processMessage(request.message, request.sessionId);
            request.resolve(response);
        }
        catch (error) {
            agentInfo.status = 'error';
            request.reject(error);
        }
        finally {
            agentInfo.currentLoad = Math.max(0, agentInfo.currentLoad - 1);
            if (agentInfo.currentLoad === 0 && agentInfo.status !== 'error') {
                agentInfo.status = 'idle';
            }
        }
    }
    selectAgent(requiredCapabilities) {
        const availableAgents = Array.from(this.agents.values()).filter(agent => agent.status === 'idle' || (agent.status === 'busy' && agent.currentLoad < 5));
        if (availableAgents.length === 0) {
            return null;
        }
        // Filter by capabilities if specified
        let candidateAgents = availableAgents;
        if (requiredCapabilities && requiredCapabilities.length > 0) {
            candidateAgents = availableAgents.filter(agent => requiredCapabilities.every(cap => agent.capabilities.includes(cap)));
            // Fallback to general agents if no specific capability match
            if (candidateAgents.length === 0) {
                candidateAgents = availableAgents.filter(agent => agent.capabilities.includes('general'));
            }
        }
        if (candidateAgents.length === 0) {
            return availableAgents[0]; // Fallback to any available agent
        }
        // Apply load balancing strategy
        switch (this.config.loadBalancingStrategy) {
            case 'round-robin':
                return candidateAgents[this.metrics.totalRequests % candidateAgents.length];
            case 'least-loaded':
                return candidateAgents.reduce((least, current) => current.currentLoad < least.currentLoad ? current : least);
            case 'random':
                return candidateAgents[Math.floor(Math.random() * candidateAgents.length)];
            case 'capability-based':
                // Prefer agents with exact capability match
                const exactMatches = candidateAgents.filter(agent => requiredCapabilities?.every(cap => agent.capabilities.includes(cap)));
                return exactMatches.length > 0 ? exactMatches[0] : candidateAgents[0];
            default:
                return candidateAgents[0];
        }
    }
    setupAgentEventListeners(agentInfo) {
        // Monitor agent health and performance
        // In a real implementation, would set up more sophisticated monitoring
    }
    startHealthChecking() {
        this.healthCheckTimer = setInterval(() => {
            this.performHealthCheck();
        }, this.config.healthCheckInterval);
    }
    async performHealthCheck() {
        const now = new Date();
        const unhealthyAgents = [];
        for (const [agentId, agentInfo] of this.agents) {
            // Check if agent has been inactive for too long
            const inactiveTime = now.getTime() - agentInfo.lastActivity.getTime();
            if (inactiveTime > this.config.healthCheckInterval * 2) {
                unhealthyAgents.push(agentId);
            }
        }
        // Handle unhealthy agents
        for (const agentId of unhealthyAgents) {
            console.warn(`🚨 Unhealthy agent detected: ${agentId}`);
            this.emit('agent_unhealthy', { agentId });
            // Could implement recovery strategies here
            // For now, just mark as error status
            const agentInfo = this.agents.get(agentId);
            if (agentInfo) {
                agentInfo.status = 'error';
            }
        }
        this.updateMetrics();
    }
    updateMetrics() {
        const agents = Array.from(this.agents.values());
        this.metrics.totalAgents = agents.length;
        this.metrics.activeAgents = agents.filter(a => a.status !== 'terminated').length;
        this.metrics.idleAgents = agents.filter(a => a.status === 'idle').length;
        this.metrics.busyAgents = agents.filter(a => a.status === 'busy').length;
        this.metrics.errorAgents = agents.filter(a => a.status === 'error').length;
        this.metrics.averageLoad = agents.length > 0
            ? agents.reduce((sum, a) => sum + a.currentLoad, 0) / agents.length
            : 0;
        // Calculate requests per second (simple moving average)
        const timeSinceLastRequest = Date.now() - this.lastRequestTime;
        if (timeSinceLastRequest > 0) {
            this.metrics.requestsPerSecond = 1000 / timeSinceLastRequest;
        }
    }
}
/**
 * Create multi-agent manager instance
 */
export function createMultiAgentManager(config = {}) {
    return new MultiAgentManager(config);
}
//# sourceMappingURL=multi-agent-manager.js.map