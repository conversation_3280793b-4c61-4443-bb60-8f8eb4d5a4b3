/**
 * Claude Code 3.0 - Multi-Agent Manager
 *
 * Manages multiple concurrent agents with orchestration, load balancing,
 * and inter-agent communication through the h2A message queue system.
 */
import { EventEmitter } from 'eventemitter3';
import { AgentCore } from './agent-core.js';
import { AgentConfig, AgentMessage, AgentResponse } from '../../types/agent.js';
export interface MultiAgentConfig {
    maxAgents: number;
    loadBalancingStrategy: 'round-robin' | 'least-loaded' | 'random' | 'capability-based';
    enableInterAgentCommunication: boolean;
    agentSpawnDelay: number;
    healthCheckInterval: number;
    enableMetrics: boolean;
    defaultAgentConfig: Partial<AgentConfig>;
}
export declare const DEFAULT_MULTI_AGENT_CONFIG: MultiAgentConfig;
export interface AgentInfo {
    id: string;
    agent: AgentCore;
    status: 'idle' | 'busy' | 'error' | 'terminated';
    capabilities: string[];
    currentLoad: number;
    totalRequests: number;
    lastActivity: Date;
    spawnTime: Date;
    config: AgentConfig;
}
export interface LoadBalancingMetrics {
    totalAgents: number;
    activeAgents: number;
    idleAgents: number;
    busyAgents: number;
    errorAgents: number;
    averageLoad: number;
    totalRequests: number;
    requestsPerSecond: number;
}
/**
 * Multi-Agent Manager
 *
 * Orchestrates multiple AI agents with load balancing, health monitoring,
 * and inter-agent communication capabilities.
 */
export declare class MultiAgentManager extends EventEmitter {
    private config;
    private agents;
    private requestQueue;
    private metrics;
    private healthCheckTimer?;
    private lastRequestTime;
    constructor(config?: Partial<MultiAgentConfig>);
    /**
     * Start the multi-agent system
     */
    start(): Promise<void>;
    /**
     * Stop the multi-agent system
     */
    stop(): Promise<void>;
    /**
     * Process message with load balancing
     */
    processMessage(message: AgentMessage, sessionId?: string, requiredCapabilities?: string[]): Promise<AgentResponse>;
    /**
     * Spawn a new agent
     */
    spawnAgent(config?: Partial<AgentConfig>, capabilities?: string[]): Promise<string>;
    /**
     * Terminate an agent
     */
    terminateAgent(agentId: string): Promise<boolean>;
    /**
     * Get agent information
     */
    getAgent(agentId: string): AgentInfo | undefined;
    /**
     * List all agents
     */
    listAgents(): AgentInfo[];
    /**
     * Get load balancing metrics
     */
    getMetrics(): LoadBalancingMetrics;
    /**
     * Send message between agents
     */
    sendInterAgentMessage(fromAgentId: string, toAgentId: string, message: any): Promise<boolean>;
    private spawnInitialAgents;
    private processRequestQueue;
    private processRequestWithAgent;
    private selectAgent;
    private setupAgentEventListeners;
    private startHealthChecking;
    private performHealthCheck;
    private updateMetrics;
}
/**
 * Create multi-agent manager instance
 */
export declare function createMultiAgentManager(config?: Partial<MultiAgentConfig>): MultiAgentManager;
//# sourceMappingURL=multi-agent-manager.d.ts.map