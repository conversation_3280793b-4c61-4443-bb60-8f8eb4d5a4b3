/**
 * Claude Code 3.0 - Agent Layer
 *
 * AI agent processing layer with nO async generator functions.
 */
export { AgentCore } from './agent-core.js';
export { MultiAgentManager } from './multi-agent-manager.js';
export * from '../../types/agent.js';
import { AgentCore } from './agent-core.js';
import { AgentConfig } from '../../types/agent.js';
/**
 * Create a standard agent with default configuration
 */
export declare function createAgent(config?: Partial<AgentConfig>): AgentCore;
/**
 * Create a high-performance agent optimized for throughput
 */
export declare function createHighPerformanceAgent(): AgentCore;
/**
 * Create a memory-efficient agent for resource-constrained environments
 */
export declare function createMemoryEfficientAgent(): AgentCore;
/**
 * Create a development agent with enhanced debugging
 */
export declare function createDevelopmentAgent(): AgentCore;
/**
 * Create multi-agent manager with default configuration
 */
export declare function createMultiAgentManager(config?: {}): any;
export declare class AgentManager {
    private agents;
    createAgent(id: string, config?: Partial<AgentConfig>): Promise<AgentCore>;
    getAgent(id: string): AgentCore | undefined;
    removeAgent(id: string): Promise<boolean>;
    getAllAgents(): AgentCore[];
    stopAll(): Promise<void>;
}
//# sourceMappingURL=index.d.ts.map