/**
 * Claude Code 3.0 - Agent Type Definitions
 *
 * Type definitions for the Agent layer, including the nO async generator
 * functions and event-driven processing system.
 */
/// <reference types="node" />
/// <reference types="node" />
export interface AgentConfig {
    model: string;
    apiKey?: string;
    baseURL?: string;
    maxConcurrentRequests: number;
    contextWindowSize: number;
    enableContextCompression: boolean;
    compressionThreshold: number;
    enableStreaming: boolean;
    streamingChunkSize: number;
    streamingTimeout: number;
    maxRetries: number;
    retryDelay: number;
    enableFallback: boolean;
    fallbackModel?: string;
    enableCaching: boolean;
    cacheSize: number;
    enableMetrics: boolean;
}
export interface AgentState {
    status: AgentStatus;
    activeRequests: number;
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    lastActivity: Date;
    contextSize: number;
    isStreaming: boolean;
}
export declare enum AgentStatus {
    IDLE = "idle",
    PROCESSING = "processing",
    STREAMING = "streaming",
    ERROR = "error",
    PAUSED = "paused"
}
export interface AgentMessage {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
    metadata?: MessageMetadata;
}
export interface MessageMetadata {
    toolCalls?: ToolCall[];
    attachments?: Attachment[];
    context?: ContextData;
    priority?: number;
    tags?: string[];
}
export interface ToolCall {
    id: string;
    name: string;
    parameters: Record<string, any>;
    result?: any;
    error?: string;
}
export interface Attachment {
    id: string;
    type: 'file' | 'image' | 'document';
    name: string;
    content: string | Buffer;
    mimeType: string;
}
export interface ContextData {
    workspaceRoot?: string;
    currentFile?: string;
    selectedText?: string;
    gitBranch?: string;
    projectType?: string;
    dependencies?: string[];
}
export interface Conversation {
    id: string;
    title: string;
    messages: AgentMessage[];
    createdAt: Date;
    updatedAt: Date;
    metadata: ConversationMetadata;
}
export interface ConversationMetadata {
    model: string;
    totalTokens: number;
    estimatedCost: number;
    tags: string[];
    archived: boolean;
}
export interface AgentSession {
    id: string;
    conversationId: string;
    startTime: Date;
    endTime?: Date;
    config: AgentConfig;
    state: AgentState;
    context: ContextData;
}
export interface StreamChunk {
    id: string;
    type: StreamChunkType;
    content: string;
    timestamp: Date;
    metadata?: any;
}
export declare enum StreamChunkType {
    TEXT = "text",
    TOOL_CALL = "tool_call",
    TOOL_RESULT = "tool_result",
    ERROR = "error",
    DONE = "done"
}
export interface StreamingResponse {
    id: string;
    chunks: AsyncIterable<StreamChunk>;
    metadata: {
        model: string;
        startTime: Date;
        estimatedTokens?: number;
    };
}
export interface nOGeneratorConfig {
    enableYielding: boolean;
    yieldInterval: number;
    maxIterations: number;
    enableContextCompression: boolean;
    compressionRatio: number;
    enableErrorRecovery: boolean;
    maxErrors: number;
    enableBatching: boolean;
    batchSize: number;
}
export interface nOGeneratorState {
    isRunning: boolean;
    currentIteration: number;
    totalIterations: number;
    lastYield: Date;
    errors: Error[];
    performance: {
        averageIterationTime: number;
        totalProcessingTime: number;
        memoryUsage: number;
    };
}
export type nOAsyncGenerator<T = any> = AsyncGenerator<T, void, unknown>;
export interface AgentEvent<T = any> {
    type: AgentEventType;
    timestamp: Date;
    agentId: string;
    data: T;
}
export declare enum AgentEventType {
    AGENT_STARTED = "agent_started",
    AGENT_STOPPED = "agent_stopped",
    MESSAGE_RECEIVED = "message_received",
    MESSAGE_PROCESSED = "message_processed",
    STREAMING_STARTED = "streaming_started",
    STREAMING_CHUNK = "streaming_chunk",
    STREAMING_COMPLETED = "streaming_completed",
    TOOL_CALL_STARTED = "tool_call_started",
    TOOL_CALL_COMPLETED = "tool_call_completed",
    CONTEXT_COMPRESSED = "context_compressed",
    ERROR_OCCURRED = "error_occurred",
    PERFORMANCE_METRIC = "performance_metric"
}
export interface ToolDefinition {
    name: string;
    description: string;
    parameters: {
        type: 'object';
        properties: Record<string, any>;
        required?: string[];
    };
}
export interface ToolExecutionContext {
    agentId: string;
    conversationId: string;
    messageId: string;
    toolCall: ToolCall;
    context: ContextData;
}
export interface ToolExecutionResult {
    success: boolean;
    result?: any;
    error?: string;
    executionTime: number;
    metadata?: any;
}
export declare const DEFAULT_AGENT_CONFIG: AgentConfig;
export declare const DEFAULT_NO_GENERATOR_CONFIG: nOGeneratorConfig;
//# sourceMappingURL=agent.d.ts.map