/**
 * Claude Code 3.0 - Type Definitions Index
 *
 * Central export point for all type definitions.
 */
export * from './system.js';
export * from './message-queue.js';
export * from './agent.js';
export interface BaseConfig {
    enabled: boolean;
    [key: string]: any;
}
export interface BaseState {
    status: string;
    lastActivity: Date;
    [key: string]: any;
}
export interface BaseMetrics {
    [key: string]: number | string | boolean;
}
export interface BaseEvent<T = any> {
    type: string;
    timestamp: Date;
    data: T;
}
export interface BaseError {
    id: string;
    timestamp: Date;
    level: 'info' | 'warning' | 'error' | 'critical';
    message: string;
    stack?: string;
    context?: Record<string, any>;
}
export type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type OptionalKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type AsyncFunction<T = void> = () => Promise<T>;
export type EventHandler<T = any> = (event: T) => void | Promise<void>;
export type ErrorHandler = (error: Error) => void | Promise<void>;
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    timestamp: Date;
}
export interface PaginatedResponse<T = any> {
    items: T[];
    total: number;
    page: number;
    pageSize: number;
    hasMore: boolean;
}
export interface ValidationResult {
    valid: boolean;
    errors: string[];
    warnings: string[];
}
export interface PluginConfig {
    name: string;
    version: string;
    enabled: boolean;
    dependencies?: string[];
    config?: Record<string, any>;
}
export interface PluginInterface {
    name: string;
    version: string;
    initialize(config: any): Promise<void>;
    start(): Promise<void>;
    stop(): Promise<void>;
    getState(): any;
}
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';
export interface LogEntry {
    level: LogLevel;
    message: string;
    timestamp: Date;
    context?: Record<string, any>;
    source?: string;
}
export interface PerformanceMetric {
    name: string;
    value: number;
    unit: string;
    timestamp: Date;
    tags?: Record<string, string>;
}
export interface PerformanceReport {
    metrics: PerformanceMetric[];
    summary: {
        averageResponseTime: number;
        throughput: number;
        errorRate: number;
        uptime: number;
    };
    period: {
        start: Date;
        end: Date;
    };
}
//# sourceMappingURL=index.d.ts.map