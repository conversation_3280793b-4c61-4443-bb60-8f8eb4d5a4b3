/**
 * Claude Code 3.0 - h2A Message Queue Type Definitions
 *
 * Type definitions for the h2A dual-buffer async message queue system.
 * Implements real-time steering mechanism for Claude Code.
 */
export interface Message<T = any> {
    id: string;
    timestamp: Date;
    type: string;
    payload: T;
    priority: MessagePriority;
    metadata?: MessageMetadata;
}
export interface MessageMetadata {
    source: string;
    destination?: string;
    correlationId?: string;
    retryCount?: number;
    maxRetries?: number;
    timeout?: number;
    tags?: string[];
}
export declare enum MessagePriority {
    LOW = 0,
    NORMAL = 1,
    HIGH = 2,
    CRITICAL = 3
}
export interface QueueConfig {
    maxBufferSize: number;
    enableDualBuffer: boolean;
    bufferSwitchThreshold: number;
    backpressureStrategy: BackpressureStrategy;
    backpressureThreshold: number;
    batchSize: number;
    flushInterval: number;
    enableMetrics: boolean;
    enableRetry: boolean;
    maxRetries: number;
    retryDelay: number;
    enableHealthCheck: boolean;
    healthCheckInterval: number;
}
export declare enum BackpressureStrategy {
    DROP_OLDEST = "drop_oldest",
    DROP_NEWEST = "drop_newest",
    BLOCK = "block",
    EXPAND_BUFFER = "expand_buffer"
}
export interface QueueState {
    status: QueueStatus;
    activeBuffer: 'primary' | 'secondary';
    primaryBufferSize: number;
    secondaryBufferSize: number;
    totalMessages: number;
    processingRate: number;
    lastActivity: Date;
}
export declare enum QueueStatus {
    IDLE = "idle",
    PROCESSING = "processing",
    BACKPRESSURE = "backpressure",
    ERROR = "error",
    SHUTDOWN = "shutdown"
}
export interface QueueMetrics {
    messagesProcessed: number;
    messagesDropped: number;
    averageProcessingTime: number;
    throughputPerSecond: number;
    bufferUtilization: number;
    bufferSwitches: number;
    maxBufferSizeReached: number;
    errorCount: number;
    retryCount: number;
    timeoutCount: number;
    memoryUsage: number;
    cpuUsage: number;
    latency: {
        p50: number;
        p95: number;
        p99: number;
    };
}
export interface QueueEvent<T = any> {
    type: QueueEventType;
    timestamp: Date;
    data: T;
}
export declare enum QueueEventType {
    MESSAGE_ENQUEUED = "message_enqueued",
    MESSAGE_DEQUEUED = "message_dequeued",
    MESSAGE_PROCESSED = "message_processed",
    MESSAGE_DROPPED = "message_dropped",
    MESSAGE_RETRY = "message_retry",
    MESSAGE_TIMEOUT = "message_timeout",
    BUFFER_SWITCH = "buffer_switch",
    BACKPRESSURE_ACTIVATED = "backpressure_activated",
    BACKPRESSURE_DEACTIVATED = "backpressure_deactivated",
    QUEUE_ERROR = "queue_error",
    QUEUE_SHUTDOWN = "queue_shutdown"
}
export type MessageHandler<T = any> = (message: Message<T>) => Promise<void> | void;
export type ErrorHandler = (error: Error, message?: Message) => Promise<void> | void;
export type EventHandler<T = any> = (event: QueueEvent<T>) => Promise<void> | void;
export interface IMessageQueue<T = any> {
    enqueue(message: Message<T>): Promise<boolean>;
    dequeue(): Promise<Message<T> | null>;
    peek(): Promise<Message<T> | null>;
    clear(): Promise<void>;
    enqueueBatch(messages: Message<T>[]): Promise<number>;
    dequeueBatch(count: number): Promise<Message<T>[]>;
    getState(): QueueState;
    getMetrics(): QueueMetrics;
    isHealthy(): boolean;
    on(event: QueueEventType, handler: EventHandler): void;
    off(event: QueueEventType, handler: EventHandler): void;
    emit(event: QueueEventType, data?: any): void;
    start(): Promise<void>;
    stop(): Promise<void>;
    pause(): Promise<void>;
    resume(): Promise<void>;
}
export interface SteeringConfig {
    queueConfig: QueueConfig;
    enableRealTimeProcessing: boolean;
    steeringInterval: number;
    adaptiveThrottling: boolean;
    enableFlowControl: boolean;
    flowControlThreshold: number;
    enableAgentIntegration: boolean;
    enableUIIntegration: boolean;
    enableToolIntegration: boolean;
}
export interface SteeringState {
    isActive: boolean;
    currentLoad: number;
    throttleLevel: number;
    lastSteeringAction: Date;
    queueState: QueueState;
}
export declare const DEFAULT_QUEUE_CONFIG: QueueConfig;
export declare const DEFAULT_STEERING_CONFIG: SteeringConfig;
//# sourceMappingURL=message-queue.d.ts.map