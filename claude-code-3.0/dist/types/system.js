/**
 * Claude Code 3.0 - System Type Definitions
 *
 * Core system types and interfaces for the 7-layer architecture.
 * Based on the "Documentation as Software" philosophy.
 */
// ============================================================================
// Default Configuration
// ============================================================================
export const DEFAULT_SYSTEM_CONFIG = {
    version: '3.0.0',
    environment: 'development',
    enableDebugMode: true,
    enableMetrics: true,
    logLevel: 'info',
    maxConcurrentOperations: 10,
    messageQueueSize: 1000,
    enableHotReload: true,
    optimizeForProduction: false,
    layers: {
        cli: {
            enabled: true,
            defaultCommand: 'help',
            enableAutoComplete: true,
            enableColorOutput: true
        },
        ui: {
            enabled: true,
            port: 3000,
            theme: 'auto',
            enableHotReload: true,
            enableDevTools: true
        },
        steering: {
            enabled: true,
            bufferSize: 1000,
            backpressureStrategy: 'drop_oldest',
            enableMetrics: true,
            flushInterval: 100
        },
        event: {
            enabled: true,
            maxListeners: 100,
            enableEventHistory: true,
            eventHistorySize: 1000
        },
        message: {
            enabled: true,
            maxMessageSize: 1024 * 1024, // 1MB
            compressionEnabled: true,
            enableMessageHistory: true
        },
        agent: {
            enabled: true,
            maxConcurrentAgents: 5,
            defaultModel: 'claude-3-sonnet',
            enableContextCompression: true,
            contextWindowSize: 200000
        },
        tool: {
            enabled: true,
            maxConcurrentTools: 10,
            enableToolMetrics: true,
            toolTimeout: 30000 // 30 seconds
        },
        api: {
            enabled: true,
            rateLimitConfig: {
                requestsPerMinute: 60,
                requestsPerHour: 1000,
                enableBurst: true,
                burstSize: 10
            },
            enableRetry: true,
            maxRetries: 3
        }
    },
    security: {
        enableSandbox: true,
        allowedFileOperations: ['read', 'write', 'create', 'delete'],
        maxFileSize: 10 * 1024 * 1024, // 10MB
        enableAuditLog: true
    },
    integrations: {
        enableClaudeAPI: true,
        enableWebSocket: true,
        enableFileWatcher: true,
        enableGitIntegration: true
    }
};
//# sourceMappingURL=system.js.map