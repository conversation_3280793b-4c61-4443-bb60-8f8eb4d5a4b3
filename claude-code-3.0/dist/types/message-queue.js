/**
 * Claude Code 3.0 - h2A Message Queue Type Definitions
 *
 * Type definitions for the h2A dual-buffer async message queue system.
 * Implements real-time steering mechanism for Claude Code.
 */
export var MessagePriority;
(function (MessagePriority) {
    MessagePriority[MessagePriority["LOW"] = 0] = "LOW";
    MessagePriority[MessagePriority["NORMAL"] = 1] = "NORMAL";
    MessagePriority[MessagePriority["HIGH"] = 2] = "HIGH";
    MessagePriority[MessagePriority["CRITICAL"] = 3] = "CRITICAL";
})(MessagePriority || (MessagePriority = {}));
export var BackpressureStrategy;
(function (BackpressureStrategy) {
    BackpressureStrategy["DROP_OLDEST"] = "drop_oldest";
    BackpressureStrategy["DROP_NEWEST"] = "drop_newest";
    BackpressureStrategy["BLOCK"] = "block";
    BackpressureStrategy["EXPAND_BUFFER"] = "expand_buffer";
})(BackpressureStrategy || (BackpressureStrategy = {}));
export var QueueStatus;
(function (QueueStatus) {
    QueueStatus["IDLE"] = "idle";
    QueueStatus["PROCESSING"] = "processing";
    QueueStatus["BACKPRESSURE"] = "backpressure";
    QueueStatus["ERROR"] = "error";
    QueueStatus["SHUTDOWN"] = "shutdown";
})(QueueStatus || (QueueStatus = {}));
export var QueueEventType;
(function (QueueEventType) {
    QueueEventType["MESSAGE_ENQUEUED"] = "message_enqueued";
    QueueEventType["MESSAGE_DEQUEUED"] = "message_dequeued";
    QueueEventType["MESSAGE_PROCESSED"] = "message_processed";
    QueueEventType["MESSAGE_DROPPED"] = "message_dropped";
    QueueEventType["MESSAGE_RETRY"] = "message_retry";
    QueueEventType["MESSAGE_TIMEOUT"] = "message_timeout";
    QueueEventType["BUFFER_SWITCH"] = "buffer_switch";
    QueueEventType["BACKPRESSURE_ACTIVATED"] = "backpressure_activated";
    QueueEventType["BACKPRESSURE_DEACTIVATED"] = "backpressure_deactivated";
    QueueEventType["QUEUE_ERROR"] = "queue_error";
    QueueEventType["QUEUE_SHUTDOWN"] = "queue_shutdown";
})(QueueEventType || (QueueEventType = {}));
// ============================================================================
// Default Configurations
// ============================================================================
export const DEFAULT_QUEUE_CONFIG = {
    maxBufferSize: 1000,
    enableDualBuffer: true,
    bufferSwitchThreshold: 0.8,
    backpressureStrategy: BackpressureStrategy.DROP_OLDEST,
    backpressureThreshold: 0.9,
    batchSize: 10,
    flushInterval: 100,
    enableMetrics: true,
    enableRetry: true,
    maxRetries: 3,
    retryDelay: 1000,
    enableHealthCheck: true,
    healthCheckInterval: 5000
};
export const DEFAULT_STEERING_CONFIG = {
    queueConfig: DEFAULT_QUEUE_CONFIG,
    enableRealTimeProcessing: true,
    steeringInterval: 50,
    adaptiveThrottling: true,
    enableFlowControl: true,
    flowControlThreshold: 0.8,
    enableAgentIntegration: true,
    enableUIIntegration: true,
    enableToolIntegration: true
};
//# sourceMappingURL=message-queue.js.map