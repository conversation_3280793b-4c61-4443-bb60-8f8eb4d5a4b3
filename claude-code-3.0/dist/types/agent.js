/**
 * Claude Code 3.0 - Agent Type Definitions
 *
 * Type definitions for the Agent layer, including the nO async generator
 * functions and event-driven processing system.
 */
export var AgentStatus;
(function (AgentStatus) {
    AgentStatus["IDLE"] = "idle";
    AgentStatus["PROCESSING"] = "processing";
    AgentStatus["STREAMING"] = "streaming";
    AgentStatus["ERROR"] = "error";
    AgentStatus["PAUSED"] = "paused";
})(AgentStatus || (AgentStatus = {}));
export var StreamChunkType;
(function (StreamChunkType) {
    StreamChunkType["TEXT"] = "text";
    StreamChunkType["TOOL_CALL"] = "tool_call";
    StreamChunkType["TOOL_RESULT"] = "tool_result";
    StreamChunkType["ERROR"] = "error";
    StreamChunkType["DONE"] = "done";
})(StreamChunkType || (StreamChunkType = {}));
export var AgentEventType;
(function (AgentEventType) {
    AgentEventType["AGENT_STARTED"] = "agent_started";
    AgentEventType["AGENT_STOPPED"] = "agent_stopped";
    AgentEventType["MESSAGE_RECEIVED"] = "message_received";
    AgentEventType["MESSAGE_PROCESSED"] = "message_processed";
    AgentEventType["STREAMING_STARTED"] = "streaming_started";
    AgentEventType["STREAMING_CHUNK"] = "streaming_chunk";
    AgentEventType["STREAMING_COMPLETED"] = "streaming_completed";
    AgentEventType["TOOL_CALL_STARTED"] = "tool_call_started";
    AgentEventType["TOOL_CALL_COMPLETED"] = "tool_call_completed";
    AgentEventType["CONTEXT_COMPRESSED"] = "context_compressed";
    AgentEventType["ERROR_OCCURRED"] = "error_occurred";
    AgentEventType["PERFORMANCE_METRIC"] = "performance_metric";
})(AgentEventType || (AgentEventType = {}));
// ============================================================================
// Default Configurations
// ============================================================================
export const DEFAULT_AGENT_CONFIG = {
    model: 'claude-3-sonnet-20240229',
    maxConcurrentRequests: 5,
    contextWindowSize: 200000,
    enableContextCompression: true,
    compressionThreshold: 150000,
    enableStreaming: true,
    streamingChunkSize: 1024,
    streamingTimeout: 30000,
    maxRetries: 3,
    retryDelay: 1000,
    enableFallback: true,
    fallbackModel: 'claude-3-haiku-20240307',
    enableCaching: true,
    cacheSize: 100,
    enableMetrics: true
};
export const DEFAULT_NO_GENERATOR_CONFIG = {
    enableYielding: true,
    yieldInterval: 100,
    maxIterations: 10000,
    enableContextCompression: true,
    compressionRatio: 0.7,
    enableErrorRecovery: true,
    maxErrors: 5,
    enableBatching: true,
    batchSize: 10
};
//# sourceMappingURL=agent.js.map