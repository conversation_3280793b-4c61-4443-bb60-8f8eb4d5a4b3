{"version": 3, "file": "message-queue.js", "sourceRoot": "", "sources": ["../../src/types/message-queue.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAyBH,MAAM,CAAN,IAAY,eAKX;AALD,WAAY,eAAe;IACzB,mDAAO,CAAA;IACP,yDAAU,CAAA;IACV,qDAAQ,CAAA;IACR,6DAAY,CAAA;AACd,CAAC,EALW,eAAe,KAAf,eAAe,QAK1B;AA+BD,MAAM,CAAN,IAAY,oBAKX;AALD,WAAY,oBAAoB;IAC9B,mDAA2B,CAAA;IAC3B,mDAA2B,CAAA;IAC3B,uCAAe,CAAA;IACf,uDAA+B,CAAA;AACjC,CAAC,EALW,oBAAoB,KAApB,oBAAoB,QAK/B;AAgBD,MAAM,CAAN,IAAY,WAMX;AAND,WAAY,WAAW;IACrB,4BAAa,CAAA;IACb,wCAAyB,CAAA;IACzB,4CAA6B,CAAA;IAC7B,8BAAe,CAAA;IACf,oCAAqB,CAAA;AACvB,CAAC,EANW,WAAW,KAAX,WAAW,QAMtB;AAuCD,MAAM,CAAN,IAAY,cAYX;AAZD,WAAY,cAAc;IACxB,uDAAqC,CAAA;IACrC,uDAAqC,CAAA;IACrC,yDAAuC,CAAA;IACvC,qDAAmC,CAAA;IACnC,iDAA+B,CAAA;IAC/B,qDAAmC,CAAA;IACnC,iDAA+B,CAAA;IAC/B,mEAAiD,CAAA;IACjD,uEAAqD,CAAA;IACrD,6CAA2B,CAAA;IAC3B,mDAAiC,CAAA;AACnC,CAAC,EAZW,cAAc,KAAd,cAAc,QAYzB;AAyED,+EAA+E;AAC/E,yBAAyB;AACzB,+EAA+E;AAE/E,MAAM,CAAC,MAAM,oBAAoB,GAAgB;IAC/C,aAAa,EAAE,IAAI;IACnB,gBAAgB,EAAE,IAAI;IACtB,qBAAqB,EAAE,GAAG;IAC1B,oBAAoB,EAAE,oBAAoB,CAAC,WAAW;IACtD,qBAAqB,EAAE,GAAG;IAC1B,SAAS,EAAE,EAAE;IACb,aAAa,EAAE,GAAG;IAClB,aAAa,EAAE,IAAI;IACnB,WAAW,EAAE,IAAI;IACjB,UAAU,EAAE,CAAC;IACb,UAAU,EAAE,IAAI;IAChB,iBAAiB,EAAE,IAAI;IACvB,mBAAmB,EAAE,IAAI;CAC1B,CAAC;AAEF,MAAM,CAAC,MAAM,uBAAuB,GAAmB;IACrD,WAAW,EAAE,oBAAoB;IACjC,wBAAwB,EAAE,IAAI;IAC9B,gBAAgB,EAAE,EAAE;IACpB,kBAAkB,EAAE,IAAI;IACxB,iBAAiB,EAAE,IAAI;IACvB,oBAAoB,EAAE,GAAG;IACzB,sBAAsB,EAAE,IAAI;IAC5B,mBAAmB,EAAE,IAAI;IACzB,qBAAqB,EAAE,IAAI;CAC5B,CAAC"}