{"version": 3, "file": "message-queue.d.ts", "sourceRoot": "", "sources": ["../../src/types/message-queue.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAMH,MAAM,WAAW,OAAO,CAAC,CAAC,GAAG,GAAG;IAC9B,EAAE,EAAE,MAAM,CAAC;IACX,SAAS,EAAE,IAAI,CAAC;IAChB,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE,CAAC,CAAC;IACX,QAAQ,EAAE,eAAe,CAAC;IAC1B,QAAQ,CAAC,EAAE,eAAe,CAAC;CAC5B;AAED,MAAM,WAAW,eAAe;IAC9B,MAAM,EAAE,MAAM,CAAC;IACf,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC;CACjB;AAED,oBAAY,eAAe;IACzB,GAAG,IAAI;IACP,MAAM,IAAI;IACV,IAAI,IAAI;IACR,QAAQ,IAAI;CACb;AAMD,MAAM,WAAW,WAAW;IAE1B,aAAa,EAAE,MAAM,CAAC;IACtB,gBAAgB,EAAE,OAAO,CAAC;IAC1B,qBAAqB,EAAE,MAAM,CAAC;IAG9B,oBAAoB,EAAE,oBAAoB,CAAC;IAC3C,qBAAqB,EAAE,MAAM,CAAC;IAG9B,SAAS,EAAE,MAAM,CAAC;IAClB,aAAa,EAAE,MAAM,CAAC;IACtB,aAAa,EAAE,OAAO,CAAC;IAGvB,WAAW,EAAE,OAAO,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IAGnB,iBAAiB,EAAE,OAAO,CAAC;IAC3B,mBAAmB,EAAE,MAAM,CAAC;CAC7B;AAED,oBAAY,oBAAoB;IAC9B,WAAW,gBAAgB;IAC3B,WAAW,gBAAgB;IAC3B,KAAK,UAAU;IACf,aAAa,kBAAkB;CAChC;AAMD,MAAM,WAAW,UAAU;IACzB,MAAM,EAAE,WAAW,CAAC;IACpB,YAAY,EAAE,SAAS,GAAG,WAAW,CAAC;IACtC,iBAAiB,EAAE,MAAM,CAAC;IAC1B,mBAAmB,EAAE,MAAM,CAAC;IAC5B,aAAa,EAAE,MAAM,CAAC;IACtB,cAAc,EAAE,MAAM,CAAC;IACvB,YAAY,EAAE,IAAI,CAAC;CACpB;AAED,oBAAY,WAAW;IACrB,IAAI,SAAS;IACb,UAAU,eAAe;IACzB,YAAY,iBAAiB;IAC7B,KAAK,UAAU;IACf,QAAQ,aAAa;CACtB;AAED,MAAM,WAAW,YAAY;IAE3B,iBAAiB,EAAE,MAAM,CAAC;IAC1B,eAAe,EAAE,MAAM,CAAC;IACxB,qBAAqB,EAAE,MAAM,CAAC;IAC9B,mBAAmB,EAAE,MAAM,CAAC;IAG5B,iBAAiB,EAAE,MAAM,CAAC;IAC1B,cAAc,EAAE,MAAM,CAAC;IACvB,oBAAoB,EAAE,MAAM,CAAC;IAG7B,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IACnB,YAAY,EAAE,MAAM,CAAC;IAGrB,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE;QACP,GAAG,EAAE,MAAM,CAAC;QACZ,GAAG,EAAE,MAAM,CAAC;QACZ,GAAG,EAAE,MAAM,CAAC;KACb,CAAC;CACH;AAMD,MAAM,WAAW,UAAU,CAAC,CAAC,GAAG,GAAG;IACjC,IAAI,EAAE,cAAc,CAAC;IACrB,SAAS,EAAE,IAAI,CAAC;IAChB,IAAI,EAAE,CAAC,CAAC;CACT;AAED,oBAAY,cAAc;IACxB,gBAAgB,qBAAqB;IACrC,gBAAgB,qBAAqB;IACrC,iBAAiB,sBAAsB;IACvC,eAAe,oBAAoB;IACnC,aAAa,kBAAkB;IAC/B,eAAe,oBAAoB;IACnC,aAAa,kBAAkB;IAC/B,sBAAsB,2BAA2B;IACjD,wBAAwB,6BAA6B;IACrD,WAAW,gBAAgB;IAC3B,cAAc,mBAAmB;CAClC;AAMD,MAAM,MAAM,cAAc,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AACpF,MAAM,MAAM,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,OAAO,KAAK,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AACrF,MAAM,MAAM,YAAY,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAMnF,MAAM,WAAW,aAAa,CAAC,CAAC,GAAG,GAAG;IAEpC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;IAC/C,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IACtC,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IACnC,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAGvB,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IACtD,YAAY,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAGnD,QAAQ,IAAI,UAAU,CAAC;IACvB,UAAU,IAAI,YAAY,CAAC;IAC3B,SAAS,IAAI,OAAO,CAAC;IAGrB,EAAE,CAAC,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,YAAY,GAAG,IAAI,CAAC;IACvD,GAAG,CAAC,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,YAAY,GAAG,IAAI,CAAC;IACxD,IAAI,CAAC,KAAK,EAAE,cAAc,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC;IAG9C,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IACvB,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IACtB,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IACvB,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;CACzB;AAMD,MAAM,WAAW,cAAc;IAE7B,WAAW,EAAE,WAAW,CAAC;IAGzB,wBAAwB,EAAE,OAAO,CAAC;IAClC,gBAAgB,EAAE,MAAM,CAAC;IACzB,kBAAkB,EAAE,OAAO,CAAC;IAG5B,iBAAiB,EAAE,OAAO,CAAC;IAC3B,oBAAoB,EAAE,MAAM,CAAC;IAG7B,sBAAsB,EAAE,OAAO,CAAC;IAChC,mBAAmB,EAAE,OAAO,CAAC;IAC7B,qBAAqB,EAAE,OAAO,CAAC;CAChC;AAED,MAAM,WAAW,aAAa;IAC5B,QAAQ,EAAE,OAAO,CAAC;IAClB,WAAW,EAAE,MAAM,CAAC;IACpB,aAAa,EAAE,MAAM,CAAC;IACtB,kBAAkB,EAAE,IAAI,CAAC;IACzB,UAAU,EAAE,UAAU,CAAC;CACxB;AAMD,eAAO,MAAM,oBAAoB,EAAE,WAclC,CAAC;AAEF,eAAO,MAAM,uBAAuB,EAAE,cAUrC,CAAC"}