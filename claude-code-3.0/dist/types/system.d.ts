/**
 * Claude Code 3.0 - System Type Definitions
 *
 * Core system types and interfaces for the 7-layer architecture.
 * Based on the "Documentation as Software" philosophy.
 */
export interface SystemConfig {
    version: string;
    environment: 'development' | 'production' | 'test';
    enableDebugMode: boolean;
    enableMetrics: boolean;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    maxConcurrentOperations: number;
    messageQueueSize: number;
    enableHotReload: boolean;
    optimizeForProduction: boolean;
    layers: {
        cli: CLILayerConfig;
        ui: UILayerConfig;
        steering: SteeringLayerConfig;
        event: EventLayerConfig;
        message: MessageLayerConfig;
        agent: AgentLayerConfig;
        tool: ToolLayerConfig;
        api: APILayerConfig;
    };
    security: SecurityConfig;
    integrations: IntegrationConfig;
}
export interface CLILayerConfig {
    enabled: boolean;
    defaultCommand: string;
    enableAutoComplete: boolean;
    enableColorOutput: boolean;
}
export interface UILayerConfig {
    enabled: boolean;
    port: number;
    theme: 'light' | 'dark' | 'auto';
    enableHotReload: boolean;
    enableDevTools: boolean;
}
export interface SteeringLayerConfig {
    enabled: boolean;
    bufferSize: number;
    backpressureStrategy: 'drop_oldest' | 'drop_newest' | 'block';
    enableMetrics: boolean;
    flushInterval: number;
}
export interface EventLayerConfig {
    enabled: boolean;
    maxListeners: number;
    enableEventHistory: boolean;
    eventHistorySize: number;
}
export interface MessageLayerConfig {
    enabled: boolean;
    maxMessageSize: number;
    compressionEnabled: boolean;
    enableMessageHistory: boolean;
}
export interface AgentLayerConfig {
    enabled: boolean;
    maxConcurrentAgents: number;
    defaultModel: string;
    enableContextCompression: boolean;
    contextWindowSize: number;
}
export interface ToolLayerConfig {
    enabled: boolean;
    maxConcurrentTools: number;
    enableToolMetrics: boolean;
    toolTimeout: number;
}
export interface APILayerConfig {
    enabled: boolean;
    claudeApiKey?: string;
    rateLimitConfig: RateLimitConfig;
    enableRetry: boolean;
    maxRetries: number;
}
export interface SystemState {
    status: SystemStatus;
    startTime: Date;
    uptime: number;
    layers: {
        cli: LayerState;
        ui: LayerState;
        steering: LayerState;
        event: LayerState;
        message: LayerState;
        agent: LayerState;
        tool: LayerState;
        api: LayerState;
    };
    metrics: SystemMetrics;
    errors: SystemError[];
    resources: ResourceUsage;
}
export type SystemStatus = 'initializing' | 'running' | 'paused' | 'stopping' | 'stopped' | 'error';
export interface LayerState {
    status: SystemStatus;
    initialized: boolean;
    lastActivity: Date;
    errorCount: number;
    metrics?: Record<string, any>;
}
export interface SecurityConfig {
    enableSandbox: boolean;
    allowedFileOperations: string[];
    maxFileSize: number;
    enableAuditLog: boolean;
}
export interface IntegrationConfig {
    enableClaudeAPI: boolean;
    enableWebSocket: boolean;
    enableFileWatcher: boolean;
    enableGitIntegration: boolean;
}
export interface RateLimitConfig {
    requestsPerMinute: number;
    requestsPerHour: number;
    enableBurst: boolean;
    burstSize: number;
}
export interface SystemMetrics {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    memoryUsage: number;
    cpuUsage: number;
    activeConnections: number;
}
export interface SystemError {
    id: string;
    timestamp: Date;
    level: 'warning' | 'error' | 'critical';
    layer: string;
    message: string;
    stack?: string;
    context?: Record<string, any>;
}
export interface ResourceUsage {
    memory: {
        used: number;
        total: number;
        percentage: number;
    };
    cpu: {
        usage: number;
        cores: number;
    };
    disk: {
        used: number;
        available: number;
        percentage: number;
    };
}
export declare const DEFAULT_SYSTEM_CONFIG: SystemConfig;
//# sourceMappingURL=system.d.ts.map