/**
 * Claude Code 3.0 - Ollama Local LLM Client
 *
 * Integration with Ollama for local LLM testing and usage.
 * Supports models like qwen2.5:3b, llama2, codellama, etc.
 */
export interface OllamaConfig {
    baseURL?: string;
    model: string;
    temperature?: number;
    maxTokens?: number;
    timeout?: number;
    stream?: boolean;
}
export interface OllamaMessage {
    role: 'user' | 'assistant' | 'system';
    content: string;
}
export interface OllamaResponse {
    model: string;
    created_at: string;
    message: {
        role: string;
        content: string;
    };
    done: boolean;
    total_duration?: number;
    load_duration?: number;
    prompt_eval_count?: number;
    prompt_eval_duration?: number;
    eval_count?: number;
    eval_duration?: number;
}
export interface OllamaStreamChunk {
    model: string;
    created_at: string;
    message: {
        role: string;
        content: string;
    };
    done: boolean;
}
export interface OllamaModelInfo {
    name: string;
    size: number;
    digest: string;
    details: {
        format: string;
        family: string;
        families: string[];
        parameter_size: string;
        quantization_level: string;
    };
    modified_at: string;
}
/**
 * Ollama Local LLM Client
 *
 * Provides integration with Ollama for local LLM testing and usage.
 * Supports streaming, model management, and performance monitoring.
 */
export declare class OllamaClient {
    private config;
    constructor(config: OllamaConfig);
    /**
     * Check if Ollama is running and accessible
     */
    isAvailable(): Promise<boolean>;
    /**
     * List available models
     */
    listModels(): Promise<OllamaModelInfo[]>;
    /**
     * Check if a specific model is available
     */
    isModelAvailable(modelName: string): Promise<boolean>;
    /**
     * Pull a model from Ollama registry
     */
    pullModel(modelName: string): Promise<void>;
    /**
     * Send a chat message to Ollama
     */
    chat(messages: OllamaMessage[], options?: Partial<OllamaConfig>): Promise<OllamaResponse>;
    /**
     * Send a streaming chat message to Ollama
     */
    chatStream(messages: OllamaMessage[], options?: Partial<OllamaConfig>): Promise<AsyncIterable<OllamaStreamChunk>>;
    /**
     * Test connection and model performance
     */
    testConnection(testPrompt?: string): Promise<{
        success: boolean;
        latency: number;
        model: string;
        response?: string;
        performance?: {
            totalDuration: number;
            loadDuration: number;
            promptEvalDuration: number;
            evalDuration: number;
            tokensPerSecond: number;
        };
        error?: string;
    }>;
    /**
     * Get model information and capabilities
     */
    getModelInfo(modelName?: string): Promise<OllamaModelInfo | null>;
    /**
     * Calculate estimated performance metrics
     */
    calculatePerformanceMetrics(response: OllamaResponse): {
        tokensPerSecond: number;
        promptTokensPerSecond: number;
        totalLatency: number;
        loadLatency: number;
        inferenceLatency: number;
    };
    private makeRequest;
    private parseStreamingResponse;
}
/**
 * Factory function to create Ollama client
 */
export declare function createOllamaClient(config: OllamaConfig): OllamaClient;
/**
 * Create Ollama client from environment variables
 */
export declare function createOllamaClientFromEnv(): OllamaClient | null;
/**
 * Auto-detect and create the best available Ollama client
 */
export declare function createAutoOllamaClient(): Promise<OllamaClient | null>;
//# sourceMappingURL=ollama-client.d.ts.map