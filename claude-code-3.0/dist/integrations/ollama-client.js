/**
 * Claude Code 3.0 - Ollama Local LLM Client
 *
 * Integration with Ollama for local LLM testing and usage.
 * Supports models like qwen2.5:3b, llama2, codellama, etc.
 */
/**
 * Ollama Local LLM Client
 *
 * Provides integration with Ollama for local LLM testing and usage.
 * Supports streaming, model management, and performance monitoring.
 */
export class OllamaClient {
    config;
    constructor(config) {
        this.config = {
            baseURL: 'http://localhost:11434',
            temperature: 0.7,
            maxTokens: 4096,
            timeout: 30000,
            stream: false,
            ...config
        };
    }
    /**
     * Check if Ollama is running and accessible
     */
    async isAvailable() {
        try {
            const response = await fetch(`${this.config.baseURL}/api/tags`, {
                method: 'GET',
                signal: AbortSignal.timeout(5000)
            });
            return response.ok;
        }
        catch (error) {
            return false;
        }
    }
    /**
     * List available models
     */
    async listModels() {
        const response = await this.makeRequest('/api/tags', {
            method: 'GET'
        });
        if (!response.ok) {
            throw new Error(`Failed to list models: ${response.status}`);
        }
        const data = await response.json();
        return data.models || [];
    }
    /**
     * Check if a specific model is available
     */
    async isModelAvailable(modelName) {
        try {
            const models = await this.listModels();
            return models.some(model => model.name === modelName);
        }
        catch (error) {
            return false;
        }
    }
    /**
     * Pull a model from Ollama registry
     */
    async pullModel(modelName) {
        const response = await this.makeRequest('/api/pull', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name: modelName })
        });
        if (!response.ok) {
            throw new Error(`Failed to pull model ${modelName}: ${response.status}`);
        }
        // Wait for pull to complete (simplified - in real implementation would handle streaming progress)
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    /**
     * Send a chat message to Ollama
     */
    async chat(messages, options = {}) {
        const config = { ...this.config, ...options };
        // Ensure model is available
        if (!(await this.isModelAvailable(config.model))) {
            throw new Error(`Model ${config.model} not available. Run: ollama pull ${config.model}`);
        }
        const requestBody = {
            model: config.model,
            messages: messages,
            stream: false,
            options: {
                temperature: config.temperature,
                num_predict: config.maxTokens
            }
        };
        const response = await this.makeRequest('/api/chat', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestBody),
            timeout: config.timeout
        });
        if (!response.ok) {
            const error = await response.text();
            throw new Error(`Ollama chat error: ${response.status} - ${error}`);
        }
        return await response.json();
    }
    /**
     * Send a streaming chat message to Ollama
     */
    async chatStream(messages, options = {}) {
        const config = { ...this.config, ...options };
        // Ensure model is available
        if (!(await this.isModelAvailable(config.model))) {
            throw new Error(`Model ${config.model} not available. Run: ollama pull ${config.model}`);
        }
        const requestBody = {
            model: config.model,
            messages: messages,
            stream: true,
            options: {
                temperature: config.temperature,
                num_predict: config.maxTokens
            }
        };
        const response = await this.makeRequest('/api/chat', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestBody),
            timeout: config.timeout
        });
        if (!response.ok) {
            const error = await response.text();
            throw new Error(`Ollama stream error: ${response.status} - ${error}`);
        }
        return this.parseStreamingResponse(response);
    }
    /**
     * Test connection and model performance
     */
    async testConnection(testPrompt = 'Hello! Please respond with "Connection successful"') {
        const startTime = Date.now();
        try {
            // Check if Ollama is running
            if (!(await this.isAvailable())) {
                return {
                    success: false,
                    latency: Date.now() - startTime,
                    model: this.config.model,
                    error: 'Ollama server not accessible. Is it running on http://localhost:11434?'
                };
            }
            // Check if model is available
            if (!(await this.isModelAvailable(this.config.model))) {
                return {
                    success: false,
                    latency: Date.now() - startTime,
                    model: this.config.model,
                    error: `Model ${this.config.model} not found. Run: ollama pull ${this.config.model}`
                };
            }
            // Test chat
            const response = await this.chat([
                { role: 'user', content: testPrompt }
            ]);
            const latency = Date.now() - startTime;
            // Calculate performance metrics
            const performance = response.total_duration ? {
                totalDuration: response.total_duration / 1000000, // Convert to ms
                loadDuration: (response.load_duration || 0) / 1000000,
                promptEvalDuration: (response.prompt_eval_duration || 0) / 1000000,
                evalDuration: (response.eval_duration || 0) / 1000000,
                tokensPerSecond: response.eval_count && response.eval_duration
                    ? (response.eval_count / (response.eval_duration / 1000000000))
                    : 0
            } : undefined;
            return {
                success: true,
                latency,
                model: response.model,
                response: response.message.content,
                performance
            };
        }
        catch (error) {
            return {
                success: false,
                latency: Date.now() - startTime,
                model: this.config.model,
                error: error.message
            };
        }
    }
    /**
     * Get model information and capabilities
     */
    async getModelInfo(modelName) {
        const targetModel = modelName || this.config.model;
        const models = await this.listModels();
        return models.find(model => model.name === targetModel) || null;
    }
    /**
     * Calculate estimated performance metrics
     */
    calculatePerformanceMetrics(response) {
        const totalDuration = (response.total_duration || 0) / 1000000; // Convert to ms
        const loadDuration = (response.load_duration || 0) / 1000000;
        const promptEvalDuration = (response.prompt_eval_duration || 0) / 1000000;
        const evalDuration = (response.eval_duration || 0) / 1000000;
        const tokensPerSecond = response.eval_count && evalDuration > 0
            ? (response.eval_count / (evalDuration / 1000))
            : 0;
        const promptTokensPerSecond = response.prompt_eval_count && promptEvalDuration > 0
            ? (response.prompt_eval_count / (promptEvalDuration / 1000))
            : 0;
        return {
            tokensPerSecond,
            promptTokensPerSecond,
            totalLatency: totalDuration,
            loadLatency: loadDuration,
            inferenceLatency: evalDuration
        };
    }
    // Private methods
    async makeRequest(endpoint, options) {
        const url = `${this.config.baseURL}${endpoint}`;
        const { timeout, ...fetchOptions } = options;
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout || this.config.timeout);
        try {
            const response = await fetch(url, {
                ...fetchOptions,
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            return response;
        }
        catch (error) {
            clearTimeout(timeoutId);
            if (error instanceof Error && error.name === 'AbortError') {
                throw new Error(`Request timeout after ${timeout || this.config.timeout}ms`);
            }
            throw error;
        }
    }
    async *parseStreamingResponse(response) {
        if (!response.body) {
            throw new Error('No response body for streaming');
        }
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done)
                    break;
                const chunk = decoder.decode(value);
                const lines = chunk.split('\n').filter(line => line.trim());
                for (const line of lines) {
                    try {
                        const data = JSON.parse(line);
                        yield data;
                        if (data.done) {
                            return;
                        }
                    }
                    catch (error) {
                        console.warn('Failed to parse streaming chunk:', line);
                    }
                }
            }
        }
        finally {
            reader.releaseLock();
        }
    }
}
/**
 * Factory function to create Ollama client
 */
export function createOllamaClient(config) {
    return new OllamaClient(config);
}
/**
 * Create Ollama client from environment variables
 */
export function createOllamaClientFromEnv() {
    const model = process.env.OLLAMA_MODEL;
    if (!model) {
        console.warn('⚠️  No Ollama model specified in OLLAMA_MODEL environment variable');
        return null;
    }
    return new OllamaClient({
        model,
        baseURL: process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
        temperature: process.env.OLLAMA_TEMPERATURE ? parseFloat(process.env.OLLAMA_TEMPERATURE) : 0.7,
        maxTokens: process.env.OLLAMA_MAX_TOKENS ? parseInt(process.env.OLLAMA_MAX_TOKENS) : 4096
    });
}
/**
 * Auto-detect and create the best available Ollama client
 */
export async function createAutoOllamaClient() {
    // Try common models in order of preference
    const commonModels = [
        'qwen2.5:3b',
        'llama3.2:3b',
        'llama3.1:8b',
        'codellama:7b',
        'mistral:7b',
        'phi3:3.8b'
    ];
    const client = new OllamaClient({ model: 'temp' });
    if (!(await client.isAvailable())) {
        return null;
    }
    const availableModels = await client.listModels();
    const availableModelNames = availableModels.map(m => m.name);
    // Find the first available model from our preferred list
    for (const model of commonModels) {
        if (availableModelNames.includes(model)) {
            return new OllamaClient({ model });
        }
    }
    // If no preferred models, use the first available model
    if (availableModels.length > 0) {
        return new OllamaClient({ model: availableModels[0].name });
    }
    return null;
}
//# sourceMappingURL=ollama-client.js.map