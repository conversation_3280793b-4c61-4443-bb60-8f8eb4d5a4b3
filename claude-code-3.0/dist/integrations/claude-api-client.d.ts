/**
 * Claude Code 3.0 - Real Claude API Client
 *
 * Actual Claude API integration for production testing and usage.
 * This replaces the mock responses when API keys are available.
 */
export interface ClaudeAPIConfig {
    apiKey: string;
    baseURL?: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
    timeout?: number;
    retries?: number;
}
export interface ClaudeMessage {
    role: 'user' | 'assistant' | 'system';
    content: string;
}
export interface ClaudeResponse {
    id: string;
    type: string;
    role: string;
    content: Array<{
        type: string;
        text: string;
    }>;
    model: string;
    stop_reason: string;
    stop_sequence: string | null;
    usage: {
        input_tokens: number;
        output_tokens: number;
    };
}
export interface StreamingClaudeResponse {
    id: string;
    chunks: AsyncIterable<ClaudeStreamChunk>;
    metadata: {
        model: string;
        startTime: Date;
    };
}
export interface ClaudeStreamChunk {
    type: 'message_start' | 'content_block_start' | 'content_block_delta' | 'content_block_stop' | 'message_delta' | 'message_stop';
    message?: Partial<ClaudeResponse>;
    content_block?: {
        type: string;
        text?: string;
    };
    delta?: {
        type: string;
        text?: string;
    };
    usage?: {
        input_tokens?: number;
        output_tokens?: number;
    };
}
/**
 * Real Claude API Client
 *
 * Provides actual integration with Anthropic's Claude API for production use.
 * Falls back to mock mode when API key is not available.
 */
export declare class ClaudeAPIClient {
    private config;
    private baseURL;
    constructor(config: ClaudeAPIConfig);
    /**
     * Check if API client is properly configured
     */
    isConfigured(): boolean;
    /**
     * Send a single message to Claude API
     */
    sendMessage(messages: ClaudeMessage[], options?: Partial<ClaudeAPIConfig>): Promise<ClaudeResponse>;
    /**
     * Send a streaming message to Claude API
     */
    sendStreamingMessage(messages: ClaudeMessage[], options?: Partial<ClaudeAPIConfig>): Promise<StreamingClaudeResponse>;
    /**
     * Test API connectivity and configuration
     */
    testConnection(): Promise<{
        success: boolean;
        latency: number;
        error?: string;
        model?: string;
    }>;
    /**
     * Get API usage statistics (if available)
     */
    getUsageStats(): Promise<{
        requestsToday?: number;
        tokensUsed?: number;
        costEstimate?: number;
    }>;
    /**
     * Calculate estimated cost for a request
     */
    calculateCost(inputTokens: number, outputTokens: number, model?: string): number;
    private makeRequest;
    private parseStreamingResponse;
}
/**
 * Factory function to create Claude API client
 */
export declare function createClaudeAPIClient(config: ClaudeAPIConfig): ClaudeAPIClient;
/**
 * Create Claude API client from environment variables
 */
export declare function createClaudeAPIClientFromEnv(): ClaudeAPIClient | null;
//# sourceMappingURL=claude-api-client.d.ts.map