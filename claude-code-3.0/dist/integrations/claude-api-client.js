/**
 * Claude Code 3.0 - Real Claude API Client
 *
 * Actual Claude API integration for production testing and usage.
 * This replaces the mock responses when API keys are available.
 */
/**
 * Real Claude API Client
 *
 * Provides actual integration with Anthropic's Claude API for production use.
 * Falls back to mock mode when API key is not available.
 */
export class ClaudeAPIClient {
    config;
    baseURL;
    constructor(config) {
        this.config = {
            baseURL: 'https://api.anthropic.com',
            model: 'claude-3-sonnet-20240229',
            maxTokens: 4096,
            temperature: 0.7,
            timeout: 30000,
            retries: 3,
            ...config
        };
        this.baseURL = this.config.baseURL;
        if (!this.config.apiKey) {
            console.warn('⚠️  No Claude API key provided. API client will not function.');
        }
    }
    /**
     * Check if API client is properly configured
     */
    isConfigured() {
        return !!this.config.apiKey;
    }
    /**
     * Send a single message to Claude API
     */
    async sendMessage(messages, options = {}) {
        if (!this.isConfigured()) {
            throw new Error('Claude API client not configured. Please provide an API key.');
        }
        const requestConfig = { ...this.config, ...options };
        const requestBody = {
            model: requestConfig.model,
            max_tokens: requestConfig.maxTokens,
            temperature: requestConfig.temperature,
            messages: messages.filter(m => m.role !== 'system'),
            system: messages.find(m => m.role === 'system')?.content
        };
        const response = await this.makeRequest('/v1/messages', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-api-key': this.config.apiKey,
                'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify(requestBody),
            timeout: requestConfig.timeout
        });
        if (!response.ok) {
            const error = await response.json().catch(() => ({ error: 'Unknown error' }));
            throw new Error(`Claude API error: ${response.status} - ${error.error?.message || error.error || 'Unknown error'}`);
        }
        return await response.json();
    }
    /**
     * Send a streaming message to Claude API
     */
    async sendStreamingMessage(messages, options = {}) {
        if (!this.isConfigured()) {
            throw new Error('Claude API client not configured. Please provide an API key.');
        }
        const requestConfig = { ...this.config, ...options };
        const requestBody = {
            model: requestConfig.model,
            max_tokens: requestConfig.maxTokens,
            temperature: requestConfig.temperature,
            messages: messages.filter(m => m.role !== 'system'),
            system: messages.find(m => m.role === 'system')?.content,
            stream: true
        };
        const response = await this.makeRequest('/v1/messages', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-api-key': this.config.apiKey,
                'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify(requestBody),
            timeout: requestConfig.timeout
        });
        if (!response.ok) {
            const error = await response.json().catch(() => ({ error: 'Unknown error' }));
            throw new Error(`Claude API error: ${response.status} - ${error.error?.message || error.error || 'Unknown error'}`);
        }
        return {
            id: `stream-${Date.now()}`,
            chunks: this.parseStreamingResponse(response),
            metadata: {
                model: requestConfig.model,
                startTime: new Date()
            }
        };
    }
    /**
     * Test API connectivity and configuration
     */
    async testConnection() {
        if (!this.isConfigured()) {
            return {
                success: false,
                latency: 0,
                error: 'API key not configured'
            };
        }
        const startTime = Date.now();
        try {
            const testMessage = [
                { role: 'user', content: 'Hello! This is a connection test. Please respond with "Connection successful".' }
            ];
            const response = await this.sendMessage(testMessage, { maxTokens: 50 });
            const latency = Date.now() - startTime;
            return {
                success: true,
                latency,
                model: response.model
            };
        }
        catch (error) {
            return {
                success: false,
                latency: Date.now() - startTime,
                error: error.message
            };
        }
    }
    /**
     * Get API usage statistics (if available)
     */
    async getUsageStats() {
        // Note: Anthropic doesn't provide usage stats via API yet
        // This is a placeholder for future implementation
        return {
            requestsToday: undefined,
            tokensUsed: undefined,
            costEstimate: undefined
        };
    }
    /**
     * Calculate estimated cost for a request
     */
    calculateCost(inputTokens, outputTokens, model) {
        // Pricing as of 2024 (subject to change)
        const pricing = {
            'claude-3-sonnet-20240229': { input: 0.000003, output: 0.000015 },
            'claude-3-haiku-20240307': { input: 0.00000025, output: 0.00000125 },
            'claude-3-opus-20240229': { input: 0.000015, output: 0.000075 }
        };
        const modelPricing = pricing[model] || pricing['claude-3-sonnet-20240229'];
        return (inputTokens * modelPricing.input) + (outputTokens * modelPricing.output);
    }
    // Private methods
    async makeRequest(endpoint, options) {
        const url = `${this.baseURL}${endpoint}`;
        const { timeout, ...fetchOptions } = options;
        // Create abort controller for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout || this.config.timeout);
        try {
            const response = await fetch(url, {
                ...fetchOptions,
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            return response;
        }
        catch (error) {
            clearTimeout(timeoutId);
            if (error instanceof Error && error.name === 'AbortError') {
                throw new Error(`Request timeout after ${timeout || this.config.timeout}ms`);
            }
            throw error;
        }
    }
    async *parseStreamingResponse(response) {
        if (!response.body) {
            throw new Error('No response body for streaming');
        }
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done)
                    break;
                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data === '[DONE]') {
                            return;
                        }
                        try {
                            const chunk = JSON.parse(data);
                            yield chunk;
                        }
                        catch (error) {
                            console.warn('Failed to parse streaming chunk:', data);
                        }
                    }
                }
            }
        }
        finally {
            reader.releaseLock();
        }
    }
}
/**
 * Factory function to create Claude API client
 */
export function createClaudeAPIClient(config) {
    return new ClaudeAPIClient(config);
}
/**
 * Create Claude API client from environment variables
 */
export function createClaudeAPIClientFromEnv() {
    const apiKey = process.env.CLAUDE_API_KEY || process.env.ANTHROPIC_API_KEY;
    if (!apiKey) {
        console.warn('⚠️  No Claude API key found in environment variables (CLAUDE_API_KEY or ANTHROPIC_API_KEY)');
        return null;
    }
    return new ClaudeAPIClient({
        apiKey,
        model: process.env.CLAUDE_MODEL || 'claude-3-sonnet-20240229',
        baseURL: process.env.CLAUDE_BASE_URL,
        maxTokens: process.env.CLAUDE_MAX_TOKENS ? parseInt(process.env.CLAUDE_MAX_TOKENS) : undefined,
        temperature: process.env.CLAUDE_TEMPERATURE ? parseFloat(process.env.CLAUDE_TEMPERATURE) : undefined
    });
}
//# sourceMappingURL=claude-api-client.js.map