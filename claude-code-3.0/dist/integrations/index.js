/**
 * Claude Code 3.0 - Integrations
 *
 * External service integrations and LLM clients.
 */
// LLM Clients
export * from './claude-api-client.js';
export * from './ollama-client.js';
export const DEFAULT_INTEGRATION_CONFIG = {
    enabled: true,
    timeout: 30000,
    retries: 3,
    enableMetrics: true
};
/**
 * Integration Manager
 * Manages external service integrations
 */
export class IntegrationManager {
    config;
    integrations = new Map();
    constructor(config = {}) {
        this.config = { ...DEFAULT_INTEGRATION_CONFIG, ...config };
    }
    /**
     * Register integration
     */
    registerIntegration(name, integration) {
        this.integrations.set(name, integration);
    }
    /**
     * Get integration
     */
    getIntegration(name) {
        return this.integrations.get(name);
    }
    /**
     * List integrations
     */
    listIntegrations() {
        return Array.from(this.integrations.keys());
    }
    /**
     * Test integration connectivity
     */
    async testIntegration(name) {
        const integration = this.integrations.get(name);
        if (!integration)
            return false;
        try {
            if (typeof integration.testConnection === 'function') {
                const result = await integration.testConnection();
                return result.success || false;
            }
            return true;
        }
        catch (error) {
            return false;
        }
    }
}
/**
 * Create integration manager
 */
export function createIntegrationManager(config = {}) {
    return new IntegrationManager(config);
}
//# sourceMappingURL=index.js.map