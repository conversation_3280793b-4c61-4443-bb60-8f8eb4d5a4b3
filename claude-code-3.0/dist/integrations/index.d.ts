/**
 * Claude Code 3.0 - Integrations
 *
 * External service integrations and LLM clients.
 */
export * from './claude-api-client.js';
export * from './ollama-client.js';
export interface IntegrationConfig {
    enabled: boolean;
    timeout: number;
    retries: number;
    enableMetrics: boolean;
}
export declare const DEFAULT_INTEGRATION_CONFIG: IntegrationConfig;
/**
 * Integration Manager
 * Manages external service integrations
 */
export declare class IntegrationManager {
    private config;
    private integrations;
    constructor(config?: Partial<IntegrationConfig>);
    /**
     * Register integration
     */
    registerIntegration(name: string, integration: any): void;
    /**
     * Get integration
     */
    getIntegration(name: string): any;
    /**
     * List integrations
     */
    listIntegrations(): string[];
    /**
     * Test integration connectivity
     */
    testIntegration(name: string): Promise<boolean>;
}
/**
 * Create integration manager
 */
export declare function createIntegrationManager(config?: Partial<IntegrationConfig>): IntegrationManager;
//# sourceMappingURL=index.d.ts.map