{"version": 3, "file": "llm-test-adapter.js", "sourceRoot": "", "sources": ["../../src/testing/llm-test-adapter.ts"], "names": [], "mappings": "AAAA;;;;;;;;GAQG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,eAAe,CAAC;AAkC7C;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,YAAY;IACtC,MAAM,CAAgB;IACtB,UAAU,GAAmB,EAAE,CAAC;IAExC,YAAY,MAAqB;QAC/B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,MAAc,EAAE,UAAe,EAAE;QACxD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,IAAI,MAAqB,CAAC;YAE1B,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBACzB,KAAK,MAAM;oBACT,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;oBAClD,MAAM;gBACR,KAAK,KAAK;oBACR,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;oBACjD,MAAM;gBACR,KAAK,OAAO;oBACV,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;oBACnD,MAAM;gBACR,KAAK,QAAQ;oBACX,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;oBACpD,MAAM;gBACR;oBACE,MAAM,IAAI,KAAK,CAAC,sBAAsB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;YAC9D,CAAC;YAED,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC7C,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAEpC,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,MAAM,GAAkB;gBAC5B,OAAO,EAAE,KAAK;gBACd,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACpC,KAAK,EAAG,KAAe,CAAC,OAAO;aAChC,CAAC;YAEF,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YACjC,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,SAAmE;QACzF,OAAO,CAAC,GAAG,CAAC,mCAAmC,IAAI,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,CAAC;QACzE,OAAO,CAAC,GAAG,CAAC,kBAAkB,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;QAElD,MAAM,OAAO,GAAoB,EAAE,CAAC;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,cAAc,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YAC3C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC9D,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAErB,yCAAyC;YACzC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QACzC,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;QACzE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEhC,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAAC,UAAkB,EAAE,aAAqB,EAAE;QACvE,MAAM,KAAK,GAAiC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC5D,MAAM,WAAW,GAAiC,EAAE,CAAC;QAErD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,UAAU,CAAC,CAAC;YAE1C,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;YAExB,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC9D,IAAI,EAAE,GAAG,IAAI,SAAS,CAAC,GAAG,CAAC,EAAE;gBAC7B,MAAM,EAAE,GAAG,UAAU,eAAe,CAAC,GAAG,CAAC,GAAG;aAC7C,CAAC,CAAC,CAAC;YAEJ,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;gBAC3D,WAAW,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACxD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,oBAAoB,IAAI,QAAQ,EAAE,KAAK,CAAC,CAAC;gBACvD,WAAW,CAAC,IAAI,CAAC,GAAG;oBAClB,QAAQ,EAAE,GAAG,IAAI,aAAa;oBAC9B,IAAI;oBACJ,mBAAmB,EAAE,CAAC;oBACtB,WAAW,EAAE,CAAC;oBACd,aAAa,EAAE,UAAU;oBACzB,cAAc,EAAE,UAAU;oBAC1B,UAAU,EAAE,CAAC;iBACd,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,YAAY,CAAC;QAClC,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;QAC1C,MAAM,IAAI,kCAAkC,CAAC;QAC7C,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;QAEhC,MAAM,IAAI,qBAAqB,CAAC;QAChC,MAAM,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;QAC3C,MAAM,IAAI,aAAa,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,SAAS,IAAI,CAAC;QAC1D,MAAM,IAAI,eAAe,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,KAAK,MAAM,CAAC;QAC5D,MAAM,IAAI,qBAAqB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,MAAM,CAAC;QAE3E,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,iDAAiD,CAAC;YAC5D,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,IAAI,iBAAiB,SAAS,CAAC,QAAQ,IAAI,CAAC;YAClD,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;YAChC,MAAM,IAAI,YAAY,SAAS,CAAC,IAAI,IAAI,CAAC;YACzC,MAAM,IAAI,sBAAsB,SAAS,CAAC,aAAa,IAAI,CAAC;YAC5D,MAAM,IAAI,oBAAoB,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;YACpE,MAAM,IAAI,6BAA6B,SAAS,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;YACtF,MAAM,IAAI,kBAAkB,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC;YAExE,IAAI,SAAS,CAAC,aAAa,EAAE,CAAC;gBAC5B,MAAM,IAAI,sBAAsB,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;YACzE,CAAC;YAED,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;gBACxB,MAAM,IAAI,mBAAmB,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;YAClE,CAAC;YAED,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC;QAED,kBAAkB;QAClB,MAAM,IAAI,uBAAuB,CAAC;QAClC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;QAEhC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAChC,MAAM,IAAI,2DAA2D,CAAC;YACtE,MAAM,IAAI,6BAA6B,CAAC;YACxC,MAAM,IAAI,iDAAiD,CAAC;YAC5D,MAAM,IAAI,oDAAoD,CAAC;YAC/D,MAAM,IAAI,qDAAqD,CAAC;QAClE,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACpE,IAAI,eAAe,CAAC,WAAW,GAAG,EAAE,EAAE,CAAC;YACrC,MAAM,IAAI,qDAAqD,CAAC;QAClE,CAAC;QAED,IAAI,eAAe,CAAC,mBAAmB,GAAG,IAAI,EAAE,CAAC;YAC/C,MAAM,IAAI,mDAAmD,CAAC;QAChE,CAAC;QAED,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;QAEhC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,2CAA2C;IAEnC,KAAK,CAAC,YAAY,CAAC,MAAc,EAAE,OAAY;QACrD,wCAAwC;QACxC,MAAM,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,aAAa;QACtD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QAEzD,iDAAiD;QACjD,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;QAED,MAAM,SAAS,GAAG;YAChB,sBAAsB,MAAM,8EAA8E;YAC1G,2FAA2F,MAAM,IAAI;YACrG,kGAAkG;YAClG,2DAA2D,MAAM,gDAAgD;SAClH,CAAC;QAEF,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;QACzE,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,yBAAyB;QAE7E,OAAO;YACL,OAAO,EAAE,IAAI;YACb,YAAY,EAAE,CAAC,EAAE,wBAAwB;YACzC,UAAU;YACV,IAAI,EAAE,UAAU,GAAG,QAAQ,EAAE,wBAAwB;YACrD,QAAQ;YACR,QAAQ,EAAE;gBACR,IAAI,EAAE,MAAM;gBACZ,cAAc,EAAE,KAAK;aACtB;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,MAAc,EAAE,OAAY;QACpD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,6CAA6C;QAC7C,4CAA4C;QAC5C,MAAM,IAAI,KAAK,CAAC,2EAA2E,CAAC,CAAC;IAC/F,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,OAAY;QACtD,sCAAsC;QACtC,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;IACjF,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,OAAY;QACvD,kCAAkC;QAClC,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC3D,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,SAAgB,EAAE,OAAwB,EAAE,SAAiB;QACtF,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACzD,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAEtD,MAAM,mBAAmB,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC;YACtD,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,GAAG,iBAAiB,CAAC,MAAM;YAC1F,CAAC,CAAC,CAAC,CAAC;QAEN,MAAM,aAAa,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC;YAChD,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,iBAAiB,CAAC,MAAM;YAC/F,CAAC,CAAC,SAAS,CAAC;QAEd,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAErE,OAAO;YACL,QAAQ,EAAE,aAAa,IAAI,CAAC,GAAG,EAAE,EAAE;YACnC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YACtB,mBAAmB;YACnB,WAAW,EAAE,CAAC,iBAAiB,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,GAAG;YAC9D,aAAa,EAAE,OAAO,CAAC,MAAM;YAC7B,cAAc,EAAE,aAAa,CAAC,MAAM;YACpC,aAAa;YACb,SAAS,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;YAChD,UAAU,EAAE,CAAC,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC,sBAAsB;SACvE,CAAC;IACJ,CAAC;CACF"}