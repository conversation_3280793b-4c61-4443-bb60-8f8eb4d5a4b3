/**
 * Claude Code 3.0 - Testing Framework
 *
 * Comprehensive testing utilities and frameworks.
 */
export * from './test-reporter.js';
export * from './llm-test-adapter.js';
export interface TestConfig {
    mode: 'unit' | 'integration' | 'performance' | 'e2e';
    timeout: number;
    retries: number;
    enableReporting: boolean;
    reportFormat: 'console' | 'json' | 'html';
}
export declare const DEFAULT_TEST_CONFIG: TestConfig;
export interface TestSuite {
    name: string;
    tests: TestCase[];
    setup?: () => Promise<void>;
    teardown?: () => Promise<void>;
}
export interface TestCase {
    name: string;
    test: () => Promise<void>;
    timeout?: number;
    skip?: boolean;
}
/**
 * Test Runner
 * Executes test suites and generates reports
 */
export declare class TestRunner {
    private config;
    private suites;
    constructor(config?: Partial<TestConfig>);
    /**
     * Add test suite
     */
    addSuite(suite: TestSuite): void;
    /**
     * Run all test suites
     */
    runAll(): Promise<void>;
    /**
     * Run specific test suite
     */
    runSuite(suite: TestSuite): Promise<void>;
    private runTest;
}
/**
 * Create test runner
 */
export declare function createTestRunner(config?: Partial<TestConfig>): TestRunner;
//# sourceMappingURL=index.d.ts.map