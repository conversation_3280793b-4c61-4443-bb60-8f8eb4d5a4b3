/**
 * Claude Code 3.0 - Comprehensive Test Reporter
 *
 * Generates detailed test reports with benchmarks, metrics, and comparisons.
 */
export interface TestResult {
    name: string;
    status: 'passed' | 'failed' | 'skipped';
    duration: number;
    error?: string;
    metrics?: Record<string, number>;
    metadata?: Record<string, any>;
}
export interface BenchmarkResult {
    name: string;
    operations: number;
    duration: number;
    opsPerSecond: number;
    averageLatency: number;
    minLatency: number;
    maxLatency: number;
    p50: number;
    p95: number;
    p99: number;
    memoryUsage: number;
    cpuUsage?: number;
}
export interface TestSuite {
    name: string;
    startTime: Date;
    endTime?: Date;
    duration?: number;
    tests: TestResult[];
    benchmarks: BenchmarkResult[];
    summary: {
        total: number;
        passed: number;
        failed: number;
        skipped: number;
        successRate: number;
    };
    environment: {
        nodeVersion: string;
        platform: string;
        arch: string;
        memory: number;
        cpuCores: number;
    };
}
export declare class TestReporter {
    private suites;
    private currentSuite?;
    /**
     * Start a new test suite
     */
    startSuite(name: string): void;
    /**
     * End the current test suite
     */
    endSuite(): void;
    /**
     * Add a test result
     */
    addTest(result: TestResult): void;
    /**
     * Add a benchmark result
     */
    addBenchmark(result: BenchmarkResult): void;
    /**
     * Generate HTML report
     */
    generateHTMLReport(outputDir?: string): Promise<string>;
    /**
     * Generate JSON report
     */
    generateJSONReport(outputDir?: string): Promise<string>;
    /**
     * Generate console report
     */
    generateConsoleReport(): void;
    private printSuiteReport;
    private printOverallSummary;
    private generateHTML;
    private generateSuiteHTML;
}
//# sourceMappingURL=test-reporter.d.ts.map