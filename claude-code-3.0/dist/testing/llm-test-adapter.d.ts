/**
 * Claude Code 3.0 - LLM Test Adapter
 *
 * Provides multiple testing strategies for LLM integration:
 * 1. Mock/Simulation mode (current)
 * 2. API key testing mode (when available)
 * 3. Local LLM integration (future)
 * 4. Benchmark comparison mode
 */
import { EventEmitter } from 'eventemitter3';
export interface LLMTestConfig {
    mode: 'mock' | 'api' | 'local' | 'hybrid';
    apiKey?: string;
    baseURL?: string;
    model?: string;
    timeout?: number;
    retries?: number;
    enableBenchmarking?: boolean;
}
export interface LLMTestResult {
    success: boolean;
    responseTime: number;
    tokenCount?: number;
    cost?: number;
    error?: string;
    response?: string;
    metadata?: Record<string, any>;
}
export interface LLMBenchmark {
    testName: string;
    mode: string;
    averageResponseTime: number;
    successRate: number;
    totalRequests: number;
    failedRequests: number;
    averageTokens?: number;
    totalCost?: number;
    throughput: number;
}
/**
 * LLM Test Adapter - Handles different testing modes
 */
export declare class LLMTestAdapter extends EventEmitter {
    private config;
    private benchmarks;
    constructor(config: LLMTestConfig);
    /**
     * Test LLM integration with different modes
     */
    testLLMIntegration(prompt: string, options?: any): Promise<LLMTestResult>;
    /**
     * Run comprehensive benchmark suite
     */
    runBenchmarkSuite(testCases: Array<{
        name: string;
        prompt: string;
        expected?: string;
    }>): Promise<LLMBenchmark[]>;
    /**
     * Compare different modes performance
     */
    compareModesPerformance(testPrompt: string, iterations?: number): Promise<Record<string, LLMBenchmark>>;
    /**
     * Generate detailed test report
     */
    generateTestReport(): string;
    private testWithMock;
    private testWithAPI;
    private testWithLocal;
    private testWithHybrid;
    private calculateBenchmark;
}
//# sourceMappingURL=llm-test-adapter.d.ts.map