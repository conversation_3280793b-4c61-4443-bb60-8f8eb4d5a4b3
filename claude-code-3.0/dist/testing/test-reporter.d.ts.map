{"version": 3, "file": "test-reporter.d.ts", "sourceRoot": "", "sources": ["../../src/testing/test-reporter.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAKH,MAAM,WAAW,UAAU;IACzB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC;IACxC,QAAQ,EAAE,MAAM,CAAC;IACjB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAChC;AAED,MAAM,WAAW,eAAe;IAC9B,IAAI,EAAE,MAAM,CAAC;IACb,UAAU,EAAE,MAAM,CAAC;IACnB,QAAQ,EAAE,MAAM,CAAC;IACjB,YAAY,EAAE,MAAM,CAAC;IACrB,cAAc,EAAE,MAAM,CAAC;IACvB,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,EAAE,MAAM,CAAC;IACnB,GAAG,EAAE,MAAM,CAAC;IACZ,GAAG,EAAE,MAAM,CAAC;IACZ,GAAG,EAAE,MAAM,CAAC;IACZ,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,SAAS;IACxB,IAAI,EAAE,MAAM,CAAC;IACb,SAAS,EAAE,IAAI,CAAC;IAChB,OAAO,CAAC,EAAE,IAAI,CAAC;IACf,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,KAAK,EAAE,UAAU,EAAE,CAAC;IACpB,UAAU,EAAE,eAAe,EAAE,CAAC;IAC9B,OAAO,EAAE;QACP,KAAK,EAAE,MAAM,CAAC;QACd,MAAM,EAAE,MAAM,CAAC;QACf,MAAM,EAAE,MAAM,CAAC;QACf,OAAO,EAAE,MAAM,CAAC;QAChB,WAAW,EAAE,MAAM,CAAC;KACrB,CAAC;IACF,WAAW,EAAE;QACX,WAAW,EAAE,MAAM,CAAC;QACpB,QAAQ,EAAE,MAAM,CAAC;QACjB,IAAI,EAAE,MAAM,CAAC;QACb,MAAM,EAAE,MAAM,CAAC;QACf,QAAQ,EAAE,MAAM,CAAC;KAClB,CAAC;CACH;AAED,qBAAa,YAAY;IACvB,OAAO,CAAC,MAAM,CAAqC;IACnD,OAAO,CAAC,YAAY,CAAC,CAAY;IAEjC;;OAEG;IACH,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAyB9B;;OAEG;IACH,QAAQ,IAAI,IAAI;IAiBhB;;OAEG;IACH,OAAO,CAAC,MAAM,EAAE,UAAU,GAAG,IAAI;IAQjC;;OAEG;IACH,YAAY,CAAC,MAAM,EAAE,eAAe,GAAG,IAAI;IAQ3C;;OAEG;IACG,kBAAkB,CAAC,SAAS,GAAE,MAAoB,GAAG,OAAO,CAAC,MAAM,CAAC;IAU1E;;OAEG;IACG,kBAAkB,CAAC,SAAS,GAAE,MAAoB,GAAG,OAAO,CAAC,MAAM,CAAC;IAa1E;;OAEG;IACH,qBAAqB,IAAI,IAAI;IAY7B,OAAO,CAAC,gBAAgB;IAuCxB,OAAO,CAAC,mBAAmB;IA2B3B,OAAO,CAAC,YAAY;IAgDpB,OAAO,CAAC,iBAAiB;CA+D1B"}