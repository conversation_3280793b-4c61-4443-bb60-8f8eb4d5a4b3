/**
 * Claude Code 3.0 - Comprehensive Test Reporter
 *
 * Generates detailed test reports with benchmarks, metrics, and comparisons.
 */
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
export class TestReporter {
    suites = new Map();
    currentSuite;
    /**
     * Start a new test suite
     */
    startSuite(name) {
        this.currentSuite = {
            name,
            startTime: new Date(),
            tests: [],
            benchmarks: [],
            summary: {
                total: 0,
                passed: 0,
                failed: 0,
                skipped: 0,
                successRate: 0
            },
            environment: {
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch,
                memory: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
                cpuCores: require('os').cpus().length
            }
        };
        this.suites.set(name, this.currentSuite);
    }
    /**
     * End the current test suite
     */
    endSuite() {
        if (!this.currentSuite)
            return;
        this.currentSuite.endTime = new Date();
        this.currentSuite.duration = this.currentSuite.endTime.getTime() - this.currentSuite.startTime.getTime();
        // Calculate summary
        const tests = this.currentSuite.tests;
        this.currentSuite.summary = {
            total: tests.length,
            passed: tests.filter(t => t.status === 'passed').length,
            failed: tests.filter(t => t.status === 'failed').length,
            skipped: tests.filter(t => t.status === 'skipped').length,
            successRate: tests.length > 0 ? (tests.filter(t => t.status === 'passed').length / tests.length) * 100 : 0
        };
    }
    /**
     * Add a test result
     */
    addTest(result) {
        if (!this.currentSuite) {
            throw new Error('No active test suite. Call startSuite() first.');
        }
        this.currentSuite.tests.push(result);
    }
    /**
     * Add a benchmark result
     */
    addBenchmark(result) {
        if (!this.currentSuite) {
            throw new Error('No active test suite. Call startSuite() first.');
        }
        this.currentSuite.benchmarks.push(result);
    }
    /**
     * Generate HTML report
     */
    async generateHTMLReport(outputDir = './reports') {
        await mkdir(outputDir, { recursive: true });
        const reportPath = join(outputDir, `test-report-${Date.now()}.html`);
        const html = this.generateHTML();
        await writeFile(reportPath, html, 'utf-8');
        return reportPath;
    }
    /**
     * Generate JSON report
     */
    async generateJSONReport(outputDir = './reports') {
        await mkdir(outputDir, { recursive: true });
        const reportPath = join(outputDir, `test-report-${Date.now()}.json`);
        const data = {
            timestamp: new Date().toISOString(),
            suites: Array.from(this.suites.values())
        };
        await writeFile(reportPath, JSON.stringify(data, null, 2), 'utf-8');
        return reportPath;
    }
    /**
     * Generate console report
     */
    generateConsoleReport() {
        console.log('\n' + '='.repeat(80));
        console.log('🧪 CLAUDE CODE 3.0 - COMPREHENSIVE TEST REPORT');
        console.log('='.repeat(80));
        for (const suite of this.suites.values()) {
            this.printSuiteReport(suite);
        }
        this.printOverallSummary();
    }
    printSuiteReport(suite) {
        console.log(`\n📋 Test Suite: ${suite.name}`);
        console.log('-'.repeat(50));
        // Environment info
        console.log(`🖥️  Environment: Node ${suite.environment.nodeVersion} on ${suite.environment.platform}-${suite.environment.arch}`);
        console.log(`💾 Memory: ${suite.environment.memory}MB, CPU Cores: ${suite.environment.cpuCores}`);
        console.log(`⏱️  Duration: ${suite.duration ? (suite.duration / 1000).toFixed(2) : 'N/A'}s`);
        // Test results
        console.log(`\n📊 Test Results:`);
        console.log(`   Total: ${suite.summary.total}`);
        console.log(`   ✅ Passed: ${suite.summary.passed}`);
        console.log(`   ❌ Failed: ${suite.summary.failed}`);
        console.log(`   ⏭️  Skipped: ${suite.summary.skipped}`);
        console.log(`   📈 Success Rate: ${suite.summary.successRate.toFixed(1)}%`);
        // Failed tests details
        const failedTests = suite.tests.filter(t => t.status === 'failed');
        if (failedTests.length > 0) {
            console.log(`\n❌ Failed Tests:`);
            failedTests.forEach(test => {
                console.log(`   • ${test.name}: ${test.error || 'Unknown error'}`);
            });
        }
        // Benchmark results
        if (suite.benchmarks.length > 0) {
            console.log(`\n🏃 Benchmark Results:`);
            suite.benchmarks.forEach(bench => {
                console.log(`   📊 ${bench.name}:`);
                console.log(`      Operations: ${bench.operations.toLocaleString()}`);
                console.log(`      Throughput: ${bench.opsPerSecond.toLocaleString()} ops/sec`);
                console.log(`      Latency: avg=${bench.averageLatency.toFixed(2)}ms, p95=${bench.p95.toFixed(2)}ms, p99=${bench.p99.toFixed(2)}ms`);
                console.log(`      Memory: ${bench.memoryUsage.toFixed(1)}MB`);
            });
        }
    }
    printOverallSummary() {
        const allSuites = Array.from(this.suites.values());
        const totalTests = allSuites.reduce((sum, suite) => sum + suite.summary.total, 0);
        const totalPassed = allSuites.reduce((sum, suite) => sum + suite.summary.passed, 0);
        const totalFailed = allSuites.reduce((sum, suite) => sum + suite.summary.failed, 0);
        const overallSuccessRate = totalTests > 0 ? (totalPassed / totalTests) * 100 : 0;
        console.log('\n' + '='.repeat(80));
        console.log('🎯 OVERALL SUMMARY');
        console.log('='.repeat(80));
        console.log(`📊 Total Test Suites: ${allSuites.length}`);
        console.log(`📊 Total Tests: ${totalTests}`);
        console.log(`✅ Total Passed: ${totalPassed}`);
        console.log(`❌ Total Failed: ${totalFailed}`);
        console.log(`📈 Overall Success Rate: ${overallSuccessRate.toFixed(1)}%`);
        if (overallSuccessRate >= 95) {
            console.log('🎉 EXCELLENT! All systems performing well.');
        }
        else if (overallSuccessRate >= 80) {
            console.log('✅ GOOD! Most systems working correctly.');
        }
        else {
            console.log('⚠️  WARNING! Some systems need attention.');
        }
        console.log('='.repeat(80));
    }
    generateHTML() {
        const suites = Array.from(this.suites.values());
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claude Code 3.0 - Test Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
        .content { padding: 30px; }
        .suite { margin-bottom: 40px; border: 1px solid #e1e5e9; border-radius: 6px; overflow: hidden; }
        .suite-header { background: #f8f9fa; padding: 20px; border-bottom: 1px solid #e1e5e9; }
        .suite-content { padding: 20px; }
        .metric { display: inline-block; margin: 10px 20px 10px 0; }
        .metric-value { font-size: 24px; font-weight: bold; color: #28a745; }
        .metric-label { font-size: 12px; color: #6c757d; text-transform: uppercase; }
        .benchmark { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .skipped { color: #ffc107; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #e1e5e9; }
        th { background: #f8f9fa; font-weight: 600; }
        .status-badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .status-passed { background: #d4edda; color: #155724; }
        .status-failed { background: #f8d7da; color: #721c24; }
        .status-skipped { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Claude Code 3.0 Test Report</h1>
            <p>Generated on ${new Date().toLocaleString()}</p>
        </div>
        <div class="content">
            ${suites.map(suite => this.generateSuiteHTML(suite)).join('')}
        </div>
    </div>
</body>
</html>`;
    }
    generateSuiteHTML(suite) {
        return `
        <div class="suite">
            <div class="suite-header">
                <h2>${suite.name}</h2>
                <div class="metrics">
                    <div class="metric">
                        <div class="metric-value">${suite.summary.total}</div>
                        <div class="metric-label">Total Tests</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value passed">${suite.summary.passed}</div>
                        <div class="metric-label">Passed</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value failed">${suite.summary.failed}</div>
                        <div class="metric-label">Failed</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">${suite.summary.successRate.toFixed(1)}%</div>
                        <div class="metric-label">Success Rate</div>
                    </div>
                </div>
            </div>
            <div class="suite-content">
                ${suite.tests.length > 0 ? `
                <h3>Test Results</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Test Name</th>
                            <th>Status</th>
                            <th>Duration</th>
                            <th>Details</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${suite.tests.map(test => `
                        <tr>
                            <td>${test.name}</td>
                            <td><span class="status-badge status-${test.status}">${test.status.toUpperCase()}</span></td>
                            <td>${test.duration.toFixed(2)}ms</td>
                            <td>${test.error || '-'}</td>
                        </tr>
                        `).join('')}
                    </tbody>
                </table>
                ` : ''}
                
                ${suite.benchmarks.length > 0 ? `
                <h3>Benchmark Results</h3>
                ${suite.benchmarks.map(bench => `
                <div class="benchmark">
                    <h4>${bench.name}</h4>
                    <p><strong>Throughput:</strong> ${bench.opsPerSecond.toLocaleString()} ops/sec</p>
                    <p><strong>Latency:</strong> avg=${bench.averageLatency.toFixed(2)}ms, p95=${bench.p95.toFixed(2)}ms, p99=${bench.p99.toFixed(2)}ms</p>
                    <p><strong>Memory Usage:</strong> ${bench.memoryUsage.toFixed(1)}MB</p>
                </div>
                `).join('')}
                ` : ''}
            </div>
        </div>`;
    }
}
//# sourceMappingURL=test-reporter.js.map