/**
 * Claude Code 3.0 - Testing Framework
 *
 * Comprehensive testing utilities and frameworks.
 */
// Test utilities
export * from './test-reporter.js';
export * from './llm-test-adapter.js';
export const DEFAULT_TEST_CONFIG = {
    mode: 'unit',
    timeout: 30000,
    retries: 0,
    enableReporting: true,
    reportFormat: 'console'
};
/**
 * Test Runner
 * Executes test suites and generates reports
 */
export class TestRunner {
    config;
    suites = [];
    constructor(config = {}) {
        this.config = { ...DEFAULT_TEST_CONFIG, ...config };
    }
    /**
     * Add test suite
     */
    addSuite(suite) {
        this.suites.push(suite);
    }
    /**
     * Run all test suites
     */
    async runAll() {
        console.log(`Running ${this.suites.length} test suites...`);
        for (const suite of this.suites) {
            await this.runSuite(suite);
        }
    }
    /**
     * Run specific test suite
     */
    async runSuite(suite) {
        console.log(`\nRunning suite: ${suite.name}`);
        // Setup
        if (suite.setup) {
            await suite.setup();
        }
        try {
            // Run tests
            for (const testCase of suite.tests) {
                if (testCase.skip) {
                    console.log(`  ⏭️  ${testCase.name} (skipped)`);
                    continue;
                }
                try {
                    await this.runTest(testCase);
                    console.log(`  ✅ ${testCase.name}`);
                }
                catch (error) {
                    console.log(`  ❌ ${testCase.name}: ${error.message}`);
                }
            }
        }
        finally {
            // Teardown
            if (suite.teardown) {
                await suite.teardown();
            }
        }
    }
    async runTest(testCase) {
        const timeout = testCase.timeout || this.config.timeout;
        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                reject(new Error(`Test timeout after ${timeout}ms`));
            }, timeout);
            testCase.test()
                .then(() => {
                clearTimeout(timer);
                resolve();
            })
                .catch((error) => {
                clearTimeout(timer);
                reject(error);
            });
        });
    }
}
/**
 * Create test runner
 */
export function createTestRunner(config = {}) {
    return new TestRunner(config);
}
//# sourceMappingURL=index.js.map