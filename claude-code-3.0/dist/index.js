/**
 * Claude Code 3.0 - Main Entry Point
 *
 * AI-driven code generation and management platform based on "Documentation as Software" philosophy.
 * Implements a 7-layer event-driven architecture with h2A dual-buffer async message queue system.
 *
 * Architecture Layers:
 * 1. CLI Layer - Command line interface
 * 2. UI Layer - React-based user interface
 * 3. Steering Layer - h2A real-time message steering
 * 4. Event Layer - Event-driven processing
 * 5. Message Layer - Message handling and routing
 * 6. Agent Layer - Core AI agent processing
 * 7. Tool Layer - Tool execution and management
 * 8. API Layer - External API integrations
 */
// ============================================================================
// Core System Exports
// ============================================================================
export { ClaudeCodeSystem } from './core/system.js';
// ============================================================================
// Layer Exports
// ============================================================================
// CLI Layer
export { CLIApplication } from './cli/index.js';
// UI Layer
export { UIApplication } from './ui/index.js';
// Steering Layer (h2A Message Queue)
export { h2AMessageQueue, SteeringManager, createSteeringEnvironment } from './layers/steering/index.js';
// Event Layer
export { EventSystem, EventBus, EventHandler } from './layers/event/index.js';
// Message Layer
export { MessageProcessor, MessageRouter, MessageQueue } from './layers/message/index.js';
// Agent Layer
export { AgentCore, AgentManager } from './layers/agent/index.js';
// Tool Layer
export { ToolManager, ToolExecutor, ToolRegistry } from './layers/tool/index.js';
// API Layer
export { APIManager, ClaudeAPIClient, ExternalAPIAdapter } from './layers/api/index.js';
// ============================================================================
// Core Components
// ============================================================================
export { FileStateManager, ConfigManager, LoggingManager, ErrorRecoveryManager } from './core/index.js';
// ============================================================================
// Tool Implementations
// ============================================================================
export { ReadTool, EditTool, TaskTool, SearchTool, ExecuteTool } from './tools/implementations/index.js';
// ============================================================================
// Type Definitions
// ============================================================================
export * from './types/index.js';
// ============================================================================
// Utilities
// ============================================================================
export * from './utils/index.js';
// ============================================================================
// Quick Start Functions
// ============================================================================
/**
 * Create a standard Claude Code 3.0 environment with default configuration
 */
export async function createStandardEnvironment(config) {
    const system = new ClaudeCodeSystem(config);
    await system.initialize();
    return system;
}
/**
 * Create a development environment with enhanced debugging and monitoring
 */
export async function createDevelopmentEnvironment(config) {
    const devConfig = {
        ...config,
        enableDebugMode: true,
        enableMetrics: true,
        logLevel: 'debug',
        enableHotReload: true
    };
    const system = new ClaudeCodeSystem(devConfig);
    await system.initialize();
    return system;
}
/**
 * Create a production environment with optimized performance settings
 */
export async function createProductionEnvironment(config) {
    const prodConfig = {
        ...config,
        enableDebugMode: false,
        enableMetrics: true,
        logLevel: 'info',
        enableHotReload: false,
        optimizeForProduction: true
    };
    const system = new ClaudeCodeSystem(prodConfig);
    await system.initialize();
    return system;
}
//# sourceMappingURL=index.js.map