/**
 * Claude Code 3.0 - API Server
 *
 * Express.js server to support the React UI with real-time data
 */
import express from 'express';
import cors from 'cors';
import { WebSocketServer } from 'ws';
import { createServer } from 'http';
const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server });
// Middleware
app.use(cors());
app.use(express.json());
// Simple mock implementations for the UI demo
class MockClaudeCodeSystem {
    async start() {
        console.log('✅ Mock Claude Code System started');
    }
    async stop() {
        console.log('🛑 Mock Claude Code System stopped');
    }
}
class MockMultiAgentManager {
    async start() {
        console.log('✅ Mock Multi-Agent Manager started');
    }
    async stop() {
        console.log('🛑 Mock Multi-Agent Manager stopped');
    }
}
// Initialize systems
const system = new MockClaudeCodeSystem();
const agentManager = new MockMultiAgentManager();
// Mock data for demonstration
let systemMetrics = {
    totalAgents: 5,
    activeAgents: 3,
    messagesPerSecond: 4322773,
    averageLatency: 0.001,
    successRate: 100,
    uptime: '99.9%',
    queueSize: 1247,
    bufferSwitches: 156,
    totalRequests: 2595
};
let agents = [
    {
        id: 'agent-1',
        name: 'General Agent',
        status: 'active',
        capabilities: ['general', 'text-processing'],
        currentLoad: 2,
        totalRequests: 1247,
        lastActivity: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
        model: 'qwen2.5:3b'
    },
    {
        id: 'agent-2',
        name: 'Code Agent',
        status: 'active',
        capabilities: ['code-generation', 'typescript'],
        currentLoad: 1,
        totalRequests: 892,
        lastActivity: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        model: 'codellama:7b'
    },
    {
        id: 'agent-3',
        name: 'Specialized Agent',
        status: 'idle',
        capabilities: ['specialized', 'analysis'],
        currentLoad: 0,
        totalRequests: 456,
        lastActivity: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        model: 'qwen2.5:3b'
    }
];
// API Routes
// System status
app.get('/api/status', (req, res) => {
    res.json({
        status: 'online',
        version: '3.0.0',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: new Date().toISOString()
    });
});
// System metrics
app.get('/api/metrics', (req, res) => {
    res.json(systemMetrics);
});
// Agents
app.get('/api/agents', (req, res) => {
    res.json(agents);
});
app.post('/api/agents', (req, res) => {
    const { name, capabilities, model } = req.body;
    const newAgent = {
        id: `agent-${Date.now()}`,
        name: name || 'New Agent',
        status: 'idle',
        capabilities: capabilities || ['general'],
        currentLoad: 0,
        totalRequests: 0,
        lastActivity: new Date().toISOString(),
        model: model || 'qwen2.5:3b'
    };
    agents.push(newAgent);
    systemMetrics.totalAgents++;
    // Broadcast update to WebSocket clients
    broadcastUpdate('agent_created', newAgent);
    res.status(201).json(newAgent);
});
app.delete('/api/agents/:id', (req, res) => {
    const { id } = req.params;
    const agentIndex = agents.findIndex(agent => agent.id === id);
    if (agentIndex === -1) {
        return res.status(404).json({ error: 'Agent not found' });
    }
    const deletedAgent = agents.splice(agentIndex, 1)[0];
    systemMetrics.totalAgents--;
    if (deletedAgent.status === 'active') {
        systemMetrics.activeAgents--;
    }
    // Broadcast update to WebSocket clients
    broadcastUpdate('agent_deleted', { id });
    res.json({ message: 'Agent deleted successfully' });
});
// LLM Integration with Streaming Support
async function callLocalLLMStreaming(prompt, agentCapabilities = ['general'], onChunk, questionType = 'general') {
    try {
        // Enhance prompt based on agent capabilities and question type
        let enhancedPrompt = prompt;
        if (agentCapabilities.includes('code-generation') && questionType === 'coding') {
            enhancedPrompt = `You are a Code Agent specialist in the Claude Code 3.0 multi-agent system. The user is asking for programming help: "${prompt}".

Please provide:
1. Practical, working code examples with clear explanations
2. Best practices and optimization tips
3. Format all code blocks with proper markdown syntax using \`\`\`language
4. Include comments in the code for clarity
5. Explain the logic and approach used

Focus on delivering functional code solutions.`;
        }
        else if (agentCapabilities.includes('research') && questionType === 'research') {
            enhancedPrompt = `You are a Research Agent specialist in the Claude Code 3.0 multi-agent system. The user is asking for academic/research information: "${prompt}".

Please provide:
1. Comprehensive, accurate explanations based on current knowledge
2. Scientific context and background information
3. Clear definitions of key terms and concepts
4. Structured response with logical flow
5. NO CODE EXAMPLES unless explicitly requested

Focus on delivering detailed conceptual understanding and educational content.`;
        }
        else if ((agentCapabilities.includes('general') || agentCapabilities.includes('text-processing')) && questionType === 'conceptual') {
            enhancedPrompt = `You are a General Agent specialist in the Claude Code 3.0 multi-agent system. The user is asking for conceptual information: "${prompt}".

Please provide:
1. Clear, detailed explanations of concepts and principles
2. Real-world examples and applications
3. Step-by-step breakdowns when appropriate
4. Well-structured response with headings and bullet points
5. NO CODE EXAMPLES unless the user specifically asks for programming examples

Focus on delivering comprehensive conceptual understanding without programming content.`;
        }
        else if (questionType === 'mixed') {
            enhancedPrompt = `You are a General Agent in the Claude Code 3.0 multi-agent system handling a mixed request: "${prompt}".

Please provide:
1. First, explain the conceptual aspects clearly
2. Then, if appropriate, provide relevant code examples
3. Clearly separate explanation from implementation
4. Use proper formatting for both text and code sections
5. Balance theoretical understanding with practical examples`;
        }
        else if (agentCapabilities.includes('specialized')) {
            enhancedPrompt = `You are a Specialized Agent in the Claude Code 3.0 multi-agent system. ${prompt}. Please provide comprehensive and technical responses with proper formatting, focusing on advanced concepts and detailed analysis.`;
        }
        else {
            enhancedPrompt = `You are a helpful General Agent from the Claude Code 3.0 multi-agent system. ${prompt}. Please provide well-structured, informative responses with clear formatting.`;
        }
        const response = await fetch('http://localhost:11434/api/generate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                model: 'qwen2.5:3b',
                prompt: enhancedPrompt,
                stream: true,
                options: {
                    temperature: 0.7,
                    top_p: 0.9,
                    max_tokens: 1500
                }
            })
        });
        if (!response.ok) {
            throw new Error(`Ollama API error: ${response.status}`);
        }
        let fullResponse = '';
        const reader = response.body?.getReader();
        const decoder = new TextDecoder();
        if (reader) {
            while (true) {
                const { done, value } = await reader.read();
                if (done)
                    break;
                const chunk = decoder.decode(value);
                const lines = chunk.split('\n').filter(line => line.trim());
                for (const line of lines) {
                    try {
                        const data = JSON.parse(line);
                        if (data.response) {
                            fullResponse += data.response;
                            onChunk(data.response);
                        }
                    }
                    catch (e) {
                        // Skip invalid JSON lines
                    }
                }
            }
        }
        return fullResponse || 'I apologize, but I was unable to generate a response.';
    }
    catch (error) {
        console.error('LLM call failed:', error);
        return `I'm experiencing technical difficulties connecting to the local AI model. Error: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
}
// Streaming message processing endpoint
app.post('/api/messages/stream', async (req, res) => {
    const { content, sessionId, capabilities = ['general'] } = req.body;
    // Set up Server-Sent Events
    res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
    });
    try {
        const startTime = Date.now();
        const messageId = `msg_${Date.now()}`;
        // Select an active agent based on capabilities
        const activeAgents = agents.filter(a => a.status === 'active');
        let selectedAgent = activeAgents[0];
        // Enhanced intelligent agent selection with proper categorization
        const lowerContent = content.toLowerCase();
        // Define coding-related keywords
        const codingKeywords = [
            'code', 'function', 'typescript', 'javascript', 'python', 'java', 'c++', 'programming',
            'algorithm', 'debug', 'syntax', 'compile', 'variable', 'loop', 'array', 'object',
            'class', 'method', 'api', 'database', 'sql', 'html', 'css', 'react', 'node',
            'write a function', 'create a script', 'implement', 'coding', 'software', 'development'
        ];
        // Define conceptual/academic keywords
        const conceptualKeywords = [
            'what is', 'explain', 'define', 'describe', 'how does', 'why does', 'what are',
            'biology', 'chemistry', 'physics', 'science', 'epigenetics', 'photosynthesis',
            'evolution', 'genetics', 'molecular', 'cellular', 'organism', 'ecosystem',
            'theory', 'concept', 'principle', 'mechanism', 'process', 'phenomenon'
        ];
        // Define research/analysis keywords
        const researchKeywords = [
            'research', 'study', 'analysis', 'investigate', 'examine', 'compare',
            'academic', 'scholarly', 'literature', 'paper', 'journal', 'methodology',
            'hypothesis', 'experiment', 'data', 'statistics', 'findings', 'conclusion'
        ];
        // Check for explicit code request
        const isCodeRequest = codingKeywords.some(keyword => lowerContent.includes(keyword)) ||
            /write.*code|create.*function|implement.*algorithm|show.*example.*code/i.test(content);
        // Check for conceptual question
        const isConceptualQuestion = conceptualKeywords.some(keyword => lowerContent.includes(keyword)) ||
            /^(what|how|why|explain|define|describe)/i.test(content.trim());
        // Check for research request
        const isResearchRequest = researchKeywords.some(keyword => lowerContent.includes(keyword));
        // Agent selection logic
        if (isCodeRequest && !isConceptualQuestion) {
            // Pure coding request - route to Code Agent
            selectedAgent = activeAgents.find(a => a.capabilities.includes('code-generation')) ||
                activeAgents.find(a => a.name.toLowerCase().includes('code')) ||
                activeAgents[1]; // Prefer second agent (Code Agent)
        }
        else if (isConceptualQuestion && !isCodeRequest) {
            // Pure conceptual question - route to General or Research Agent
            if (isResearchRequest) {
                selectedAgent = activeAgents.find(a => a.capabilities.includes('research')) ||
                    activeAgents.find(a => a.name.toLowerCase().includes('research')) ||
                    activeAgents[2]; // Prefer third agent (Research Agent)
            }
            else {
                selectedAgent = activeAgents.find(a => a.capabilities.includes('general')) ||
                    activeAgents.find(a => a.name.toLowerCase().includes('general')) ||
                    activeAgents[0]; // Prefer first agent (General Agent)
            }
        }
        else if (isCodeRequest && isConceptualQuestion) {
            // Mixed request - route to General Agent for balanced response
            selectedAgent = activeAgents.find(a => a.capabilities.includes('general')) ||
                activeAgents[0];
        }
        else {
            // Default routing based on content analysis
            if (lowerContent.includes('advanced') || lowerContent.includes('complex') || lowerContent.includes('technical')) {
                selectedAgent = activeAgents.find(a => a.capabilities.includes('specialized')) || activeAgents[2];
            }
            else {
                selectedAgent = activeAgents[systemMetrics.totalRequests % activeAgents.length] || activeAgents[0];
            }
        }
        // Send initial agent selection event
        res.write(`data: ${JSON.stringify({
            type: 'agent_selected',
            agentId: selectedAgent?.id,
            agentName: selectedAgent?.name,
            messageId
        })}\n\n`);
        // Broadcast agent activity
        broadcastUpdate('agent_activity', {
            agentId: selectedAgent?.id,
            status: 'processing',
            content: content.substring(0, 50) + '...'
        });
        let fullContent = '';
        // Determine question type for proper LLM prompting
        let questionType = 'general';
        if (isCodeRequest && !isConceptualQuestion) {
            questionType = 'coding';
        }
        else if (isConceptualQuestion && !isCodeRequest) {
            questionType = isResearchRequest ? 'research' : 'conceptual';
        }
        else if (isCodeRequest && isConceptualQuestion) {
            questionType = 'mixed';
        }
        // Stream LLM response with proper question type
        await callLocalLLMStreaming(content, selectedAgent?.capabilities || ['general'], (chunk) => {
            fullContent += chunk;
            res.write(`data: ${JSON.stringify({
                type: 'content_chunk',
                chunk,
                messageId
            })}\n\n`);
        }, questionType);
        const endTime = Date.now();
        const processingTime = (endTime - startTime) / 1000;
        // Send completion event
        res.write(`data: ${JSON.stringify({
            type: 'message_complete',
            messageId,
            content: fullContent,
            processingTime: Math.round(processingTime * 1000) / 1000,
            timestamp: new Date().toISOString(),
            agentId: selectedAgent?.id || 'agent-1',
            model: 'qwen2.5:3b',
            isRealLLM: true
        })}\n\n`);
        // Update metrics
        systemMetrics.totalRequests++;
        systemMetrics.averageLatency = (systemMetrics.averageLatency * (systemMetrics.totalRequests - 1) + processingTime) / systemMetrics.totalRequests;
        if (selectedAgent) {
            selectedAgent.totalRequests++;
            selectedAgent.lastActivity = new Date().toISOString();
            selectedAgent.currentLoad = Math.min(5, selectedAgent.currentLoad + 0.2);
            setTimeout(() => {
                selectedAgent.currentLoad = Math.max(0, selectedAgent.currentLoad - 0.1);
            }, 5000);
        }
        // Broadcast completion
        broadcastUpdate('agent_activity', {
            agentId: selectedAgent?.id,
            status: 'completed',
            processingTime
        });
        res.end();
    }
    catch (error) {
        console.error('Streaming failed:', error);
        res.write(`data: ${JSON.stringify({
            type: 'error',
            error: error instanceof Error ? error.message : 'Unknown error'
        })}\n\n`);
        res.end();
    }
});
// Keep the original non-streaming endpoint for compatibility
app.post('/api/messages', async (req, res) => {
    const { content, sessionId, capabilities = ['general'] } = req.body;
    try {
        const startTime = Date.now();
        // Select an active agent based on capabilities
        const activeAgents = agents.filter(a => a.status === 'active');
        let selectedAgent = activeAgents[0];
        // Use the same enhanced agent selection logic as streaming endpoint
        const lowerContent = content.toLowerCase();
        // Define keywords (same as streaming endpoint)
        const codingKeywords = [
            'code', 'function', 'typescript', 'javascript', 'python', 'java', 'c++', 'programming',
            'algorithm', 'debug', 'syntax', 'compile', 'variable', 'loop', 'array', 'object',
            'class', 'method', 'api', 'database', 'sql', 'html', 'css', 'react', 'node',
            'write a function', 'create a script', 'implement', 'coding', 'software', 'development'
        ];
        const conceptualKeywords = [
            'what is', 'explain', 'define', 'describe', 'how does', 'why does', 'what are',
            'biology', 'chemistry', 'physics', 'science', 'epigenetics', 'photosynthesis',
            'evolution', 'genetics', 'molecular', 'cellular', 'organism', 'ecosystem',
            'theory', 'concept', 'principle', 'mechanism', 'process', 'phenomenon'
        ];
        const researchKeywords = [
            'research', 'study', 'analysis', 'investigate', 'examine', 'compare',
            'academic', 'scholarly', 'literature', 'paper', 'journal', 'methodology',
            'hypothesis', 'experiment', 'data', 'statistics', 'findings', 'conclusion'
        ];
        // Check question type
        const isCodeRequest = codingKeywords.some(keyword => lowerContent.includes(keyword)) ||
            /write.*code|create.*function|implement.*algorithm|show.*example.*code/i.test(content);
        const isConceptualQuestion = conceptualKeywords.some(keyword => lowerContent.includes(keyword)) ||
            /^(what|how|why|explain|define|describe)/i.test(content.trim());
        const isResearchRequest = researchKeywords.some(keyword => lowerContent.includes(keyword));
        // Agent selection (same logic as streaming)
        if (isCodeRequest && !isConceptualQuestion) {
            selectedAgent = activeAgents.find(a => a.capabilities.includes('code-generation')) ||
                activeAgents.find(a => a.name.toLowerCase().includes('code')) ||
                activeAgents[1];
        }
        else if (isConceptualQuestion && !isCodeRequest) {
            if (isResearchRequest) {
                selectedAgent = activeAgents.find(a => a.capabilities.includes('research')) ||
                    activeAgents.find(a => a.name.toLowerCase().includes('research')) ||
                    activeAgents[2];
            }
            else {
                selectedAgent = activeAgents.find(a => a.capabilities.includes('general')) ||
                    activeAgents.find(a => a.name.toLowerCase().includes('general')) ||
                    activeAgents[0];
            }
        }
        else if (isCodeRequest && isConceptualQuestion) {
            selectedAgent = activeAgents.find(a => a.capabilities.includes('general')) ||
                activeAgents[0];
        }
        else {
            if (lowerContent.includes('advanced') || lowerContent.includes('complex') || lowerContent.includes('technical')) {
                selectedAgent = activeAgents.find(a => a.capabilities.includes('specialized')) || activeAgents[2];
            }
            else {
                selectedAgent = activeAgents[systemMetrics.totalRequests % activeAgents.length] || activeAgents[0];
            }
        }
        // Determine question type
        let questionType = 'general';
        if (isCodeRequest && !isConceptualQuestion) {
            questionType = 'coding';
        }
        else if (isConceptualQuestion && !isCodeRequest) {
            questionType = isResearchRequest ? 'research' : 'conceptual';
        }
        else if (isCodeRequest && isConceptualQuestion) {
            questionType = 'mixed';
        }
        // Call the local LLM model (non-streaming) with proper question type
        const responseContent = await callLocalLLMStreaming(content, selectedAgent?.capabilities || ['general'], () => { }, questionType);
        const endTime = Date.now();
        const processingTime = (endTime - startTime) / 1000;
        const response = {
            id: `msg_${Date.now()}`,
            content: responseContent,
            processingTime: Math.round(processingTime * 1000) / 1000,
            timestamp: new Date().toISOString(),
            agentId: selectedAgent?.id || 'agent-1',
            model: 'qwen2.5:3b',
            isRealLLM: true
        };
        // Update metrics and agent stats
        systemMetrics.totalRequests++;
        systemMetrics.averageLatency = (systemMetrics.averageLatency * (systemMetrics.totalRequests - 1) + processingTime) / systemMetrics.totalRequests;
        if (selectedAgent) {
            selectedAgent.totalRequests++;
            selectedAgent.lastActivity = new Date().toISOString();
            selectedAgent.currentLoad = Math.min(5, selectedAgent.currentLoad + 0.2);
            setTimeout(() => {
                selectedAgent.currentLoad = Math.max(0, selectedAgent.currentLoad - 0.1);
            }, 5000);
        }
        broadcastUpdate('message_processed', response);
        res.json(response);
    }
    catch (error) {
        console.error('Message processing failed:', error);
        res.status(500).json({
            error: 'Message processing failed',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Performance data
app.get('/api/performance', (req, res) => {
    // Generate mock performance data
    const performanceData = [];
    const now = new Date();
    for (let i = 6; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 5 * 60 * 1000);
        performanceData.push({
            time: time.toISOString(),
            latency: 0.001 + Math.random() * 0.0005,
            throughput: 4200000 + Math.random() * 200000,
            agents: Math.min(3 + Math.floor(i / 2), 5),
            memoryUsage: 4.5 + Math.random() * 0.5
        });
    }
    res.json(performanceData);
});
// Queue status
app.get('/api/queue', (req, res) => {
    res.json({
        primaryBuffer: {
            messages: 847,
            processingRate: 2100000,
            utilization: 0.65
        },
        secondaryBuffer: {
            messages: 400,
            processingRate: 2200000,
            utilization: 0.35
        },
        totalThroughput: systemMetrics.messagesPerSecond,
        averageLatency: systemMetrics.averageLatency,
        bufferSwitches: systemMetrics.bufferSwitches
    });
});
// WebSocket handling
wss.on('connection', (ws) => {
    console.log('WebSocket client connected');
    // Send initial data
    ws.send(JSON.stringify({
        type: 'initial_data',
        data: {
            metrics: systemMetrics,
            agents: agents
        }
    }));
    ws.on('close', () => {
        console.log('WebSocket client disconnected');
    });
});
// Broadcast updates to all WebSocket clients
function broadcastUpdate(type, data) {
    const message = JSON.stringify({ type, data, timestamp: new Date().toISOString() });
    wss.clients.forEach((client) => {
        if (client.readyState === 1) { // WebSocket.OPEN
            client.send(message);
        }
    });
}
// Simulate real-time updates
setInterval(() => {
    // Update metrics with some variation
    systemMetrics.messagesPerSecond = 4200000 + Math.random() * 400000;
    systemMetrics.averageLatency = 0.001 + Math.random() * 0.0005;
    systemMetrics.queueSize = 1000 + Math.random() * 500;
    systemMetrics.totalRequests += Math.floor(Math.random() * 10);
    // Update agent activity
    agents.forEach(agent => {
        if (agent.status === 'active' && Math.random() > 0.7) {
            agent.currentLoad = Math.max(0, agent.currentLoad + (Math.random() - 0.5) * 2);
            agent.totalRequests += Math.floor(Math.random() * 3);
            agent.lastActivity = new Date().toISOString();
        }
    });
    // Broadcast updates
    broadcastUpdate('metrics_update', systemMetrics);
    broadcastUpdate('agents_update', agents);
}, 5000); // Update every 5 seconds
// Start server
const PORT = process.env.PORT || 8080;
async function startServer() {
    try {
        // Initialize systems
        await system.start();
        await agentManager.start();
        server.listen(PORT, () => {
            console.log(`🚀 Claude Code 3.0 API Server running on port ${PORT}`);
            console.log(`📊 WebSocket server ready for real-time updates`);
            console.log(`🎨 UI available at http://localhost:3000`);
        });
    }
    catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}
// Graceful shutdown
process.on('SIGTERM', async () => {
    console.log('Shutting down gracefully...');
    await agentManager.stop();
    await system.stop();
    server.close();
    process.exit(0);
});
startServer();
