/**
 * Claude Code 3.0 - Core Components
 *
 * Central export point for all core system components.
 */
// Main system
export { ClaudeCodeSystem } from './system.js';
// Core managers (placeholders for future implementation)
export class FileStateManager {
    fileStates = new Map();
    getFileState(path) {
        return this.fileStates.get(path);
    }
    setFileState(path, state) {
        this.fileStates.set(path, state);
    }
    removeFileState(path) {
        return this.fileStates.delete(path);
    }
    getAllFileStates() {
        return new Map(this.fileStates);
    }
}
export class ConfigManager {
    config = {};
    get(key) {
        return this.config[key];
    }
    set(key, value) {
        this.config[key] = value;
    }
    getAll() {
        return { ...this.config };
    }
    merge(newConfig) {
        this.config = { ...this.config, ...newConfig };
    }
}
export class LoggingManager {
    logs = [];
    log(level, message, context) {
        const entry = {
            level,
            message,
            context,
            timestamp: new Date()
        };
        this.logs.push(entry);
        // Keep only recent logs
        if (this.logs.length > 1000) {
            this.logs = this.logs.slice(-1000);
        }
        // Output to console in development
        if (process.env.NODE_ENV === 'development') {
            console.log(`[${level.toUpperCase()}] ${message}`, context || '');
        }
    }
    getLogs(level) {
        if (level) {
            return this.logs.filter(log => log.level === level);
        }
        return [...this.logs];
    }
    clearLogs() {
        this.logs = [];
    }
}
export class ErrorRecoveryManager {
    errorHistory = [];
    recoveryStrategies = new Map();
    recordError(error) {
        this.errorHistory.push(error);
        // Keep only recent errors
        if (this.errorHistory.length > 100) {
            this.errorHistory = this.errorHistory.slice(-100);
        }
    }
    registerRecoveryStrategy(errorType, strategy) {
        this.recoveryStrategies.set(errorType, strategy);
    }
    async attemptRecovery(error) {
        const strategy = this.recoveryStrategies.get(error.constructor.name);
        if (strategy) {
            try {
                await strategy(error);
                return true;
            }
            catch (recoveryError) {
                console.error('Recovery strategy failed:', recoveryError);
                return false;
            }
        }
        return false;
    }
    getErrorHistory() {
        return [...this.errorHistory];
    }
    getErrorStats() {
        const stats = new Map();
        for (const error of this.errorHistory) {
            const type = error.constructor.name;
            stats.set(type, (stats.get(type) || 0) + 1);
        }
        return Object.fromEntries(stats);
    }
}
//# sourceMappingURL=index.js.map