{"version": 3, "file": "system.js", "sourceRoot": "", "sources": ["../../src/core/system.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,eAAe,CAAC;AAC7C,OAAO,EAAmB,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AACzF,OAAO,EAOL,qBAAqB,EACtB,MAAM,oBAAoB,CAAC;AAE5B;;;;;;;;;;;;GAYG;AACH,MAAM,OAAO,gBAAiB,SAAQ,YAAY;IACxC,MAAM,CAAe;IACrB,KAAK,CAAc;IAE3B,kBAAkB;IACV,eAAe,CAAkB;IAEzC,qCAAqC;IAC7B,UAAU,CAAO;IACjB,SAAS,CAAO;IAChB,YAAY,CAAO;IACnB,cAAc,CAAO;IACrB,YAAY,CAAO;IACnB,WAAW,CAAO;IAClB,UAAU,CAAO;IAEzB,iBAAiB;IACT,qBAAqB,CAAiB;IACtC,eAAe,CAAiB;IAChC,mBAAmB,CAAkB;IAE7C,YAAY,SAAgC,EAAE;QAC5C,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,qBAAqB,EAAE,GAAG,MAAM,EAAE,CAAC;QACtD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAEpC,6BAA6B;QAC7B,IAAI,CAAC,eAAe,GAAG,yBAAyB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAE9E,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAED,+EAA+E;IAC/E,mBAAmB;IACnB,+EAA+E;IAE/E;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC1D,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,YAAY,CAAC,OAAO,EAAE,CAAC;YAC/C,OAAO;QACT,CAAC;QAED,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAExB,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC;QACzC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAElC,wBAAwB;QACxB,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAEnC,0BAA0B;QAC1B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC9C,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;QACxC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM;QACV,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC;QACzC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO;YACL,aAAa,EAAE,CAAC,EAAE,oBAAoB;YACtC,kBAAkB,EAAE,CAAC;YACrB,cAAc,EAAE,CAAC;YACjB,mBAAmB,EAAE,CAAC;YACtB,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ;YAC3C,QAAQ,EAAE,CAAC,EAAE,+CAA+C;YAC5D,iBAAiB,EAAE,CAAC;SACrB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,CACL,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,YAAY,CAAC,OAAO;YAC1C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE;YAC7B,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,QAAQ,CACzC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAI,OAAY;QAC/B,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IAC1D,CAAC;IAED,+EAA+E;IAC/E,yBAAyB;IACzB,+EAA+E;IAEvE,eAAe;QACrB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvB,OAAO;YACL,MAAM,EAAE,YAAY,CAAC,YAAY;YACjC,SAAS,EAAE,GAAG;YACd,MAAM,EAAE,CAAC;YACT,MAAM,EAAE;gBACN,GAAG,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBAC5B,EAAE,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBAC3B,QAAQ,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBACjC,KAAK,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBAC9B,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBAChC,KAAK,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBAC9B,IAAI,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBAC7B,GAAG,EAAE,IAAI,CAAC,gBAAgB,EAAE;aAC7B;YACD,OAAO,EAAE;gBACP,aAAa,EAAE,CAAC;gBAChB,kBAAkB,EAAE,CAAC;gBACrB,cAAc,EAAE,CAAC;gBACjB,mBAAmB,EAAE,CAAC;gBACtB,WAAW,EAAE,CAAC;gBACd,QAAQ,EAAE,CAAC;gBACX,iBAAiB,EAAE,CAAC;aACrB;YACD,MAAM,EAAE,EAAE;YACV,SAAS,EAAE;gBACT,MAAM,EAAE;oBACN,IAAI,EAAE,CAAC;oBACP,KAAK,EAAE,CAAC;oBACR,UAAU,EAAE,CAAC;iBACd;gBACD,GAAG,EAAE;oBACH,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,CAAC;iBACT;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE,CAAC;oBACP,SAAS,EAAE,CAAC;oBACZ,UAAU,EAAE,CAAC;iBACd;aACF;SACF,CAAC;IACJ,CAAC;IAEO,gBAAgB;QACtB,OAAO;YACL,MAAM,EAAE,YAAY,CAAC,YAAY;YACjC,WAAW,EAAE,KAAK;YAClB,YAAY,EAAE,IAAI,IAAI,EAAE;YACxB,UAAU,EAAE,CAAC;SACd,CAAC;IACJ,CAAC;IAEO,kBAAkB;QACxB,yBAAyB;QACzB,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,kBAAkB,EAAE,GAAG,EAAE;YAC/C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC;YACzD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;YAC9C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,kBAAkB,EAAE,GAAG,EAAE;YAC/C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC;YACzD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,KAAK,EAAE,EAAE;YAClD,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,uBAAuB;QACvB,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACzB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,qBAAqB;QACjC,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,YAAY,CAAC;YAE9C,wCAAwC;YACxC,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,KAAK,IAAI,EAAE;gBAChD,uDAAuD;gBACvD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;YAChD,CAAC,CAAC,CAAC;YAEH,gCAAgC;YAChC,0EAA0E;YAC1E,8EAA8E;YAC9E,0EAA0E;YAC1E,wEAAwE;YACxE,sEAAsE;YACtE,oEAAoE;YACpE,sEAAsE;YAEtE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC,iBAAiB;YAC3D,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;QAE7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC;YACvC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,KAAc,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,SAAiB,EAAE,WAAgC;QAC/E,IAAI,CAAC;YACH,MAAM,WAAW,EAAE,CAAC;YACpB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAA2C,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC;YAClF,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,KAAc,CAAC,CAAC;YAClD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,QAAQ,CAAC;YAE1C,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAE5B,mCAAmC;YACnC,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;YAElC,0BAA0B;YAE1B,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC;YACzC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;QAEzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC;YACvC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,KAAc,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,WAAW;QACjB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAEnE,wBAAwB;QACxB,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,GAAG,QAAQ,CAAC,QAAQ,CAAC;QACrD,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC;QACvD,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;QAExF,sBAAsB;QACtB,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC;YAC7C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC;YACzD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,GAAG,GAAG,CAAC;QAChD,CAAC;IACH,CAAC;IAEO,qBAAqB;QAC3B,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC,GAAG,EAAE;YAC1C,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,wBAAwB;IACpC,CAAC;IAEO,oBAAoB;QAC1B,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACxC,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;QACvC,CAAC;IACH,CAAC;IAEO,kBAAkB;QACxB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEnC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,YAAY,CAAC,OAAO,EAAE,CAAC;YAC7D,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB;aACrD,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS;YACT,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;SAC3B,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,KAAa,EAAE,KAAY;QACnD,MAAM,WAAW,GAAgB;YAC/B,EAAE,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC9D,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,KAAK,EAAE,OAAO;YACd,KAAK;YACL,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,KAAK,EAAE,KAAK,CAAC,KAAK;SACnB,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEpC,0BAA0B;QAC1B,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YACnC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;QACpD,CAAC;QAED,2BAA2B;QAC3B,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAuC,CAAC,EAAE,CAAC;YAC/D,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAuC,CAAC,CAAC,UAAU,EAAE,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;IACzC,CAAC;CACF"}