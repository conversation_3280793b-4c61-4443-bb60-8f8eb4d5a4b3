/**
 * Claude Code 3.0 - Core Components
 *
 * Central export point for all core system components.
 */
export { ClaudeCodeSystem } from './system.js';
export declare class FileStateManager {
    private fileStates;
    getFileState(path: string): any;
    setFileState(path: string, state: any): void;
    removeFileState(path: string): boolean;
    getAllFileStates(): Map<string, any>;
}
export declare class ConfigManager {
    private config;
    get(key: string): any;
    set(key: string, value: any): void;
    getAll(): any;
    merge(newConfig: any): void;
}
export declare class LoggingManager {
    private logs;
    log(level: string, message: string, context?: any): void;
    getLogs(level?: string): any[];
    clearLogs(): void;
}
export declare class ErrorRecoveryManager {
    private errorHistory;
    private recoveryStrategies;
    recordError(error: Error): void;
    registerRecoveryStrategy(errorType: string, strategy: Function): void;
    attemptRecovery(error: Error): Promise<boolean>;
    getErrorHistory(): Error[];
    getErrorStats(): any;
}
//# sourceMappingURL=index.d.ts.map