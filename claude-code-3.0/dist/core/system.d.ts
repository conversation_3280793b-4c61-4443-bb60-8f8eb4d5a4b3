/**
 * Claude Code 3.0 - Main System Class
 *
 * Central orchestrator for the entire Claude Code 3.0 system.
 * Manages all layers and provides the main system interface.
 */
import { EventEmitter } from 'eventemitter3';
import { SteeringManager } from '../layers/steering/index.js';
import { SystemConfig, SystemState, SystemMetrics } from '../types/system.js';
/**
 * Claude Code 3.0 System
 *
 * Main system class that orchestrates all layers:
 * 1. CLI Layer - Command line interface
 * 2. UI Layer - React-based user interface
 * 3. Steering Layer - h2A message queue system
 * 4. Event Layer - Event-driven processing
 * 5. Message Layer - Message handling
 * 6. Agent Layer - AI agent processing
 * 7. Tool Layer - Tool execution
 * 8. API Layer - External integrations
 */
export declare class ClaudeCodeSystem extends EventEmitter {
    private config;
    private state;
    private steeringManager;
    private cliManager?;
    private uiManager?;
    private eventManager?;
    private messageManager?;
    private agentManager?;
    private toolManager?;
    private apiManager?;
    private initializationPromise?;
    private shutdownPromise?;
    private healthCheckInterval?;
    constructor(config?: Partial<SystemConfig>);
    /**
     * Initialize the entire system
     */
    initialize(): Promise<void>;
    /**
     * Start the system
     */
    start(): Promise<void>;
    /**
     * Stop the system gracefully
     */
    stop(): Promise<void>;
    /**
     * Pause the system
     */
    pause(): Promise<void>;
    /**
     * Resume the system
     */
    resume(): Promise<void>;
    /**
     * Get current system state
     */
    getState(): SystemState;
    /**
     * Get system configuration
     */
    getConfig(): SystemConfig;
    /**
     * Get comprehensive system metrics
     */
    getMetrics(): SystemMetrics;
    /**
     * Check if system is healthy
     */
    isHealthy(): boolean;
    /**
     * Get steering manager for direct access
     */
    getSteering(): SteeringManager;
    /**
     * Send a message through the system
     */
    sendMessage<T>(message: any): Promise<boolean>;
    private initializeState;
    private createLayerState;
    private setupEventHandlers;
    private performInitialization;
    private initializeLayer;
    private performShutdown;
    private updateState;
    private startHealthMonitoring;
    private stopHealthMonitoring;
    private performHealthCheck;
    private handleSystemError;
}
//# sourceMappingURL=system.d.ts.map