/**
 * Claude Code 3.0 - Utilities
 *
 * Common utility functions and helpers.
 */
/**
 * Generate unique ID
 */
export declare function generateId(prefix?: string): string;
/**
 * Sleep for specified milliseconds
 */
export declare function sleep(ms: number): Promise<void>;
/**
 * Retry function with exponential backoff
 */
export declare function retry<T>(fn: () => Promise<T>, options?: {
    retries?: number;
    delay?: number;
    backoff?: number;
}): Promise<T>;
/**
 * Debounce function
 */
export declare function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void;
/**
 * Throttle function
 */
export declare function throttle<T extends (...args: any[]) => any>(func: T, limit: number): (...args: Parameters<T>) => void;
/**
 * Deep clone object
 */
export declare function deepClone<T>(obj: T): T;
/**
 * Format bytes to human readable string
 */
export declare function formatBytes(bytes: number, decimals?: number): string;
/**
 * Format duration to human readable string
 */
export declare function formatDuration(ms: number): string;
/**
 * Calculate percentile from array of numbers
 */
export declare function calculatePercentile(values: number[], percentile: number): number;
/**
 * Create a promise that resolves after specified time
 */
export declare function timeout<T>(promise: Promise<T>, ms: number): Promise<T>;
/**
 * Check if value is empty (null, undefined, empty string, empty array, empty object)
 */
export declare function isEmpty(value: any): boolean;
/**
 * Safe JSON parse with fallback
 */
export declare function safeJsonParse<T>(json: string, fallback: T): T;
/**
 * Create a hash from string
 */
export declare function createHash(str: string): string;
//# sourceMappingURL=index.d.ts.map