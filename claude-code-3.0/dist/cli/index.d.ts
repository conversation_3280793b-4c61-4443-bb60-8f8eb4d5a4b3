/**
 * Claude Code 3.0 - CLI Application
 *
 * Command line interface for the Claude Code 3.0 system.
 */
/**
 * CLI Application Class
 */
export declare class CLIApplication {
    private system?;
    private agent?;
    constructor();
    /**
     * Run the CLI application
     */
    run(args: string[]): Promise<void>;
    private setupCommands;
    private startSystem;
    private stopSystem;
    private showStatus;
    private startChat;
    private askQuestion;
    private runTests;
    private startDevelopment;
    private getStatusColor;
    private formatUptime;
    private formatBytes;
    private calculateSuccessRate;
}
//# sourceMappingURL=index.d.ts.map