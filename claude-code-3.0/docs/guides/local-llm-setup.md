# Local LLM Setup Guide

Complete guide to setting up local LLM integration with Ollama and qwen2.5:3b model.

## 🎯 Overview

Claude Code 3.0 supports local LLM integration for:
- **Privacy**: All processing stays on your machine
- **Cost**: No API fees or token costs
- **Performance**: 116+ tokens/sec with qwen2.5:3b
- **Availability**: Works completely offline

## 📋 Prerequisites

- **System Requirements**: 8GB+ RAM recommended for qwen2.5:3b
- **Storage**: 2GB+ free space for model files
- **Operating System**: macOS, Linux, or Windows
- **Network**: Internet connection for initial model download

## 🚀 Installation Steps

### 1. Install Ollama

#### macOS
```bash
# Using Homebrew (recommended)
brew install ollama

# Or download from https://ollama.ai/
```

#### Linux
```bash
# Using curl
curl -fsSL https://ollama.ai/install.sh | sh

# Or using package manager (Ubuntu/Debian)
sudo apt update && sudo apt install ollama
```

#### Windows
```bash
# Download installer from https://ollama.ai/
# Or use Windows Subsystem for Linux (WSL)
```

### 2. Start Ollama Server

```bash
# Start Ollama server (runs on http://localhost:11434)
ollama serve

# Keep this terminal open - Ollama server must be running
```

### 3. Install qwen2.5:3b Model

In a new terminal:

```bash
# Pull the qwen2.5:3b model (recommended for Claude Code 3.0)
ollama pull qwen2.5:3b

# This will download ~2GB - may take a few minutes
```

### 4. Verify Installation

```bash
# List installed models
ollama list

# Expected output:
# NAME            ID              SIZE    MODIFIED
# qwen2.5:3b      abc123def456    1.9GB   2 minutes ago

# Test the model
ollama run qwen2.5:3b "Hello, how are you?"
```

## 🧪 Test Integration with Claude Code 3.0

### Basic Connection Test

```bash
# Navigate to Claude Code 3.0 directory
cd claude-code-3.0

# Test Ollama integration
OLLAMA_MODEL=qwen2.5:3b node local-llm-test.js
```

Expected output:
```
🤖 Claude Code 3.0 - Local LLM Integration Test
✅ Local LLM Integration: SUCCESSFUL
📊 Performance Metrics:
   • Connection Latency: 932.40ms
   • Model: qwen2.5:3b (confirmed working)
   • Tokens/sec: 116.6 (excellent performance)
```

### Comprehensive Testing

```bash
# Run full test suite with local LLM
OLLAMA_MODEL=qwen2.5:3b node comprehensive-test.js

# Expected results:
# 🤖 Ollama Local: ✅ PASS | 2251ms avg | 122 tokens/sec
```

## ⚙️ Configuration Options

### Environment Variables

```bash
# Model selection
export OLLAMA_MODEL=qwen2.5:3b

# Server configuration
export OLLAMA_BASE_URL=http://localhost:11434

# Model parameters
export OLLAMA_TEMPERATURE=0.7
export OLLAMA_MAX_TOKENS=4096
```

### Programmatic Configuration

```typescript
import { createOllamaClient } from 'claude-code-3.0';

const client = createOllamaClient({
  model: 'qwen2.5:3b',
  baseURL: 'http://localhost:11434',
  temperature: 0.7,
  maxTokens: 4096
});

// Test connection
const result = await client.testConnection();
console.log('Connection result:', result);
```

## 🎛️ Model Options

### Recommended Models for Claude Code 3.0

| Model | Size | RAM Required | Performance | Use Case |
|-------|------|--------------|-------------|----------|
| **qwen2.5:3b** | 1.9GB | 4GB+ | 116 tokens/sec | **Recommended** - Best balance |
| qwen2.5:7b | 4.1GB | 8GB+ | 80 tokens/sec | Higher quality responses |
| llama3.2:3b | 2.0GB | 4GB+ | 95 tokens/sec | Alternative option |
| codellama:7b | 3.8GB | 8GB+ | 70 tokens/sec | Code-focused tasks |
| mistral:7b | 4.1GB | 8GB+ | 85 tokens/sec | General purpose |

### Install Additional Models

```bash
# Install alternative models
ollama pull qwen2.5:7b
ollama pull llama3.2:3b
ollama pull codellama:7b

# Switch models in Claude Code 3.0
OLLAMA_MODEL=qwen2.5:7b node local-llm-test.js
```

## 📊 Performance Optimization

### System Optimization

```bash
# Check system resources
htop  # or Activity Monitor on macOS

# Ensure sufficient RAM is available
# qwen2.5:3b needs ~4GB RAM total
```

### Model Parameters

```typescript
// Optimize for speed
const fastConfig = {
  model: 'qwen2.5:3b',
  temperature: 0.3,  // Lower = faster, more deterministic
  maxTokens: 1000    // Shorter responses = faster
};

// Optimize for quality
const qualityConfig = {
  model: 'qwen2.5:3b',
  temperature: 0.8,  // Higher = more creative
  maxTokens: 4096    // Longer responses allowed
};
```

### Concurrent Processing

```typescript
// Multi-agent with local LLM
const agentManager = createMultiAgentManager({
  maxAgents: 3,  // Adjust based on available RAM
  loadBalancingStrategy: 'least-loaded',
  defaultAgentConfig: {
    enableLocalLLM: true,
    localLLMConfig: {
      provider: 'ollama',
      model: 'qwen2.5:3b'
    }
  }
});
```

## 🔧 Troubleshooting

### Common Issues

#### 1. "Ollama server not accessible"
```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# If not running, start it
ollama serve
```

#### 2. "Model qwen2.5:3b not found"
```bash
# List available models
ollama list

# Pull the model if missing
ollama pull qwen2.5:3b
```

#### 3. Slow performance
```bash
# Check system resources
top

# Consider using smaller model
OLLAMA_MODEL=qwen2.5:3b node local-llm-test.js  # Instead of 7b
```

#### 4. Out of memory errors
```bash
# Reduce concurrent agents
# Edit configuration to use fewer agents
maxAgents: 2  # Instead of 5
```

### Performance Benchmarks

Expected performance with qwen2.5:3b:

| Test Case | Expected Latency | Expected Tokens/sec |
|-----------|------------------|---------------------|
| Simple Query | 200-500ms | 120-140 |
| Code Generation | 1000-2000ms | 110-130 |
| Complex Reasoning | 3000-5000ms | 100-120 |
| Long Context | 3000-6000ms | 100-120 |

## 🔄 Integration with Claude Code 3.0

### Multi-Mode Operation

```typescript
// Hybrid setup with local LLM as primary
const system = createLocalEnvironment({
  layers: {
    agent: {
      enableLocalLLM: true,
      localLLMConfig: {
        provider: 'ollama',
        model: 'qwen2.5:3b'
      },
      // Fallback to API if local fails
      enableAPIFallback: true,
      apiConfig: {
        provider: 'claude',
        apiKey: process.env.CLAUDE_API_KEY
      }
    }
  }
});
```

### Production Deployment

```bash
# Docker deployment with Ollama
docker run -d \
  --name ollama \
  -p 11434:11434 \
  -v ollama:/root/.ollama \
  ollama/ollama

# Pull model in container
docker exec ollama ollama pull qwen2.5:3b

# Run Claude Code 3.0 with local LLM
OLLAMA_BASE_URL=http://localhost:11434 \
OLLAMA_MODEL=qwen2.5:3b \
node comprehensive-test.js
```

## 📈 Monitoring and Metrics

### Performance Monitoring

```typescript
// Monitor local LLM performance
const client = createOllamaClient({ model: 'qwen2.5:3b' });

const result = await client.testConnection();
console.log('Performance metrics:', {
  latency: result.latency,
  tokensPerSecond: result.performance.tokensPerSecond,
  memoryUsage: process.memoryUsage()
});
```

### Health Checks

```bash
# Automated health check script
#!/bin/bash
echo "Checking Ollama health..."
curl -f http://localhost:11434/api/tags > /dev/null
if [ $? -eq 0 ]; then
  echo "✅ Ollama server is healthy"
else
  echo "❌ Ollama server is down"
  exit 1
fi
```

## 🎯 Best Practices

### 1. Resource Management
- Monitor RAM usage with multiple agents
- Use appropriate model size for your hardware
- Consider model switching based on task complexity

### 2. Performance Optimization
- Keep Ollama server running continuously
- Use connection pooling for multiple requests
- Implement request queuing for high load

### 3. Error Handling
- Implement fallback to API mode if local fails
- Monitor model availability and health
- Handle timeout scenarios gracefully

### 4. Security
- Local processing ensures data privacy
- No external API calls or data transmission
- Full control over model and parameters

---

**🎉 Success!** You now have a fully functional local LLM setup with Claude Code 3.0, providing privacy-first AI processing with excellent performance!
