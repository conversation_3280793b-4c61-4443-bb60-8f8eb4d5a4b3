# Quick Start Guide

Get up and running with Claude Code 3.0 in just 5 minutes!

## 🚀 Prerequisites

- **Node.js**: v18 or higher
- **npm**: v8 or higher
- **Optional**: Ollama for local LLM support

## 📦 Installation

### 1. <PERSON><PERSON> and Setup

```bash
# Clone the repository
git clone https://github.com/shareAI-lab/analysis_claude_code.git
cd analysis_claude_code/claude-code-3.0

# Install dependencies (when available)
npm install

# Or run without installation using our validation scripts
node validate.js
```

### 2. Verify Installation

```bash
# Run comprehensive validation
node validate.js

# Expected output:
# 🎉 All checks passed! (6/6)
# ✅ Claude Code 3.0 framework is ready for development!
```

## 🧪 Run Your First Test

### Basic Framework Test

```bash
# Run comprehensive test suite
node comprehensive-test.js

# Expected results:
# 📊 Total Test Suites: 3
# ✅ Total Passed: 6
# 📈 Overall Success Rate: 100.0%
```

### Architecture Performance Test

```bash
# Test the zero-latency architecture
node quick-architecture-benchmark.js

# Expected results:
# ⚡ h2A Architecture: 0.001ms latency
# 🏆 4,960x faster than traditional approaches
```

## 🤖 Multi-Agent System

### Test Multi-Agent Framework

```bash
# Run multi-agent system test
node multi-agent-test.js

# Expected results:
# ✅ System Throughput: 101 messages/second
# ✅ Load Balancing: Distributed across agents
# 🏆 EXCELLENT: High-throughput processing achieved!
```

## 🏠 Local LLM Integration (Optional)

### Setup Ollama with qwen2.5:3b

```bash
# Install Ollama (macOS)
brew install ollama

# Start Ollama server
ollama serve

# Pull qwen2.5:3b model (in another terminal)
ollama pull qwen2.5:3b

# Test local LLM integration
OLLAMA_MODEL=qwen2.5:3b node local-llm-test.js

# Expected results:
# ✅ Status: SUCCESSFUL
# 📊 Performance: 116.6 tokens/sec
# 🤖 Model: qwen2.5:3b (confirmed working)
```

## 💻 Basic Usage Example

Create a simple test file:

```typescript
// test-example.js
import { createStandardEnvironment, createMultiAgentManager } from './src/index.js';

async function quickExample() {
  console.log('🚀 Claude Code 3.0 Quick Example');
  
  // Create system environment
  const system = createStandardEnvironment({
    environment: 'development',
    enableDebugMode: true
  });
  
  // Create multi-agent manager
  const agentManager = createMultiAgentManager({
    maxAgents: 3,
    loadBalancingStrategy: 'least-loaded'
  });
  
  try {
    // Start systems
    await system.start();
    await agentManager.start();
    
    // Process a message
    const message = {
      id: 'quick-test',
      role: 'user',
      content: 'Hello, Claude Code 3.0! How are you working?',
      timestamp: new Date()
    };
    
    const response = await agentManager.processMessage(message);
    console.log('✅ Response received:', response.content);
    
    // Get system metrics
    const metrics = agentManager.getMetrics();
    console.log('📊 System metrics:', metrics);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    // Cleanup
    await agentManager.stop();
    await system.stop();
  }
}

quickExample().catch(console.error);
```

Run the example:

```bash
node test-example.js
```

## 🎯 What You've Accomplished

After completing this quick start, you have:

- ✅ **Validated the framework** with 100% test success rate
- ✅ **Tested zero-latency architecture** with 0.001ms latency
- ✅ **Verified multi-agent system** with 101 msg/sec throughput
- ✅ **Optional**: Integrated local LLM with 116+ tokens/sec performance
- ✅ **Run your first example** with the framework

## 🔄 Different Operating Modes

### 1. Mock Mode (Default)
```bash
# Fast development with simulated responses
node comprehensive-test.js
# ✅ 566ms average latency, no external dependencies
```

### 2. Local LLM Mode
```bash
# Privacy-first local processing
OLLAMA_MODEL=qwen2.5:3b node local-llm-test.js
# ✅ 2251ms average latency, 122 tokens/sec, completely offline
```

### 3. API Mode (when API key available)
```bash
# Cloud LLM integration
CLAUDE_API_KEY=your_key node comprehensive-test.js
# ✅ Variable latency, high quality, requires internet
```

### 4. Hybrid Mode
```bash
# Best of all worlds with automatic fallback
CLAUDE_API_KEY=your_key OLLAMA_MODEL=qwen2.5:3b node comprehensive-test.js
# ✅ Robust operation with multiple fallback options
```

## 📊 Performance Expectations

Based on your quick start tests, you should see:

| Component | Expected Performance | Your Results |
|-----------|---------------------|--------------|
| **Framework Validation** | 100% success rate | ✅ Check |
| **Architecture Latency** | 0.001ms average | ✅ Check |
| **Multi-Agent Throughput** | 100+ msg/sec | ✅ Check |
| **Local LLM (if enabled)** | 116+ tokens/sec | ✅ Check |
| **Memory Usage** | <10MB total | ✅ Check |

## 🎯 Next Steps

Now that you're up and running:

1. **📖 Learn the Architecture**: Read the [Architecture Overview](architecture-overview.md)
2. **🤖 Explore Multi-Agent**: Check out [Multi-Agent Framework](multi-agent-framework.md)
3. **🏠 Setup Local LLM**: Follow [Local LLM Setup](local-llm-setup.md) for detailed configuration
4. **📊 Optimize Performance**: Review [Performance Guide](performance-guide.md)
5. **🔧 Deploy**: See [Production Deployment](production-deployment.md)

## 🆘 Need Help?

- **Common Issues**: Check [Troubleshooting](../troubleshooting/common-issues.md)
- **FAQ**: See [Frequently Asked Questions](../troubleshooting/faq.md)
- **GitHub Issues**: [Report problems](https://github.com/shareAI-lab/analysis_claude_code/issues)

---

**🎉 Congratulations!** You've successfully set up Claude Code 3.0 and verified its zero-latency, multi-agent capabilities. The framework is ready for development!
