/**
 * Integration test setup
 */
import { jest } from '@jest/globals';
// Increase timeout for integration tests
jest.setTimeout(30000);
// Global test setup
beforeAll(async () => {
    // Setup code that runs before all tests
    console.log('Setting up integration test environment...');
});
afterAll(async () => {
    // Cleanup code that runs after all tests
    console.log('Cleaning up integration test environment...');
});
// Mock external dependencies for testing
jest.mock('fs/promises', () => ({
    readFile: jest.fn(),
    writeFile: jest.fn(),
    mkdir: jest.fn(),
    stat: jest.fn()
}));
// Suppress console output during tests unless explicitly needed
const originalConsole = console;
global.console = {
    ...originalConsole,
    log: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
};
//# sourceMappingURL=integration.setup.js.map