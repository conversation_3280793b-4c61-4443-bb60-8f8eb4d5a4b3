/**
 * Claude Code 3.0 - Performance Benchmarks
 *
 * Performance benchmarks for the h2A message queue system and core components.
 */
/**
 * Message Queue Benchmarks
 */
declare function benchmarkMessageQueue(): Promise<void>;
/**
 * Steering Manager Benchmarks
 */
declare function benchmarkSteeringManager(): Promise<void>;
/**
 * Agent Core Benchmarks
 */
declare function benchmarkAgentCore(): Promise<void>;
/**
 * Main benchmark runner
 */
declare function runAllBenchmarks(): Promise<void>;
export { runAllBenchmarks, benchmarkMessageQueue, benchmarkSteeringManager, benchmarkAgentCore };
//# sourceMappingURL=performance.benchmark.d.ts.map