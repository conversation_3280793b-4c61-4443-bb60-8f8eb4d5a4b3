/**
 * Claude Code 3.0 - Performance Benchmarks
 * 
 * Performance benchmarks for the h2A message queue system and core components.
 */

import { performance } from 'perf_hooks';
import { v4 as uuidv4 } from 'uuid';
import {
  h2AMessageQueue,
  createHighPerformanceQueue,
  createMemoryEfficientQueue
} from '../../src/layers/steering/message-queue.js';
import { SteeringManager } from '../../src/layers/steering/steering-manager.js';
import { AgentCore } from '../../src/layers/agent/agent-core.js';
import { Message, MessagePriority } from '../../src/types/message-queue.js';

// Benchmark configuration
const BENCHMARK_CONFIG = {
  messageCount: 10000,
  concurrentOperations: 100,
  warmupIterations: 1000,
  measurementIterations: 5
};

/**
 * Benchmark utilities
 */
class BenchmarkRunner {
  private results: Map<string, number[]> = new Map();
  
  async runBenchmark(name: string, fn: () => Promise<void>, iterations: number = 1): Promise<void> {
    console.log(`\n🏃 Running benchmark: ${name}`);
    
    const times: number[] = [];
    
    for (let i = 0; i < iterations; i++) {
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const start = performance.now();
      await fn();
      const end = performance.now();
      
      times.push(end - start);
      
      if (i % Math.max(1, Math.floor(iterations / 10)) === 0) {
        process.stdout.write('.');
      }
    }
    
    this.results.set(name, times);
    this.printResults(name, times);
  }
  
  private printResults(name: string, times: number[]): void {
    const avg = times.reduce((sum, time) => sum + time, 0) / times.length;
    const min = Math.min(...times);
    const max = Math.max(...times);
    const median = times.sort((a, b) => a - b)[Math.floor(times.length / 2)];
    
    console.log(`\n📊 Results for ${name}:`);
    console.log(`   Average: ${avg.toFixed(2)}ms`);
    console.log(`   Median:  ${median.toFixed(2)}ms`);
    console.log(`   Min:     ${min.toFixed(2)}ms`);
    console.log(`   Max:     ${max.toFixed(2)}ms`);
    console.log(`   Ops/sec: ${(1000 / avg).toFixed(0)}`);
  }
  
  printSummary(): void {
    console.log('\n📈 Benchmark Summary:');
    console.log('=' .repeat(50));
    
    for (const [name, times] of this.results) {
      const avg = times.reduce((sum, time) => sum + time, 0) / times.length;
      const opsPerSec = Math.round(1000 / avg);
      console.log(`${name.padEnd(30)} ${opsPerSec.toString().padStart(8)} ops/sec`);
    }
  }
}

/**
 * Message Queue Benchmarks
 */
async function benchmarkMessageQueue(): Promise<void> {
  const runner = new BenchmarkRunner();
  
  console.log('\n🚀 Message Queue Performance Benchmarks');
  console.log('=' .repeat(50));
  
  // Standard queue benchmarks
  await runner.runBenchmark('Standard Queue - Enqueue', async () => {
    const queue = new h2AMessageQueue({ maxBufferSize: 50000 });
    await queue.start();
    
    for (let i = 0; i < BENCHMARK_CONFIG.messageCount; i++) {
      await queue.enqueue(createTestMessage(`message-${i}`));
    }
    
    await queue.stop();
  }, BENCHMARK_CONFIG.measurementIterations);
  
  await runner.runBenchmark('Standard Queue - Dequeue', async () => {
    const queue = new h2AMessageQueue({ maxBufferSize: 50000 });
    await queue.start();
    
    // Fill queue first
    for (let i = 0; i < BENCHMARK_CONFIG.messageCount; i++) {
      await queue.enqueue(createTestMessage(`message-${i}`));
    }
    
    // Benchmark dequeue
    for (let i = 0; i < BENCHMARK_CONFIG.messageCount; i++) {
      await queue.dequeue();
    }
    
    await queue.stop();
  }, BENCHMARK_CONFIG.measurementIterations);
  
  // High-performance queue benchmarks
  await runner.runBenchmark('High-Performance Queue - Enqueue', async () => {
    const queue = createHighPerformanceQueue();
    await queue.start();
    
    for (let i = 0; i < BENCHMARK_CONFIG.messageCount; i++) {
      await queue.enqueue(createTestMessage(`message-${i}`));
    }
    
    await queue.stop();
  }, BENCHMARK_CONFIG.measurementIterations);
  
  // Memory-efficient queue benchmarks
  await runner.runBenchmark('Memory-Efficient Queue - Enqueue', async () => {
    const queue = createMemoryEfficientQueue();
    await queue.start();
    
    for (let i = 0; i < 1000; i++) { // Smaller count for memory-efficient queue
      await queue.enqueue(createTestMessage(`message-${i}`));
    }
    
    await queue.stop();
  }, BENCHMARK_CONFIG.measurementIterations);
  
  // Batch operations benchmark
  await runner.runBenchmark('Batch Enqueue (1000 messages)', async () => {
    const queue = new h2AMessageQueue({ maxBufferSize: 50000 });
    await queue.start();
    
    const messages = Array.from({ length: 1000 }, (_, i) => 
      createTestMessage(`batch-message-${i}`)
    );
    
    await queue.enqueueBatch(messages);
    
    await queue.stop();
  }, BENCHMARK_CONFIG.measurementIterations);
  
  // Concurrent operations benchmark
  await runner.runBenchmark('Concurrent Enqueue/Dequeue', async () => {
    const queue = new h2AMessageQueue({ maxBufferSize: 50000 });
    await queue.start();
    
    const operations = [];
    
    // Start concurrent enqueue operations
    for (let i = 0; i < BENCHMARK_CONFIG.concurrentOperations; i++) {
      operations.push(
        queue.enqueue(createTestMessage(`concurrent-${i}`))
      );
    }
    
    // Start concurrent dequeue operations
    for (let i = 0; i < BENCHMARK_CONFIG.concurrentOperations; i++) {
      operations.push(queue.dequeue());
    }
    
    await Promise.all(operations);
    await queue.stop();
  }, BENCHMARK_CONFIG.measurementIterations);
  
  runner.printSummary();
}

/**
 * Steering Manager Benchmarks
 */
async function benchmarkSteeringManager(): Promise<void> {
  const runner = new BenchmarkRunner();
  
  console.log('\n🎯 Steering Manager Performance Benchmarks');
  console.log('=' .repeat(50));
  
  await runner.runBenchmark('Message Routing', async () => {
    const steering = new SteeringManager({
      enableRealTimeProcessing: false, // Disable for pure routing benchmark
      queueConfig: { maxBufferSize: 50000 }
    });
    
    await steering.start();
    
    for (let i = 0; i < 5000; i++) {
      await steering.routeMessage(createTestMessage(`route-${i}`, MessagePriority.NORMAL, 'agent'));
    }
    
    await steering.stop();
  }, BENCHMARK_CONFIG.measurementIterations);
  
  await runner.runBenchmark('Multi-Queue Routing', async () => {
    const steering = new SteeringManager({
      enableRealTimeProcessing: false,
      queueConfig: { maxBufferSize: 50000 }
    });
    
    await steering.start();
    
    const destinations = ['agent', 'ui', 'tool', 'system'];
    
    for (let i = 0; i < 2000; i++) {
      const destination = destinations[i % destinations.length];
      await steering.routeMessage(createTestMessage(`multi-${i}`, MessagePriority.NORMAL, destination));
    }
    
    await steering.stop();
  }, BENCHMARK_CONFIG.measurementIterations);
  
  runner.printSummary();
}

/**
 * Agent Core Benchmarks
 */
async function benchmarkAgentCore(): Promise<void> {
  const runner = new BenchmarkRunner();
  
  console.log('\n🤖 Agent Core Performance Benchmarks');
  console.log('=' .repeat(50));
  
  await runner.runBenchmark('Message Processing', async () => {
    const agent = new AgentCore({
      enableStreaming: false, // Disable streaming for pure processing benchmark
      enableMetrics: false
    });
    
    await agent.start();
    
    const promises = [];
    for (let i = 0; i < 100; i++) {
      const message = {
        id: uuidv4(),
        role: 'user' as const,
        content: `Benchmark message ${i}`,
        timestamp: new Date()
      };
      
      promises.push(agent.processMessage(message));
    }
    
    const responses = await Promise.all(promises);
    
    // Consume all streams
    for (const response of responses) {
      for await (const chunk of response.chunks) {
        if (chunk.type === 'done') break;
      }
    }
    
    await agent.stop();
  }, 3); // Fewer iterations for agent benchmarks
  
  await runner.runBenchmark('Streaming Response', async () => {
    const agent = new AgentCore({
      enableStreaming: true,
      streamingChunkSize: 100
    });
    
    await agent.start();
    
    const message = {
      id: uuidv4(),
      role: 'user' as const,
      content: 'Generate a long response for streaming benchmark',
      timestamp: new Date()
    };
    
    const response = await agent.processMessage(message);
    
    let chunkCount = 0;
    for await (const chunk of response.chunks) {
      chunkCount++;
      if (chunk.type === 'done') break;
    }
    
    await agent.stop();
  }, 3);
  
  runner.printSummary();
}

/**
 * Memory Usage Benchmarks
 */
async function benchmarkMemoryUsage(): Promise<void> {
  console.log('\n💾 Memory Usage Benchmarks');
  console.log('=' .repeat(50));
  
  const getMemoryUsage = () => {
    const usage = process.memoryUsage();
    return {
      heapUsed: Math.round(usage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(usage.heapTotal / 1024 / 1024),
      external: Math.round(usage.external / 1024 / 1024)
    };
  };
  
  console.log('Initial memory usage:', getMemoryUsage());
  
  // Test message queue memory usage
  const queue = new h2AMessageQueue({ maxBufferSize: 100000 });
  await queue.start();
  
  console.log('After queue creation:', getMemoryUsage());
  
  // Fill queue with messages
  for (let i = 0; i < 50000; i++) {
    await queue.enqueue(createTestMessage(`memory-test-${i}`));
  }
  
  console.log('After filling queue (50k messages):', getMemoryUsage());
  
  // Clear queue
  await queue.clear();
  
  if (global.gc) {
    global.gc();
  }
  
  console.log('After clearing queue:', getMemoryUsage());
  
  await queue.stop();
}

/**
 * Utility functions
 */
function createTestMessage(content: string, priority: MessagePriority = MessagePriority.NORMAL, destination?: string): Message {
  return {
    id: uuidv4(),
    timestamp: new Date(),
    type: 'benchmark_message',
    payload: { content },
    priority,
    metadata: destination ? { destination } : undefined
  };
}

/**
 * Main benchmark runner
 */
async function runAllBenchmarks(): Promise<void> {
  console.log('🏁 Claude Code 3.0 Performance Benchmarks');
  console.log('=' .repeat(50));
  console.log(`Configuration:`);
  console.log(`  Message Count: ${BENCHMARK_CONFIG.messageCount}`);
  console.log(`  Concurrent Ops: ${BENCHMARK_CONFIG.concurrentOperations}`);
  console.log(`  Iterations: ${BENCHMARK_CONFIG.measurementIterations}`);
  
  const startTime = performance.now();
  
  try {
    await benchmarkMessageQueue();
    await benchmarkSteeringManager();
    await benchmarkAgentCore();
    await benchmarkMemoryUsage();
    
    const totalTime = performance.now() - startTime;
    console.log(`\n✅ All benchmarks completed in ${(totalTime / 1000).toFixed(2)}s`);
    
  } catch (error) {
    console.error('❌ Benchmark failed:', error);
    process.exit(1);
  }
}

// Run benchmarks if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllBenchmarks().catch(console.error);
}

export { runAllBenchmarks, benchmarkMessageQueue, benchmarkSteeringManager, benchmarkAgentCore };
