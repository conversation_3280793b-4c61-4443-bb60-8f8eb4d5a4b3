#!/usr/bin/env node
/**
 * <PERSON><PERSON> vs Real LLM Performance Comparison
 * Demonstrates the difference between mock responses and real LLM integration
 */
declare class MockVsRealComparison {
    apiUrl: string;
    results: {
        mock: never[];
        real: never[];
    };
    sendMessage(content: any, sessionId?: string): Promise<any>;
    testResponseVariability(): Promise<any[]>;
    testComplexityHandling(): Promise<void>;
    testAgentSpecialization(): Promise<void>;
    testPerformanceMetrics(): Promise<{
        beforeMetrics: any;
        afterMetrics: any;
        testResults: any[];
    }>;
    runComparison(): Promise<void>;
}
declare const comparison: MockVsRealComparison;
//# sourceMappingURL=mock-vs-real-comparison.d.ts.map