/**
 * Claude Code 3.0 - h2A Message Queue Tests
 * 
 * Comprehensive test suite for the h2A dual-buffer async message queue system.
 */

import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { v4 as uuidv4 } from 'uuid';
import {
  h2AMessageQueue,
  createMessageQueue,
  createHighPerformanceQueue,
  createMemoryEfficientQueue
} from '../../../src/layers/steering/message-queue.js';
import {
  Message,
  MessagePriority,
  QueueStatus,
  QueueEventType,
  BackpressureStrategy
} from '../../../src/types/message-queue.js';

// ============================================================================
// Test Utilities
// ============================================================================

function createTestMessage<T = any>(payload: T, priority: MessagePriority = MessagePriority.NORMAL): Message<T> {
  return {
    id: uuidv4(),
    timestamp: new Date(),
    type: 'test_message',
    payload,
    priority,
    metadata: {
      source: 'test'
    }
  };
}

// ============================================================================
// Core Queue Tests
// ============================================================================

describe('h2AMessageQueue', () => {
  let queue: h2AMessageQueue;

  beforeEach(() => {
    queue = new h2AMessageQueue({
      maxBufferSize: 100,
      enableMetrics: true,
      enableHealthCheck: false // Disable for tests
    });
  });

  afterEach(async () => {
    await queue.stop();
  });

  describe('Basic Operations', () => {
    test('should enqueue and dequeue messages', async () => {
      const message = createTestMessage('test payload');
      
      const enqueued = await queue.enqueue(message);
      expect(enqueued).toBe(true);
      
      const dequeued = await queue.dequeue();
      expect(dequeued).toEqual(message);
    });

    test('should return null when dequeuing from empty queue', async () => {
      const result = await queue.dequeue();
      expect(result).toBeNull();
    });

    test('should peek at next message without removing it', async () => {
      const message = createTestMessage('test payload');
      await queue.enqueue(message);
      
      const peeked = await queue.peek();
      expect(peeked).toEqual(message);
      
      const dequeued = await queue.dequeue();
      expect(dequeued).toEqual(message);
    });

    test('should clear all messages', async () => {
      await queue.enqueue(createTestMessage('message 1'));
      await queue.enqueue(createTestMessage('message 2'));
      
      await queue.clear();
      
      const result = await queue.dequeue();
      expect(result).toBeNull();
    });
  });

  describe('Priority Handling', () => {
    test('should process high priority messages first', async () => {
      const lowPriorityMessage = createTestMessage('low', MessagePriority.LOW);
      const highPriorityMessage = createTestMessage('high', MessagePriority.HIGH);
      const normalPriorityMessage = createTestMessage('normal', MessagePriority.NORMAL);
      
      await queue.enqueue(lowPriorityMessage);
      await queue.enqueue(normalPriorityMessage);
      await queue.enqueue(highPriorityMessage);
      
      const first = await queue.dequeue();
      const second = await queue.dequeue();
      const third = await queue.dequeue();
      
      expect(first?.payload).toBe('high');
      expect(second?.payload).toBe('normal');
      expect(third?.payload).toBe('low');
    });

    test('should handle critical priority messages', async () => {
      const criticalMessage = createTestMessage('critical', MessagePriority.CRITICAL);
      const highMessage = createTestMessage('high', MessagePriority.HIGH);
      
      await queue.enqueue(highMessage);
      await queue.enqueue(criticalMessage);
      
      const first = await queue.dequeue();
      expect(first?.payload).toBe('critical');
    });
  });

  describe('Batch Operations', () => {
    test('should enqueue multiple messages in batch', async () => {
      const messages = [
        createTestMessage('message 1'),
        createTestMessage('message 2'),
        createTestMessage('message 3')
      ];
      
      const successCount = await queue.enqueueBatch(messages);
      expect(successCount).toBe(3);
      
      const state = queue.getState();
      expect(state.totalMessages).toBe(3);
    });

    test('should dequeue multiple messages in batch', async () => {
      const messages = [
        createTestMessage('message 1'),
        createTestMessage('message 2'),
        createTestMessage('message 3')
      ];
      
      await queue.enqueueBatch(messages);
      
      const dequeuedMessages = await queue.dequeueBatch(2);
      expect(dequeuedMessages).toHaveLength(2);
      
      const remaining = await queue.dequeue();
      expect(remaining).not.toBeNull();
    });
  });

  describe('State and Metrics', () => {
    test('should track queue state correctly', async () => {
      const initialState = queue.getState();
      expect(initialState.status).toBe(QueueStatus.IDLE);
      expect(initialState.totalMessages).toBe(0);
      
      await queue.enqueue(createTestMessage('test'));
      
      const updatedState = queue.getState();
      expect(updatedState.totalMessages).toBe(1);
    });

    test('should provide metrics', async () => {
      const metrics = queue.getMetrics();
      
      expect(metrics).toHaveProperty('messagesProcessed');
      expect(metrics).toHaveProperty('messagesDropped');
      expect(metrics).toHaveProperty('bufferUtilization');
      expect(metrics).toHaveProperty('errorCount');
    });

    test('should report health status', async () => {
      expect(queue.isHealthy()).toBe(true);
      
      // Fill queue to near capacity to test health reporting
      const messages = Array.from({ length: 95 }, (_, i) => createTestMessage(`message ${i}`));
      await queue.enqueueBatch(messages);
      
      // Should still be healthy at 95% capacity
      expect(queue.isHealthy()).toBe(true);
    });
  });

  describe('Event System', () => {
    test('should emit events for queue operations', async () => {
      const enqueuedEvents: any[] = [];
      const dequeuedEvents: any[] = [];
      
      queue.on(QueueEventType.MESSAGE_ENQUEUED, (event) => {
        enqueuedEvents.push(event);
      });
      
      queue.on(QueueEventType.MESSAGE_DEQUEUED, (event) => {
        dequeuedEvents.push(event);
      });
      
      const message = createTestMessage('test');
      await queue.enqueue(message);
      await queue.dequeue();
      
      expect(enqueuedEvents).toHaveLength(1);
      expect(dequeuedEvents).toHaveLength(1);
      expect(enqueuedEvents[0].message).toEqual(message);
    });

    test('should emit backpressure events', async () => {
      const backpressureEvents: any[] = [];
      
      queue.on(QueueEventType.BACKPRESSURE_ACTIVATED, (event) => {
        backpressureEvents.push(event);
      });
      
      // Create a small queue to trigger backpressure
      const smallQueue = new h2AMessageQueue({
        maxBufferSize: 5,
        backpressureThreshold: 0.8
      });
      
      smallQueue.on(QueueEventType.BACKPRESSURE_ACTIVATED, (event) => {
        backpressureEvents.push(event);
      });
      
      // Fill queue beyond backpressure threshold
      const messages = Array.from({ length: 10 }, (_, i) => createTestMessage(`message ${i}`));
      await smallQueue.enqueueBatch(messages);
      
      expect(backpressureEvents.length).toBeGreaterThan(0);
      
      await smallQueue.stop();
    });
  });

  describe('Lifecycle Management', () => {
    test('should start and stop correctly', async () => {
      expect(queue.getState().status).toBe(QueueStatus.IDLE);
      
      await queue.start();
      expect(queue.getState().status).toBe(QueueStatus.PROCESSING);
      
      await queue.stop();
      expect(queue.getState().status).toBe(QueueStatus.SHUTDOWN);
    });

    test('should pause and resume correctly', async () => {
      await queue.start();
      
      await queue.pause();
      expect(queue.getState().status).toBe(QueueStatus.IDLE);
      
      await queue.resume();
      expect(queue.getState().status).toBe(QueueStatus.PROCESSING);
    });
  });
});

// ============================================================================
// Factory Function Tests
// ============================================================================

describe('Factory Functions', () => {
  test('createMessageQueue should create standard queue', () => {
    const queue = createMessageQueue();
    expect(queue).toBeInstanceOf(h2AMessageQueue);
  });

  test('createHighPerformanceQueue should create optimized queue', () => {
    const queue = createHighPerformanceQueue();
    expect(queue).toBeInstanceOf(h2AMessageQueue);
    
    const state = queue.getState();
    // High performance queue should have larger buffers
    expect(state.primaryBufferSize + state.secondaryBufferSize).toBeLessThanOrEqual(10000); // 2 * 5000
  });

  test('createMemoryEfficientQueue should create small queue', () => {
    const queue = createMemoryEfficientQueue();
    expect(queue).toBeInstanceOf(h2AMessageQueue);
    
    const state = queue.getState();
    // Memory efficient queue should have smaller buffers
    expect(state.primaryBufferSize + state.secondaryBufferSize).toBeLessThanOrEqual(200); // 2 * 100
  });
});

// ============================================================================
// Backpressure Strategy Tests
// ============================================================================

describe('Backpressure Strategies', () => {
  test('DROP_OLDEST strategy should remove oldest messages', async () => {
    const queue = new h2AMessageQueue({
      maxBufferSize: 3,
      backpressureStrategy: BackpressureStrategy.DROP_OLDEST,
      backpressureThreshold: 0.8
    });

    // Fill queue
    await queue.enqueue(createTestMessage('message 1'));
    await queue.enqueue(createTestMessage('message 2'));
    await queue.enqueue(createTestMessage('message 3'));
    
    // This should trigger backpressure and drop the oldest
    await queue.enqueue(createTestMessage('message 4'));
    
    const first = await queue.dequeue();
    expect(first?.payload).toBe('message 2'); // message 1 should be dropped
    
    await queue.stop();
  });

  test('DROP_NEWEST strategy should reject new messages', async () => {
    const queue = new h2AMessageQueue({
      maxBufferSize: 2,
      backpressureStrategy: BackpressureStrategy.DROP_NEWEST,
      backpressureThreshold: 0.8
    });

    await queue.enqueue(createTestMessage('message 1'));
    await queue.enqueue(createTestMessage('message 2'));
    
    // This should be rejected
    const result = await queue.enqueue(createTestMessage('message 3'));
    expect(result).toBe(false);
    
    const first = await queue.dequeue();
    expect(first?.payload).toBe('message 1');
    
    await queue.stop();
  });
});
