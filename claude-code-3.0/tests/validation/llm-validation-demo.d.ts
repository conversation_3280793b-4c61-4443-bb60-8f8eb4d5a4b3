#!/usr/bin/env node
/**
 * LLM Integration Validation Demo
 * Demonstrates real vs mock responses and system capabilities
 */
declare class LLMValidationDemo {
    apiUrl: string;
    sendMessage(content: any, sessionId?: string): Promise<any>;
    demonstrateCodeGeneration(): Promise<any>;
    demonstrateTechnicalExplanation(): Promise<any>;
    demonstrateAgentSelection(): Promise<any[]>;
    demonstratePerformanceMetrics(): Promise<{
        metrics: any;
        concurrentResults: any[];
    }>;
    demonstrateResponseQuality(): Promise<void>;
    runFullDemo(): Promise<void>;
}
declare const demo: LLMValidationDemo;
//# sourceMappingURL=llm-validation-demo.d.ts.map