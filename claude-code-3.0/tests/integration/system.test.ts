/**
 * Claude Code 3.0 - System Integration Tests
 * 
 * End-to-end tests for the complete system integration.
 */

import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { ClaudeCodeSystem } from '../../src/core/system.js';
import { AgentCore } from '../../src/layers/agent/agent-core.js';
import { SteeringManager } from '../../src/layers/steering/steering-manager.js';
import { v4 as uuidv4 } from 'uuid';

describe('System Integration Tests', () => {
  let system: ClaudeCodeSystem;

  beforeEach(async () => {
    // Create system with test configuration
    system = new ClaudeCodeSystem({
      environment: 'test',
      enableDebugMode: true,
      enableMetrics: true,
      layers: {
        steering: {
          enabled: true,
          bufferSize: 100,
          enableMetrics: true
        },
        agent: {
          enabled: true,
          maxConcurrentAgents: 2,
          enableMetrics: true
        },
        ui: {
          enabled: false // Disable UI for tests
        },
        cli: {
          enabled: false // Disable CLI for tests
        }
      }
    });
  });

  afterEach(async () => {
    if (system) {
      await system.stop();
    }
  });

  describe('System Lifecycle', () => {
    test('should initialize and start system successfully', async () => {
      await system.initialize();
      
      const initialState = system.getState();
      expect(initialState.status).toBe('stopped');
      
      await system.start();
      
      const runningState = system.getState();
      expect(runningState.status).toBe('running');
      expect(runningState.layers.steering.initialized).toBe(true);
    });

    test('should stop system gracefully', async () => {
      await system.initialize();
      await system.start();
      
      expect(system.getState().status).toBe('running');
      
      await system.stop();
      
      expect(system.getState().status).toBe('stopped');
    });

    test('should pause and resume system', async () => {
      await system.initialize();
      await system.start();
      
      await system.pause();
      expect(system.getState().status).toBe('paused');
      
      await system.resume();
      expect(system.getState().status).toBe('running');
    });
  });

  describe('Message Routing', () => {
    test('should route messages through steering layer', async () => {
      await system.initialize();
      await system.start();
      
      const message = {
        id: uuidv4(),
        timestamp: new Date(),
        type: 'test_message',
        payload: { content: 'test message' },
        priority: 1,
        metadata: { destination: 'agent' }
      };
      
      const result = await system.sendMessage(message);
      expect(result).toBe(true);
    });

    test('should handle message routing errors gracefully', async () => {
      await system.initialize();
      await system.start();
      
      const invalidMessage = {
        // Missing required fields
        type: 'invalid_message'
      };
      
      const result = await system.sendMessage(invalidMessage);
      expect(result).toBe(false);
    });
  });

  describe('System Health', () => {
    test('should report healthy status when running normally', async () => {
      await system.initialize();
      await system.start();
      
      // Wait a bit for system to stabilize
      await new Promise(resolve => setTimeout(resolve, 100));
      
      expect(system.isHealthy()).toBe(true);
    });

    test('should provide system metrics', async () => {
      await system.initialize();
      await system.start();
      
      const metrics = system.getMetrics();
      
      expect(metrics).toHaveProperty('totalRequests');
      expect(metrics).toHaveProperty('memoryUsage');
      expect(metrics.memoryUsage).toBeGreaterThan(0);
    });

    test('should track system state correctly', async () => {
      await system.initialize();
      await system.start();
      
      const state = system.getState();
      
      expect(state).toHaveProperty('status');
      expect(state).toHaveProperty('startTime');
      expect(state).toHaveProperty('uptime');
      expect(state).toHaveProperty('layers');
      expect(state.uptime).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Error Handling', () => {
    test('should handle initialization errors', async () => {
      // Create system with invalid configuration
      const invalidSystem = new ClaudeCodeSystem({
        layers: {
          steering: {
            enabled: true,
            bufferSize: -1 // Invalid buffer size
          }
        }
      });
      
      // Should not throw, but should handle gracefully
      await expect(invalidSystem.initialize()).resolves.not.toThrow();
    });

    test('should emit error events', async () => {
      await system.initialize();
      await system.start();
      
      const errorEvents: any[] = [];
      system.on('system_error', (error) => {
        errorEvents.push(error);
      });
      
      // Trigger an error by sending invalid message
      await system.sendMessage(null);
      
      // Wait for error to be processed
      await new Promise(resolve => setTimeout(resolve, 50));
      
      // Note: This test might not trigger errors in the current implementation
      // but demonstrates the error handling pattern
    });
  });
});

describe('Agent Integration Tests', () => {
  let agent: AgentCore;

  beforeEach(() => {
    agent = new AgentCore({
      model: 'test-model',
      enableStreaming: true,
      enableMetrics: true
    });
  });

  afterEach(async () => {
    if (agent) {
      await agent.stop();
    }
  });

  describe('Agent Lifecycle', () => {
    test('should start and stop agent successfully', async () => {
      await agent.start();
      
      const state = agent.getState();
      expect(state.status).toBe('idle');
      
      await agent.stop();
    });

    test('should process messages with streaming', async () => {
      await agent.start();
      
      const message = {
        id: uuidv4(),
        role: 'user' as const,
        content: 'Hello, test message',
        timestamp: new Date()
      };
      
      const response = await agent.processMessage(message);
      
      expect(response).toHaveProperty('id');
      expect(response).toHaveProperty('chunks');
      expect(response).toHaveProperty('metadata');
      
      // Collect chunks
      const chunks = [];
      for await (const chunk of response.chunks) {
        chunks.push(chunk);
        if (chunk.type === 'done') break;
      }
      
      expect(chunks.length).toBeGreaterThan(0);
      expect(chunks[chunks.length - 1].type).toBe('done');
    });

    test('should track agent state during processing', async () => {
      await agent.start();
      
      const initialState = agent.getState();
      expect(initialState.activeRequests).toBe(0);
      expect(initialState.totalRequests).toBe(0);
      
      const message = {
        id: uuidv4(),
        role: 'user' as const,
        content: 'Test message',
        timestamp: new Date()
      };
      
      const responsePromise = agent.processMessage(message);
      
      // Check state during processing
      const processingState = agent.getState();
      expect(processingState.totalRequests).toBe(1);
      
      // Wait for completion
      const response = await responsePromise;
      
      // Consume the stream
      for await (const chunk of response.chunks) {
        if (chunk.type === 'done') break;
      }
      
      const finalState = agent.getState();
      expect(finalState.activeRequests).toBe(0);
      expect(finalState.totalRequests).toBe(1);
    });
  });

  describe('Session Management', () => {
    test('should create and manage sessions', async () => {
      await agent.start();
      
      const sessionId = uuidv4();
      const message = {
        id: uuidv4(),
        role: 'user' as const,
        content: 'Test message',
        timestamp: new Date()
      };
      
      const response = await agent.processMessage(message, sessionId);
      
      // Consume the stream
      for await (const chunk of response.chunks) {
        if (chunk.type === 'done') break;
      }
      
      const sessions = agent.getActiveSessions();
      expect(sessions.length).toBe(1);
      expect(sessions[0].id).toBe(sessionId);
    });

    test('should maintain conversation history', async () => {
      await agent.start();
      
      const sessionId = uuidv4();
      
      // Send first message
      const message1 = {
        id: uuidv4(),
        role: 'user' as const,
        content: 'First message',
        timestamp: new Date()
      };
      
      const response1 = await agent.processMessage(message1, sessionId);
      for await (const chunk of response1.chunks) {
        if (chunk.type === 'done') break;
      }
      
      // Send second message
      const message2 = {
        id: uuidv4(),
        role: 'user' as const,
        content: 'Second message',
        timestamp: new Date()
      };
      
      const response2 = await agent.processMessage(message2, sessionId);
      for await (const chunk of response2.chunks) {
        if (chunk.type === 'done') break;
      }
      
      const sessions = agent.getActiveSessions();
      const conversation = agent.getConversation(sessions[0].conversationId);
      
      expect(conversation).toBeDefined();
      expect(conversation!.messages.length).toBeGreaterThanOrEqual(2);
    });
  });
});

describe('Steering Integration Tests', () => {
  let steering: SteeringManager;

  beforeEach(() => {
    steering = new SteeringManager({
      enableRealTimeProcessing: true,
      steeringInterval: 50,
      queueConfig: {
        maxBufferSize: 100,
        enableMetrics: true
      }
    });
  });

  afterEach(async () => {
    if (steering) {
      await steering.stop();
    }
  });

  describe('Message Routing', () => {
    test('should route messages to appropriate queues', async () => {
      await steering.start();
      
      const agentMessage = {
        id: uuidv4(),
        timestamp: new Date(),
        type: 'agent_request',
        payload: { content: 'test' },
        priority: 1,
        metadata: { destination: 'agent' }
      };
      
      const result = await steering.routeMessage(agentMessage);
      expect(result).toBe(true);
    });

    test('should handle multiple message types', async () => {
      await steering.start();
      
      const messages = [
        {
          id: uuidv4(),
          timestamp: new Date(),
          type: 'agent_request',
          payload: { content: 'agent message' },
          priority: 1,
          metadata: { destination: 'agent' }
        },
        {
          id: uuidv4(),
          timestamp: new Date(),
          type: 'ui_update',
          payload: { content: 'ui message' },
          priority: 1,
          metadata: { destination: 'ui' }
        },
        {
          id: uuidv4(),
          timestamp: new Date(),
          type: 'tool_execute',
          payload: { content: 'tool message' },
          priority: 1,
          metadata: { destination: 'tool' }
        }
      ];
      
      const results = await Promise.all(
        messages.map(msg => steering.routeMessage(msg))
      );
      
      expect(results.every(result => result === true)).toBe(true);
    });
  });

  describe('System Monitoring', () => {
    test('should provide system metrics', async () => {
      await steering.start();
      
      const metrics = steering.getSystemMetrics();
      
      expect(metrics).toHaveProperty('agent');
      expect(metrics).toHaveProperty('ui');
      expect(metrics).toHaveProperty('tool');
      expect(metrics).toHaveProperty('system');
      expect(metrics).toHaveProperty('steering');
    });

    test('should track steering state', async () => {
      await steering.start();
      
      const state = steering.getState();
      
      expect(state).toHaveProperty('isActive');
      expect(state).toHaveProperty('currentLoad');
      expect(state).toHaveProperty('throttleLevel');
      expect(state.isActive).toBe(true);
    });
  });
});
