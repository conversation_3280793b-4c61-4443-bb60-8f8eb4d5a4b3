#!/usr/bin/env node
export default LLMIntegrationTester;
/**
 * Comprehensive LLM Integration Test Suite
 * Tests the Claude Code 3.0 multi-agent system with real local LLM models
 */
declare class LLMIntegrationTester {
    apiUrl: string;
    testResults: any[];
    startTime: number;
    runTest(testName: any, testFn: any): Promise<any>;
    testCodeGeneration(): Promise<any>;
    testTechnicalExplanation(): Promise<any>;
    testGeneralQuery(): Promise<any>;
    testSystemAnalysis(): Promise<any>;
    testEmptyMessage(): Promise<any>;
    testLongInput(): Promise<any>;
    testConcurrentRequests(): Promise<any>;
    testAgentSelection(): Promise<any>;
    testResponseTime(): Promise<any>;
    runAllTests(): Promise<void>;
    printSummary(): void;
}
//# sourceMappingURL=llm-integration-test.d.ts.map