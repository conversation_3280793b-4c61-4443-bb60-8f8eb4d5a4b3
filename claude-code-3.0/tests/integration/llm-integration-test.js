#!/usr/bin/env node

/**
 * Comprehensive LLM Integration Test Suite
 * Tests the Claude Code 3.0 multi-agent system with real local LLM models
 */

// Using built-in fetch (Node.js 18+)

class LLMIntegrationTester {
  constructor() {
    this.apiUrl = 'http://localhost:8080';
    this.testResults = [];
    this.startTime = Date.now();
  }

  async runTest(testName, testFn) {
    console.log(`\n🧪 Running: ${testName}`);
    const start = Date.now();
    
    try {
      const result = await testFn();
      const duration = Date.now() - start;
      
      this.testResults.push({
        name: testName,
        status: 'PASS',
        duration,
        result
      });
      
      console.log(`✅ PASS: ${testName} (${duration}ms)`);
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      
      this.testResults.push({
        name: testName,
        status: 'FAIL',
        duration,
        error: error.message
      });
      
      console.log(`❌ FAIL: ${testName} (${duration}ms) - ${error.message}`);
      throw error;
    }
  }

  async testCodeGeneration() {
    return this.runTest('Code Generation Request', async () => {
      const response = await fetch(`${this.apiUrl}/api/messages`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: 'Create a TypeScript function that implements binary search',
          sessionId: 'test-code-gen',
          capabilities: ['code-generation']
        })
      });

      const data = await response.json();
      
      // Validate response structure
      if (!data.content || !data.isRealLLM || !data.model) {
        throw new Error('Invalid response structure');
      }
      
      // Validate it's from real LLM
      if (data.model !== 'qwen2.5:3b') {
        throw new Error('Response not from expected LLM model');
      }
      
      // Validate processing time is realistic (should be > 1 second for real LLM)
      if (data.processingTime < 1.0) {
        throw new Error('Processing time too fast for real LLM');
      }
      
      // Validate content contains code
      if (!data.content.includes('function') && !data.content.includes('typescript')) {
        throw new Error('Response does not contain expected code content');
      }
      
      return {
        agentId: data.agentId,
        processingTime: data.processingTime,
        contentLength: data.content.length,
        hasCode: data.content.includes('```')
      };
    });
  }

  async testTechnicalExplanation() {
    return this.runTest('Technical Explanation Request', async () => {
      const response = await fetch(`${this.apiUrl}/api/messages`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: 'Explain how machine learning algorithms work and their applications',
          sessionId: 'test-tech-explain',
          capabilities: ['text-processing']
        })
      });

      const data = await response.json();
      
      if (!data.isRealLLM || data.processingTime < 1.0) {
        throw new Error('Not a real LLM response');
      }
      
      if (data.content.length < 200) {
        throw new Error('Response too short for technical explanation');
      }
      
      return {
        agentId: data.agentId,
        processingTime: data.processingTime,
        contentLength: data.content.length
      };
    });
  }

  async testGeneralQuery() {
    return this.runTest('General Query Request', async () => {
      const response = await fetch(`${this.apiUrl}/api/messages`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: 'What are the benefits of using TypeScript over JavaScript?',
          sessionId: 'test-general',
          capabilities: ['general']
        })
      });

      const data = await response.json();
      
      if (!data.isRealLLM) {
        throw new Error('Not a real LLM response');
      }
      
      return {
        agentId: data.agentId,
        processingTime: data.processingTime,
        contentLength: data.content.length
      };
    });
  }

  async testSystemAnalysis() {
    return this.runTest('System Analysis Request', async () => {
      const response = await fetch(`${this.apiUrl}/api/messages`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: 'Analyze the performance characteristics of different sorting algorithms',
          sessionId: 'test-analysis',
          capabilities: ['specialized']
        })
      });

      const data = await response.json();
      
      if (!data.isRealLLM || data.processingTime < 1.0) {
        throw new Error('Not a real LLM response');
      }
      
      return {
        agentId: data.agentId,
        processingTime: data.processingTime,
        contentLength: data.content.length
      };
    });
  }

  async testEmptyMessage() {
    return this.runTest('Empty Message Handling', async () => {
      const response = await fetch(`${this.apiUrl}/api/messages`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: '',
          sessionId: 'test-empty'
        })
      });

      const data = await response.json();
      
      // Should still get a response, even for empty input
      if (!data.content) {
        throw new Error('No response for empty message');
      }
      
      return {
        agentId: data.agentId,
        processingTime: data.processingTime,
        contentLength: data.content.length
      };
    });
  }

  async testLongInput() {
    return this.runTest('Long Input Handling', async () => {
      const longContent = 'Explain in detail '.repeat(50) + 'how neural networks work and their applications in modern AI systems.';
      
      const response = await fetch(`${this.apiUrl}/api/messages`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: longContent,
          sessionId: 'test-long'
        })
      });

      const data = await response.json();
      
      if (!data.isRealLLM) {
        throw new Error('Not a real LLM response');
      }
      
      return {
        agentId: data.agentId,
        processingTime: data.processingTime,
        inputLength: longContent.length,
        outputLength: data.content.length
      };
    });
  }

  async testConcurrentRequests() {
    return this.runTest('Concurrent Requests', async () => {
      const requests = [
        'Write a Python function for quicksort',
        'Explain database indexing',
        'What is machine learning?',
        'Create a React component',
        'Describe REST APIs'
      ];

      const promises = requests.map((content, index) => 
        fetch(`${this.apiUrl}/api/messages`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            content,
            sessionId: `test-concurrent-${index}`
          })
        }).then(res => res.json())
      );

      const results = await Promise.all(promises);
      
      // Validate all responses are from real LLM
      for (const result of results) {
        if (!result.isRealLLM) {
          throw new Error('Not all responses from real LLM');
        }
      }
      
      // Check agent distribution
      const agentIds = results.map(r => r.agentId);
      const uniqueAgents = new Set(agentIds);
      
      return {
        totalRequests: results.length,
        uniqueAgents: uniqueAgents.size,
        avgProcessingTime: results.reduce((sum, r) => sum + r.processingTime, 0) / results.length,
        allFromRealLLM: results.every(r => r.isRealLLM)
      };
    });
  }

  async testAgentSelection() {
    return this.runTest('Agent Selection Logic', async () => {
      const testCases = [
        { content: 'Write JavaScript code', expectedCapability: 'code-generation' },
        { content: 'Analyze this text', expectedCapability: 'text-processing' },
        { content: 'Complex technical analysis', expectedCapability: 'specialized' }
      ];

      const results = [];
      
      for (const testCase of testCases) {
        const response = await fetch(`${this.apiUrl}/api/messages`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            content: testCase.content,
            sessionId: `test-agent-${Date.now()}`
          })
        });

        const data = await response.json();
        results.push({
          content: testCase.content,
          agentId: data.agentId,
          processingTime: data.processingTime
        });
      }
      
      return {
        testCases: results.length,
        allFromRealLLM: results.every(r => r.processingTime > 1.0)
      };
    });
  }

  async testResponseTime() {
    return this.runTest('Response Time Validation', async () => {
      const start = Date.now();
      
      const response = await fetch(`${this.apiUrl}/api/messages`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: 'Quick test message',
          sessionId: 'test-timing'
        })
      });

      const data = await response.json();
      const totalTime = Date.now() - start;
      
      // Validate response time is reasonable (under 10 seconds)
      if (totalTime > 10000) {
        throw new Error('Response time too slow');
      }
      
      // Validate processing time is realistic for LLM
      if (data.processingTime < 0.5) {
        throw new Error('Processing time too fast for real LLM');
      }
      
      return {
        totalTime,
        processingTime: data.processingTime,
        networkTime: totalTime - (data.processingTime * 1000),
        isRealLLM: data.isRealLLM
      };
    });
  }

  async runAllTests() {
    console.log('🚀 Starting Comprehensive LLM Integration Test Suite');
    console.log('=' .repeat(60));
    
    try {
      // Test individual capabilities
      await this.testCodeGeneration();
      await this.testTechnicalExplanation();
      await this.testGeneralQuery();
      await this.testSystemAnalysis();
      
      // Test edge cases
      await this.testEmptyMessage();
      await this.testLongInput();
      
      // Test system capabilities
      await this.testConcurrentRequests();
      await this.testAgentSelection();
      await this.testResponseTime();
      
      this.printSummary();
      
    } catch (error) {
      console.error('\n💥 Test suite failed:', error.message);
      this.printSummary();
      process.exit(1);
    }
  }

  printSummary() {
    const totalTime = Date.now() - this.startTime;
    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;
    
    console.log('\n' + '=' .repeat(60));
    console.log('📊 TEST SUMMARY');
    console.log('=' .repeat(60));
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⏱️  Total Time: ${totalTime}ms`);
    console.log(`🎯 Success Rate: ${((passed / this.testResults.length) * 100).toFixed(1)}%`);
    
    if (failed === 0) {
      console.log('\n🎉 ALL TESTS PASSED! LLM Integration is working correctly.');
    } else {
      console.log('\n⚠️  Some tests failed. Check the details above.');
    }
  }
}

// Run the test suite
const tester = new LLMIntegrationTester();
tester.runAllTests().catch(console.error);

export default LLMIntegrationTester;
