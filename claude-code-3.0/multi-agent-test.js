#!/usr/bin/env node

/**
 * Claude Code 3.0 - Multi-Agent System Test
 * 
 * Tests the multi-agent framework with concurrent operations,
 * load balancing, and inter-agent communication.
 */

import { performance } from 'perf_hooks';

// Simple multi-agent implementation for testing
class SimpleMultiAgentManager {
  constructor(config = {}) {
    this.config = {
      maxAgents: 5,
      loadBalancingStrategy: 'least-loaded',
      enableInterAgentCommunication: true,
      ...config
    };
    
    this.agents = new Map();
    this.requestQueue = [];
    this.metrics = {
      totalAgents: 0,
      activeAgents: 0,
      totalRequests: 0,
      averageResponseTime: 0
    };
  }
  
  async start() {
    console.log('🤖 Starting Multi-Agent Manager...');
    
    // Spawn initial agents
    for (let i = 0; i < 3; i++) {
      await this.spawnAgent(`agent-${i}`, ['general', 'text-processing']);
    }
    
    console.log(`✅ Multi-Agent Manager started with ${this.agents.size} agents`);
  }
  
  async stop() {
    console.log('🛑 Stopping Multi-Agent Manager...');
    
    for (const [agentId, agentInfo] of this.agents) {
      agentInfo.status = 'terminated';
    }
    
    this.agents.clear();
    console.log('✅ Multi-Agent Manager stopped');
  }
  
  async spawnAgent(agentId, capabilities = []) {
    if (this.agents.size >= this.config.maxAgents) {
      throw new Error(`Maximum agent limit reached: ${this.config.maxAgents}`);
    }
    
    const agentInfo = {
      id: agentId,
      status: 'idle',
      capabilities,
      currentLoad: 0,
      totalRequests: 0,
      lastActivity: new Date(),
      spawnTime: new Date()
    };
    
    this.agents.set(agentId, agentInfo);
    this.updateMetrics();
    
    console.log(`🤖 Agent spawned: ${agentId} with capabilities: [${capabilities.join(', ')}]`);
    return agentId;
  }
  
  async terminateAgent(agentId) {
    const agentInfo = this.agents.get(agentId);
    if (!agentInfo) return false;
    
    agentInfo.status = 'terminated';
    this.agents.delete(agentId);
    this.updateMetrics();
    
    console.log(`🗑️  Agent terminated: ${agentId}`);
    return true;
  }
  
  async processMessage(message, sessionId, requiredCapabilities) {
    return new Promise((resolve, reject) => {
      const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      this.requestQueue.push({
        id: requestId,
        message,
        sessionId,
        resolve,
        reject,
        timestamp: new Date(),
        requiredCapabilities
      });
      
      setImmediate(() => this.processRequestQueue());
      this.metrics.totalRequests++;
    });
  }
  
  async processRequestQueue() {
    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift();
      if (!request) continue;
      
      try {
        const selectedAgent = this.selectAgent(request.requiredCapabilities);
        
        if (!selectedAgent) {
          if (this.agents.size < this.config.maxAgents) {
            const newAgentId = `agent-${this.agents.size}`;
            await this.spawnAgent(newAgentId, request.requiredCapabilities || ['general']);
            const newAgent = this.agents.get(newAgentId);
            if (newAgent) {
              await this.processRequestWithAgent(request, newAgent);
            } else {
              request.reject(new Error('Failed to spawn agent'));
            }
          } else {
            // Wait a bit and retry with any available agent
            await new Promise(resolve => setTimeout(resolve, 10));
            const anyAgent = Array.from(this.agents.values()).find(a => a.status !== 'terminated');
            if (anyAgent) {
              await this.processRequestWithAgent(request, anyAgent);
            } else {
              request.reject(new Error('No available agents'));
            }
          }
        } else {
          await this.processRequestWithAgent(request, selectedAgent);
        }
      } catch (error) {
        request.reject(error);
      }
    }
  }
  
  async processRequestWithAgent(request, agentInfo) {
    agentInfo.status = 'busy';
    agentInfo.currentLoad++;
    agentInfo.totalRequests++;
    agentInfo.lastActivity = new Date();
    
    const startTime = performance.now();
    
    try {
      // Simulate processing time
      const processingTime = 100 + Math.random() * 400; // 100-500ms
      await new Promise(resolve => setTimeout(resolve, processingTime));
      
      const response = {
        id: `resp_${Date.now()}`,
        agentId: agentInfo.id,
        content: `Response from ${agentInfo.id} to: "${request.message.content}"`,
        processingTime,
        timestamp: new Date()
      };
      
      request.resolve(response);
    } catch (error) {
      agentInfo.status = 'error';
      request.reject(error);
    } finally {
      const responseTime = performance.now() - startTime;
      this.updateAverageResponseTime(responseTime);
      
      agentInfo.currentLoad = Math.max(0, agentInfo.currentLoad - 1);
      if (agentInfo.currentLoad === 0 && agentInfo.status !== 'error') {
        agentInfo.status = 'idle';
      }
    }
  }
  
  selectAgent(requiredCapabilities) {
    const availableAgents = Array.from(this.agents.values()).filter(agent => 
      agent.status === 'idle' || (agent.status === 'busy' && agent.currentLoad < 3)
    );
    
    if (availableAgents.length === 0) return null;
    
    // Filter by capabilities
    let candidateAgents = availableAgents;
    if (requiredCapabilities && requiredCapabilities.length > 0) {
      candidateAgents = availableAgents.filter(agent =>
        requiredCapabilities.every(cap => agent.capabilities.includes(cap))
      );
      
      if (candidateAgents.length === 0) {
        candidateAgents = availableAgents.filter(agent => 
          agent.capabilities.includes('general')
        );
      }
    }
    
    if (candidateAgents.length === 0) return availableAgents[0];
    
    // Apply load balancing strategy
    switch (this.config.loadBalancingStrategy) {
      case 'least-loaded':
        return candidateAgents.reduce((least, current) => 
          current.currentLoad < least.currentLoad ? current : least
        );
      case 'round-robin':
        return candidateAgents[this.metrics.totalRequests % candidateAgents.length];
      case 'random':
        return candidateAgents[Math.floor(Math.random() * candidateAgents.length)];
      default:
        return candidateAgents[0];
    }
  }
  
  async sendInterAgentMessage(fromAgentId, toAgentId, message) {
    if (!this.config.enableInterAgentCommunication) {
      throw new Error('Inter-agent communication is disabled');
    }
    
    const fromAgent = this.agents.get(fromAgentId);
    const toAgent = this.agents.get(toAgentId);
    
    if (!fromAgent || !toAgent) return false;
    
    console.log(`📨 Inter-agent message: ${fromAgentId} -> ${toAgentId}`);
    return true;
  }
  
  getMetrics() {
    this.updateMetrics();
    return { ...this.metrics };
  }
  
  listAgents() {
    return Array.from(this.agents.values());
  }
  
  updateMetrics() {
    const agents = Array.from(this.agents.values());
    this.metrics.totalAgents = agents.length;
    this.metrics.activeAgents = agents.filter(a => a.status !== 'terminated').length;
  }
  
  updateAverageResponseTime(responseTime) {
    const totalRequests = this.metrics.totalRequests;
    this.metrics.averageResponseTime = 
      (this.metrics.averageResponseTime * (totalRequests - 1) + responseTime) / totalRequests;
  }
}

async function runMultiAgentTest() {
  console.log('🚀 Claude Code 3.0 - Multi-Agent System Test');
  console.log('=' .repeat(70));
  console.log('Testing multi-agent orchestration, load balancing, and communication');
  console.log('=' .repeat(70));
  
  const manager = new SimpleMultiAgentManager({
    maxAgents: 5,
    loadBalancingStrategy: 'least-loaded',
    enableInterAgentCommunication: true
  });
  
  try {
    // Start the multi-agent system
    await manager.start();
    
    console.log('\n📊 Initial System State:');
    console.log(`   Agents: ${manager.getMetrics().totalAgents}`);
    console.log(`   Active: ${manager.getMetrics().activeAgents}`);
    
    // Test 1: Basic message processing
    console.log('\n🧪 Test 1: Basic Message Processing');
    console.log('-'.repeat(50));
    
    const testMessages = [
      { content: 'Hello from user 1', capabilities: ['general'] },
      { content: 'Process this text', capabilities: ['text-processing'] },
      { content: 'General query', capabilities: ['general'] },
    ];
    
    for (const testMsg of testMessages) {
      const message = {
        id: `msg_${Date.now()}`,
        role: 'user',
        content: testMsg.content,
        timestamp: new Date()
      };
      
      const startTime = performance.now();
      const response = await manager.processMessage(message, 'session-1', testMsg.capabilities);
      const duration = performance.now() - startTime;
      
      console.log(`   ✅ Message processed by ${response.agentId} in ${duration.toFixed(2)}ms`);
    }
    
    // Test 2: Concurrent load testing
    console.log('\n🧪 Test 2: Concurrent Load Testing (50 messages)');
    console.log('-'.repeat(50));
    
    const concurrentMessages = [];
    const startTime = performance.now();
    
    for (let i = 0; i < 50; i++) {
      const message = {
        id: `concurrent_${i}`,
        role: 'user',
        content: `Concurrent message ${i}`,
        timestamp: new Date()
      };
      
      concurrentMessages.push(
        manager.processMessage(message, `session-${i % 5}`, ['general'])
      );
    }
    
    const responses = await Promise.all(concurrentMessages);
    const totalDuration = performance.now() - startTime;
    
    console.log(`   ✅ Processed ${responses.length} messages in ${totalDuration.toFixed(2)}ms`);
    console.log(`   📊 Average: ${(totalDuration / responses.length).toFixed(2)}ms per message`);
    console.log(`   ⚡ Throughput: ${(responses.length / totalDuration * 1000).toFixed(0)} msg/sec`);
    
    // Test 3: Load balancing verification
    console.log('\n🧪 Test 3: Load Balancing Verification');
    console.log('-'.repeat(50));
    
    const agents = manager.listAgents();
    const agentStats = {};
    
    for (const agent of agents) {
      agentStats[agent.id] = agent.totalRequests;
    }
    
    console.log('   Agent Request Distribution:');
    for (const [agentId, requestCount] of Object.entries(agentStats)) {
      console.log(`     ${agentId}: ${requestCount} requests`);
    }
    
    // Calculate load distribution variance
    const requestCounts = Object.values(agentStats);
    const avgRequests = requestCounts.reduce((sum, count) => sum + count, 0) / requestCounts.length;
    const variance = requestCounts.reduce((sum, count) => sum + Math.pow(count - avgRequests, 2), 0) / requestCounts.length;
    
    console.log(`   📊 Average requests per agent: ${avgRequests.toFixed(1)}`);
    console.log(`   📊 Load distribution variance: ${variance.toFixed(2)}`);
    
    if (variance < avgRequests * 0.5) {
      console.log('   ✅ Load balancing: EXCELLENT (low variance)');
    } else if (variance < avgRequests) {
      console.log('   ✅ Load balancing: GOOD');
    } else {
      console.log('   ⚠️  Load balancing: NEEDS IMPROVEMENT');
    }
    
    // Test 4: Agent lifecycle management
    console.log('\n🧪 Test 4: Agent Lifecycle Management');
    console.log('-'.repeat(50));
    
    // Try to spawn additional agent (may hit limit)
    try {
      const newAgentId = await manager.spawnAgent('specialized-agent', ['specialized']);
      console.log(`   ✅ Spawned specialized agent: ${newAgentId}`);
    } catch (error) {
      console.log(`   ⚠️  Cannot spawn more agents: ${error.message}`);
    }
    
    // Test inter-agent communication
    const agents2 = manager.listAgents();
    if (agents2.length >= 2) {
      const success = await manager.sendInterAgentMessage(
        agents2[0].id, 
        agents2[1].id, 
        { type: 'greeting', content: 'Hello from agent 1' }
      );
      console.log(`   ${success ? '✅' : '❌'} Inter-agent communication: ${success ? 'SUCCESS' : 'FAILED'}`);
    }
    
    // Terminate an existing agent
    const agentsToTerminate = manager.listAgents();
    if (agentsToTerminate.length > 0) {
      const terminated = await manager.terminateAgent(agentsToTerminate[0].id);
      console.log(`   ${terminated ? '✅' : '❌'} Agent termination: ${terminated ? 'SUCCESS' : 'FAILED'}`);
    }
    
    // Test 5: Performance metrics
    console.log('\n📊 Final Performance Metrics:');
    console.log('-'.repeat(50));
    
    const finalMetrics = manager.getMetrics();
    console.log(`   Total Agents: ${finalMetrics.totalAgents}`);
    console.log(`   Active Agents: ${finalMetrics.activeAgents}`);
    console.log(`   Total Requests: ${finalMetrics.totalRequests}`);
    console.log(`   Average Response Time: ${finalMetrics.averageResponseTime.toFixed(2)}ms`);
    
    // Performance analysis
    console.log('\n🎯 Multi-Agent Performance Analysis:');
    console.log('-'.repeat(50));
    
    const throughput = responses.length / (totalDuration / 1000);
    console.log(`✅ Concurrent Processing: ${responses.length} messages simultaneously`);
    console.log(`✅ System Throughput: ${throughput.toFixed(0)} messages/second`);
    console.log(`✅ Load Balancing: Distributed across ${finalMetrics.activeAgents} agents`);
    console.log(`✅ Agent Orchestration: Automatic spawning and lifecycle management`);
    console.log(`✅ Inter-Agent Communication: Message routing between agents`);
    
    if (throughput > 100) {
      console.log('🏆 EXCELLENT: High-throughput multi-agent processing achieved!');
    } else if (throughput > 50) {
      console.log('✅ GOOD: Solid multi-agent performance');
    } else {
      console.log('⚠️  REVIEW: Multi-agent performance could be improved');
    }
    
  } catch (error) {
    console.error('❌ Multi-agent test failed:', error);
  } finally {
    await manager.stop();
  }
  
  console.log('\n' + '='.repeat(70));
  console.log('🎉 Multi-Agent System Test Complete!');
}

// Run test if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runMultiAgentTest().catch(console.error);
}
