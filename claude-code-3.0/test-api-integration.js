#!/usr/bin/env node

/**
 * Claude Code 3.0 - API Integration Test
 * 
 * Tests real Claude API integration when API key is available.
 * Demonstrates the difference between mock and real API testing.
 */

// Simple Claude API client for testing
class SimpleClaudeClient {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseURL = 'https://api.anthropic.com';
  }
  
  async testConnection() {
    if (!this.apiKey) {
      return { success: false, error: 'No API key provided' };
    }
    
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${this.baseURL}/v1/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.apiKey,
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify({
          model: 'claude-3-haiku-20240307', // Use cheapest model for testing
          max_tokens: 50,
          messages: [
            { role: 'user', content: 'Hello! Please respond with exactly: "API connection successful"' }
          ]
        })
      });
      
      const latency = Date.now() - startTime;
      
      if (!response.ok) {
        const error = await response.json().catch(() => ({ error: 'Unknown error' }));
        return {
          success: false,
          latency,
          error: `HTTP ${response.status}: ${error.error?.message || error.error || 'Unknown error'}`
        };
      }
      
      const data = await response.json();
      
      return {
        success: true,
        latency,
        model: data.model,
        response: data.content[0]?.text || 'No response text',
        usage: data.usage,
        cost: this.calculateCost(data.usage.input_tokens, data.usage.output_tokens)
      };
      
    } catch (error) {
      return {
        success: false,
        latency: Date.now() - startTime,
        error: error.message
      };
    }
  }
  
  calculateCost(inputTokens, outputTokens) {
    // Claude 3 Haiku pricing (as of 2024)
    const inputCost = inputTokens * 0.00000025;  // $0.25 per 1M tokens
    const outputCost = outputTokens * 0.00000125; // $1.25 per 1M tokens
    return inputCost + outputCost;
  }
}

// Mock client for comparison
class MockClaudeClient {
  async testConnection() {
    const delay = 200 + Math.random() * 400; // 200-600ms
    await new Promise(resolve => setTimeout(resolve, delay));
    
    return {
      success: true,
      latency: delay,
      model: 'mock-claude-3-haiku',
      response: 'API connection successful (simulated)',
      usage: { input_tokens: 15, output_tokens: 8 },
      cost: 0.000013 // Simulated cost
    };
  }
}

async function runAPIIntegrationTest() {
  console.log('🧪 Claude Code 3.0 - API Integration Test');
  console.log('=' .repeat(60));
  
  const apiKey = process.env.CLAUDE_API_KEY || process.env.ANTHROPIC_API_KEY;
  
  console.log('🔍 Environment Check:');
  console.log(`   API Key: ${apiKey ? '✅ Provided' : '❌ Not found'}`);
  console.log(`   Node.js: ${process.version}`);
  console.log(`   Platform: ${process.platform}-${process.arch}`);
  
  console.log('\n📊 Testing Both Modes:');
  console.log('-' .repeat(40));
  
  // Test Mock Mode
  console.log('\n🎭 Mock Mode Test:');
  const mockClient = new MockClaudeClient();
  const mockResult = await mockClient.testConnection();
  
  console.log(`   Status: ${mockResult.success ? '✅ Success' : '❌ Failed'}`);
  console.log(`   Latency: ${mockResult.latency.toFixed(2)}ms`);
  console.log(`   Model: ${mockResult.model}`);
  console.log(`   Response: "${mockResult.response}"`);
  console.log(`   Tokens: ${mockResult.usage.input_tokens} in, ${mockResult.usage.output_tokens} out`);
  console.log(`   Cost: $${mockResult.cost.toFixed(6)}`);
  
  // Test Real API Mode
  console.log('\n🌐 Real API Mode Test:');
  
  if (!apiKey) {
    console.log('   Status: ⏭️  Skipped (no API key)');
    console.log('   To test real API integration:');
    console.log('   1. Get API key from https://console.anthropic.com/');
    console.log('   2. Set environment variable: export CLAUDE_API_KEY=your_key');
    console.log('   3. Re-run this test');
  } else {
    const realClient = new SimpleClaudeClient(apiKey);
    const realResult = await realClient.testConnection();
    
    console.log(`   Status: ${realResult.success ? '✅ Success' : '❌ Failed'}`);
    console.log(`   Latency: ${realResult.latency.toFixed(2)}ms`);
    
    if (realResult.success) {
      console.log(`   Model: ${realResult.model}`);
      console.log(`   Response: "${realResult.response}"`);
      console.log(`   Tokens: ${realResult.usage.input_tokens} in, ${realResult.usage.output_tokens} out`);
      console.log(`   Cost: $${realResult.cost.toFixed(6)}`);
    } else {
      console.log(`   Error: ${realResult.error}`);
    }
  }
  
  // Performance Comparison
  console.log('\n📈 Performance Comparison:');
  console.log('-' .repeat(40));
  
  if (apiKey) {
    console.log('   Mock vs Real API:');
    console.log(`   • Mock Latency: ${mockResult.latency.toFixed(2)}ms`);
    
    const realClient = new SimpleClaudeClient(apiKey);
    const realResult = await realClient.testConnection();
    
    if (realResult.success) {
      console.log(`   • Real Latency: ${realResult.latency.toFixed(2)}ms`);
      console.log(`   • Difference: ${(realResult.latency - mockResult.latency).toFixed(2)}ms`);
      console.log(`   • Real/Mock Ratio: ${(realResult.latency / mockResult.latency).toFixed(2)}x`);
    }
  } else {
    console.log('   Mock Mode Performance:');
    console.log(`   • Latency: ${mockResult.latency.toFixed(2)}ms`);
    console.log('   • Consistent and predictable');
    console.log('   • No API costs or rate limits');
    console.log('   • Perfect for development and CI/CD');
  }
  
  // Recommendations
  console.log('\n💡 Recommendations:');
  console.log('-' .repeat(40));
  
  if (!apiKey) {
    console.log('   🔧 For Development:');
    console.log('   • Mock mode is perfect for development and testing');
    console.log('   • Provides realistic response times and behavior');
    console.log('   • No costs or rate limits');
    console.log('');
    console.log('   🚀 For Production Validation:');
    console.log('   • Set up Claude API key for real testing');
    console.log('   • Validate actual response quality and performance');
    console.log('   • Monitor costs and rate limits');
  } else {
    console.log('   ✅ You have both modes available!');
    console.log('   • Use Mock mode for development and CI/CD');
    console.log('   • Use API mode for production validation');
    console.log('   • Consider hybrid mode for robust testing');
  }
  
  console.log('\n🎯 Framework Testing Status:');
  console.log('-' .repeat(40));
  console.log('   ✅ Mock mode: Fully functional');
  console.log(`   ${apiKey ? '✅' : '⚠️ '} API mode: ${apiKey ? 'Ready' : 'Requires API key'}`);
  console.log('   ✅ Performance benchmarking: Available');
  console.log('   ✅ Comprehensive reporting: Implemented');
  console.log('   ✅ Error handling: Robust');
  
  console.log('\n' + '=' .repeat(60));
  console.log('🎉 API Integration Test Complete!');
  
  if (!apiKey) {
    console.log('💡 Next step: Add CLAUDE_API_KEY for full testing capability');
  } else {
    console.log('✨ Full testing capability available!');
  }
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
  runAPIIntegrationTest().catch(console.error);
}
